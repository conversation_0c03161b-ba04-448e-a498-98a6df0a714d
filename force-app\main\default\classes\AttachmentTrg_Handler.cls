public class AttachmentTrg_Handler
{
    public static void createRecord123(Attachment att){
        Map<Integer,String> fieldNumberMap = new Map<Integer,String>();
        Map<String,list<string> > phoneMap = new Map<String,list<string>>();
        List<String> lstFieldNames = new list<String>();
        Integer fieldNumber;
        String fieldValue;
        Blob csvFileBody;
        map<String,list<Data_Import_Mapping__c>> dataImportMap = new map<String,list<Data_Import_Mapping__c>>();
        map<String,String> dataImportFieldMap = new map<String,String>();

        String csvFullData;
        csvFileBody = att.Body;
        csvFullData = ''+csvFileBody.toString();
        system.debug('csvFullData>>>>'+csvFullData);
                        
        String[] csvRowValues = csvFullData.split('\n');
        string[] headerNames = csvRowValues[0].split(',');
        system.debug('csvRowValues>>>>'+csvRowValues);
        system.debug('headerNames>>>>'+headerNames);
        system.debug('headerNames>>>>'+headerNames.size());

        for(Data_Import_Mapping__c dimp : [select id,name,CSV_Column_Header__c,Target_sObject_API_Name__c from Data_Import_Mapping__c limit 10000])
        {
            String objName = dimp.Target_sObject_API_Name__c;
            objName = objName.trim().toUpperCase();
            
            if(!dataImportMap.containsKey(objName))
            {
                dataImportMap.put(objName, new List<Data_Import_Mapping__c>());
            }
            dataImportMap.get(objName).add(dimp);
        }
                
        for(Data_Import_Mapping__c dimp1 : dataImportMap.get('ACCOUNT'))
        {
            String clmStr = dimp1.CSV_Column_Header__c;
            clmStr = clmStr.trim().removeEnd(' ').removeStart(' ').toUpperCase();
            dataImportFieldMap.put(clmStr,dimp1.name);
        }

        System.debug('dataImportFieldMap>>>>'+dataImportFieldMap);

        for(Integer i = 0; i < headerNames.size(); i++)
        {
            String hName = headerNames[i];
            System.debug('hName1>>>>'+hName);
            hName = hName.trim().removeEnd(' ').removeStart(' ').toUpperCase();
            System.debug('hName2>>>>'+hName);
            fieldNumberMap.put(i,hName);
        }
        
        system.debug('fieldNumberMap>>>>'+fieldNumberMap);
        List<Account> listAccount = new List<Account>();
        
        for (Integer i = 1; i < csvRowValues.size(); i++)
        {
            account conObj = new account();
            system.debug('csvRowValues>>>>'+csvRowValues[i]);
                                
            string[] csvRecordData = csvRowValues[i].split(',');
            system.debug('csvRecordData>>>>'+csvRecordData);
              
            Account acc = new Account();
            for(integer k =0; k <csvRecordData.size(); k++)
            {    
                
                system.debug('k>>>>'+k);
                system.debug('fieldNumberMap.get>>>>'+fieldNumberMap.get(k));
                system.debug('dataImportFieldMap>>>>'+dataImportFieldMap);
                system.debug('dataImportFieldMap values>>>>'+dataImportFieldMap.get(fieldNumberMap.get(k)));
                system.debug('csvRecordData[k]>>>>'+csvRecordData[k]);
                
                if(dataImportFieldMap!=null && dataImportFieldMap.get(fieldNumberMap.get(k))!=null)
                    acc.put(''+dataImportFieldMap.get(fieldNumberMap.get(k)), ''+csvRecordData[k]);
                system.debug('acc>>>>'+acc);
                
                
            }  
            if(acc!=null)
            {
                acc.put('Title__c','qwe');
                listAccount.add(acc);
            }
        }  
        
        system.debug('listAccount>>>>'+listAccount);
        insert listAccount;

        
                      
    }
    
    public static void createRecord(Attachment att){
        Map < String, Integer > fieldNumberMap = new Map < String, Integer > ();
        Map<String,list<string> > phoneMap = new Map<String,list<string>>();
        List<String> lstFieldNames = new list<String>();
        Integer fieldNumber;
        String fieldValue;
        Blob csvFileBody;

        String csvFullData;
        csvFileBody = att.Body;
        csvFullData = ''+csvFileBody.toString();
        system.debug('csvFullData>>>>'+csvFullData);
                        
        String[] csvRowValues = csvFullData.split('\n');
        string[] headerNames = csvRowValues[0].split(',');
        system.debug('csvRowValues>>>>'+csvRowValues);
        system.debug('headerNames>>>>'+headerNames);
        system.debug('headerNames>>>>'+headerNames.size());
        
 
        for(Integer i = 0; i < headerNames.size(); i++)
        {
            String hName = headerNames[i];
            System.debug('hName1>>>>'+hName);
            hName = hName.trim().removeEnd(' ').removeStart(' ');
            System.debug('hName2>>>>'+hName);
            fieldNumberMap.put(hName, i);
            lstFieldNames.add(headerNames[i].trim());
        }
        
        system.debug('fieldNumberMap>>>>'+fieldNumberMap);

        for (Integer i = 1; i < csvRowValues.size(); i++)
        {
            Contact conObj = new Contact();
            system.debug('csvRowValues>>>>'+csvRowValues[i]);
                                
            string[] csvRecordData = csvRowValues[i].split(',');
            system.debug('csvRecordData>>>>'+csvRecordData);
            
            fieldNumber = fieldNumberMap.get('Phone');
            system.debug('fieldNumber>>>>'+fieldNumber);
            
            fieldValue = csvRecordData[fieldNumber];
            system.debug('fieldValue>>>>'+fieldValue);

           // phoneSet.add(fieldValue.trim());
            phoneMap.put(fieldValue.trim(),csvRecordData);
            system.debug('phoneSet>>>>'+phoneMap);
        }  
        
        list<contact> listContact = new List<Contact>();
        listContact = [select id,name,Phone,AccountId from Contact where Phone in: phoneMap.keyset()];
        
        system.debug('phoneMap>>>>'+phoneMap);
        system.debug('listContact>>>>'+listContact);
        if(listContact.size()>0)
        {
            list<task> testTaskList = new list<task>();
            list<Events__c> eventList = [select id,Name, Event_Type__c,Startup_Round__r.Name from Events__c where id=:att.parentId];
            for(contact ct : listContact)
            {
                Task newTask = new Task(Description = +'Attended by - '+ct.Name+' , Phone - '+ct.Phone,
                                            Priority = 'Normal',
                                            Status = 'Completed',
                                            Subject = 'Start up '+eventList[0].Startup_Round__r.Name+' Event '+eventList[0].name+' attended by '+ct.Name,
                                            ActivityDate = System.now().DAte(),
                                            //IsReminderSet = true,
                                            //ReminderDateTime = System.now()+1,
                                            WhatId = ct.AccountId);             
                testTaskList.add(newTask); 
                phoneMap.remove(''+ct.phone);
            }
            system.debug('phoneMap after>>>>'+phoneMap);
            system.debug('testTaskList>>>>'+testTaskList);
            //insert testTaskList;
        }
        
        if(phoneMap!=null && phoneMap.size()>0)
        {
            list<lead> leadList = new list<lead>();
            system.debug('phoneMap11111>>>>'+phoneMap);
            for(String key : phoneMap.keyset())
            {
                system.debug('key>>>>'+key);
                system.debug('phoneMap value>>>>'+phoneMap.get(key));
                system.debug('fieldNumberMap12212>>>>'+fieldNumberMap);
                
                fieldNumber = fieldNumberMap.get('Last Name');
                system.debug('Last NAME>>>>'+fieldNumberMap.get('Last Name'));
                system.debug('First Name>>>>'+fieldNumberMap.get('First Name'));
                
                String LastName = phoneMap.get(key)[fieldNumberMap.get('Last Name')];
                String CmpName = phoneMap.get(key)[0] +' '+phoneMap.get(key)[fieldNumberMap.get('Last Name')];
                
                Lead l = new Lead(lastname=LastName, company=CmpName,LeadSource='Event');
                leadList.add(l);
            }
            
            if(leadList.size()>0)
                insert leadList;
                
            system.debug('leadList>>>'+leadList);
        }
                      
    }
    
    public static void createRecordNew(Blob csvFileBody,String parentID){
        Map < String, Integer > fieldNumberMap = new Map < String, Integer > ();
        Map<String,string[] > phoneMap = new Map<String,string[] >();
        List<String> lstFieldNames = new list<String>();
        Integer fieldNumber;
        String fieldValue;
        //Blob csvFileBody;

        String csvFullData;
        //csvFileBody = att.Body;
        csvFullData = ''+csvFileBody.toString();
        system.debug('csvFullData>>>>'+csvFullData);
                        
        String[] csvRowValues = csvFullData.split('\n');
        string[] headerNames = csvRowValues[0].split(',');
        system.debug('csvRowValues>>>>'+csvRowValues);
        system.debug('headerNames>>>>'+headerNames);
        system.debug('headerNames>>>>'+headerNames.size());
        
 
        for(Integer i = 0; i < headerNames.size(); i++)
        {
            String hName = headerNames[i];
            System.debug('hName1>>>>'+hName);
            hName = hName.trim().removeEnd(' ').removeStart(' ');
            System.debug('hName2>>>>'+hName);
            fieldNumberMap.put(hName, i);
            lstFieldNames.add(headerNames[i].trim());
        }
        
        system.debug('fieldNumberMap>>>>'+fieldNumberMap);

        for (Integer i = 1; i < csvRowValues.size(); i++)
        {
            Contact conObj = new Contact();
            system.debug('csvRowValues>>>>'+csvRowValues[i]);
                                
            string[] csvRecordData = csvRowValues[i].split(',');
            system.debug('csvRecordData>>>>'+csvRecordData);
            
            fieldNumber = fieldNumberMap.get('Phone');
            system.debug('fieldNumber>>>>'+fieldNumber);
            
            fieldValue = csvRecordData[fieldNumber];
            system.debug('fieldValue>>>>'+fieldValue);

           // phoneSet.add(fieldValue.trim());
            phoneMap.put(fieldValue.trim(),csvRecordData);
            system.debug('phoneSet>>>>'+phoneMap);
        }  
        
        list<contact> listContact = new List<Contact>();
        listContact = [select id,name,Phone,AccountId from Contact where Phone in: phoneMap.keyset()];
        
        system.debug('phoneMap>>>>'+phoneMap);
        system.debug('listContact>>>>'+listContact);
        if(listContact.size()>0)
        {
            list<task> testTaskList = new list<task>();
            list<Attendance__c> attendList = new list<Attendance__c>();
            list<Events__c> eventList = [select id,Name, Event_Type__c,Startup_Round__r.Name from Events__c where id=:parentID];
            for(contact ct : listContact)
            {
               // Date dt = Date.parse(''+phoneMap.get(ct.phone)[fieldNumberMap.get('Registration Time')]);
                //String myDtString = dt.format();
               // System.debug('hjhjh>>>'+myDtString);

                // “Event Type” of “Startup name” was attended (By Contact)
                Task newTask = new Task(Description = +'Attended by - '+ct.Name+' , Phone - '+ct.Phone,
                                            Priority = 'Normal',
                                            Status = 'Completed',
                                            //Subject = 'Event Type '+eventList[0].Startup_Round__r.Name+' Event '+eventList[0].name+' attended by '+ct.Name,
                                            Subject = 'Event Type '+eventList[0].name+' of '+eventList[0].Startup_Round__r.Name+' was attended(By '+ct.Name+').',
                                            ActivityDate = System.now().DAte(),
                                            //IsReminderSet = true,
                                            //ReminderDateTime = System.now()+1,
                                            WhatId = ct.AccountId);             
                testTaskList.add(newTask); 
                
                Attendance__c attend = new Attendance__c();
                attend.Events__c = eventList[0].ID;
                attend.Account__c = ct.AccountId;
                attendList.add(attend);
                
                phoneMap.remove(''+ct.phone);
            }
            system.debug('phoneMap after>>>>'+phoneMap);
            system.debug('testTaskList>>>>'+testTaskList);
            insert testTaskList;
            insert attendList;
        }
        
        if(phoneMap!=null && phoneMap.size()>0)
        {
            list<lead> leadList = new list<lead>();
            system.debug('phoneMap11111>>>>'+phoneMap);
            for(String key : phoneMap.keyset())
            {
                system.debug('key>>>>'+key);
                system.debug('phoneMap value>>>>'+phoneMap.get(key));
                system.debug('fieldNumberMap12212>>>>'+fieldNumberMap);
                
                fieldNumber = fieldNumberMap.get('Last Name');
                system.debug('Last NAME>>>>'+fieldNumberMap.get('Last Name'));
                system.debug('First Name>>>>'+fieldNumberMap.get('First Name'));
                
                String LastName = phoneMap.get(key)[fieldNumberMap.get('Last Name')];
                String CmpName = phoneMap.get(key)[0] +' '+phoneMap.get(key)[fieldNumberMap.get('Last Name')];
                
                Lead l = new Lead(lastname='tes'+LastName, company='fe'+CmpName,LeadSource='Event',
                                FirstName =phoneMap.get(key)[0],phone=key,Email=phoneMap.get(key)[fieldNumberMap.get('Email')]
                                );
                leadList.add(l);
            }
            
            if(leadList.size()>0)
                insert leadList;
                
            system.debug('leadList>>>'+leadList);
        }
                      
    }
}