<apex:page standardController="Startup__c" extensions="VCPointsFilterCtrl" lightningStylesheets="true" >
    <apex:form >
        <apex:pageBlock >
        <!--
            <apex:pageBlockButtons location="bottom">
                <apex:outputPanel id="myButtons" style="align:canter;">
                    <apex:commandButton action="{!Beginning}" title="Beginning" value="<<" disabled="{!disablePrevious}" reRender="myPanel,myButtons"/>
                    <apex:commandButton action="{!Previous}" title="Previous" value="<" disabled="{!disablePrevious}" reRender="myPanel,myButtons"/>        
                    <apex:commandButton action="{!Next}" title="Next" value=">"  disabled="{!disableNext}" reRender="myPanel,myButtons"/>
                    <apex:commandButton action="{!End}" title="End" value=">>" disabled="{!disableNext}" reRender="myPanel,myButtons"/>  
                </apex:outputPanel>
            </apex:pageBlockButtons>
       
            <apex:outputPanel id="myPanel">

                <apex:pageBlockTable value="{!ventureCList}" var="stObj">
                    <apex:column headerValue="Venture Connect Name">
                        <apex:outputLink value="/{!stObj.Id}">{!stObj.Name}</apex:outputLink>
                    </apex:column>
                    <apex:column value="{!stObj.Type__c}"/>
                    <apex:column value="{!stObj.Investment_Stage__c}"/>
                    <apex:column value="{!stObj.Industry__c}"/>
                    <apex:column value="{!stObj.Sector_Focus__c}"/>
                    <apex:column value="{!stObj.Geographical_Focus__c}"/>
                    <apex:column value="{!stObj.Investment_Size_From__c}"/>
                </apex:pageBlockTable>
            </apex:outputPanel>       
             -->
             
             <apex:outputPanel id="myPanel">
                <apex:pageBlockTable value="{!StartupVConnectWrapperList}" var="stObj">
                    
                    <apex:column headerValue="Venture Connect Name">
                        <apex:outputLink value="/{!stObj.vConnect.Id}">{!stObj.vConnect.Name}</apex:outputLink>
                    </apex:column>
                    <apex:column value="{!stObj.vConnect.Industry__c}"/>
                    <apex:column value="{!stObj.vConnect.Sector_Focus__c}"/>
                    <apex:column value="{!stObj.vConnect.Series__c}"/>
                    <apex:column value="{!stObj.vConnect.Investment_Size_From__c}"/>
                    <apex:column value="{!stObj.vConnect.Investment_Size_To__c}"/>
                    
                    <apex:column >
                         <apex:facet name="header">   
                           <apex:commandLink action="{!toggleSort}" value="No of criteria match{!IF(sortDirvf ='asc','▼','▲')}" id="cmdSort" reRender="myPanel,myButtons">
                           </apex:commandLink>
                         </apex:facet>
                         <apex:outputtext value="{!stObj.noOfCriteriaMatch}"></apex:outputtext>
                    </apex:column> 
                    
                </apex:pageBlockTable>
                
            </apex:outputPanel>
            <apex:pageBlockButtons location="bottom">
                <apex:outputPanel id="myButtons" style="align:canter;">
                    <apex:commandButton action="{!Beginning}" title="First" value="<<" disabled="{!disablePrevious}" reRender="myPanel,myButtons"/>
                    <apex:commandButton action="{!Previous}" title="Previous" value="<" disabled="{!disablePrevious}" reRender="myPanel,myButtons"/>        
                    <apex:commandButton action="{!Next}" title="Next" value=">"  disabled="{!disableNext}" reRender="myPanel,myButtons"/>
                    <apex:commandButton action="{!End}" title="Last" value=">>" disabled="{!disableNext}" reRender="myPanel,myButtons"/>  
                </apex:outputPanel>
            </apex:pageBlockButtons>
        </apex:pageBlock>
    </apex:form>
</apex:page>