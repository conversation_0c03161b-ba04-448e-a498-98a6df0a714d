public class AttendanceTriggerHandler
{
    
    public void beforeInsert(List<Attendance__c> attList)
    {
        Map<String,Account> priConAccountMap = new Map<String,Account>();
        Map<String,Lead__c> priConLeadMap = new Map<String,Lead__c>();
        map<id,String> RecordtypeName = new map<id,string>();
        List<Lead__c> leadInsertList = new List<Lead__c>();
        id LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        id LeadRecordTypeIdCXO = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('CXO Genie').getRecordTypeId();
        id AccountRecordTypeIdIPV = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        id AccountRecordTypeIdCXO = Schema.SObjectType.Account.getRecordTypeInfosByName().get('CXO Genie').getRecordTypeId();
        id AttendenceRecordTypeIdIPV = Schema.SObjectType.Attendance__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        id AttendenceRecordTypeIdCXO = Schema.SObjectType.Attendance__c.getRecordTypeInfosByName().get('CXO').getRecordTypeId();
        
        for(Attendance__c att : attList)
        {
            if(att.RecordTypeId == AttendenceRecordTypeIdIPV)
            {
                priConAccountMap.put(AccountRecordTypeIdIPV,null);
                priConLeadMap.put(LeadRecordTypeIdIPV,null);
            }else{
                priConAccountMap.put(AccountRecordTypeIdCXO,null);
                priConLeadMap.put(LeadRecordTypeIdCXO,null);
            }
            if(att.Primary_Contact__c!=null && att.Primary_Contact__c!='')
            {
                priConAccountMap.put(att.Primary_Contact__c,null);
                priConLeadMap.put(att.Primary_Contact__c,null);
            }
            if(att.Personal_Email__c!=null && att.Personal_Email__c!='')
            {
                priConAccountMap.put(att.Personal_Email__c,null);
                priConLeadMap.put(att.Personal_Email__c,null); 
            }
            if(att.Account__c != null || att.Account__c != '')
            {
                att.Attendant_Type__c ='Account';
            }
            else
                att.Attendant_Type__c ='Lead';
        }
        
        if(priConAccountMap!=null && priConAccountMap.size()>0)
        {
            for(Account acc : [select id,Primary_Contact__c,Secondary_Contact__c,Personal_Email__c,Official_Email__c,RecordTypeId,Company__c,Designation__c from Account where ((Primary_Contact__c = :priConAccountMap.keySet() OR Secondary_Contact__c in :priConAccountMap.keySet() OR Personal_Email__c in :priConAccountMap.keySet() OR Official_Email__c in :priConAccountMap.keySet()) AND RecordTypeId in:priConAccountMap.keyset())])
            {
                System.debug('AccList'+acc);
                if(acc.Primary_Contact__c!=null && priConAccountMap.containsKey(acc.Primary_Contact__c))
                    priConAccountMap.put(acc.Primary_Contact__c,acc);
                else if(acc.Secondary_Contact__c !=null && priConAccountMap.containsKey(acc.Secondary_Contact__c ))
                    priConAccountMap.put(acc.Secondary_Contact__c,acc);
                else if(acc.Personal_Email__c!=null && priConAccountMap.containsKey(acc.Personal_Email__c))
                    priConAccountMap.put(acc.Personal_Email__c,acc);
                else if(acc.Official_Email__c!=null && priConAccountMap.containsKey(acc.Official_Email__c))
                    priConAccountMap.put(acc.Official_Email__c,acc);  
                // Added by Karan to check RecordType to convert lead.
                else if(acc.RecordTypeId !=null && priConAccountMap.containsKey(acc.RecordTypeId))
                    priConAccountMap.put(acc.RecordTypeId,acc);
            }
        }
        
        if(priConLeadMap!=null && priConLeadMap.size()>0)
        {
            for(Lead__c ld : [select id,Primary_Contact__c,Secondary_Contact__c,Personal_Email__c,Official_Email__c,RecordTypeId from Lead__c where (Primary_Contact__c = :priConLeadMap.keySet()  OR Secondary_Contact__c in : priConLeadMap.keyset() OR Personal_Email__c in : priConLeadMap.keyset() OR Official_Email__c in : priConLeadMap.keyset()) AND RecordTypeId IN :priConLeadMap.keySet() ])
            {
                
                if(ld.Primary_Contact__c!=null && priConLeadMap.containsKey(ld.Primary_Contact__c))
                    priConLeadMap.put(ld.Primary_Contact__c,ld);
                else if(ld.Secondary_Contact__c !=null && priConLeadMap.containsKey(ld.Secondary_Contact__c ))
                    priConLeadMap.put(ld.Secondary_Contact__c,ld);
                else if(ld.Personal_Email__c!=null && priConLeadMap.containsKey(ld.Personal_Email__c))
                    priConLeadMap.put(ld.Personal_Email__c,ld);
                else if(ld.Official_Email__c!=null && priConLeadMap.containsKey(ld.Official_Email__c))
                    priConLeadMap.put(ld.Official_Email__c,ld);
                // Added by Karan to check RecordType to convert lead.
                else if(ld.RecordTypeId !=null && priConLeadMap.containsKey(ld.RecordTypeId))
                    priConLeadMap.put(ld.RecordTypeId,ld); 
            }
        }
        
        for(Attendance__c att : attList)
        {
            if(att.Primary_Contact__c!=null && att.Primary_Contact__c!='')
            {
                if(priConAccountMap!=null && priConAccountMap.get(att.Primary_Contact__c)!=null)
                {
                    att.Account__c = priConAccountMap.get(att.Primary_Contact__c).ID;  
                }
                else if(priConAccountMap!=null && priConAccountMap.get(att.Personal_Email__c)!=null)
                {
                    att.Account__c = priConAccountMap.get(att.Personal_Email__c).ID;
                }
                // Added by Karan to check RecordType to convert lead.
                else if(priConAccountMap!=null && priConAccountMap.get(att.RecordTypeId)!=null)
                {
                    att.Account__c = priConAccountMap.get(att.RecordTypeId).ID;
                }
                else if(priConLeadMap!=null && priConLeadMap.get(att.Primary_Contact__c)!=null)
                {
                    att.Lead__c = priConLeadMap.get(att.Primary_Contact__c).ID;
                }
                else if(priConLeadMap!=null && priConLeadMap.get(att.Personal_Email__c)!=null)
                {
                    att.Lead__c = priConLeadMap.get(att.Personal_Email__c).ID;
                }
                // Added by Karan to check RecordType to convert lead.
                else if(priConLeadMap!=null && priConLeadMap.get(att.RecordTypeId)!=null)
                {
                    att.Lead__c = priConLeadMap.get(att.RecordTypeId).ID;
                } 
                else
                {
                    Lead__c ldObj = new Lead__c();
                    ldObj.Primary_Contact__c = att.Primary_Contact__c;
                    ldObj.Name = att.Name;
                    ldObj.Personal_Email__c = att.Personal_Email__c ;
                    ldObj.Company__c = att.Company__c;
                    ldObj.Designation__c = att.Designation__c;
                    //ldObj.RecordTypeId = att.RecordTypeId;
                    if(att.RecordTypeId == AttendenceRecordTypeIdIPV)
                    {
                        ldObj.RecordTypeId = LeadRecordTypeIdIPV;
                    } 
                    else if (att.RecordTypeId == AttendenceRecordTypeIdCXO)
                    {
                        ldObj.RecordTypeId = LeadRecordTypeIdCXO;
                    }
                    
                    /* Commented as per the requirement that the Lead default owner will be Pallavi and Title should not be mandatory
ldObj.Relationship_Owner__c = att.Relationship_Owner__c;
ldObj.Title__c = att.Title__c ;
*/
                    
                    if(System.Label.Lead_Default_RM_Owner!=null && System.Label.Lead_Default_RM_Owner!='')
                        ldObj.Relationship_Owner__c = System.Label.Lead_Default_RM_Owner; 
                    
                    ldObj.Lead_Source__c = att.Lead_Source__c;
                    ldObj.Preferred_Email__c = att.Preferred_Email__c;
                    ldObj.Primary_Country_Code__c = att.Primary_Country_Code__c;
                    ldObj.Id = att.Lead__c;
                    leadInsertList.add(ldObj);
                }
            }
        }
        
        if(leadInsertList!=null && leadInsertList.size()>0)
        {   
            insert leadInsertList;
            priConLeadMap = new Map<String,Lead__c>();
            
            for(Lead__c ld : leadInsertList)
            {
                priConLeadMap.put(ld.Primary_Contact__c ,ld);
            }
            
            for(Attendance__c att : attList)
            {               
                if(priConLeadMap!=null && priConLeadMap.get(att.Primary_Contact__c)!=null)
                {
                    att.Lead__c = priConLeadMap.get(att.Primary_Contact__c).ID;
                }
            }
        }
    }
    
    //ADDED BY JAY DABHI on 6th september 2024 for handling user activity based on Attendence Insert
    public void handleEventActivity(List<Attendance__c> attandenceList) {
    List<User_Activity__c> userActivityRecords = new List<User_Activity__c>();
    id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
    
   
    Set<Id> attendanceIds = new Set<Id>();
    for (Attendance__c attendenceRecord : attandenceList) {
        attendanceIds.add(attendenceRecord.Id);
    }

    List<Attendance__c> attendanceRecordsWithDetails = [SELECT Id, Events__r.Name, Event_Type__c, Event_Date__c, Account__c, Lead__c
                                                       FROM Attendance__c
                                                       WHERE Id IN :attendanceIds];

    Map<Id, Attendance__c> attendanceMap = new Map<Id, Attendance__c>();
    for (Attendance__c attendance : attendanceRecordsWithDetails) {
        attendanceMap.put(attendance.Id, attendance);
    }
    for (Attendance__c attendenceRecord : attandenceList) {
        if (!attendanceMap.containsKey(attendenceRecord.Id)) {
            system.debug('Skipping event due to missing data for event: ' + attendenceRecord.Id);
            continue; // Skip if record doesn't have the required data
        }
        
        attendenceRecord = attendanceMap.get(attendenceRecord.Id); 

        Set<String> validEventTypes = new Set<String>{
            'Investor call', 'Feedback Form', 'Performance Review Call', 'Masterclass',
            'FGD', 'Interested in Investing', 'Feedback Form Complete', 'Annual Event', 'Ask Me Anything',
            'Panel Discussion', 'Offline Events', 'Expert Panel', 'Founder Stories', 'Other IPV Sessions', 'Online Events', 'Founder Call'
        };

        if (attendenceRecord != null && validEventTypes.contains(attendenceRecord.Event_Type__c)) {
            string eventUrl;
            eventUrl = URL.getOrgDomainURL().toExternalForm() + '/' + attendenceRecord.Events__c;

            User_Activity__c accountActivity = new User_Activity__c();
            accountActivity.Related_Account__c = attendenceRecord.Account__c;
            accountActivity.Activity_Type__c = 'Attendance - FC, IC, Events, Feedback Form, and all attendance that gets uploaded';
            accountActivity.Activity_Detail_RICH__c = 'Attended ' + (eventUrl != '' ? '<a href="' + eventUrl + '" target="_blank">' + attendenceRecord.Events__r.Name + '</a>' : attendenceRecord.Events__r.Name) + ' (' + attendenceRecord.Event_Type__c + ')';
            accountActivity.Time_Stamp__c = attendenceRecord.Event_Date__c;
            userActivityRecords.add(accountActivity);
            system.debug('Adding Event Raised Activity for event: ' + attendenceRecord.Id + ' Details: ' + accountActivity);

            User_Activity__c leadActivity = new User_Activity__c();
            leadActivity.Related_Lead__c = attendenceRecord.Lead__c;
            leadActivity.Activity_Type__c = 'Attendance - FC, IC, Events, Feedback Form, and all attendance that gets uploaded';
            leadActivity.Activity_Detail_RICH__c = 'Attended ' + (eventUrl != '' ? '<a href="' + eventUrl + '" target="_blank">' + attendenceRecord.Events__r.Name + '</a>' : attendenceRecord.Events__r.Name) + ' (' + attendenceRecord.Event_Type__c + ')';
            leadActivity.Time_Stamp__c = attendenceRecord.Event_Date__c;
            userActivityRecords.add(leadActivity);
            system.debug('Adding Event Raised Activity for event: ' + attendenceRecord.Id + ' Details: ' + leadActivity);
        } else {
            system.debug('Skipped event Insert for event: ' + attendenceRecord.Id + ' Status: ' + attendenceRecord.Event_Type__c);
        }
    }

    
    if (!userActivityRecords.isEmpty()) {
        insert userActivityRecords;
    }
}

        
    //  Changed By Sahil For Attendance Counts On Account Object.  ==> 17.05.2024
    public void updateCountOnAccount(List<Attendance__c> attendanceList)
    {
        Set<Id> accountIds = new Set<Id>();
        Set<Id> leadIds = new Set<Id>();
        for (Attendance__c att : attendanceList) {
            if (att.Account__c != null) {
                accountIds.add(att.Account__c);
            }
            if(att.Lead__c != null)
            {
                leadIds.add(att.Lead__c);
            }
        }
    
        if (accountIds.isEmpty() && leadIds.isEmpty())
        {
            return;
        }
        
        if(!accountIds.isEmpty())
        {
            Map<Id, Integer> onlineOfflineMap = new Map<Id, Integer>();
            Map<Id, Integer> investorCallMap = new Map<Id, Integer>();
            Map<Id, Integer> founderCallMap = new Map<Id, Integer>();
            
            
            for (AggregateResult aggregate : [SELECT COUNT(Id) counts,  Account__c account FROM Attendance__c WHERE Events__r.Event_Type__c IN ('Online Events' , 'Offline Events') AND Events__r.Participant_Status__c = 'Attended' AND Account__c IN :accountIds GROUP BY Account__c ]) {
                Id accountId = (Id) aggregate.get('account');
                Integer counts = (Integer)aggregate.get('counts');
                onlineOfflineMap.put(accountId , counts);
            }
            
            for (AggregateResult aggregate : [SELECT COUNT(Id) counts,  Account__c account FROM Attendance__c WHERE Events__r.Event_Type__c IN ('Investor call') AND Account__c IN :accountIds GROUP BY Account__c]) {
                Id accountId = (Id) aggregate.get('account');
                Integer counts = (Integer)aggregate.get('counts');
                investorCallMap.put(accountId , counts);
            }
    
            for (AggregateResult aggregate : [SELECT COUNT(Id) counts,  Account__c account FROM Attendance__c WHERE Events__r.Event_Type__c IN ('Founder Call') AND Account__c IN :accountIds GROUP BY Account__c]) {
                Id accountId = (Id) aggregate.get('account');
                Integer counts = (Integer)aggregate.get('counts');
                founderCallMap.put(accountId , counts);
            }
            
            List<Account> accountsToUpdate = new List<Account>();
            
            for (Id accountId : accountIds) {
                Boolean flag = false;
                Account acc = new Account(Id = accountId);
                
                if(onlineOfflineMap.containsKey(accountId))
                {
                    System.debug('Account Name >>> '+ acc.Name + ' Offline Online Count Before >>> ' + acc.Total_Numbe_of_Offline_Online_event__c);
                    acc.Total_Numbe_of_Offline_Online_event__c = onlineOfflineMap.get(accountId);
                    System.debug('Account Name >>> '+ acc.Name + ' Offline Online Count After >>> ' + onlineOfflineMap.get(accountId));
                    flag = true;                
                }
                else
                {
                    acc.Total_Numbe_of_Offline_Online_event__c = 0;
                    flag = true; 
                }
                
                if(investorCallMap.containsKey(accountId))
                {
                    System.debug('Account Name >>> '+ acc.Name + ' Investor Call Count Before >>> ' + acc.Total_Number_of_Investor_Calls__c);
                    acc.Total_Number_of_Investor_Calls__c = investorCallMap.get(accountId);
                    System.debug('Account Name >>> '+ acc.Name + ' Investor Call Count After >>> ' + onlineOfflineMap.get(accountId));
                    flag = true;
                }
                else
                {
                    acc.Total_Number_of_Investor_Calls__c = 0;
                    flag = true;
                }
                
                if(founderCallMap.containsKey(accountId))
                {
                    System.debug('Account Name >>> '+ acc.Name + ' Founder Call Count Before >>> ' + acc.Total_Number_of_Founder_Calls__c);
                    acc.Total_Number_of_Founder_Calls__c = founderCallMap.get(accountId);
                    System.debug('Account Name >>> '+ acc.Name + ' Founder Call Count After >>> ' + onlineOfflineMap.get(accountId));
                    flag = true;
                }
                else
                {
                    acc.Total_Number_of_Founder_Calls__c = 0;
                    flag = true;
                }
                if(flag)
                {
                    accountsToUpdate.add(acc);
                }        
            }
        
            if (!accountsToUpdate.isEmpty()) {
                update accountsToUpdate;
            }
        }
        
        if(!leadIds.isEmpty())
        {
            Map<Id, Integer> onlineOfflineMap = new Map<Id, Integer>();
            Map<Id, Integer> investorCallMap = new Map<Id, Integer>();
            Map<Id, Integer> founderCallMap = new Map<Id, Integer>();
            
            
            for (AggregateResult aggregate : [SELECT COUNT(Id) counts,  Lead__c lead FROM Attendance__c WHERE Events__r.Event_Type__c IN ('Online Events' , 'Offline Events') AND Events__r.Participant_Status__c = 'Attended' AND Lead__c IN :leadIds GROUP BY Lead__c ]) {
                Id leadId = (Id) aggregate.get('lead');
                Integer counts = (Integer)aggregate.get('counts');
                onlineOfflineMap.put(leadId , counts);
            }
            
            for (AggregateResult aggregate : [SELECT COUNT(Id) counts,  Lead__c lead FROM Attendance__c WHERE Events__r.Event_Type__c IN ('Investor call') AND Lead__c IN :leadIds GROUP BY Lead__c]) {
                Id leadId = (Id) aggregate.get('lead');
                Integer counts = (Integer)aggregate.get('counts');
                investorCallMap.put(leadId , counts);
            }
    
            for (AggregateResult aggregate : [SELECT COUNT(Id) counts,  Lead__c lead FROM Attendance__c WHERE Events__r.Event_Type__c IN ('Founder Call') AND Lead__c IN :leadIds GROUP BY Lead__c]) {
                Id leadId = (Id) aggregate.get('lead');
                Integer counts = (Integer)aggregate.get('counts');
                founderCallMap.put(leadId , counts);
            }
            
            List<Lead__c> leadsToUpdate = new List<Lead__c>();
            
            for (Id leadId : leadIds) {
                Boolean flag = false;
                Lead__c lead = new Lead__c(Id = leadId);
                
                if(onlineOfflineMap.containsKey(leadId))
                {
                    System.debug('Lead Name >>> '+ lead.Name + ' Offline Online Count Before >>> ' + lead.Total_Number_of_Offline_Online_event__c);
                    lead.Total_Number_of_Offline_Online_event__c = onlineOfflineMap.get(leadId);
                    System.debug('Lead Name >>> '+ lead.Name + ' Offline Online Count After >>> ' + onlineOfflineMap.get(leadId));
                    flag = true;                
                }
                else
                {
                    lead.Total_Number_of_Offline_Online_event__c = 0;
                    flag = true; 
                }
                
                if(investorCallMap.containsKey(leadId))
                {
                    System.debug('Lead Name >>> '+ lead.Name + ' Investor Call Count Before >>> ' + lead.Total_Number_of_Investor_Calls__c);
                    lead.Total_Number_of_Investor_Calls__c = investorCallMap.get(leadId);
                    System.debug('Lead Name >>> '+ lead.Name + ' Investor Call Count After >>> ' + onlineOfflineMap.get(leadId));
                    flag = true;
                }
                else
                {
                    lead.Total_Number_of_Investor_Calls__c = 0;
                    flag = true;
                }
                
                if(founderCallMap.containsKey(leadId))
                {
                    System.debug('Lead Name >>> '+ lead.Name + ' Founder Call Count Before >>> ' + lead.Total_Number_of_Founder_Calls__c);
                    lead.Total_Number_of_Founder_Calls__c = founderCallMap.get(leadId);
                    System.debug('Lead Name >>> '+ lead.Name + ' Founder Call Count After >>> ' + onlineOfflineMap.get(leadId));
                    flag = true;
                }
                else
                {
                    lead.Total_Number_of_Founder_Calls__c = 0;
                    flag = true;
                }
                if(flag)
                {
                    leadsToUpdate.add(lead);
                }        
            }
        
            if (!leadsToUpdate.isEmpty()) {
                update leadsToUpdate;
            }  
        }
    }
    
    public void createPointTransaction(List<Attendance__c> attList){
        set<Id> setEVId = new set<Id>();
        set<Id> setATId = new set<Id>();
        system.debug('in attendance trigger ::');
        for(Attendance__c at : attList){
            setEVId.add(at.Events__c);
            setATId.add(at.Id);
        }
        system.debug('in trigger ::'+setEVId);
        if(setEVId != null){
            List<Attendance__c> attList1 = [SELECT Id,Event_Date__c,Account__c,Events__c,Events__r.Startup_Round__r.Date_Of_Founders_Call__c,Events__r.Startup_Round__c,Events__r.Event_Type__c , Events__r.Startup_Round__r.Startup__c,Events__r.Startup_Round__r.Date_of_Investor_Call__c FROM Attendance__c 
                                            WHERE Id In :setATId and Events__c IN :setEVId and (Events__r.Event_Type__c = 'Investor Call' OR Events__r.Event_Type__c = 'Feedback Form Complete')];
            system.debug('in trigger ::'+attList1);
            if(!attList.isEmpty()){
                List<Points_Transaction__c> points = new List<Points_Transaction__c>();
                for(Attendance__c at : attList1){
                    if(at.Account__c !=  null){
                        Points_Transaction__c point =  new Points_Transaction__c();
                        point.Credit_To__c = at.Account__c;
                        point.Debit_From__c = Label.PT_Debit_Account;
                        point.Points_Alloted__c = 10;
                        point.Event_Date__c = at.Events__r.Startup_Round__r.Date_of_Investor_Call__c;
                        point.Event_Name__c = at.Events__c;
                        point.Member_Name__c = at.Account__c;
                        point.Startup__c = at.Events__r.Startup_Round__r.Startup__c;
                        point.Startup_Round__c = at.Events__r.Startup_Round__c;
                        point.Point_Type__c = 'Investor Call';
                        if(at.Events__r.Event_Type__c == 'Feedback Form Complete'){
                            point.Points_Alloted__c = 20;
                            point.Point_Type__c = 'Form Complete';
                            point.Event_Date__c =at.Events__r.Startup_Round__r.Date_Of_Founders_Call__c;
                        }
                            
                        
                        points.add(point);
                    }                
                }
                if(!points.isEmpty()){
                    system.debug('in trigger ::'+points);
                    insert points; 
                }
            }
        }
    }
}