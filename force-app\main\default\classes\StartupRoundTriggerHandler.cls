public class StartupRoundTriggerHandler
{
    //Added by <PERSON><PERSON><PERSON> for nomeclature Date: 23-10-2023
    public void beforeInsertNew(List<Startup_Round__c> triggerNew)
    {
        Map<Id,Map<String,Integer>> startupChildPreEmptiveCountMap = new Map<Id,Map<String,Integer>>();
        Map<Id,String> startupPublicNameMap = new Map<Id,String>();
        //Map<Id, integer> masterChildCountMap = new Map<Id, integer>();
        system.debug('triggerNew[0].Createddate>>>>>>>'+triggerNew[0].Createddate);
        Date recordCreatedDate = Date.today();
        for(Startup_Round__c sr : triggerNew)
        {
            if(sr.round_type__c == 'Raise')
                startupChildPreEmptiveCountMap.put(sr.Startup__c,new Map<String,Integer>());
        }
        system.debug('startupChildPreEmptiveCountMap size>>>>>>'+startupChildPreEmptiveCountMap.size());
        if(startupChildPreEmptiveCountMap.size()==0 || startupChildPreEmptiveCountMap.isEmpty())
            return;
        
        /* 
//We need to eliminate/remove this code. for now conitinue untill new name will work fine.
for(Startup__c sr : [SELECT id,Public_Name__c, (SELECT id,Round_type__C FROM Startup_Round__r) FROM Startup__c where id in : startupChildPreEmptiveCountMap.keyset()])
{ 
masterChildCountMap.put(sr.Id,sr.Startup_Round__r.size());
}
*/
        
        /*
for(Startup__c stObj : [select id,Public_Name__c from Startup__c where id in : startupChildPreEmptiveCountMap.keyset()])
{
startupPublicNameMap.put(stObj.Id,stObj.Public_Name__c);
}
*/
        
        List<Startup_Round__c> startupRoundList = new List<Startup_Round__c>();
        startupRoundList = [SELECT id,name,createddate,Round_type__c,Startup__c,Startup__r.Public_Name__c,Pre_Emptive_Deal__c,Startup_name_auto__c FROM Startup_Round__c where Startup__c in :startupChildPreEmptiveCountMap.keyset() AND round_type__c ='Raise' order by createddate desc];
        
        for(Startup_Round__c stRound : startupRoundList){
            Integer lastRowCount = 0;
            System.debug('stRound.createddate.Date()>>>>>>>'+stRound.createddate.Date());
            System.debug('recordCreatedDate >>>>>>>'+recordCreatedDate );
            startupPublicNameMap.put(stRound.Startup__c,stRound.Startup__r.Public_Name__c);
            
            if(!startupChildPreEmptiveCountMap.get(stRound.Startup__c).ContainsKey('Pre_Emptive_Deal') && stRound.Pre_Emptive_Deal__c){
                
                //get the last number of Startup_name_auto__c field and pass it to Map instead of AutoSequence__c
                lastRowCount = getLastSequence(stRound.Startup_name_auto__c!= null? stRound.Startup_name_auto__c: stRound.name,'_P');
                if(stRound.createddate.Date() != recordCreatedDate)
                    lastRowCount = lastRowCount + 1;
                
                startupChildPreEmptiveCountMap.get(stRound.Startup__c).put('Pre_Emptive_Deal',lastRowCount );
            }
            else if(!startupChildPreEmptiveCountMap.get(stRound.Startup__c).ContainsKey('Non_Pre_Emptive_Deal') && !stRound.Pre_Emptive_Deal__c){
                //get the last number of Startup_name_auto__c field and pass it to Map instead of AutoSequence__c
                lastRowCount = getLastSequence(stRound.Startup_name_auto__c!= null? stRound.Startup_name_auto__c: stRound.name,'_R');
                if(stRound.createddate.Date() != recordCreatedDate)
                    lastRowCount = lastRowCount + 1;
                
                startupChildPreEmptiveCountMap.get(stRound.Startup__c).put('Non_Pre_Emptive_Deal',lastRowCount);
            }
        }
        
        if(startupPublicNameMap.isEmpty())
        {
            for(Startup__c stObj : [select id,Public_Name__c from Startup__c where id in : startupChildPreEmptiveCountMap.keyset()])
            {
                startupPublicNameMap.put(stObj.Id,stObj.Public_Name__c);
            }
        }
        
        for(Startup_Round__c sr : triggerNew)
        {
            if(startupChildPreEmptiveCountMap.containsKey(sr.Startup__c))
            {
                String publicStr = startupPublicNameMap.get(sr.Startup__c); 
                //Integer oldLastNo = masterChildCountMap.get(sr.Startup__c) + 1;
                
                Integer lastNo = 1;  //masterChildCountMap.get(sr.Startup__c) + 1;        
                //Integer lastnoforPreEm = masterChildPrimitiveCountMap.get(sr.Startup__c) + 1;
                //Integer lastnoRaise = masterChildRaiseCountMap.get(sr.Startup__c) + 1;
                
                if(publicStr !=null && publicStr !='')
                {
                    if(publicStr.length()>8)
                    {
                        publicStr = publicStr.substring(0, 8);
                    }
                    publicStr = publicStr.remove('-');
                    //sr.Name = 'SR_'+publicStr +'_R'+lastNo;
                    //Nomenclature for Rounds should be “Startup Name_Round Number” for example, Wishup_R1 for Round 1, Wishup_R2 for Round 2
                    //sr.Name = publicStr +'_R'+oldLastNo;
                    
                    if(sr.round_type__c =='Raise'){
                        // Added for new round name nomeclature 20.9.23
                        if(sr.Pre_Emptive_Deal__c == true){
                            if(startupChildPreEmptiveCountMap.get(sr.Startup__c).containsKey('Pre_Emptive_Deal'))
                                lastNo = startupChildPreEmptiveCountMap.get(sr.Startup__c).get('Pre_Emptive_Deal');    
                            
                            sr.Startup_name_auto__c = publicStr +'_P'+lastNo;
                            startupChildPreEmptiveCountMap.get(sr.Startup__c).put('Pre_Emptive_Deal',lastNo);
                        }
                        else if(Sr.Pre_Emptive_Deal__c == false){
                            if(startupChildPreEmptiveCountMap.get(sr.Startup__c).containsKey('Non_Pre_Emptive_Deal'))
                                lastNo = startupChildPreEmptiveCountMap.get(sr.Startup__c).get('Non_Pre_Emptive_Deal');
                            
                            sr.Startup_name_auto__c = publicStr +'_R'+lastNo;
                            startupChildPreEmptiveCountMap.get(sr.Startup__c).put('Non_Pre_Emptive_Deal',lastNo);
                        }
                    }
                }
                else
                {
                    sr.addError('Public name is required on parent Start up.');
                }
            }
        }        
    }
    
    Public Integer getLastSequence(String strName,String separator){
        Integer ret = 0;        
        if(strName!=null && strName!='' && strName.contains(separator))
        {
            String lastStr = strName.substringAfterLast(separator);
            if(lastStr.isNumeric())
                ret = Integer.valueOf(lastStr);
        }
        return  ret;
    }
    
    public void beforeInsert(List<Startup_Round__c> triggerNew)
    {
        Map<Id, Startup__c> masterChildMap = new Map<Id, Startup__c>();
        Map<Id, integer> masterChildCountMap = new Map<Id, integer>();
        
        for(Startup_Round__c sr : triggerNew)
        {
            masterChildMap.put(sr.Startup__c,null);
        }
        system.debug('1. masterChildMap>>>>'+masterChildMap);
        
        for(Startup__c sr : [SELECT id,Public_Name__c, (SELECT id FROM Startup_Round__r) FROM Startup__c where id in : masterChildMap.keyset()])
        {
            //system.debug(sr.Startup_Round__r.totalSize);
            system.debug(sr.Startup_Round__r.size());
            masterChildMap.put(sr.Id,sr);
            masterChildCountMap.put(sr.Id,sr.Startup_Round__r.size());
        }
        
        system.debug('2. masterChildMap>>>>'+masterChildMap);
        
        for(Startup_Round__c sr : triggerNew)
        {
            if(masterChildMap.get(sr.Startup__c)!=null && masterChildMap.get(sr.Startup__c).Public_Name__c!=null)
            {
                String publicStr = masterChildMap.get(sr.Startup__c).Public_Name__c; 
                Integer lastNo = masterChildCountMap.get(sr.Startup__c) + 1;        
                
                if(publicStr !=null && publicStr !='')
                {
                    if(publicStr.length()>5)
                    {
                        publicStr = publicStr.substring(0, 5);
                    }
                    publicStr = publicStr.remove('-');
                    //sr.Name = 'SR_'+publicStr +'_R'+lastNo;
                    //Nomenclature for Rounds should be “Startup Name_Round Number” for example, Wishup_R1 for Round 1, Wishup_R2 for Round 2
                    sr.Name = publicStr +'_R'+lastNo;
                }
                else
                {
                    //sr.Name = 'SR_R'+lastNo;
                    sr.addError('Public name is required on parent Start up.');
                }     
                masterChildCountMap.put(sr.Startup__c,lastNo);        
            }
        }    
    }
    
    /*  public void calculateAndUpdateStartupNameAuto(List<Startup_Round__c> startupRounds) 
{
// Create a set of Startup Ids for the rounds passed into the method
Set<Id> startupIds = new Set<Id>();
for (Startup_Round__c sr : startupRounds) {
startupIds.add(sr.Startup__c);
}

// Query existing rounds for the startups in startupIds
Map<Id, List<Startup_Round__c>> startupToExistingRoundsMap = new Map<Id, List<Startup_Round__c>>();
for (Startup_Round__c existingRound : [SELECT Id, Startup__c, Round_Type__c FROM Startup_Round__c WHERE Startup__c IN :startupIds]) {
if (!startupToExistingRoundsMap.containsKey(existingRound.Startup__c)) {
startupToExistingRoundsMap.put(existingRound.Startup__c, new List<Startup_Round__c>());
}
startupToExistingRoundsMap.get(existingRound.Startup__c).add(existingRound);
}

Map<String, Integer> roundTypeCountMap = new Map<String, Integer>();
for (Startup_Round__c sr : startupRounds) {
String roundType = sr.Round_Type__c;
String publicStr = sr.Startup_public_name__c;
if (!roundTypeCountMap.containsKey(roundType)) {
roundTypeCountMap.put(roundType, 1);
} else {
roundTypeCountMap.put(roundType, roundTypeCountMap.get(roundType) + 1);
}

Integer existingRoundCount = startupToExistingRoundsMap.containsKey(sr.Startup__c) ? startupToExistingRoundsMap.get(sr.Startup__c).size() : 0;
Integer totalRoundCount = existingRoundCount + roundTypeCountMap.get(roundType);

if (roundType == 'Raise') {
sr.Startup_name_auto__c = publicStr.substring(0, 4) + '_R_R' + totalRoundCount;
} else if (roundType == 'Exit') {
sr.Startup_name_auto__c = publicStr.substring(0, 4) + '_E_R' + totalRoundCount;
} else if (roundType == 'Internal Transfer') {
sr.Startup_name_auto__c = publicStr.substring(0, 4) + '_IT_R' + totalRoundCount;
} else if (roundType == 'Pre-Emptive') {
sr.Startup_name_auto__c = publicStr.substring(0, 4) + '_PE_R' + totalRoundCount;
}
}
}*/
    
    public void afterInsert(List<Startup_Round__c> triggerNew)
    {
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('StartupRoundTriggerHandlers API setting>>>'+settingList);
        Set<Id> roundForAPI1 = new Set<Id>();
        Set<Id> roundForAPI2 = new Set<Id>();
        Integer count = 0;
        system.debug('StartupRoundTriggerHandlers API setting>>>'+settingList);
        
        //Addedd by bharat for secondaary Module Requirement 15-05-2025
        List<Startup__c> startupToUpdate = new List<Startup__c>();
        Map<Id, Decimal> startupReservePriceMap = new Map<Id, Decimal>();
        Set<Id> startupIdSet = new Set<Id>();
        List<Startup_Round__c> childRoundList = new List<Startup_Round__c>();
        Boolean isfundingClose;
        
        //Addedd by bharat for secondaary Module Requirement 15-05-2025
        for(Startup_Round__c sRound: triggerNew){
            
            startupIdSet.add(sRound.Startup__c);
        }
        System.debug('startupIdSet>>>>>>>>>>>>' + startupIdSet);
        
        //Addedd by bharat for secondaary Module Requirement 15-05-2025
        If(startupIdSet != null && startupIdSet.size()>0 ){
            startupReservePriceMap = findLatestRoundIssuePrice(startupIdSet);
            childRoundList = [SELECT Id , Name , Startup__c , App_Category__c FROM Startup_Round__c WHERE Startup__c IN : startupIdSet AND Round_Type__c = 'Raise'];
        }
        
        //Addedd by bharat for secondaary Module Requirement 15-05-2025
        if(childRoundList != null && childRoundList.size()>0 )
        {
            for(Startup_round__c sr : childRoundList)
            {
                if(sr.App_Category__c == 'Funding Closed'){
                    isfundingClose = TRUE;
                }else{
                    isfundingClose = false;
                    break;
                }
                System.debug('count>>>>>>>>>>' + count );
                if(roundForAPI1.size() < 100){
                    roundForAPI1.add(sr.Id);
                }else{
                    roundForAPI2.add(sr.Id);
                }
                count ++;
            }
        }
        
        //Addedd by bharat for secondaary Module Requirement 15-05-2025        
        if(startupReservePriceMap != null)
        {
            for(Id startupId : startupReservePriceMap.keySet())
            {
                Startup__c updatedStr = new Startup__C();
                updatedStr.Id = StartupId ;
                updatedStr.Reserve_Price__c = startupReservePriceMap.get(startupId);
                
                /*if(isfundingClose){
                    updatedStr.Applicable_for_Secondary_App__c = 'Yes';
                }else{
                    updatedStr.Applicable_for_Secondary_App__c = 'No - Funding is in progress';
                }*/
                startupToUpdate.add(updatedStr);
            }
        }
        
        //Addedd by bharat for secondaary Module Requirement 15-05-2025
        If(startupToUpdate != null && startupToUpdate.size()>0 ){
            Update startupToUpdate;
        }
        System.debug('startupToUpdate>>>>>>>>>>>' + startupToUpdate);
    
        if(settingList!=null && settingList.size()>0 && settingList[0].Enable_Startup_API_Call__c)
        {
            for(Startup_Round__c sr : triggerNew) {
                if(sr.Startup__c != null && sr.Pre_Money_Valuation__c != null && sr.Date_Of_Founders_Call__c != null 
                   && sr.Doi_Percent_Equity__c != null && sr.Doi_Percent_Fee__c != null){
                       
                    if(count < 100)
                        roundForAPI1.add(sr.ID);
                    else
                        roundForAPI2.add(sr.ID);
                    
                    count ++;
                }              
            }
            system.debug('--roundForAPI1>>>'+roundForAPI1);
            system.debug('--roundForAPI2>>>'+roundForAPI2);
            if(roundForAPI1.size()>0)
                StartupRestAPIController.updateStartupDetails(roundForAPI1,true); 
            if(roundForAPI2.size()>0)
                StartupRestAPIController.updateStartupDetails(roundForAPI2,true); 
        }   
        createPointTransaction(triggerNew,null);
    }
    
    public void afterUpdate(Map<Id,Startup_Round__c> triggerNewMap,Map<id,Startup_Round__c> triggerOldMap)
    {
        List<Investment__c> invUpdateList = new List<Investment__c>();
        List<Investment__c> invQueryList = new List<Investment__c>();
        List<Startup_Round__c> listForPoints = new List<Startup_Round__c>();
        Set<Id> sRoundSet = new Set<Id>();
        Set<Id> sRoundRaiseExitPreEmptiveSet = new Set<Id>();
        Set<Id> roundForAPI1 = new Set<Id>();
        Set<Id> roundForAPI2 = new Set<Id>();
        Set<Id> roundInvUpdateForAPI1 = new Set<Id>();
        Set<Id> roundInvUpdateForAPI2 = new Set<Id>();
        Boolean callRoundAPI = False;
        Integer countRd = 0;
        Integer countInv = 0;
        Set<Id> invDeleteIdsAPI1 = new Set<Id>();
        Set<Id> invDeleteIdsAPI2 = new Set<Id>();
        Set<Id> invDeleteIdsAPI3 = new Set<Id>();
        // Added by bharat for Secondary Module Requirement on 16-05-2025
        Set<Id> startupIds= new Set<Id>();
        List<Startup__c> startupToUpdate = new List<Startup__c>();
        Map<Id, Decimal> startupReservePriceMap = new Map<Id, Decimal>();
        List<Startup_Round__c> childRoundList = new List<Startup_Round__c>();
        Boolean isfundingClose;
        
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('StartupRoundTriggerHandler API_Setting__c>>>'+settingList);
        if(settingList!=null && settingList.size()>0 && settingList[0].Enable_Startup_API_Call__c)
            callRoundAPI = true;
        
        System.debug('callRoundAPI>>>>>>>>>>>>>' + callRoundAPI);
        for(Startup_Round__c sr : triggerNewMap.values())
        {
            if(callRoundAPI)
            {
                if(sr.Startup__c != null && sr.Pre_Money_Valuation__c != null && sr.Date_Of_Founders_Call__c != null 
                   && sr.Doi_Percent_Equity__c != null && sr.Doi_Percent_Fee__c != null)
                {
                    if(countRd < 100)
                        roundForAPI1.add(sr.Id);
                    else
                        roundForAPI2.add(sr.Id);
                    
                    countRd ++;       
                }
                if(sr.Issue_Price__c!=triggerOldMap.get(sr.Id).Issue_Price__c || sr.Issue_Price_FnF__c!=triggerOldMap.get(sr.Id).Issue_Price_FnF__c)
                {
                    roundInvUpdateForAPI1.add(sr.Id);            
                }
                if(sr.Round_Type__c!=triggerOldMap.get(sr.Id).Round_Type__c && (sr.Round_Type__c=='Raise' ||  sr.Round_Type__c=='Exit' || sr.Round_Type__c=='Pre-Emptive'))
                {
                    sRoundRaiseExitPreEmptiveSet.add(sr.Id);
                }                      
            }      
            if(sr.Date_of_sending_out_call_for_money__c!=null && sr.Date_of_sending_out_call_for_money__c != triggerOldMap.get(sr.Id).Date_of_sending_out_call_for_money__c)
            {
                
                sRoundSet.add(sr.Id);
            }
            //To Update exit child investment
            if(sr.Exit_Price__c != triggerOldMap.get(sr.Id).Exit_Price__c || sr.Date_Of_Exit__c != triggerOldMap.get(sr.Id).Date_Of_Exit__c || sr.Issue_Price__c != triggerOldMap.get(sr.Id).Issue_Price__c)
            {
                sRoundSet.add(sr.Id);
            }
            
            // Added by bharat for Secondary Module Requirement on 16-05-2025
            //if(sr.Issue_Price__c != triggerOldMap.get(sr.Id).Issue_Price__c || sr.Date_of_sending_out_call_for_money__c != triggerOldMap.get(sr.Id).Date_of_sending_out_call_for_money__c || sr.Date_of_sending_out_call_for_money_AIF__c != triggerOldMap.get(sr.Id).Date_of_sending_out_call_for_money_AIF__c || sr.App_Category__c != triggerOldMap.get(sr.Id).App_Category__c ){
            //    startupIds.add(sr.Startup__c);
            //}
            if(sr.Issue_Price__c != null ){
                startupIds.add(sr.Startup__c);
			}
            
        }
        system.debug('--sRoundRaiseExitPreEmptiveSet+'+sRoundRaiseExitPreEmptiveSet);
        system.debug('--idd1 +'+roundForAPI1);
        system.debug('--idd2 +'+roundForAPI2);
        system.debug('sRoundSet11111>>>>'+sRoundSet);
        system.debug('roundInvUpdateForAPI1>>>>'+roundInvUpdateForAPI1);
        system.debug('roundInvUpdateForAPI2>>>>'+roundInvUpdateForAPI2);
         System.debug('startupIdSet>>>>>>>>>>>>' + startupIds);
        
        if(!sRoundSet.isEmpty() || !roundInvUpdateForAPI1.isEmpty() || !sRoundRaiseExitPreEmptiveSet.isEmpty())
        {                     
            invQueryList = [select id,Type__c,Parent_Investment__c,Exit_Date__c,Exit_amount_to_be_transferred__c,Number_Of_Shares__c,Exit_Price__c,IRR_Value__c,Startup_Round__c ,Investment_Year__c from Investment__c where Startup_Round__c in : sRoundSet OR Startup_Round__c in : roundInvUpdateForAPI1 OR Startup_Round__c in :sRoundRaiseExitPreEmptiveSet];
        }
        
        system.debug('invQueryList >>>>'+invQueryList );
        
        if(callRoundAPI && (!roundInvUpdateForAPI1.isEmpty() || !sRoundRaiseExitPreEmptiveSet.isEmpty()) && !invQueryList.isEmpty())
        {
            roundInvUpdateForAPI1 = new Set<Id>();
            for(Investment__c inv : invQueryList)
            {
                if(countInv < 100)
                    roundInvUpdateForAPI1.add(inv.Id);
                else
                    roundInvUpdateForAPI2.add(inv.Id);
                
                //Collect Investments list to be deleted from App side
                if(sRoundRaiseExitPreEmptiveSet.Contains(inv.Startup_Round__c))
                {
                    if(invDeleteIdsAPI1.size()<100)
                    {
                        invDeleteIdsAPI1.add(inv.Id);
                    }
                    else if(invDeleteIdsAPI2.size()<200){
                        invDeleteIdsAPI2.add(inv.Id);
                    }
                    else{
                        invDeleteIdsAPI3.add(inv.Id);
                    }
                }   
                countInv ++;                   
            }
            
            if(roundInvUpdateForAPI1.size()>0){
                InvestmentRestAPIController.sendInvestmentDetails(roundInvUpdateForAPI1,false);
                
            }
            if(roundInvUpdateForAPI2.size()>0){
                InvestmentRestAPIController.sendInvestmentDetails(roundInvUpdateForAPI2,false);
            }
        }
        
        if(!sRoundSet.isEmpty() && !invQueryList.isEmpty())
        { 
            Boolean sendForExitUpdate = false;
            Map<Id,Decimal> roundIssuePriceMap = new Map<Id,Decimal>();
            for(Investment__c inv : invQueryList)
            {
                Date invDate = triggerNewMap.get(inv.Startup_Round__c).Date_of_sending_out_call_for_money__c;
                Boolean needToAdd = false;
                if(invDate!=null && inv.Investment_Year__c!=''+invDate.year())
                {
                    inv.Investment_Year__c = ''+invDate.year();
                    needToAdd = true;
                    //invUpdateList.add(inv);
                }
                if(inv.Type__c =='Exit' && inv.Parent_Investment__c!=null && (triggerNewMap.get(inv.Startup_Round__c).Issue_Price__c != triggerOldMap.get(inv.Startup_Round__c).Issue_Price__c || inv.Exit_Date__c != triggerNewMap.get(inv.Startup_Round__c).Date_Of_Exit__c || inv.Exit_Price__c != triggerNewMap.get(inv.Startup_Round__c).Exit_Price__c)) 
                {
                    //invUpdateList.add(inv);
                    Decimal exitPrice = 0;
                    
                    if(triggerNewMap.get(inv.Startup_Round__c).Exit_Price__c>0)
                        exitPrice = triggerNewMap.get(inv.Startup_Round__c).Exit_Price__c;
                    
                    system.debug('inv.Number_Of_Shares__c>>>>>'+inv.Number_Of_Shares__c);    
                    system.debug('inv.Exit_Price__c>>>>>'+inv.Exit_Price__c);    
                    
                    inv.Exit_Date__c = triggerNewMap.get(inv.Startup_Round__c).Date_Of_Exit__c;
                    inv.Exit_Price__c = exitPrice;//triggerNewMap.get(inv.Startup_Round__c).Exit_Price__c;
                    inv.Exit_amount_to_be_transferred__c = inv.Number_Of_Shares__c * inv.Exit_Price__c;
                    roundIssuePriceMap.put(inv.Startup_Round__c,triggerNewMap.get(inv.Startup_Round__c).Issue_Price__c);
                    needToAdd = true;
                    sendForExitUpdate = true;
                } 
                
                if(needToAdd)
                    invUpdateList.add(inv);
            }
            system.debug('sendForExitUpdate>>>>'+sendForExitUpdate);
            system.debug('invUpdateList before 777777777>>>>'+invUpdateList);
            if(sendForExitUpdate)
            {
                InvestmentTriggerHandler hndl = new InvestmentTriggerHandler();
                invUpdateList = hndl.calculateIRR(invUpdateList,roundIssuePriceMap);
            }
            
            system.debug('invUpdateList after 777777777>>>>'+invUpdateList);
            system.debug('invUpdateList Size 777>>>>'+invUpdateList.size());
            system.debug('--idd +'+roundForAPI1);
            if(invUpdateList!=null && invUpdateList.size()>0)
                Database.SaveResult [] updateResult = Database.update(invUpdateList, false);
            //update invList;
            
        }
        
        // Added by bharat for Secondary Module Requirement on 16-05-2025
        if(startupIds != null && startupIds.size()>0)
        {
            startupReservePriceMap = findLatestRoundIssuePrice(startupIds);
            childRoundList = [SELECT Id , Name , Startup__c , App_Category__c, Round_Type__c FROM Startup_Round__c WHERE Startup__c IN : startupIds AND Round_Type__c = 'Raise'];
        }
        
        // Added by bharat for Secondary Module Requirement on 16-05-2025
        if(childRoundList != null && childRoundList.size()>0 )
        {
            for(Startup_Round__c sr : childRoundList)
            {
                System.debug('childRoundList????????' + childRoundList);
                if(sr.App_Category__c == 'Funding Closed'){
                    isfundingClose = TRUE;
                }else{
                    isfundingClose = FALSE;
                    break;
                }
                
                System.debug('countRd>>>>>>>>>>' + countRd );
                if(roundForAPI1.size() < 100){
                    roundForAPI1.add(sr.Id);
                }else{
                    roundForAPI2.add(sr.Id);
                }
                countRd ++;
            }
        }
        
        // Added by bharat for Secondary Module Requirement on 16-05-2025
        if(startupReservePriceMap != null )
        {
            for(Id startupId : startupReservePriceMap.keySet())
            {
                Startup__c updatedStr = new Startup__C();
                updatedStr.Id = StartupId ;
                updatedStr.Reserve_Price__c = startupReservePriceMap.get(startupId);
                
                /*if(isfundingClose){
                    updatedStr.Applicable_for_Secondary_App__c = 'Yes';
                }else{
                    updatedStr.Applicable_for_Secondary_App__c = 'No - Funding is in progress ';
                }*/
                startupToUpdate.add(updatedStr);
            }
        }
        
        // Added by bharat for Secondary Module Requirement on 16-05-2025
        If(startupToUpdate != null && startupToUpdate.size()>0 ){
            Update startupToUpdate;
        }
        System.debug('startupToUpdate>>>>>>>>>>>' + startupToUpdate);
        
        system.debug('--idd +'+roundForAPI1);
        if(callRoundAPI)
        {
            if(roundForAPI1.size()>0){
                StartupRestAPIController.updateStartupDetails(roundForAPI1,false);
            }
            
            if(roundForAPI2.size()>0 ){
                StartupRestAPIController.updateStartupDetails(roundForAPI2,false);
            }
            
            system.debug('--invDeleteIdsAPI1>>>>'+invDeleteIdsAPI1);
            
            if(invDeleteIdsAPI1.size()>0){
                RestLoginController.genericBulkDeleteAPI(invDeleteIdsAPI1,'salesforce_investment_id','startups/startupInvestment/deleteStartupInvestmentUsingSalesforceId');
            }
            if(invDeleteIdsAPI2.size()>0){
                RestLoginController.genericBulkDeleteAPI(invDeleteIdsAPI2,'salesforce_investment_id','startups/startupInvestment/deleteStartupInvestmentUsingSalesforceId');
            }
            if(invDeleteIdsAPI3.size()>0){
                RestLoginController.genericBulkDeleteAPI(invDeleteIdsAPI3,'salesforce_investment_id','startups/startupInvestment/deleteStartupInvestmentUsingSalesforceId');
            }
        }
        createPointTransaction(triggerNewMap.values(),triggerOldMap);
    }
    
    //Creating points transactions
    public void createPointTransaction(List<Startup_Round__c> roundList,Map<id,Startup_Round__c> triggerOldMap)
    {
        if (pointTransactionHelper.hasExecuted) {
            System.debug('createPointTransaction already executed. Skipping...');
            return;
        }
        pointTransactionHelper.hasExecuted = true; // Set the flag to true to prevent re-entry
             // Null checks for the parameters
        if (roundList == null || roundList.isEmpty()) {
            System.debug('roundList is null or empty. Exiting method.');
            return;
        }
        if (triggerOldMap == null) {
            System.debug('triggerOldMap is null. Proceeding with roundList only.');
        }
        system.debug('in  '+roundList);
        List<Points_Transaction__c> points = new List<Points_Transaction__c>();
        Set<Id> setMemebrList = new Set<id>();
        Set<Id> setStartup = new Set<id>();
        set<Id> memberID = new set<Id>();
        Set<Id> setStartupRoundId = new Set<id>();
        Set<Id> startupRoundSpecificId = new Set<id>();
        for(Startup_Round__c SR : roundList){
            if(SR.Date_of_Investor_Call__c != null &&  (SR.SME_1__c != null ||  SR.SME_2__c != null ||
                                                        SR.SME_3__c != null ||  SR.SME_4__c != null ||
                                                        SR.SME_5__c != null || SR.Co_Lead_Member__c != null
                                                        || SR.Lead_Member__c != null))
            {                
                setStartup.add(Sr.Startup__c);
                setStartupRoundId.add(SR.Id);
                system.debug('in  '+Sr.Startup__c);
                if(SR.SME_1__c != null)memberID.add(SR.SME_1__c);                
                if(SR.SME_2__c != null)memberID.add(SR.SME_2__c);
                if(SR.SME_3__c != null)memberID.add(SR.SME_3__c);
                if(SR.SME_4__c != null)memberID.add(SR.SME_4__c);
                if(SR.SME_5__c != null)memberID.add(SR.SME_5__c);
            }    
            startupRoundSpecificId.add(SR.Id);
        }
        system.debug('memberID ::'+memberID);
     
        List<Attendance__c> attendances = [SELECT Id,Account__c,Events__r.Startup_Round__c FROM Attendance__c WHERE Account__c IN :memberId and Events__r.Event_Type__c = 'Investor call' AND Events__r.Startup_Round__c IN :startupRoundSpecificId];
          /* Map<ID,Attendance__c> attendancesMap = new Map<ID,Attendance__c>();
        for(Attendance__c att : attendances){
            attendancesMap.put(att.Account__c,att);
        }*/
        // Added by karan for to many SOQL error on Attented List 12/03/2024:
             Map<Id, List<Attendance__c>> attendancesMap = new Map<Id, List<Attendance__c>>();
        for (Attendance__c att : attendances) {
            if (!attendancesMap.containsKey(att.Account__c)) {
                attendancesMap.put(att.Account__c, new List<Attendance__c>());
            }
            attendancesMap.get(att.Account__c).add(att);
        }
        system.debug('attendancesMap ::'+attendancesMap);
        Map<ID,Startup_Round__c> mapStartupRound = new Map<ID,Startup_Round__c>();
        List<Startup_Round__c> lstRound = [SELECT ID,Lead_Member__c,Co_Lead_Member__c,Lead_Member__r.Membership_Status__c from 
                                           Startup_Round__c where id in : setStartupRoundId];
        for(Startup_Round__c str : lstRound){
            mapStartupRound.put(str.Id,str);
        }
        Map<ID,Startup__c> mapStartup = new Map<ID,Startup__c>();
        if(setStartup.size()>0)
        {
            List<Startup__c> liststartup = [SELECT ID,Lead_Member__c,Co_Lead_Member__c,Lead_Member__r.Membership_Status__c,
                                            Lead_Member__r.Other_Membership_type__c,Lead_Member__r.LetsGrow_Lead_Investor_Mentor__c,
                                            Lead_Member__r.LetsGrow_Investor_Mentor__c,Lead_Member__r.Account_Premier_Status__c,
                                            Lead_Member__r.Membership_Slab__c
                                            from Startup__c where ID IN :setStartup];
            for(Startup__c str : liststartup){
                mapStartup.put(str.Id,str);
            }
        }
        system.debug('mapStartup  '+mapStartup);
        if(mapStartup.size()>0)
        {
            for(Startup_Round__c SR : roundList){
                if(SR.Date_of_Investor_Call__c != null){ 
                    if(((SR.SME_1__c != null && triggerOldMap == null) || (triggerOldMap != null && SR.SME_1__c != null && sr.SME_1__c != triggerOldMap.get(sr.Id).SME_1__c)) && (attendancesMap.containsKey(SR.SME_1__c))){
                        Points_Transaction__c point1Att1 =  new Points_Transaction__c();
                        point1Att1.Credit_To__c = SR.SME_1__c;
                        point1Att1.Debit_From__c = '001F300001f7qMqIAI';
                        point1Att1.Points_Alloted__c = 100;
                        point1Att1.Point_Type__c = 'Help as SME during DD and present findings on the Investor Call';
                        //point1Att1.date__c = system.today(); commented by sahil on 19.09.2024
                        point1Att1.Startup_Round__c = SR.Id;
                        point1Att1.date_of_IC__c = Sr.Date_of_Investor_Call__c;
                        //point1Att1.Startup__c = Sr.Startup__c; commented by sahil on 19.09.2024
                        point1Att1.Member_Name__c = SR.SME_1__c;
                        points.add(point1Att1);
                        System.debug('Second  condition>>>>>>>>>.');
                    }else{
                        if((SR.SME_1__c != null && triggerOldMap == null)|| (triggerOldMap != null && SR.SME_1__c != null && sr.SME_1__c != triggerOldMap.get(sr.Id).SME_1__c )){
                            Points_Transaction__c point1 =  new Points_Transaction__c();
                            point1.Credit_To__c = SR.SME_1__c;
                            point1.Debit_From__c = '001F300001f7qMqIAI';                                      
                            point1.Points_Alloted__c = 50;                             
                            point1.Point_Type__c = 'Help as SME during DD';
                            point1.date__c = system.today();
                            point1.Startup_Round__c = SR.Id;
                            point1.date_of_IC__c = Sr.Date_of_Investor_Call__c;
                            //point1.Investor__c = SR.inve
                            point1.Startup__c = Sr.Startup__c;
                            point1.Member_Name__c = SR.SME_1__c;
                            //point1.Investment_Type__c = Sr.Inv
                            points.add(point1); 
                            System.debug('First condition>>>>>>>>>.');
                        }
                    }
                    
                    if(((SR.SME_2__c != null && triggerOldMap == null)|| (triggerOldMap != null && SR.SME_2__c != null && sr.SME_2__c != triggerOldMap.get(sr.Id).SME_2__c) ) && (attendancesMap.containsKey(SR.SME_2__c))){
                        Points_Transaction__c point1Att2 =  new Points_Transaction__c();
                        point1Att2.Credit_To__c = SR.SME_2__c;
                        point1Att2.Debit_From__c = '001F300001f7qMqIAI';                                      
                        point1Att2.Points_Alloted__c = 100;                             
                        point1Att2.Point_Type__c = 'Help as SME during DD and present findings on the Investor Call';
                        point1Att2.date__c = system.today();
                        point1Att2.Startup_Round__c = SR.Id;
                        point1Att2.date_of_IC__c = Sr.Date_of_Investor_Call__c;
                        point1Att2.Startup__c = Sr.Startup__c;
                        point1Att2.Member_Name__c = SR.SME_2__c;
                        points.add(point1Att2);
                    }else {
                        if((SR.SME_2__c != null && triggerOldMap == null)|| (triggerOldMap != null && SR.SME_2__c != null && sr.SME_2__c != triggerOldMap.get(sr.Id).SME_2__c) ){
                            Points_Transaction__c point2 =  new Points_Transaction__c();
                            point2.Credit_To__c = SR.SME_2__c;
                            point2.Debit_From__c = '001F300001f7qMqIAI';                                      
                            point2.Points_Alloted__c = 50;                             
                            point2.Point_Type__c = 'Help as SME during DD';
                            point2.date__c = system.today();
                            point2.date_of_IC__c = Sr.Date_of_Investor_Call__c;
                            point2.Startup_Round__c = SR.Id;
                            //point1.Investor__c = SR.inve
                            point2.Startup__c = Sr.Startup__c;
                            point2.Member_Name__c = SR.SME_2__c;
                            //point1.Investment_Type__c = Sr.Inv
                            points.add(point2);
                        }
                    }
                    
                    if(((SR.SME_3__c != null && triggerOldMap == null)|| (triggerOldMap != null && SR.SME_3__c != null && sr.SME_3__c != triggerOldMap.get(sr.Id).SME_3__c)) && (attendancesMap.containsKey(SR.SME_3__c))){
                        Points_Transaction__c point1Att3 =  new Points_Transaction__c();
                        point1Att3.Credit_To__c = SR.SME_3__c;
                        point1Att3.Debit_From__c = '001F300001f7qMqIAI';                                      
                        point1Att3.Points_Alloted__c = 100;                             
                        point1Att3.Point_Type__c = 'Help as SME during DD and present findings on the Investor Call';
                        point1Att3.date__c = system.today();
                        point1Att3.Startup_Round__c = SR.Id;
                        point1Att3.date_of_IC__c = Sr.Date_of_Investor_Call__c;
                        point1Att3.Startup__c = Sr.Startup__c;
                        point1Att3.Member_Name__c = SR.SME_3__c;
                        points.add(point1Att3);
                    }else {
                        if((SR.SME_3__c != null && triggerOldMap == null)|| (triggerOldMap != null && SR.SME_3__c != null && sr.SME_3__c != triggerOldMap.get(sr.Id).SME_3__c )){
                            
                            Points_Transaction__c point3 =  new Points_Transaction__c();
                            point3.Credit_To__c = SR.SME_3__c;
                            point3.Debit_From__c = '001F300001f7qMqIAI';                                      
                            point3.Points_Alloted__c = 50;                             
                            point3.Point_Type__c = 'Help as SME during DD';
                            point3.date__c = system.today();
                            point3.Startup_Round__c = SR.Id;
                            point3.date_of_IC__c = Sr.Date_of_Investor_Call__c;
                            //point1.Investor__c = SR.inve
                            point3.Startup__c = Sr.Startup__c;
                            point3.Member_Name__c = SR.SME_3__c;
                            //point1.Investment_Type__c = Sr.Inv
                            points.add(point3);
                        }
                    }
                    
                    if(((SR.SME_4__c != null && triggerOldMap == null)|| (triggerOldMap != null && SR.SME_4__c != null && sr.SME_4__c != triggerOldMap.get(sr.Id).SME_4__c)) && (attendancesMap.containsKey(SR.SME_4__c))){
                        Points_Transaction__c point1Att4 =  new Points_Transaction__c();
                        point1Att4.Credit_To__c = SR.SME_4__c;
                        point1Att4.Debit_From__c = '001F300001f7qMqIAI';                                      
                        point1Att4.Points_Alloted__c = 100;                             
                        point1Att4.Point_Type__c = 'Help as SME during DD and present findings on the Investor Call';
                        point1Att4.date__c = system.today();
                        point1Att4.Startup_Round__c = SR.Id;
                        point1Att4.date_of_IC__c = Sr.Date_of_Investor_Call__c;
                        point1Att4.Startup__c = Sr.Startup__c;
                        point1Att4.Member_Name__c = SR.SME_4__c;
                        points.add(point1Att4);}else {
                            if((SR.SME_4__c != null && triggerOldMap == null)|| (triggerOldMap != null && SR.SME_4__c != null && sr.SME_4__c != triggerOldMap.get(sr.Id).SME_4__c)){
                        
                                Points_Transaction__c point4 =  new Points_Transaction__c();
                                point4.Credit_To__c = SR.SME_4__c;
                                point4.Debit_From__c = '001F300001f7qMqIAI';                                      
                                point4.Points_Alloted__c = 50;                             
                                point4.Point_Type__c = 'Help as SME during DD';
                                point4.date__c = system.today();
                                point4.Startup_Round__c = SR.Id;
                                point4.date_of_IC__c = Sr.Date_of_Investor_Call__c;
                                //point1.Investor__c = SR.inve
                                point4.Startup__c = Sr.Startup__c;
                                point4.Member_Name__c = SR.SME_4__c;
                                //point1.Investment_Type__c = Sr.Inv
                                points.add(point4); }
                        }
                        
                }else {
                   
                }
                
                if(((SR.SME_5__c != null && triggerOldMap == null)|| (triggerOldMap != null && SR.SME_5__c != null && sr.SME_5__c != triggerOldMap.get(sr.Id).SME_5__c)) && (attendancesMap.containsKey(SR.SME_5__c))){
                    Points_Transaction__c point1Att5 =  new Points_Transaction__c();
                    point1Att5.Credit_To__c = SR.SME_5__c;
                    point1Att5.Debit_From__c = '001F300001f7qMqIAI';                                      
                    point1Att5.Points_Alloted__c = 100;                             
                    point1Att5.Point_Type__c = 'Help as SME during DD and present findings on the Investor Call';
                    point1Att5.date__c = system.today();
                    point1Att5.Startup_Round__c = SR.Id;
                    point1Att5.date_of_IC__c = Sr.Date_of_Investor_Call__c;
                    point1Att5.Startup__c = Sr.Startup__c;
                    point1Att5.Member_Name__c = SR.SME_5__c;
                    points.add(point1Att5);
                }else {
                    if((SR.SME_5__c != null && triggerOldMap == null)|| (triggerOldMap != null && SR.SME_5__c != null && sr.SME_5__c != triggerOldMap.get(sr.Id).SME_5__c)){
                        Points_Transaction__c point5 =  new Points_Transaction__c();
                        point5.Credit_To__c = SR.SME_5__c;
                        point5.Debit_From__c = '001F300001f7qMqIAI';                                      
                        point5.Points_Alloted__c = 50;                             
                        point5.Point_Type__c = 'Help as SME during DD';
                        point5.date__c = system.today();
                        point5.Startup_Round__c = SR.Id;
                        point5.date_of_IC__c = Sr.Date_of_Investor_Call__c;
                        //point1.Investor__c = SR.inve
                        point5.Startup__c = Sr.Startup__c;
                        point5.Member_Name__c = SR.SME_5__c;
                        //point5.Investment_Type__c = Sr.Inv
                        points.add(point5);  
                    }
                }
                
                
                if(mapStartup != null){
                    if(mapStartup.containsKey(SR.Startup__c))
                    {
                        system.debug('Lead_Member__c  '+SR.Lead_Member__c);
                        system.debug('Co_Lead_Member__c  '+SR.Co_Lead_Member__c);
                        system.debug('SR.Lead_Member__r.Membership_Status__c  '+mapStartupRound.get(sr.Id).Lead_Member__r.Membership_Status__c );
                        //if(SR.Lead_Member__c != null && SR.Lead_Member__c == (mapStartup.get(SR.Startup__c)).Lead_Member__c ){
                        IF(SR.Date_of_Investor_Call__c != null && SR.Lead_Member__c != null)
                        {
                            if(((SR.Date_of_Investor_Call__C != null && triggerOldMap == null) || (SR.Date_of_Investor_Call__c != triggerOldMap.get(SR.Id).Date_of_Investor_Call__c) || ((SR.Lead_Member__c != null && triggerOldMap == null) || (triggerOldMap != null && SR.Lead_Member__c != triggerOldMap.get(sr.Id).Lead_Member__c))))
                            {       
                                setMemebrList.add(SR.Lead_Member__c);
                                if(mapStartupRound.get(sr.Id).Lead_Member__r.Membership_Status__c == 'Platinum'){
                                    Points_Transaction__c point =  new Points_Transaction__c();
                                    point.Credit_To__c = SR.Lead_Member__c;
                                    point.Debit_From__c = '001F300001f7qMqIAI';                                      
                                    point.Points_Alloted__c = 200;                             
                                    point.Point_Type__c = 'Worked as a Lead Member';
                                    point.date__c = system.today();
                                    point.Startup_Round__c = SR.Id;
                                    point.date_of_IC__c = Sr.Date_of_Investor_Call__c;
                                    //point1.Investor__c = SR.inve
                                    point.Startup__c = Sr.Startup__c;
                                    point.Member_Name__c = SR.Lead_Member__c;
                                    //point1.Investment_Type__c = Sr.Inv
                                    points.add(point);   
                                }
                            }
                        }   
                        //if(SR.Co_Lead_Member__c != null && mapStartup.get(SR.Startup__c).Co_Lead_Member__c == SR.Co_Lead_Member__c){
                        IF((SR.Date_of_Investor_Call__c != null && SR.Co_Lead_Member__c != null))
                        {
                            if(((SR.Co_Lead_Member__c != null && triggerOldMap == null) || (triggerOldMap != null && SR.Co_Lead_Member__c != triggerOldMap.get(sr.Id).Co_Lead_Member__c) || (SR.Date_of_Investor_Call__C != null && triggerOldMap == null) || (triggerOldMap != null && SR.Date_of_Investor_Call__c != triggerOldMap.get(sr.Id).Date_of_Investor_Call__c))) 
                            {           
                                system.debug('innn ');
                                setMemebrList.add(SR.Lead_Member__c);                           
                                Points_Transaction__c point =  new Points_Transaction__c();
                                point.Credit_To__c = SR.Co_Lead_Member__c;
                                point.Debit_From__c = '001F300001f7qMqIAI';                               
                                point.Points_Alloted__c = 200;                             
                                point.Point_Type__c = 'Worked as a Co-lead';
                                point.date__c = system.today();
                                point.Date_of_IC__c = Sr.Date_of_Investor_Call__c;
                                point.Startup_Round__c = SR.Id;
                                //point1.Investor__c = SR.inve
                                point.Startup__c = Sr.Startup__c;
                                point.Member_Name__c = SR.Co_Lead_Member__c;
                                //point1.Investment_Type__c = Sr.Inv
                                points.add(point);   
                                
                            }
                        }
                    }
                }
            }
        }
        system.debug('Point ::'+points);
        System.debug('Total Point Transaction>>>> ' + points.Size());
       /* if(!points.isEmpty()){
            insert points; 
        }*/
         if (!points.isEmpty()) {
        try {
            insert points;
        } catch (DmlException e) {
            System.debug('Error while inserting Points_Transaction__c: ' + e.getMessage());
        }
    }
    }
    
    //Addedd by bharat for secondaary Module Requirement
    public Map<Id, Decimal> findLatestRoundIssuePrice(Set<Id> idSet){
        Startup__c sr = new Startup__C ();
        decimal issuePrice;
        Map<Id, Decimal> startupReservePriceMap = new Map<Id, Decimal>();
        Map<Id , List<Startup_Round__c>> startupANdRoundMap = new Map<Id , List<Startup_Round__c>>();
        System.debug('idSet>>>>>>>>>>>>>>>>>' + idSet);
        List<Startup_Round__c> roundList = [SELECT Id , Startup__c ,Date_of_sending_out_call_for_money_AIF__c, Date_of_sending_out_call_for_money__c, Issue_Price__c , Follow_On_Price__c FROM Startup_Round__c WHERE Startup__c IN : idSet AND Round_Type__c = 'Raise' ];
        System.debug('roundList>>>>>>>>>>>>>>>>>' + roundList);
        
        for(Id startupId : idSet){
            startupANdRoundMap.Put(startupId , new List<Startup_Round__c>());
            System.debug('startupANdRoundMap>>>..... 1st Phase' + startupANdRoundMap);
        }
        
        for(Startup_Round__c srd : roundList){
            startupANdRoundMap.get(srd.Startup__c).add(srd);
        }
        System.debug('startupANdRoundMap>>>>>>>>>>>.' + startupANdRoundMap);
        for (Id startupId : startupAndRoundMap.keySet()) {
            List<Startup_Round__c> rounds = startupAndRoundMap.get(startupId);
            System.debug('rounds>>>>>>>>>>>.' + rounds);
            Startup_Round__c latestRound;
            Date latestDate;
            Decimal reservePrice;
            
            for (Startup_Round__c round : rounds) {
                Date pltDate = round.Date_of_sending_out_call_for_money__c;
                Date aifDate = round.Date_of_sending_out_call_for_money_AIF__c;
                Date maxDate;
                
                System.debug('aifDate>>>>>>>>>>>.' + aifDate);
                System.debug('pltDate>>>>>>>>>>>.' + pltDate);
                If(aifDate != null && pltDate != null){
                    If(aifDate > pltDate){
                        maxDate = aifDate;
                    }else{
                        maxDate = pltDate;
                    }
                }else {
                    if(aifDate != null){
                        maxDate = aifDate;
                    }else{
                        maxDate = pltDate;
                    }
                }
                
                if (maxDate != null && (latestDate == null || maxDate > latestDate)) {
                    latestDate = maxDate;
                    latestRound = round;
                }
                System.debug('latestDate>>>>>>>>>>>.' + latestDate);
                System.debug('latestRound>>>>>>>>>>' + latestRound);
            }
            
            if (latestRound != null ) {
                
                if(latestRound.Follow_On_Price__c != null){
                    reservePrice = latestRound.Follow_On_Price__c * 1.3 ;
                }else if(latestRound.Follow_On_Price__c == null && latestRound.Issue_Price__c == null){
                    reservePrice = 0 ;
                }else{
                    reservePrice = latestRound.Issue_Price__c * 1.3 ;
                }
                startupReservePriceMap.put(startupId, reservePrice);
            }
        }
        System.debug('startupReservePriceMap>>>>>>>>>>>.' + startupReservePriceMap);
        return startupReservePriceMap;
    }
}