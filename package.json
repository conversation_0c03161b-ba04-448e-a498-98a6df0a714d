{"name": "salesforce-app", "private": true, "version": "1.0.0", "description": "Salesforce App", "scripts": {"lint": "eslint **/{aura,lwc}/**/*.js", "test": "npm run test:unit", "test:unit": "sfdx-lwc-jest", "test:unit:watch": "sfdx-lwc-jest --watch", "test:unit:debug": "sfdx-lwc-jest --debug", "test:unit:coverage": "sfdx-lwc-jest --coverage", "prettier": "prettier --write \"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}\"", "prettier:verify": "prettier --check \"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}\"", "postinstall": "husky install", "precommit": "lint-staged"}, "devDependencies": {"@lwc/eslint-plugin-lwc": "^2.2.0", "@prettier/plugin-xml": "^3.2.2", "@salesforce/eslint-config-lwc": "^3.2.3", "@salesforce/eslint-plugin-aura": "^2.0.0", "@salesforce/eslint-plugin-lightning": "^1.0.0", "@salesforce/sfdx-lwc-jest": "^3.1.0", "eslint": "^8.11.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jest": "^27.6.0", "husky": "^8.0.3", "lint-staged": "^15.1.0", "prettier": "3.5.3", "prettier-plugin-apex": "2.2.6"}, "lint-staged": {"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}": ["prettier --write"], "**/{aura,lwc}/**/*.js": ["eslint"]}}