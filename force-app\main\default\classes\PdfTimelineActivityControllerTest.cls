@isTest
public class PdfTimelineActivityControllerTest {
    
    @testSetup
    static void setupTestData() {
       
        account acc = TestFactory.createAccount();
            acc.Name = 'Test Account';
            acc.Membership_Status__c = 'Platinum';
            acc.Relationship_Manager__c = UserInfo.getUserId();
            acc.Title__c = 'Ms';
            acc.App_signup_date__c = Date.today().addDays(-1);
        
        insert acc;

        List<User_Activity__c> activityLogs = new List<User_Activity__c>{
            new User_Activity__c(
                Related_Account__c = acc.Id,
                Activity_Type__c = 'Sign in',
                Activity_Detail_RICH__c = 'User Sign in',
                Time_Stamp__c = DateTime.now().addDays(-1)
            ),
            new User_Activity__c(
                Related_Account__c = acc.Id,
                Activity_Type__c = 'Agenda Tab',
                Activity_Detail_RICH__c = 'Agenda Tab',
                Time_Stamp__c = DateTime.now().addDays(-2)
            )
        };
        insert activityLogs;
    }

    @isTest
    static void testPdfTimelineActivityControllerWithValidData() {
        Account acc = [SELECT Id, Name FROM Account LIMIT 1];

        Test.setCurrentPage(Page.TimelineActivityPdfGenerator);
        ApexPages.currentPage().getParameters().put('accountId', acc.Id);
        ApexPages.currentPage().getParameters().put('startDate', '2024-09-01');
        ApexPages.currentPage().getParameters().put('endDate', '2024-10-01');
        ApexPages.currentPage().getParameters().put('filters', 'Sign in,Agenda Tab');

        Test.startTest();
        PdfTimelineActivityController controller = new PdfTimelineActivityController();
        Test.stopTest();

    }

    @isTest
    static void testPdfTimelineActivityControllerWithNoFilters() {
        Account acc = [SELECT Id FROM Account LIMIT 1];

        
        Test.setCurrentPage(Page.TimelineActivityPdfGenerator);
        ApexPages.currentPage().getParameters().put('accountId', acc.Id);
        ApexPages.currentPage().getParameters().put('startDate', '2024-09-01');
        ApexPages.currentPage().getParameters().put('endDate', '2024-10-01');
        ApexPages.currentPage().getParameters().put('filters', '');

        Test.startTest();
        PdfTimelineActivityController controller = new PdfTimelineActivityController();
        Test.stopTest();

       
    }

    @isTest
    static void testPdfTimelineActivityControllerWithNoDates() {
        Account acc = [SELECT Id FROM Account LIMIT 1];

      
        Test.setCurrentPage(Page.TimelineActivityPdfGenerator);
        ApexPages.currentPage().getParameters().put('accountId', acc.Id);
        ApexPages.currentPage().getParameters().put('filters' ,'Login,Agenda Tab');

     
        Test.startTest();
        PdfTimelineActivityController controller = new PdfTimelineActivityController();
        Test.stopTest();

     
    }

}