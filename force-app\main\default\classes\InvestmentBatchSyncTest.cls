@isTest
public class InvestmentBatchSyncTest {
    
    @testSetup static void setup() {
        Startup__c st = TestFactory.createStartUp();
        insert st;     
        System.debug('Startup insert....12' + st);
        
          id recordTypeIdIPV = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
            List<Account> accList = new List<Account>();
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            for(Account accAux: accList)
            {
                accAux.RecordTypeId = recordTypeIdIPV;
                accAux.ShippingStreet       = '';
                accAux.ShippingState        = '';
                accAux.ShippingPostalCode   = '';
                accAux.ShippingCountry      = '';
                accAux.ShippingCity         = '';
                accAux.Description          = '';
                accAux.BillingStreet        = '';
                accAux.BillingState         = '';
                accAux.BillingPostalCode    = '';
                accAux.BillingCountry       = '';
                accAux.BillingCity          = '';
                accAux.Designation__c ='Test';
                accAux.Company__c = 'Codiot';
                accAux.Are_you_a_first_time_investor_c__c ='';
                accAux.Preferred_Email__c = 'Personal';
                accAux.Personal_Email__c = '<EMAIL>';
            }
            insert accList;
            System.debug('invTrigger Acct insert....13' + accList);    
        
            List<Contact> conList = new List<Contact>();
            integer i = 1;
            for(Account acc : accList)
            {
                Contact cont = new Contact();
                cont.FirstName='Test';
                cont.LastName='Test';
                cont.Email = i+'<EMAIL>';
                cont.Accountid= acc.id;
                cont.Investor_s_PAN__c = 'ABCDE'+i+'173D';
                cont.Investment_in_Own_Name_Family_Member__c = 'Own Name';
                cont.Send_Auto_Com_Email__c = true;
                conList.add(cont);
                i++;
            }
            insert conList;
            System.debug('invTrigger Cont insert....14' + conList);
            Startup__c stObj = [select id from Startup__c limit 1];
            
            Startup_Round__c strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,stObj.Id);
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 12, 9);
            strObj.Send_Auto_Comms_Email__c = true; 
            strObj.Startup_Converted__c = TRUE;
            strObj.Exit_Unit_Price__c = 10;
            strObj.Round_Type__c = 'Raise';
            insert strObj;  

    }

    static testMethod void investmentBatchSync()
    {
            Test.startTest();
            user ur = TestFactory.CreateUser();
            ur.ProfileId = Userinfo.getProfileId();
            insert ur;
            Startup_Round__c strObj = [select id,name from Startup_Round__c limit 1];
            List<Account> accList = [Select id,Personal_Email__c,Preferred_Email__c,Membership_status__c,Membership_Validity__c,Email_Id_cc__c,Relationship_Manager__c,Primary_Contact__c from account];
            //Contribution_Agreement__c conAgree = [SELECT Id FROM Contribution_Agreement__c LIMIT 1];
        
            List<Investment__c> invList = new list<Investment__c >();       
            Investment__c inv1 = TestFactory.createInvestment(accList[0].Id); 
            inv1.Investor_s_PAN__c = '**********'; 
            inv1.Startup_Round_Name__c = strObj.Name;
            inv1.Investor_Type__c ='Via Platform';
            //Added for lead tracker.
            inv1.Investment_Amount__c = 10;
            inv1.Number_Of_Shares__c =25;
            inv1.Type__c = 'Invested';
            inv1.Investment_Amount_Due__c = 20;
            inv1.Date_of_FnF_CFM__c = date.newInstance(2022, 07, 23);
            inv1.Call_For_Money_Sent__c = true;
            inv1.Issue_Type__c ='Primary';
            inv1.Intransfer_balance_share__c = false;
            inv1.IPVFnF_Shadow_Balance_share__c = true;
            inv1.Carry_fee_received__c = 10;
            inv1.Startup_Converted__c = true;
            inv1.IRR_Value__c = 10;
            inv1.SAF_soft_copy__c = true;
            
            //inv.Startup_Round__c = strObj.Id;
            invList.add(inv1);

            insert invList;

            InvestmentBatchSync batchJob = new InvestmentBatchSync();
            Id batchId = Database.executeBatch(batchJob , 50);
            Test.stopTest();
    }

    static testMethod void invAPITest()
    {
        // Test.setMock(HttpCalloutMock.class, new RestMock());
        test.startTest();
        
            user ur = TestFactory.CreateUser();
            ur.ProfileId = Userinfo.getProfileId();
            insert ur;
            Startup_Round__c strObj = [select id,name from Startup_Round__c limit 1];
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 05, 02);
            strObj.Date_of_sending_out_call_for_money__c = Date.newInstance(2022,08,05);
            strObj.Send_Auto_Comms_Email__c = true;
          strObj.Issue_Price__c = 14028.00;
          strObj.Issue_Price_FnF__c = 1428.00;
          strObj.Round_Type__c = 'Internal Transfers';
          strObj.Old_Issue_Price__c = 148.00;
          strObj.Old_Type_of_Shares__c = 'CCD';
          strObj.Old_Issue_Price__c = 123;
          strObj.Currency__c ='USD';
          update strObj;   
            
            List<Account> accList = [Select id,Personal_Email__c,Preferred_Email__c,Membership_status__c,Membership_Validity__c,Email_Id_cc__c,Relationship_Manager__c,Primary_Contact__c from account];
            List<Contact> conList = [select id from contact];
            Boolean callWhatsappAPI = false;
            List<Investment__c> invList = new list<Investment__c >();       
            Investment__c inv = TestFactory.createInvestment(accList[2].Id); 
             
            inv = TestFactory.createInvestment(accList[2].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_in_Own_Name_Family_Member__c = 'LLP';
            inv.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
            inv.Is_Primary__c=true;
            inv.Investor_Type__c = 'Via AIF';
            inv.Type__c= 'Invested';
            inv.Issue_Type__c = 'Primary';
            inv.SAF_soft_copy__c = false;
          inv.Number_Of_Shares__c = 25;
            inv.Investor__c = conList[0].ID; //System.Label.Investor_IPV_Advisors_Pvt_Ltd;
            inv.Carry_fee_received__c = 123;
          inv.Startup_Converted__c = true;
          inv.Date_of_Gross_Gain_Loss_starts__c = Date.newInstance(2023, 04, 21);
          inv.Investment_Fee_Received__c = 0908;
          inv.Date_of_transaction__c = Date.TODAY();
          inv.Residential_Status__c ='Resident';
          inv.Share_Certificates_Scan_Sent__c = 'yes';
          inv.Share_Certificates_Delivered_Hard_Copy__c ='Exited';
          inv.Share_Certificates_Tracking_ID__c = 'yes';
          invList.add(inv);
        
          Investment__c inv2 = TestFactory.createInvestment(accList[2].Id); 
             
            inv2 = TestFactory.createInvestment(accList[2].Id); 
            inv2.Investor_s_PAN__c = '**********'; 
            inv2.Startup_Round_Name__c = strObj.Name;
            inv2.Investment_in_Own_Name_Family_Member__c = 'LLP';
            inv2.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
            inv2.Is_Primary__c=true;
            inv2.Investor_Type__c = 'Via AIF';
            inv2.Type__c= 'Exit';
            inv2.Issue_Type__c = 'Primary';
            inv2.SAF_soft_copy__c = false;
          inv2.Number_Of_Shares__c = 25;
            inv2.Investor__c = conList[0].ID; //System.Label.Investor_IPV_Advisors_Pvt_Ltd;
            inv2.Carry_fee_received__c = 123;
          inv2.Startup_Converted__c = true;
          inv2.Date_of_Gross_Gain_Loss_starts__c = Date.newInstance(2023, 04, 21);
          inv2.Investment_Fee_Received__c = 0908;
          inv2.Date_of_transaction__c = Date.TODAY();
          inv2.Residential_Status__c ='Resident';
          inv2.Share_Certificates_Scan_Sent__c = 'yes';
          inv2.Share_Certificates_Delivered_Hard_Copy__c ='Exited';
          inv2.Share_Certificates_Tracking_ID__c = 'yes';
          inv2.Date_of_Allotment__c = Date.TODAY();
          inv2.Final_Commitment_Amount__c = 0987;
          inv2.Exit_Type__c = null;
          inv2.Exit_Date__c= Date.TODAY();
          inv2.Old_Date_of_Gross_Gain_Loss_starts__c = Date.TODAY();
          inv2.Old_Date_of_Allotment__c =Date.Today() - 21;
          inv2.Old_Number_of_Shares__c = 123;
          inv2.Old_Number_of_Units_Held__c = 12;
          inv2.Old_Investment_Amount_Remitted__c = 998877;
          inv2.IRR_Value__c = 20.25;
          inv2.Funds_Cheque__c = TRUE;
          inv2.SAF_soft_copy__c = TRUE;
          inv2.PAS_4_Investor_Bankdetails_soft_copy__c = TRUE;
          inv2.Investment_Amount__c= 5643;
          inv2.Investment_AIF_Class__c = 'Class D';
          inv2.Exit_Fee_received__c = 123;
          inv2.Exit_amount_transferred__c = 123;
          
          invList.add(inv2);
        
          Investment__c inv3 = TestFactory.createInvestment(accList[2].Id); 
             
          inv3 = TestFactory.createInvestment(accList[2].Id); 
          inv3.Investor_s_PAN__c = '**********'; 
          inv3.Startup_Round_Name__c = strObj.Name;
          inv3.Investment_in_Own_Name_Family_Member__c = 'LLP';
          inv3.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
          inv3.Is_Primary__c=true;
          inv3.Investor_Type__c = 'Via AIF';
          inv3.Type__c= 'Invested';
          inv3.Issue_Type__c = 'Primary';
          inv3.SAF_soft_copy__c = false;
          inv3.Number_Of_Shares__c = 25;
          inv3.Investor__c = conList[0].ID; //System.Label.Investor_IPV_Advisors_Pvt_Ltd;
          inv3.Carry_fee_received__c = 123;
          inv3.Startup_Converted__c = False;
          inv3.Date_of_Gross_Gain_Loss_starts__c = Date.newInstance(2023, 04, 21);
          inv3.Investment_Fee_Received__c = 0908;
          inv3.Date_of_transaction__c = Date.TODAY();
          inv3.Residential_Status__c ='Resident';
          inv3.Share_Certificates_Scan_Sent__c = 'yes';
          inv3.Share_Certificates_Delivered_Hard_Copy__c ='Exited';
          inv3.Share_Certificates_Tracking_ID__c = 'yes';
          inv3.Date_of_Allotment__c = Date.TODAY();
          inv3.Final_Commitment_Amount__c = 0987;
          inv3.Exit_Type__c = null;
          inv3.Exit_Date__c= Date.TODAY();
          inv3.Old_Date_of_Gross_Gain_Loss_starts__c = Date.TODAY();
          inv3.Old_Date_of_Allotment__c =Date.Today() - 21;
          inv3.Old_Number_of_Shares__c = 123;
          inv3.Old_Number_of_Units_Held__c = 12;
          inv3.Old_Investment_Amount_Remitted__c = 998877;
          inv3.IRR_Value__c = 20.25;
          inv3.Funds_Cheque__c = TRUE;
          inv3.SAF_soft_copy__c = TRUE;
          inv3.PAS_4_Investor_Bankdetails_soft_copy__c = TRUE;
          inv3.Investment_Amount__c= 5643;
          inv3.Investment_AIF_Class__c = 'Class D';
          inv3.Exit_Fee_received__c = 123;
          inv3.Exit_amount_transferred__c = 123;
          
          invList.add(inv3);
          
            insert invList;
            System.debug('invAPITest Investment insert....26' + invList);
          
          // Call future method for Insert
        Set<Id> investmentIds = new Set<Id>();
        for (Investment__c investment : invList) {
            investmentIds.add(investment.Id);
          }
        InvestmentRestAPIController.sendInvestmentDetails(investmentIds, true);
            //Added by karan Update scenario
            accList[2].Membership_status__c = 'On Trial Community';
            accList[2].Membership_Validity__c = Date.newInstance(2023, 04, 21);
            //accList[2].Relationship_Manager__r.Email = '<EMAIL>';
            update accList;
            
            System.debug('invTrigger Acct Update....27' + accList);
            accList[1].Membership_status__c = 'On Trial';
            accList[1].Membership_Validity__c = Date.newInstance(2023, 04, 21);
            update accList;
            System.debug('invAPITest Acct update....28' + accList);
            
            callWhatsappAPI = true;
            List<Investment__c> InvesList1 = new List<Investment__c>();
            List<Investment__c> InvesList2 = new List<Investment__c>();
            List<Investment__c> InvesList = [SELECT Id, Name,Issue_Type__c, IPV_Fees_Cheque__c,Reason_for_waitlist__c,Type__c, Membership_status__c FROM Investment__c WHERE Id IN: invList];
       
            System.debug('invAPITest Investment update....30' + InvesList2);
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();

            inv.IPV_Fees_Cheque__c = 'yes';
            inv.Issue_Type__c = 'IPV HQ - Shadow';
          inv.Type__c= 'Committed';
            //inv.Type__c = 'Back out approved';
            update inv;         
            System.debug('invAPITest investment update....31' + inv); 
           // Call future method for Update
        investmentIds.clear(); // Reset the set
           for (Investment__c investment : invList) {
            investmentIds.add(investment.Id);
             }
        InvestmentRestAPIController.sendInvestmentDetails(investmentIds, false);
        
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
      
          inv.IPV_Fees_Cheque__c = 'yes';
            inv.Issue_Type__c = 'IPV HQ - Shadow';
          inv.Date_of_Payment__c = Date.newInstance(2023, 04, 21);
            inv.Type__c= 'Committed';
            update inv;
             // Call future method for Update
        investmentIds.clear(); // Reset the set
           for (Investment__c investment : invList) {
            investmentIds.add(investment.Id);
             }
        InvestmentRestAPIController.sendInvestmentDetails(investmentIds, false);
            System.debug('invAPITest Investment update....31' + InvesList2);
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            invList[0].Type__c= 'Round Closed - Commitment Released';
            update invList;
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            invList[0].Type__c= 'Invested';
            update invList;
            InvestmentBatchSync batchJob = new InvestmentBatchSync();
            Id batchId = Database.executeBatch(batchJob , 50);
            test.stopTest();
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            invList[0].Type__c= 'Committed';
            update invList;
          
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            invList[0].Type__c= 'Waitlist';
            update invList;
  
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
          invList[0].Type__c= 'Round closed - deal dropped';
            update invList;
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            invList[0].Type__c= 'Waitlist';
            invList[0].Reason_for_waitlist__c= 'IPV Fee Pending';
            
            update invList;
            
            System.debug('invAPITest Investment update....32' + InvesList2);
        
        
        delete invList;

    }
}