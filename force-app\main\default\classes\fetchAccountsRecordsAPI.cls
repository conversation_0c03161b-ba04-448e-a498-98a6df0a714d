@RestResource(urlMapping='/fetchAccountsRecordsAPI/*')
global with sharing class fetchAccountsRecordsAPI{

    @HttpPost
    global Static ResponseWrapper fetchAccount()
    {
         
        ResponseWrapper res = new ResponseWrapper();
        res.status= true;
        res.message = 'testing';
        List<sObject> objList = [select id,Primary_Contact__c,Primary_Country_Code__c from account limit 11];
        res.objMap = new Map<String,List<sObject>>();
        if(objList!=null && objList.size()>0)
        {
            res.objMap.put('Account',objList);
        }                 
        
        objList = [select id,name from contact limit 10];
        res.objMap.put('Contact',objList);
        //return JSON.Serialize(res);
        return res;
    }

    global class requestWrapper{
       global string subject;
       global string origin;
       global string status;
       global string issueType;
       global string description;

    }
    
    global class responseWrapper{
       global boolean status;
       global string message;
       global Map<String,List<sObject>> objMap;
       //global object data;
    }    
}