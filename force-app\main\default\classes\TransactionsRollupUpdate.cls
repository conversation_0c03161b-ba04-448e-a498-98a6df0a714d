public class TransactionsRollupUpdate implements Database.Batchable<sObject>{

   public final String query;
   public final Set<Id> investorIdSet1;
    
   public TransactionsRollupUpdate(Set<Id> investorIdSet){
       this.investorIdSet1 = investorIdSet;
       query = 'Select id,AccountId,Investor_SIP_Balance__c from contact where Id IN: investorIdSet1';
       system.debug('query>>>>'+query);  
   }

   public Database.QueryLocator start(Database.BatchableContext BC){
      return Database.getQueryLocator(query);
   }

   public void execute(Database.BatchableContext BC, List<Contact> scope){
       System.debug('scope>>>>'+scope);  
       
       Map<Id,Decimal> investorTotalBalanceMap = new Map<Id,Decimal>();
       Map<Id,Decimal> investorBalanceMap = new Map<Id,Decimal>();
       Map<Id,Decimal> accountBalanceMap = new Map<Id,Decimal>();
       List<Transaction__c> transactionList = new List<Transaction__c>();
       List<Contact> conList = new List<Contact>();
       List<Account> accList = new List<Account>();
       List<Account> accUpdateList = new List<Account>();

       transactionList = [select id,Amount__c,Investor__c from transaction__c where Investor__c in : scope];
       
       for(Contact con : scope)
       {
           investorBalanceMap.put(con.Id,con.Investor_SIP_Balance__c);
           accountBalanceMap.put(con.AccountId,0);
       }
       
       for(Transaction__c trn: transactionList){
           if(!investorTotalBalanceMap.containsKey(trn.Investor__c))
               investorTotalBalanceMap.put(trn.Investor__c,0);
           
           investorTotalBalanceMap.put(trn.Investor__c,investorTotalBalanceMap.get(trn.Investor__c) + trn.Amount__c); 
       }
       
       for(Id invId : investorTotalBalanceMap.keyset()){
           Contact c = new Contact();
           c.Id= invId;
           c.Investor_SIP_Balance__c = investorTotalBalanceMap.get(invId);
           if(investorBalanceMap.get(invId)!=c.Investor_SIP_Balance__c)
               conList.add(c);
       }
       update conList;
       
       accList = [select id,Account_SIP_Balance__c,(select id,Investor_SIP_Balance__c  from contacts where Applied_for_SIP__c=true) from account where Id in:accountBalanceMap.Keyset()]; 
       
       for(Account acc : accList){
           Decimal totalSIPBal=0;
           for(contact con : acc.contacts){
               totalSIPBal = totalSIPBal+con.Investor_SIP_Balance__c;
           }
       
           if(acc.Account_SIP_Balance__c!=totalSIPBal)
           {
               Account accObj = new Account();
               accObj.Id=acc.Id;
               accObj.Account_SIP_Balance__c = totalSIPBal;
               accUpdateList.add(accObj);
           }
       }
       if(accUpdateList.size()>0)
           update accUpdateList;
   }

   public void finish(Database.BatchableContext BC){
   }
}