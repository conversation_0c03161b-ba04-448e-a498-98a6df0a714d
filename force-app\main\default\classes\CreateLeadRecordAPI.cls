@RestResource(urlMapping='/CreateLeadRecordAPI/*')
global with sharing class CreateLeadRecordAPI{

    @HttpPost
    global Static ResponseWrapper createLead()
    {
        ResponseWrapper wResponse = new ResponseWrapper();
        //Id IPVRecordTypeId = Schema.SObjectType.Lead.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
 
        try
        {
            RestRequest req = RestContext.request;
            RestResponse res = Restcontext.response;
            string jsonReqString=req.requestBody.tostring();
            System.debug('jsonReqString>>>>>'+jsonReqString);

            requestWrapper wResp=(requestWrapper) JSON.deserialize(jsonReqString,requestWrapper.class);
            System.debug('wResp>>>>>'+wResp);
            If(wResp!=null && wResp.leadList!=null)
            {
                List<Lead__c> leadInsertList = new List<Lead__c>();
                Map<String,Id> primaryNumberMap = new Map<String,Id>();
                Set<Integer> countryCodeSet = new Set<Integer>();
                Set<String> primaryNumberSet = new Set<String>();
                Map<String,Boolean> duplicateLeadMap = new  Map<String,Boolean>();
                
                List<Account> accList = new List<Account>();
                List<Lead__c> leadList = new List<Lead__c>();
                
                for(leadRequestWrapper crw : wResp.leadList)
                {
                    system.debug('crw>>>>'+crw);
                    if(String.isNotBlank(crw.leadPrimaryContact)){
                    	crw.leadPrimaryContact = crw.leadPrimaryContact.replaceAll('[^\\d]','');
                        
                        if(crw.leadPrimaryContact.startsWith('0')){
                            crw.leadPrimaryContact = crw.leadPrimaryContact.substring(1);
                        }
                    }
                    System.debug('leadPrimaryContact>>>>>>>.' + crw.leadPrimaryContact);
                    if(crw.referredByContact !=null)
                    {  
                        if(String.isNotBlank(crw.referredByCountryCode))
                            countryCodeSet.add(Integer.valueof(crw.referredByCountryCode.Trim()));
                        else
                            countryCodeSet.add(91);
                            
                        if(String.isNotBlank(crw.referredByContact))    
                            primaryNumberSet.add(crw.referredByContact.Trim());
                    }
     
                }
                
                System.debug('primaryNumberSet>>>>>'+primaryNumberSet);
                System.debug('countryCodeSet>>>>>'+countryCodeSet);
                accList = [select id,Primary_Contact__c,Primary_Country_Code__c from account where Primary_Contact__c in :primaryNumberSet AND Primary_Country_Code__c in :countryCodeSet];
                
                System.debug('accList>>>>>>>>>>>' +  accList);
                for(Account acc : accList)
                {
                    primaryNumberMap.put(acc.Primary_Country_Code__c +'-'+acc.Primary_Contact__c,acc.Id);
                }
                
                if(!(primaryNumberMap != null && primaryNumberMap.size()>0)){
                    leadList = [select id,Primary_Contact__c,Primary_Country_Code__c from Lead__c where Primary_Contact__c in :primaryNumberSet AND Primary_Country_Code__c in :countryCodeSet];
                    System.debug('leadList>>>>>>>>>>>' +  leadList);
                    for(Lead__c ld : leadList){
                        primaryNumberMap.put(ld.Primary_Country_Code__c +'-'+ld.Primary_Contact__c,ld.Id);
                    }
                    System.debug('primaryNumberMap>>>>>'+primaryNumberMap);
                }
                
                duplicateLeadMap = checkForDuplicate(wResp.leadList);
                System.debug('duplicateLeadMap>>>>>'+duplicateLeadMap);
                
                for(leadRequestWrapper crw : wResp.leadList)
                {
                    if(duplicateLeadMap!=null && duplicateLeadMap.containsKey(crw.leadPrimaryContact) && duplicateLeadMap.get(crw.leadPrimaryContact))
                    {
                        wResponse.status = false;
                        wResponse.message= 'Lead or Account is already exist for the same primary contact : '+crw.leadPrimaryContact;
                        return wResponse;
                    }
                    else
                    {
                        Lead__c ld = new Lead__c();
                        String key = '';
                         if(crw.memberName == null  && (crw.LeadSource == 'App - Startup Referral' || crw.LeadSource == 'App'))
                         {
                              ld.name = 'App_Signup';
                         }
                        else
                        ld.name = crw.memberName;
                        ld.Lead_Source__c = crw.leadSource;
                        ld.Preferred_Email__c = 'Personal';
                        ld.Primary_Contact__c = crw.leadPrimaryContact;
                        ld.Membership_Status__c = crw.mStatus;
                        ld.Lead_External_ID_App__c = crw.leadIdFromApp;
                        if(crw.LeadSource == 'App - Startup Referral' && crw.Dateofreceivinglead == null)
                            ld.Date_of_receiving_lead__c = date.today();
                        else
                            ld.Date_of_receiving_lead__c = crw.Dateofreceivinglead;
                        if(crw.campaign != null)
                            ld.campaign__c = crw.campaign;
                        else 
                            ld.Campaign__c = '';
                        if(crw.StartupName != null)
                            ld.Startup_name__c = crw.StartupName;
                         // Added by ankush for app IRS Screen and other capture intrest 17.7.23
                        if(Crw.IRS_Date != null)
                            ld.IRS_Date__c = Crw.IRS_Date;
                        If(Crw.IRS_Time != null)
                            ld.IRS_Time__c = Crw.IRS_Time;
                        If(Crw.App_Company != null)
                            ld.App_Company__c =Crw.App_Company;
                         If(Crw.App_Designation != null)
                            ld.App_Designation__c = Crw.App_Designation;
                        If(Crw.App_Address != null)
                            ld.App_Address__c = Crw.App_Address;
                        If(Crw.App_Postal_Code != null)
                            ld.App_Postal_Code__c = Crw.App_Postal_Code;
                         If(Crw.App_Industry != null)
                            ld.App_Industry__c = Crw.App_Industry;
                         If(Crw.App_Expertise != null)
                            ld.App_Expertise__c = Crw.App_Expertise;
                        //Added By Bharat as disscussed With Ashish 29-08-23
                        If(Crw.App_Full_Name != null)
                            ld.App_Full_Name__c = Crw.App_Full_Name;
                        If(Crw.App_Email != null)
                            ld.App_Email__c = Crw.App_Email;
                        If(Crw.App_City != null)
                            ld.App_City__c = Crw.App_City;
                        If(Crw.App_Country != null)
                            ld.App_Country__c = Crw.App_Country;
                        
                        //ld.Date_of_receiving_lead__c = crw.referDate;
                        
                        if(String.isNotBlank(crw.leadCountryCode)) 
                            ld.Primary_Country_Code__c = Integer.valueof(crw.leadCountryCode);
                        else
                            ld.Primary_Country_Code__c = 91;
                        
                        if(String.isNotBlank(crw.referredByCountryCode)) 
                            key = ''+crw.referredByCountryCode.Trim()+'-'+crw.referredByContact.Trim();
                        else
                            key = 91+'-'+crw.referredByContact.Trim();	
    
                        system.debug('Key>>>>'+key);
                        
                        if(primaryNumberMap!=null && primaryNumberMap.size()>0 && primaryNumberMap.containsKey(key) && primaryNumberMap.get(key)!=null){
                            Id recordId = primaryNumberMap.get(key);
                            String sObjectName = recordId.getSObjectType().getDescribe().getName();
                            if(sObjectName == 'Account'){
                                ld.Referred_By__c = recordId;
                                System.debug('primaryNumberMap.get(key);>>>>>>>>>>>' +  primaryNumberMap.get(key));
                            }else If(sObjectName == 'Lead__c'){
                                ld.Referred_By_Lead__c = recordId;
                            }
                        }else{
                            /*
                            wResponse.status = false;
                            wResponse.message= 'Referred By is not found for given primary contact';
                            return wResponse;
                            */
                        } 
                        
                        // Added by Sahil on 09.01.2024
                        if(crw.LeadSource =='App - Startup Referral')
                        {
                            ld.Relationship_Owner__c = Label.Lead_RM_Owner_For_App_Lead;
                        }
                        else if(crw.LeadSource =='App Sign Up' || crw.LeadSource =='App Referral')
                        {
                          	ld.Relationship_Owner__c = Label.Lead_Rm_for_FB_And_LKDN;	
                        }
                       
						else
                        {
                            ld.Relationship_Owner__c = Label.Lead_Default_RM_Owner;
                        }
						
                        //Added By Bharat For Referred by Referral Code 
                        
                        if(Crw.APP_Referred_By_Referral_Code != NULL)
                        {
                            Account referredByAccount = [SELECT Id , Name , Referred_By_Referral_Code__c, Unique_referral_code__c FROM Account WHERE Unique_referral_code__c =: Crw.APP_Referred_By_Referral_Code LIMIT 1] ;
                            ld.Referred_By__c = referredByAccount.Id ;
                            ld.Referred_By_Referral_Code__c = referredByAccount.Unique_referral_code__c;
                        }
                        
                       //ld.RecordTypeId = IPVRecordTypeId;
                        
                        System.debug('ld >>>>>>>>>>'+ld);
                        System.debug('ld.name>>>>>>>>>>'+ld.name);

                        leadInsertList.add(ld); 
                    }                   
                }
                System.debug('NEW LEAD LIST>>>>> ' + leadInsertList);
                if(leadInsertList != null && leadInsertList.size()>0){
                	Insert leadInsertList;
                    System.debug('NEW LEAD CREATED ID>>>>>');                    
                }else{
                    System.debug('LEAD is not created ');
                }
                
                for(Lead__c ld: leadInsertList)
                {
                    System.debug('NEW LEAD CREATED ID>>>>>'+ld.Id);
                    System.debug('NEW LEAD CREATED Name>>>>>'+ld.Name);
                    System.debug('NEW Lead>>>>>'+ld);
                }
                
                wResponse.status = true;
                wResponse.message= 'Lead records are created successfully';
            }
            else
            {
                wResponse.status = false;
                wResponse.message= 'Lead list is null';
            }
        }
        catch(exception e)
        {
            wResponse.status = false;
            wResponse.message= 'Exception:'+e.getMessage();
        }
        
        return wResponse;
        //return JSON.Serialize(wResponse);
       
    }
    
    Public Static Map<String,Boolean> checkForDuplicate(List<leadRequestWrapper> leadWrapList)
    {
        Map<String,Boolean> duplicateMap = new Map<String,Boolean>();
        List<String> soqlLikeStr = new List<String>();
 
        for(leadRequestWrapper crw : leadWrapList)
        {
            if(String.isBlank(crw.leadCountryCode) && String.isNotBlank(crw.leadPrimaryContact))
            {
                duplicateMap.put(crw.leadPrimaryContact,false);
                soqlLikeStr.add('%'+crw.leadPrimaryContact+'%');
            }
        }
        
        List<Account> accList = [select id,Primary_Contact__c,Full_Primary_Contact__c from Account where Full_Primary_Contact__c LIKE :soqlLikeStr];
        List<Lead__c> leadList = [select id,Primary_Contact__c,Full_Primary_Contact__c from Lead__c where Full_Primary_Contact__c LIKE :soqlLikeStr];

        System.debug('duplicateMap>>>>>>'+duplicateMap);
        System.debug('accList >>>>>>'+accList );
        System.debug('leadList >>>>>>'+leadList );
        
        if((accList!=null && accList.size()>0) || (leadList!=null && leadList.size()>0))
        {
            for(String key : duplicateMap.keyset())
            {
                for(Account acc : accList)
                {
                    if(acc.Full_Primary_Contact__c.contains(key))
                        duplicateMap.put(key,true);
                }
                
                for(Lead__c ld : leadList)
                {
                    System.debug('ld.Full_Primary_Contact__c>>>>>>'+ld.Full_Primary_Contact__c);
                    System.debug('ld.key>>>>>>'+key);
                    if(ld.Full_Primary_Contact__c.contains(key))
                        duplicateMap.put(key,true);
                }
            }
        }  
        System.debug('duplicateMap111>>>>>>'+duplicateMap);
        return duplicateMap;
    }
    global class leadRequestWrapper{

       global string memberName;
       global string leadSource;
       global string leadCountryCode ;
       global string leadPrimaryContact;
       //Referred By 
       global string referredByContact;
       global string referredByCountryCode;
       global string mStatus;
       global Date referDate;
       global string leadIdFromApp;
       global string campaign ;
       global String StartupName;
       Global Date Dateofreceivinglead;
       Global Date IRS_Date;
	   Global Time IRS_Time;
       Global String App_Company;
	   Global String App_Designation;
	   Global String App_Address;
	   Global Decimal App_Postal_Code;
	   Global String App_Industry;
	   Global String App_Expertise;
       Global String App_Full_Name;
       Global String App_Email;
       global String App_City;
       Global String App_Country;
       global String APP_Referred_By_Referral_Code;
    }
    global class requestWrapper{
        global List<leadRequestWrapper> leadList;
    
    }
    global class ResponseWrapper{
       global boolean status;
       global string message;
       //global Map<String,String> feedbackIdMap;
    }    
}