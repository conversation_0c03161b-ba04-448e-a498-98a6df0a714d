// Added by Bharat As per VC Connect requirement 

public class CommunicationSummaryTriggerHandler {
    public static void handleBeforeInsert(List<Communication_Summary__c> summaryList) {
        Date today = Date.today();

        for (Communication_Summary__c cs : summaryList) {
            if (cs.Date_of_Introduction_to_VC__c != null) {
                Integer daysSinceIntroduction = cs.Date_of_Introduction_to_VC__c.daysBetween(today);
                cs.Lead_Tracking__c = calculateLeadTracking(daysSinceIntroduction);

                System.debug('daysSinceIntroduction: ' + daysSinceIntroduction);
                System.debug('Lead_Tracking__c: ' + cs.Lead_Tracking__c);
            }
        }
        System.debug('triggerNew: ' + summaryList);
    }

    private static String calculateLeadTracking(Integer daysSinceIntroduction) {
        if (daysSinceIntroduction < 45) {
            return 'Hot';
        } else if (daysSinceIntroduction < 75) {
            return 'Warm';
        } else if (daysSinceIntroduction < 120) {
            return 'Cold';
        } else {
            return 'Dropped';
        }
    }
}