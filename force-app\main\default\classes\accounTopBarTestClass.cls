@isTest
public class accounTopBarTestClass {

    @isTest
    static void testGetAccountData() {
        // Create test data
        account testAccount = TestFactory.createAccount();
        insert testAccount;
        
        // Call the method to test
        Test.startTest();
        Map<String, String> accountData = accounTopBarCtrl.getAccountData(testAccount.Id);
        System.debug('accountData>>>>>>>>>' + accountData);
        Test.stopTest();
    }
}