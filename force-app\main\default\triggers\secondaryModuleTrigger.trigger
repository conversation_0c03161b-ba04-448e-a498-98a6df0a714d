trigger secondaryModuleTrigger on Secondary_Module__c (before insert , after insert , after update) {

    secondaryModuleTriggerHandler smCtrl = new secondaryModuleTriggerHandler();
    
    if(Trigger.isInsert && Trigger.isBefore)
    {
        smCtrl.beforeInsert(trigger.new);
    }
    
    if(Trigger.isInsert && Trigger.isAfter)
    {
        smCtrl.afterInsert(trigger.new);
    }
    
    if(Trigger.isUpdate && Trigger.isAfter){
        smCtrl.afterUpdate(trigger.New , Trigger.oldMap);
    }
}