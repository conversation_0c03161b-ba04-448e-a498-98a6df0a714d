@RestResource(urlMapping='/InvestorAIFMeterAPI/*')
global with sharing class InvestorAIFMeterAPI {

    @HttpPost
    global static void getPointTransactionDetails() {
        RestResponse res = RestContext.response;
        ResponseWrapper wResponse = new ResponseWrapper();
        
        try {

            RestRequest req = RestContext.request;
            String jsonReqString = req.requestBody.toString();
            RequestWrapper wRequest = (RequestWrapper) JSON.deserialize(jsonReqString, RequestWrapper.class);
            
            Set<String> primaryNumberSet = new Set<String>();
            Set<Integer> countryCodeSet = new Set<Integer>();
            
            for (ObjPrimaryContactWrapper primaryContact : wRequest.primaryContactList) {
                if (String.isNotBlank(primaryContact.primaryNumber)) {
                    primaryNumberSet.add(primaryContact.primaryNumber);
                }
                
                if (String.isNotBlank(primaryContact.countryCode) && primaryContact.countryCode.isNumeric()) {
                    countryCodeSet.add(Integer.valueOf(primaryContact.countryCode.trim()));
                }
            }
            
            if (primaryNumberSet.isEmpty()) {
                wResponse = createResponse(false, 'No primary numbers provided', null);
                res.responseBody = Blob.valueOf(JSON.serialize(wResponse));
                res.statusCode = 400;
                return;
            }
            
            Id IPVRecordType = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
            
            List<Account> accountList = new List<Account>();
            accountList = [SELECT Id, Name , Primary_Contact__c , Primary_Country_Code__c FROM Account
                           WHERE Primary_Contact__c IN :primaryNumberSet AND Primary_Country_Code__c IN :countryCodeSet AND RecordTypeId = :IPVRecordType];
            
            Set<Id> accountIds = new Set<Id>();
            Map<Id , Account> accountMap = new Map<Id , Account>();
            Map<Id, List<Map<String, Object>>> responseMap = new Map<Id, List<Map<String, Object>>>();

            for(Account account : accountList) {
                accountIds.add(account.Id);
                accountMap.put(account.Id , account);
                responseMap.put(account.Id, new List<Map<String, Object>>());
            }

            List<Contact> investorList = new List<Contact>();
            investorList = [SELECT Id, Name , AccountId FROM Contact WHERE AccountId IN :accountIds AND AIF_Contributor__c = true];
            
            Set<Id> investorIds = new Set<Id>();
            Map<Id , Contact> investorMap = new Map<Id , Contact>();

            for(Contact investor : investorList) {
                investorIds.add(investor.Id);
                investorMap.put(investor.Id , investor);
            }

            List<Investment__c> investmentList = new List<Investment__c>();
            investmentList = [SELECT Id , Name FROM Investment__c WHERE Account__c IN :accountIds AND Investment_in_Own_Name_Family_Member__c = 'Joint A/c' AND Investor_Type__c != null AND Investor_Type__c != 'Via AIF' ];
            
            List<Contribution_Agreement__c> contributionAgreementList = new List<Contribution_Agreement__c>();
            contributionAgreementList = [SELECT Id , Investor1__c , Investor2__c , Investor1__r.Name , Investor1__r.AccountId , Investor2__r.Name , Total_Drawdowns__c , Total_Committed_Amount__c , Balance_Amounts__c , Total_Contribution_Amount__c , RecordType.Name FROM Contribution_Agreement__c WHERE (Investor1__c IN :investorIds AND Investor1__c != null) OR (RecordType.Name = 'Joint' AND (Investor1__c IN :investorIds OR Investor2__c IN :investorIds))];

            Map<Id , Contribution_Agreement__c> contributionAgreementMap = new Map<Id , Contribution_Agreement__c>();
            Map<Contact , Contribution_Agreement__c> contributionAgreementOfInvestorMap = new Map<Contact , Contribution_Agreement__c>();
            Map<Contact , Contribution_Agreement__c> jointContributionAgreementOfInvestorMap = new Map<Contact , Contribution_Agreement__c>();
            
            for (Contribution_Agreement__c conAgree : contributionAgreementList) {
                contributionAgreementMap.put(conAgree.Id , conAgree);
                contributionAgreementOfInvestorMap.put(investorMap.get(conAgree.Investor1__c) , conAgree);
                
                // if(conAgree.Investor1__c != null && conAgree.Investor2__c == null && conAgree.RecordType.Name != 'Joint') {
                // }
                // else if(conAgree.Investor1__c != null && conAgree.Investor2__c != null && conAgree.RecordType.Name == 'Joint') {
                //     contributionAgreementOfInvestorMap.put(investorMap.get(conAgree.Investor2__c) , conAgree);
                // }
            }

            for (Contact investor : contributionAgreementOfInvestorMap.keySet()) {
                Contribution_Agreement__c conAgree = contributionAgreementOfInvestorMap.get(investor);
                Map<String, Object> AIFMeterMap = new Map<String, Object>();
                
                if(conAgree.RecordType.Name == 'Joint' && conAgree.Investor2__c != null) {
                    String jointInvestorName = conAgree.Investor1__r.Name + ' & ' +conAgree.Investor2__r.Name;
                    AIFMeterMap = constructAIFMeterMap(investor, conAgree , true , jointInvestorName);
                }
                else
                {
                    AIFMeterMap = constructAIFMeterMap(investor, conAgree , false , null);
                }
                
                if (responseMap.containsKey(conAgree.Investor1__r.AccountId)) {
                    responseMap.get(conAgree.Investor1__r.AccountId).add(AIFMeterMap);
                }

            }

            List<Map<String, Object>> responseList = new List<Map<String, Object>>();
            
            for (Account account : accountList) {
                Map<String, Object> accMap = constructAccountMap(account);
                List<Map<String, Object>> investorAIFDetails = responseMap.get(account.Id);

                if (investorAIFDetails == null) {
                    investorAIFDetails = new List<Map<String, Object>>();
                }
                
                responseList.add(new Map<String, Object>{
                    'Account' => accMap,
                    'investorAIFDetails' => investorAIFDetails
                });
            }

            if (!responseList.isEmpty()) {
                wResponse = createResponse(true, 'AIF Meter details synced successfully', responseList);
                res.responseBody = Blob.valueOf(JSON.serialize(wResponse));
                res.statusCode = 200;
            } else {
                wResponse = createResponse(false, 'Records Not Found', null);
                res.responseBody = Blob.valueOf(JSON.serialize(wResponse));
                res.statusCode = 404;
            }
            

            System.debug('Total Number of Accounts That We Get >>>>>> ' + accountList.Size());
            System.debug('Total Number of Investors We That We Get >>>>>> ' + investorList.Size());
            System.debug('Total Number of Contribution Agreements That We Get >>>>>> ' + contributionAgreementList.Size());

        }
        catch(Exception e)
        {
            wResponse = createResponse(false, 'Exception : ' + e.getMessage(), null);
            res.responseBody = Blob.valueOf(JSON.serialize(wResponse));
            res.statusCode = 500;
        }
    }

    private static ResponseWrapper createResponse(Boolean isSuccess, String message, List<Map<String, Object>> dataList) {
        ResponseWrapper res = new ResponseWrapper();
        res.isSuccess = isSuccess;
        res.message = message;
        
        if (dataList != null && !dataList.isEmpty()) {
            res.dataList = dataList;
        }
        System.debug('Response >>>> ' + res);
        return res;
    }

    private static Map<String, Object> constructAccountMap(Account account) {
        Map<String, Object> accountMap = new Map<String, Object>();
        accountMap.put('Id', account.Id);
        accountMap.put('Name', account.Name);
        accountMap.put('Primary_Contact__c', account.Primary_Contact__c);
        accountMap.put('Primary_Country_Code__c', account.Primary_Country_Code__c);

        return accountMap;
    }
    
    private static Map<String,Object> constructAIFMeterMap(Contact investor , Contribution_Agreement__c conAgree , Boolean isJointCA , String jointInvestorName) {
        Map<String, Object> AIFMeterMap = new Map<String, Object>();
        AIFMeterMap.put('salesforce_investor_id', conAgree.Investor1__c);
        AIFMeterMap.put('investor_name', conAgree.Investor1__r.Name);
        AIFMeterMap.put('invested', conAgree.Total_Drawdowns__c);
        AIFMeterMap.put('pending', conAgree.Balance_Amounts__c);
        
        if(conAgree.Total_Committed_Amount__c != null){
            AIFMeterMap.put('committed', conAgree.Total_Committed_Amount__c);
        }
        else {
            AIFMeterMap.put('committed', 0);
        }

        if(conAgree.Total_Contribution_Amount__c != null)
        {
            AIFMeterMap.put('total_contribution_amount', conAgree.Total_Contribution_Amount__c);
        }
        else {
            AIFMeterMap.put('total_contribution_amount', 0);
        }
        
        if(isJointCA) {
            AIFMeterMap.put('isJointCA', true);
            AIFMeterMap.put('joint_investor_name', jointInvestorName);
        }
        else{
            AIFMeterMap.put('isJointCA', false);
            AIFMeterMap.put('joint_investor_name', null);
        }
        return AIFMeterMap;
    }

    global class ObjPrimaryContactWrapper {
        global String countryCode;
		global String primaryNumber;
    }
     
    global class RequestWrapper {
        global List<ObjPrimaryContactWrapper> primaryContactList;
    }

    global class ResponseWrapper {
        global Boolean isSuccess;
        global String message;
        global List<Map<String, Object>> dataList;

        global ResponseWrapper() {
            isSuccess = false;
            message = '';
            dataList = new List<Map<String, Object>>();
        }
    }
}