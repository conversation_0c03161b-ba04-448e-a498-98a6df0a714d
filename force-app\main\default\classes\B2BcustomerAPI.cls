@RestResource(urlMapping='/B2BcustomerAPI/*')
global with sharing class B2BcustomerAPI {
    private static String CLASS_NAME = 'B2BcustomerAPI';
    
    @HttpPost
    //method to fetch request and sending responce 
    global static responseWrapper sendCustomerDetails() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        
        Map<String, Object> requestBody = (Map<String, Object>)JSON.deserializeUntyped(req.requestBody.toString());
        System.debug('requestBody' + requestBody);
        Integer pageSize = (Integer)requestBody.get('pageSize');
        Integer pageNo = (Integer)requestBody.get('pageNo');
        List<Object> orglistObj = (List<Object>)requestBody.get('orglist');
        
        Set<Id> AccIdSet = new Set<Id>();
        responseWrapper errorResponse = new responseWrapper();
        for(Object obj : orglistObj) {
            String orgIdString = String.valueOf(obj);
            if (orgIdString.length() == 18 || orgIdString.length() == 15) {
                AccIdSet.add((Id)orgIdString);
            } else {
                errorResponse.error = 'Invalid Salesforce Id';
                System.debug('Invalid Salesforce Id: ' + orgIdString);
                return errorResponse; 
            }
        }
        
        System.debug('pageSize: ' + pageSize +' ' + 'pageNo: ' + pageNo + ' ' +'AccIdSet: ' + AccIdSet);

        if (pageSize == null || pageNo == null || AccIdSet == null) {
            res.statusCode = 400; // Bad Request
            errorResponse.error = 'pageSize, pageNo, and orglist are required parameters';
            return errorResponse;
        } 

        Id specificId = System.Label.b2b_tenenat_user;
        Set<Id> nonB2bAccIds = new Set<Id>();
        
        Boolean containsSpecificId = AccIdSet.contains(specificId);
        if (containsSpecificId) {
            nonB2bAccIds = getNonB2bAccountIds();
            AccIdSet.addAll(nonB2bAccIds);
        }
        
        List<CustomerWrapper> customersList;
        try {
           customersList = queryCustomers(AccIdSet, pageSize, pageNo, containsSpecificId, nonB2bAccIds);
            responseWrapper jsonResponse = new responseWrapper();
            jsonResponse.customers = customersList;
            jsonResponse.pageSize = pageSize;
            jsonResponse.pageNo = pageNo;
            jsonResponse.totalCount = customersList.size();
            jsonResponse.error = '';
            
            res.statusCode = 200; // OK
            res.addHeader('Content-Type', 'application/json');
            res.responseBody = Blob.valueOf(JSON.serialize(jsonResponse));
            System.debug('jsonResponse===>' + JSON.serialize(jsonResponse));
            return jsonResponse;
            
        } catch(Exception e) {
            res.statusCode = 500; // Internal Server Error
            Error_Log__c log =  new Error_Log__c();
            log.Class_Name__c = CLASS_NAME;
            log.method__c = 'sendCustomerDetails' ;
            log.Error_Message__c = e.getMessage();
            insert log;
            System.debug('Error sending request to B2B web App : ' + log.Error_Message__c); 
            
            //send error email
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            String[] toAddresses = new String[] { '<EMAIL>' }; 
            email.setToAddresses(toAddresses);
            email.setSubject('Error while sending customer data');
            email.setPlainTextBody(
                'An error occurred in the class B2BcustomerAPI. Below are the details:\n\n' +
                'Timestamp: ' + DateTime.now().format('yyyy-MM-dd HH:mm:ss') + '\n' +
                'Request Parameters:\n' +
                '  pageSize: ' + pageSize + '\n' +
                '  pageNo: ' + pageNo + '\n' +
                '  orglist: ' + JSON.serialize(orglistObj) + '\n' +
                'AccIdSet: ' + JSON.serialize(AccIdSet) + '\n\n' +
                'Class Name: ' + CLASS_NAME + '\n' +
                'Error Message: ' + e.getMessage() + '\n' +
                'Error Stack Trace: ' + e.getStackTraceString() + '\n\n' +
                'Error Log ID: ' + log.Id + '\n' 
            );

   		    Messaging.sendEmail(new Messaging.SingleEmailMessage[] { email });

            errorResponse.error = 'An error occurred while querying customer data';
            return errorResponse;
        }    
    }
    
    // Method to prepare customer JSON response
    public static List<CustomerWrapper> queryCustomers(Set<Id> AccIdSet, Integer pageSize, Integer pageNo, Boolean containsSpecificId, Set<Id> nonB2bAccIds) {
        List<CustomerWrapper> customersList = new List<CustomerWrapper>();
        Integer startingRow = (pageNo - 1) * pageSize;
        if (AccIdSet == null) {    
            return customersList; 
        }
        Id specificId = System.Label.b2b_tenenat_user;
        
        
       String accountQuery = 'SELECT Id, Name, Primary_Contact__c, Primary_Country_Code__C, Actual_Email__c, Membership_Status__c, Membership_Validity__c, PAN__c, Org_RM__c, Org_RM__r.Name, Org_RM__r.Actual_Email__c, Onboarding_Status__c, Org_RM__r.Primary_Country_Code__C, Org_RM__r.Primary_Contact__c, Relationship_Manager__c, Relationship_Manager__r.Name, Relationship_Manager__r.Email, Relationship_Manager__r.Phone, (SELECT Id FROM Contacts), Total_Contribution_Amount__c, Total_Drawdowns__c, Amount_Paid__c, Special_Relationship_Type__c FROM Account';
        
         if (!containsSpecificId) {
            accountQuery += ' WHERE Partner_parent_account__c IN :AccIdSet AND B2B_User_Type__c = \'Customer\'';
        } else {
            accountQuery += ' WHERE Id IN :nonB2bAccIds';
            AccIdSet.removeAll(nonB2bAccIds);
            
            if (!AccIdSet.isEmpty()) {
                accountQuery += ' OR (Partner_parent_account__c IN :AccIdSet AND B2B_User_Type__c = \'Customer\')';
            }     
        }
        
        accountQuery += ' ORDER BY Id LIMIT :pageSize OFFSET :startingRow';
        
        List<Account> accounts = Database.query(accountQuery); 
       
        
        // Map to group investors by Account Id
        Map<Id, List<Contact>> accountInvestorsMap = new Map<Id, List<Contact>>();
        
        for (Account acc : accounts) {
            if (acc.Contacts != null && !acc.Contacts.isEmpty()) {
                accountInvestorsMap.put(acc.Id, acc.Contacts);
            }
        }
        
        Set<Id> investorIds = new Set<Id>();
        
        for (List<Contact> contactList : accountInvestorsMap.values()) {
            for (Contact con : contactList) {
                investorIds.add(con.Id);
            }
        }

         String investmentSubQuery = 'SELECT Id, Type__c, Parent_Investment__c, Investment_Amount__c, Investor_Type__c, Final_Commitment_Amount__c, ' +
                                'Investment_Fee_Received__c, Investment_Date__c, Exit_Fee_received__c, IRR_Value__c, IRR_Percentage_Formula__c, ' +
                                'Number_of_Units_Held__c, Exit_Date__c, Exit_amount_transferred__c, Date_of_Drawdown_sent__c, Commitment_Timestamp1__c, ' +
                                'Date_of_transaction__c, Startup_Round__c, Startup_Round__r.Syndicate_Owner__c,  Startup_Round__r.Name, Startup_Round__r.Startup__c, Startup_Round__r.Startup__r.Public_Name__c, ' +
                                'Investment_Amount_Due__c, Investment_Fee_Due__c, createdDate, Startup_Round__r.Startup__r.Legal_Name__c, ' +
                                'Startup_Round__r.Startup__r.Portfolio_map_Sector__c, Startup_Round__r.External_Deal__c, Startup_Round__r.Round_Closed__c, ' +
                                'Investment_in_Own_Name_Family_Member__c FROM Investments__r WHERE Type__c NOT IN (\'Incomplete Data\', \'Invested - Shadow\')';


         List<Contact> investorList = Database.query(
        'SELECT Id, Name, Phone, Email, Investor_s_PAN__c, Contribution_Agreement__r.Total_Contribution_Amount__c, ' +
        'Contribution_Agreement__r.Total_Drawdowns__c, Contribution_Agreement__r.Id, KYC_Status__c, (' + investmentSubQuery + ') ' +
        'FROM Contact WHERE Id IN :investorIds'
    );

        Map<Id, List<Investment__c>> parentChildMap = new Map<Id, List<Investment__c>>();
        List<Investment__c> childInvestments = new List<Investment__c>();
        
        // Build the parent-child relationship map and gather child investments
        for (Contact investor : investorList) {
            for (Investment__c inv : investor.Investments__r) {
                if (inv.Parent_Investment__c != null && (inv.Type__c == 'Partial Exit' || inv.Type__c == 'Exit')) {
                    if (!parentChildMap.containsKey(inv.Parent_Investment__c)) {
                        parentChildMap.put(inv.Parent_Investment__c, new List<Investment__c>());
                    }
                    parentChildMap.get(inv.Parent_Investment__c).add(inv);
                    childInvestments.add(inv); // Add to child investments list
                } else {
                    if (!parentChildMap.containsKey(inv.Id)) {
                        parentChildMap.put(inv.Id, new List<Investment__c>());
                    }
                }
            }
        }
        
        // Map to hold investors with their Ids
        Map<Id, Contact> investorMap = new Map<Id, Contact>(investorList);
        Date today = Date.today();
        Date oneMonthFromToday = today.addMonths(1);
        
        for (Account customer : accounts) {
            String memberShip;
            
            if (customer.Membership_Validity__c > oneMonthFromToday) {
                memberShip = 'Active';
            } else if (customer.Membership_Validity__c <= oneMonthFromToday && customer.Membership_Validity__c >= today) {
                memberShip = 'Due';
            } else {
                memberShip = 'Expired';
            }
            String specialRelationType = (customer.Special_Relationship_Type__c == 'Category 8 (Syndicate)') ? 'Syndicate' : 'IPV';
           
            String validEmail = getFirstValidEmail(customer.Actual_Email__c);

            CustomerWrapper customerObj = new CustomerWrapper(customer.Id, customer.Name, validEmail, String.valueOf(customer.Primary_Country_Code__C), customer.Primary_Contact__c, customer.Onboarding_Status__c, memberShip, String.valueOf(customer.Membership_Validity__c), customer.Amount_Paid__c, customer.PAN__c, specialRelationType);
            
            ipvRMWrapper ipvWrapperObj = new ipvRMWrapper();
            ipvWrapperObj.salesforceId = customer.Relationship_Manager__c;
            ipvWrapperObj.name = customer.Relationship_Manager__r.Name;
            ipvWrapperObj.email = isValidEmail(customer.Relationship_Manager__r.Email) ? customer.Relationship_Manager__r.Email : '';
            ipvWrapperObj.mobile = customer.Relationship_Manager__r.Phone;
            
            String orgRMEmail = getFirstValidEmail(customer.Org_RM__r.Actual_Email__c);
            orgRMWrapper orgWrapperObj = new orgRMWrapper();
            orgWrapperObj.salesforceId = customer.Org_RM__c;
            orgWrapperObj.name = customer.Org_RM__r.Name;
            orgWrapperObj.email = orgRMEmail;
            orgWrapperObj.countryCode = String.valueOf(customer.Org_RM__r.Primary_Country_Code__C);
            orgWrapperObj.mobile = customer.Org_RM__r.Primary_Contact__c;
            
            customerObj.ipvRM = ipvWrapperObj;
            customerObj.orgRM = orgWrapperObj;
            
            Decimal totalContributionAmount = 0;
            Decimal totalDrawdownAmount = 0;
            Decimal cat2totalContributionAmount = 0;
            Decimal cat2totalDrawdownAmount = 0;
            Set<Id> processedContributionAgreements = new Set<Id>();
            
            List<Contact> investors = accountInvestorsMap.get(customer.Id);
            
            if (investors != null) {
                for (Contact invContact : investors) {
                    Contact investor = investorMap.get(invContact.Id);
                    
                    String kycStatus;
                    if(investor.KYC_Status__c == 'KYC Pending')
                        kycStatus = 'KYC - WIP';
                    else if(investor.KYC_Status__c == 'KYC Completed')
                        kycStatus = 'CA - WIP';
                    else if(investor.KYC_Status__c == 'KYC Declined')
                        kycStatus = 'KYC Declined';
                    else if(investor.KYC_Status__c == 'CA Declined')
                        kycStatus = 'CA Dropped';
                    else if(investor.KYC_Status__c == 'CA Completed')
                        kycStatus = 'Completed';
                    else 
                        kycStatus =  'NA';
                    
                    InvestorWrapper investorObj = new InvestorWrapper(investor.Id, investor.Name, investor.Email, investor.Phone, kycStatus);
                    
                    for (Investment__c inv : investor.Investments__r) {
                        
                        if (inv.Parent_Investment__c == null && parentChildMap.containsKey(inv.Id) && parentChildMap.get(inv.Id).size() > 0) {
                            continue;
                        }
                        // Process child investments and parent investments with no children
                        
                        String productType;
                        Decimal investedAmount;
                        String investmentType;
                        if (inv.Investor_Type__c == 'Via AIF') {
                            productType = 'CAT-1';
                        } else if (inv.Investor_Type__c == 'Via Platform' || inv.Investor_Type__c == 'Via LLP') {
                            productType = 'Direct';
                        } else {
                            productType = 'Direct'; // Default value if no match found
                        }
                        if(inv.Type__c == 'Invested'){
                            investedAmount = inv.Investment_Amount__c;
                            investmentType = 'Invested';
                        }
                        else if(inv.Type__c == 'Committed' || inv.Type__c == 'Pending confirmation' ){
                            investedAmount = inv.Final_Commitment_Amount__c;
                            investmentType = 'Committed';
                        } else if(inv.Type__c == 'Waitlist'){
                            investedAmount = inv.Final_Commitment_Amount__c;
                            investmentType = 'Waitlisted';
                        }
                        else if(inv.Type__c == 'Back out approved' || inv.Type__c == 'Back out unapproved'){
                            investmentType = 'Backed Out';
                        }
                        else if(inv.Type__c == 'Round closed - deal dropped'){
                            investmentType = 'Deal Dropped';
                        }
                        else if(inv.Type__c == 'Round Closed-commitment Released'){
                            investmentType = 'Commitment Released';
                        }
                        else {
                            investmentType = 'Exit';
                            investedAmount = inv.Investment_Amount__c;
                        }
                        
                        String dealType = (inv.Startup_Round__r.External_Deal__c == 'Yes') ? 'Syndicate' : 'IPV';
                        
                        String dealStatus = (inv.Startup_Round__r.Round_Closed__c == True) ? 'Closed' : 'WIP';
                        Decimal drawdownAmount = (inv.Investment_Fee_Due__c != null ? inv.Investment_Fee_Due__c : 0) + (inv.Investment_Amount_Due__c != null ? inv.Investment_Amount_Due__c : 0);
                        String consentDate = (inv.Commitment_Timestamp1__c != null) ? inv.Commitment_Timestamp1__c : inv.createdDate.format('yyyy-MM-dd HH:mm:ss');
                        boolean isJoint = false;
                        if(inv.Investment_in_Own_Name_Family_Member__c == 'Joint A/c')
                            isJoint = true;
                        
                        InvestmentWrapper investmentObj = new InvestmentWrapper(inv.Id, investedAmount, investmentType, productType, inv.Investment_Fee_Received__c,  String.valueOf(inv.Date_of_transaction__c), inv.Exit_Fee_received__c, inv.IRR_Value__c, inv.IRR_Percentage_Formula__c, inv.Number_of_Units_Held__c,  String.valueOf(inv.Exit_Date__c), inv.Exit_amount_transferred__c, dealType, dealStatus, inv.Final_Commitment_Amount__c, drawdownAmount, String.valueOf(inv.Date_of_Drawdown_sent__c),consentDate ,String.valueOf(inv.Date_of_transaction__c), isJoint);
                        StartupWrapper startupObj = new StartupWrapper();
                        startupObj.startupId = inv.Startup_Round__r.Startup__c;
                        startupObj.publicName = inv.Startup_Round__r.Startup__r.Public_Name__c;
                        startupObj.legalName = inv.Startup_Round__r.Startup__r.Legal_Name__c;
                        startupObj.sector = inv.Startup_Round__r.Startup__r.Portfolio_map_Sector__c;
                        
                        StartupRoundWrapper roundObj = new StartupRoundWrapper();
                        roundObj.id = inv.Startup_Round__c;
                        roundObj.name = inv.Startup_Round__r.Name;
                        roundObj.dealType = dealType;
                        roundObj.dealStatus = dealStatus;
                        roundObj.syndicateOwner = inv.Startup_Round__r.Syndicate_Owner__c;
                        startupObj.startupRoundObj = roundObj;
                        if (inv.Startup_Round__r.Syndicate_Owner__c != null) {
                            roundObj.source = 'Syndicate';
                        } else {
                            roundObj.source = 'IPV';
                        }
                        startupObj.startupRoundObj = roundObj;
                        investmentObj.startup = startupObj;
                        investorObj.investments.add(investmentObj);
                    }
                    
                    if (investor.Contribution_Agreement__r != null && !processedContributionAgreements.contains(investor.Contribution_Agreement__r.Id)) {
                        if (investor.Contribution_Agreement__r.Total_Contribution_Amount__c != null) {
                            totalContributionAmount += investor.Contribution_Agreement__r.Total_Contribution_Amount__c;
                        }
                        if (investor.Contribution_Agreement__r.Total_Drawdowns__c != null) {
                            totalDrawdownAmount += investor.Contribution_Agreement__r.Total_Drawdowns__c;
                        }
                        processedContributionAgreements.add(investor.Contribution_Agreement__r.Id);
                    }
                    
                    customerObj.investors.add(investorObj);
                }
            }
            
            ContributionAgreementWrapper caObj = new ContributionAgreementWrapper();
            caObj.totalContributionAmount = totalContributionAmount;
            caObj.totalDrawdownAmount = totalDrawdownAmount;
            customerObj.cat1Details = caObj;
            if(customer.Total_Contribution_Amount__c != null){
                cat2totalContributionAmount += customer.Total_Contribution_Amount__c;
            }
            if(customer.Total_Drawdowns__c != null){
                cat2totalDrawdownAmount += customer.Total_Drawdowns__c;
            }
            ContributionAgreementWrapper cat2 = new ContributionAgreementWrapper();
            cat2.totalContributionAmount = cat2totalContributionAmount;
            cat2.totalDrawdownAmount = cat2totalDrawdownAmount;
            customerObj.cat2Details = cat2;
            
            customersList.add(customerObj);
        }
        
        System.debug('customersList => ' + customersList);
        return customersList;
    }
    
    public static Set<Id> getNonB2bAccountIds(){
        Set<Id> accountIds = new Set<Id>();
        
        List<Startup_Round__c> startupRounds = [
            select Id , Name ,(SELECT Id, name, Investor__r.AccountId from Investments__r where Account__r.Is_Tenant__c = false AND Account__r.B2B_User_Type__c = null) from Startup_Round__c where External_Deal__c = 'Yes'
        ];
        
        for (Startup_Round__c startupRound : startupRounds) {
            for (Investment__c investment : startupRound.Investments__r) {
                accountIds.add(investment.Investor__r.AccountId);
            }
        }
        System.debug('Non b2b Account IDs: ' + accountIds);
        return accountIds;

    }
    
    //method to validate email
    public static Boolean isValidEmail(String email) {
        if (String.isEmpty(email)) {
            return false;
        }
        
        String emailRegex = '^(?!.*\\.\\.)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$';
        return Pattern.matches(emailRegex, email);
    }
    	
    //method to get the first valid email from Actual_Email__c field
    public static String getFirstValidEmail(String emailField) {
        if (String.isBlank(emailField)) {
            return '';
        }
      
        String[] emails = emailField.split(' ');
        String firstEmail = emails[0];
      
        return isValidEmail(firstEmail) ? firstEmail : '';
    }

    // Wrapper class for customer details
    global class CustomerWrapper {
        public String salesforceId;
        public String name;
        public String email;
        public String countryCode;
        public String mobile;
        public orgRMWrapper orgRM;
        public ipvRMWrapper ipvRM;
        public String onBoardingStatus;
        public String memberShip;
        public String membershipValidity;
        public Decimal membershipFees;
        public String pan;
        public ContributionAgreementWrapper cat1Details;
        public ContributionAgreementWrapper cat2Details;
        public List<InvestorWrapper> investors;
        public String investorType; 
        
        global CustomerWrapper(String salesforceId, String name, String email, String countryCode, String mobile, String onBoardingStatus, String memberShip, String membershipValidity,Decimal membershipFees, String pan, String investorType ) {
            this.salesforceId = salesforceId;
            this.name = name;
            this.email = email;
            this.countryCode = countryCode;
            this.mobile = mobile;
            this.orgRM = new orgRMWrapper();
            this.ipvRM = new ipvRMWrapper();
            this.onBoardingStatus = onBoardingStatus;
            this.memberShip = memberShip;
            this.membershipValidity = membershipValidity;
            this.membershipFees = membershipFees;
            this.pan = pan;
            this.cat1Details = new ContributionAgreementWrapper();
            this.cat2Details = new ContributionAgreementWrapper();
            this.investors = new List<InvestorWrapper>();
            this.investorType = investorType;
        }
    }
    
    // Wrapper for IPV RM
    public class ipvRMWrapper {
        public String salesforceId;
        public String name;
        public String email;
        public String mobile;
    }
    
    // Wrapper for Org RM
    public class orgRMWrapper {
        public String salesforceId;
        public String name;
        public String email;
        public String countryCode;
        public String mobile;      
    }
    
    public class ContributionAgreementWrapper {
        public Decimal totalContributionAmount;
        public Decimal totalDrawdownAmount; 
    }

    // Wrapper class for investor details
    public class InvestorWrapper {
        public String Id;
        public String name;
        public String email;
        public String phone;
        public List<InvestmentWrapper> investments;
        public String kycStatus;
        
        public InvestorWrapper(String Id, String name, String email, String phone, String kycStatus) {
            this.Id = Id;
            this.name = name;
            this.email = email;
            this.phone = phone;
            this.investments = new List<InvestmentWrapper>();
            this.kycStatus = kycStatus;
        }
    }
    
    // Wrapper class for investment details
    public class InvestmentWrapper {
        public String Id;
        public String productType;
        public Decimal investedAmount;
        public String investmentType;
        public Decimal investmentFeePaid;
        public String dateOfInvestment;
        public Decimal exitFee;
        public Decimal IRRrealised;
        public Decimal IRRunrealised;
        public Decimal unitsAlloted;
        public String exitDate;
        public Decimal exitAmount;
        public StartupWrapper startup;
        public String dealType;
        public String dealStatus;
        public Decimal consentAmount;
        public Decimal drawdownAmount;
        public String drawdownDate;
        public String consentDate;
        public String invDate;
        public Boolean isJoint;
        
        public InvestmentWrapper(String Id, Decimal investedAmount, String investmentType, String productType, Decimal investmentFeePaid, String dateOfInvestment, Decimal exitFee, Decimal IRRrealised, Decimal IRRunrealised,Decimal unitsAlloted, String exitDate, Decimal exitAmount, String dealType, String dealStatus, Decimal consentAmount,Decimal drawdownAmount,String drawdownDate,String consentDate, String invDate , Boolean isJoint ) {
            this.Id = Id;
            this.productType = productType;
            this.investedAmount = investedAmount;
            this.investmentType = investmentType;
            this.investmentFeePaid = investmentFeePaid;
            this.dateOfInvestment = dateOfInvestment;
            this.exitFee = exitFee;
            this.IRRrealised = IRRrealised;
            this.IRRunrealised = IRRunrealised;
            this.unitsAlloted = unitsAlloted;
            this.exitDate = exitDate;
            this.exitAmount = exitAmount;
            this.startup = new StartupWrapper();
            this.dealType = dealType;
            this.dealStatus = dealStatus;
            this.consentAmount = consentAmount;
            this.drawdownAmount = drawdownAmount;
            this.drawdownDate = drawdownDate;
            this.consentDate = consentDate;
            this.invDate = invDate;
            this.isJoint = isJoint;
            
        }
    }  
    
    public class StartupWrapper{
        public String startupId;
        public String publicName;
        public String legalName;
        public String sector;
        public StartupRoundWrapper startupRoundObj;
    } 
    
    public class StartupRoundWrapper{
        public String id;
        public String name;
        public String dealType;
        public String dealStatus;
        public String syndicateOwner;
        public String source;
    }
    
    global class responseWrapper{
       global List<CustomerWrapper>  customers{get;set;}
       global Integer pageSize{get;set;}
       global Integer pageNo{get;set;}
       global Integer totalCount{get;set;}
	   global String error{get; set;}
    }
}