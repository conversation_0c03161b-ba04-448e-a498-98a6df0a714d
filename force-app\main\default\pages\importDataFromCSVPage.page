<apex:page controller="importDataFromCSVController" lightningStylesheets="true">
    <apex:form >
        <apex:pagemessages />
        <apex:pageBlock >
            <apex:pageBlockButtons >
                <apex:commandButton value="Import" action="{!importCSVFile}"/>
            </apex:pageBlockButtons>
            <apex:pageBlockSection columns="2">
                <apex:selectList value="{!selectedObj}" multiselect="false" size="1" label="Select Available Object: ">
                    <apex:selectOptions value="{!ObjOptions}"/>
                </apex:selectList>
                <apex:inputFile id="Attachment" value="{!csvFileBody}"  filename="{!csvAsString}"  title="Please select .csv Only: "/>

            </apex:pageBlockSection>
            
        </apex:pageBlock>
        <apex:pageBlock >
           <apex:pageblocktable value="{!accList}" var="acc">
              <apex:column value="{!acc.name}" />
              <apex:column value="{!acc.AccountNumber}" />
              <apex:column value="{!acc.Type}" />
              <apex:column value="{!acc.Accountsource}" />
              <apex:column value="{!acc.Industry }" />
        </apex:pageblocktable>
     </apex:pageBlock>
   </apex:form>
</apex:page>