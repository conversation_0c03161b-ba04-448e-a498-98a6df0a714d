global class VCStartupFilterCtrl
{
    
    Public sObject sObjStd{get;set;} 
    Public static String sObjectType;
    
    Public List<StartupVConnectWrapper> StartupVConnectWrapperFullList{get;set;}
    Public List<StartupVConnectWrapper> StartupVConnectWrapperList{get;set;}
    Map<Integer,List<StartupVConnectWrapper>> startupVConnectWrapPageMap = new Map<Integer,List<StartupVConnectWrapper>>();   
    
    Public List<Venture_Connect__c> ventureCList{get;set;}
    private integer currentPageNo = 1;  //keeps track of the offset
    public integer maxRecordSize=10; //sets the page size or number of rows
    public String queryString = '';
    global static String sortDir;
    Public String sortDirvf {get;set;}

    
    Public VCStartupFilterCtrl(ApexPages.StandardController controller)
    {
        sortDirvf = 'desc';
        ventureCList = new List<Venture_Connect__c>();
        StartupVConnectWrapperList = new List<StartupVConnectWrapper>();
        StartupVConnectWrapperFullList = new List<StartupVConnectWrapper>();
        sObjectType = controller.getRecord().getSObjectType().getDescribe().getName();
        
        Map<Integer,String> intStrMap = new Map<Integer,String>();
        intStrMap.put(1,'f');
        intStrMap.put(2,'e');
        intStrMap.put(3,'d');
        intStrMap.put(4,'c');
        intStrMap.put(5,'b');
        intStrMap.put(6,'a');
        
        if(sObjectType=='Venture_Connect__c') 
        { 
            List<Startup__c> startupTempList = new List<Startup__c>();
            sObjStd = [select id,name,Investment_Stage__c,Sector_Focus__c,Geographical_Focus__c,Industry__c,Investment_Size_From__c,Investment_Size_To__c,Series__c from Venture_Connect__c where id=:controller.getRecord().Id limit 1];
            queryString = getVCSOQLStr(sObjStd);
            Venture_Connect__c vcObj;
            vcObj = (Venture_Connect__c)sObjStd;
            
            system.debug('## queryString :'+queryString);
            
            if(String.isNotBlank(queryString))
            {
                //queryString += ' limit ' + maxRecordSize + ' offset ' + counter;
                queryString += ' limit 1000';
                startupTempList = Database.query(queryString);
            }
            system.debug('##startupTempList>>>>>'+startupTempList);
            
            for(Startup__c strup : startupTempList)
            {
                if(startupTempList.size()==3)
                {
                 //   break;
                }
                //Match all 6 condition
                if(vcObj.Industry__c!=null && strup.Industry__c!=null && isMultiPicklistMatch(''+vcObj.Industry__c,strup.Industry__c)
                && vcObj.Investment_Size_From__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_From__c <= strup.Fundraise__c
                && vcObj.Investment_Size_To__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_To__c >= strup.Fundraise__c
                && vcObj.Sector_Focus__c!=null && strup.Sector_Focus__c!=null && isMultiPicklistMatch(''+vcObj.Sector_Focus__c,strup.Sector_Focus__c)
                && vcObj.Geographical_Focus__c!=null && strup.Geographical_Focus__c!=null && isGeographicalFocusMatch(vcObj.Geographical_Focus__c,strup.Geographical_Focus__c)                
                && vcObj.Series__c!=null && strup.Round__c!=null && isMultiPicklistMatch(''+vcObj.Series__c,strup.Round__c)
                && vcObj.Investment_Stage__c!=null && strup.Investment_Stage__c!=null && isMultiPicklistMatch(''+vcObj.Investment_Stage__c,strup.Investment_Stage__c)
                )
                {
                    StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(strup,6,'a'));
                }
                //5 condition
                else if(vcObj.Industry__c!=null && strup.Industry__c!=null && isMultiPicklistMatch(''+vcObj.Industry__c,strup.Industry__c)
                && vcObj.Investment_Size_From__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_From__c <= strup.Fundraise__c
                && vcObj.Investment_Size_To__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_To__c >= strup.Fundraise__c
                && vcObj.Sector_Focus__c!=null && strup.Sector_Focus__c!=null && isMultiPicklistMatch(''+vcObj.Sector_Focus__c,strup.Sector_Focus__c)
                && vcObj.Geographical_Focus__c!=null && strup.Geographical_Focus__c!=null && isGeographicalFocusMatch(vcObj.Geographical_Focus__c,strup.Geographical_Focus__c)                
                && vcObj.Series__c!=null && strup.Round__c!=null && isMultiPicklistMatch(''+vcObj.Series__c,strup.Round__c)
                )
                {
                    StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(strup,5,'b'));
                }
                //4 condition
                else if(vcObj.Industry__c!=null && strup.Industry__c!=null && isMultiPicklistMatch(''+vcObj.Industry__c,strup.Industry__c)
                && vcObj.Investment_Size_From__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_From__c <= strup.Fundraise__c
                && vcObj.Investment_Size_To__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_To__c >= strup.Fundraise__c
                && vcObj.Sector_Focus__c!=null && strup.Sector_Focus__c!=null && isMultiPicklistMatch(''+vcObj.Sector_Focus__c,strup.Sector_Focus__c)
                && vcObj.Geographical_Focus__c!=null && strup.Geographical_Focus__c!=null && isGeographicalFocusMatch(vcObj.Geographical_Focus__c,strup.Geographical_Focus__c)                
                )
                {
                    StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(strup,4,'c'));
                }
                //3 condition
                else if(vcObj.Industry__c!=null && strup.Industry__c!=null && isMultiPicklistMatch(''+vcObj.Industry__c,strup.Industry__c)
                && vcObj.Investment_Size_From__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_From__c <= strup.Fundraise__c
                && vcObj.Investment_Size_To__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_To__c >= strup.Fundraise__c
                && vcObj.Sector_Focus__c!=null && strup.Sector_Focus__c!=null && isMultiPicklistMatch(''+vcObj.Sector_Focus__c,strup.Sector_Focus__c)
                )
                {
                    StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(strup,3,'d'));
                } 
                //2 condition
                else if(vcObj.Industry__c!=null && strup.Industry__c!=null && isMultiPicklistMatch(''+vcObj.Industry__c,strup.Industry__c)
                && vcObj.Investment_Size_From__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_From__c <= strup.Fundraise__c
                && vcObj.Investment_Size_To__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_To__c >= strup.Fundraise__c
                )
                {
                    StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(strup,2,'e'));
                }
                else if( 
                        vcObj.Investment_Stage__c!=null && strup.Investment_Stage__c!=null && isMultiPicklistMatch(''+vcObj.Investment_Stage__c,strup.Investment_Stage__c)
                        || (vcObj.Investment_Size_To__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_To__c >= strup.Fundraise__c
                            && vcObj.Investment_Size_From__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_From__c <= strup.Fundraise__c
                            )
                        || vcObj.Geographical_Focus__c!=null && strup.Geographical_Focus__c!=null && isGeographicalFocusMatch(vcObj.Geographical_Focus__c,strup.Geographical_Focus__c)                
                        || vcObj.Sector_Focus__c!=null && strup.Sector_Focus__c!=null && isMultiPicklistMatch(''+vcObj.Sector_Focus__c,strup.Sector_Focus__c)
                        || vcObj.Industry__c!=null && strup.Industry__c!=null && isMultiPicklistMatch(''+vcObj.Industry__c,strup.Industry__c)
                        || vcObj.Series__c!=null && strup.Round__c!=null && isMultiPicklistMatch(''+vcObj.Series__c,strup.Round__c)
                )
                {
                    Integer finalCount = 0;
                        if(vcObj.Investment_Stage__c!=null && strup.Investment_Stage__c!=null && isMultiPicklistMatch(''+vcObj.Investment_Stage__c,strup.Investment_Stage__c))
                        {
                            system.debug('<<<Investment_Stage__c>>>>');    
                            finalCount++;
                        }
                        if(vcObj.Investment_Size_To__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_To__c >= strup.Fundraise__c
                            && vcObj.Investment_Size_From__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_From__c <= strup.Fundraise__c
                        )
                        {
                            system.debug('<<<Investment_Size_To__c>>>>');
                            finalCount++;
                        }
                        if(vcObj.Geographical_Focus__c!=null && strup.Geographical_Focus__c!=null && isGeographicalFocusMatch(vcObj.Geographical_Focus__c,strup.Geographical_Focus__c))
                        {
                            system.debug('<<<Geographical_Focus__c>>>>');
                            finalCount++;
                        }
                        
                        if(vcObj.Sector_Focus__c!=null && strup.Sector_Focus__c!=null && isMultiPicklistMatch(''+vcObj.Sector_Focus__c,strup.Sector_Focus__c))
                        {
                            system.debug('<<<Sector_Focus__c>>>>');    
                            finalCount++;
                        }
                        if(vcObj.Industry__c!=null && strup.Industry__c!=null && isMultiPicklistMatch(''+vcObj.Industry__c,strup.Industry__c))
                        {
                            system.debug('<<<Industry__c>>>>');    
                            finalCount++;
                        }
                        if(vcObj.Series__c!=null && strup.Round__c!=null && isMultiPicklistMatch(''+vcObj.Series__c,strup.Round__c))
                        {
                            system.debug('<<<Series__c>>>>');    
                            finalCount++;
                        } 
						   
                        system.debug('strup Id>>>>'+strup.Id);    
                        system.debug('vcObj >>>>'+vcObj);    
                        system.debug('strup >>>>'+strup);    
                        system.debug('finalCount >>>>'+finalCount);    
                        StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(strup,finalCount,intStrMap.get(finalCount)));
                } 
               
            }
            
            system.debug('##StartupVConnectWrapperFullList>>>>>'+StartupVConnectWrapperFullList);
            system.debug('##StartupVConnectWrapperFullList.size()>>>>>'+StartupVConnectWrapperFullList.size());
            /*
            if(StartupVConnectWrapperFullList.size()<3)
            {
                for(Startup__c strup : startupTempList)
                {
                    if(StartupVConnectWrapperFullList.size()==3)
                    {
                       // break;
                    }
                    //Match any of 5 condition
                    if(!processObjIds.contains(strup.Id) && 
                        (
                        vcObj.Investment_Stage__c!=null && strup.Investment_Stage__c!=null && isMultiPicklistMatch(''+vcObj.Investment_Stage__c,strup.Investment_Stage__c)
                        || (vcObj.Investment_Size_To__c!=null && strup.Total_Investment_In_INR__c!=null && vcObj.Investment_Size_To__c >= strup.Total_Investment_In_INR__c
                            && vcObj.Investment_Size_From__c!=null && strup.Total_Investment_In_INR__c!=null && vcObj.Investment_Size_From__c <= strup.Total_Investment_In_INR__c
                            )
                        || vcObj.Geographical_Focus__c!=null && strup.Geographical_Focus__c!=null && vcObj.Geographical_Focus__c == strup.Geographical_Focus__c
                        || vcObj.Sector_Focus__c!=null && strup.Sector_Focus__c!=null && isMultiPicklistMatch(''+vcObj.Sector_Focus__c,strup.Sector_Focus__c)
                        || vcObj.Industry__c!=null && strup.Industry__c!=null && isMultiPicklistMatch(''+vcObj.Industry__c,strup.Industry__c)
                        || vcObj.Series__c!=null && strup.Round__c!=null && isMultiPicklistMatch(''+vcObj.Series__c,strup.Round__c)
                        )
                    )
                    {
                        StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(strup,1,'f'));
                    }
                }
            }
            */
            
            sortDir = 'desc';
            StartupVConnectWrapperFullList.sort();
            system.debug('## AFTER OR StartupVConnectWrapperFullList>>>>>'+StartupVConnectWrapperFullList);
            system.debug('## AFTER OR StartupVConnectWrapperFullList.size()>>>>>'+StartupVConnectWrapperFullList.size());
            
            startupVConnectWrapPageMap = getStartupRecordPageMap(StartupVConnectWrapperFullList);
            StartupVConnectWrapperList = startupVConnectWrapPageMap.get(1);
            
        }
        else
        {
            sObjStd = [select id,Industry__c,Investment_Stage__c,Sector_Focus__c,Geographical_Focus__c,Fundraise__c,Round__c,Total_Investment_In_INR__c from Startup__c WHERE id=:controller.getRecord().Id limit 1];
            queryString = getStartUpSOQLStr(sObjStd);
            ventureCList = new List<Venture_Connect__c>();
            List<Venture_Connect__c> ventureCTempList = new List<Venture_Connect__c>();
            Startup__c strNewObj;
            strNewObj = (Startup__c)sObjStd;
            
            if(String.isNotBlank(queryString))
            {
                system.debug('## queryString111 :'+queryString);
                //queryString += ' limit ' + maxRecordSize + ' offset ' + counter;
                queryString += ' limit 1000';
                ventureCTempList = Database.query(queryString);
            }
            system.debug('ventureCTempList::'+ventureCTempList);
            
            /*****************************************************************/
            
            
            system.debug('## queryString :'+queryString);
                           
            for(Venture_Connect__c vcNewObj : ventureCTempList)
            {
                if(ventureCList.size()==3)
                {
                   // break;
                }
                //Match all 6 condition
                if(strNewObj.Industry__c!=null && vcNewObj.Industry__c!=null && isMultiPicklistMatch(''+strNewObj.Industry__c,vcNewObj.Industry__c)
                && vcNewObj.Investment_Size_From__c >= strNewObj.Fundraise__c
                && vcNewObj.Investment_Size_To__c <= strNewObj.Fundraise__c
                && strNewObj.Sector_Focus__c!=null && vcNewObj.Sector_Focus__c!=null && isMultiPicklistMatch(''+strNewObj.Sector_Focus__c,vcNewObj.Sector_Focus__c)
                && strNewObj.Geographical_Focus__c!=null && vcNewObj.Geographical_Focus__c!=null && isGeographicalFocusMatch(strNewObj.Geographical_Focus__c,vcNewObj.Geographical_Focus__c)                
                && strNewObj.Round__c!=null && vcNewObj.Series__c!=null && isMultiPicklistMatch(''+strNewObj.Round__c,vcNewObj.Series__c)
                && strNewObj.Investment_Stage__c!=null && vcNewObj.Investment_Stage__c!=null && isMultiPicklistMatch(''+strNewObj.Investment_Stage__c,vcNewObj.Investment_Stage__c)
                )
                {
                    system.debug('## MapStartUpSOQLStr6 :'+vcNewObj);
                    StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(vcNewObj,6,'a'));
                }
                //5 condition
				
                else if(strNewObj.Industry__c!=null && vcNewObj.Industry__c!=null && isMultiPicklistMatch(''+strNewObj.Industry__c,vcNewObj.Industry__c)
                && vcNewObj.Investment_Size_From__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_From__c >= strNewObj.Fundraise__c
                && vcNewObj.Investment_Size_To__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_To__c <= strNewObj.Fundraise__c
                && strNewObj.Sector_Focus__c!=null && vcNewObj.Sector_Focus__c!=null && isMultiPicklistMatch(''+strNewObj.Sector_Focus__c,vcNewObj.Sector_Focus__c)
                && strNewObj.Geographical_Focus__c!=null && vcNewObj.Geographical_Focus__c!=null && isGeographicalFocusMatch(strNewObj.Geographical_Focus__c,vcNewObj.Geographical_Focus__c)                
                && strNewObj.Round__c!=null && vcNewObj.Series__c!=null && isMultiPicklistMatch(''+strNewObj.Round__c,vcNewObj.Series__c)
                )
                {
                    system.debug('## MapStartUpSOQLStr5 :'+vcNewObj);
                    StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(vcNewObj,5,'b'));
                }
				
                //4 condition
                else if(strNewObj.Industry__c!=null && vcNewObj.Industry__c!=null && isMultiPicklistMatch(''+strNewObj.Industry__c,vcNewObj.Industry__c)
                && vcNewObj.Investment_Size_From__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_From__c >= strNewObj.Fundraise__c
                && vcNewObj.Investment_Size_To__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_To__c <= strNewObj.Fundraise__c
                && strNewObj.Sector_Focus__c!=null && vcNewObj.Sector_Focus__c!=null && isMultiPicklistMatch(''+strNewObj.Sector_Focus__c,vcNewObj.Sector_Focus__c)
                && strNewObj.Geographical_Focus__c!=null && vcNewObj.Geographical_Focus__c!=null && isGeographicalFocusMatch(strNewObj.Geographical_Focus__c,vcNewObj.Geographical_Focus__c)                
                )
                {
                    system.debug('## MapStartUpSOQLStr4 :'+vcNewObj);
                    StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(vcNewObj,4,'c'));
                }
                //3 condition
                else if(strNewObj.Industry__c!=null && vcNewObj.Industry__c!=null && isMultiPicklistMatch(''+strNewObj.Industry__c,vcNewObj.Industry__c)
                && vcNewObj.Investment_Size_From__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_From__c >= strNewObj.Fundraise__c
                && vcNewObj.Investment_Size_To__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_To__c <= strNewObj.Fundraise__c
                && strNewObj.Sector_Focus__c!=null && vcNewObj.Sector_Focus__c!=null && isMultiPicklistMatch(''+strNewObj.Sector_Focus__c,vcNewObj.Sector_Focus__c)
                )
                {
                    system.debug('## MapStartUpSOQLStr3 :'+vcNewObj);
                    StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(vcNewObj,3,'d'));
                }
                //2 condition
                else if(strNewObj.Industry__c!=null && vcNewObj.Industry__c!=null && isMultiPicklistMatch(''+strNewObj.Industry__c,vcNewObj.Industry__c)
                && vcNewObj.Investment_Size_From__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_From__c >= strNewObj.Fundraise__c
                && vcNewObj.Investment_Size_To__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_To__c <= strNewObj.Fundraise__c
                )
                {
                    system.debug('## MapStartUpSOQLStr52 :'+vcNewObj);
                    StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(vcNewObj,2,'e'));
                } 
                //1 condition
                else if(
                    strNewObj.Industry__c!=null && vcNewObj.Industry__c!=null && isMultiPicklistMatch(''+strNewObj.Industry__c,vcNewObj.Industry__c)
                    || (vcNewObj.Investment_Size_From__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_From__c >= strNewObj.Fundraise__c
                           && vcNewObj.Investment_Size_To__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_To__c <= strNewObj.Fundraise__c
                       )
                    || strNewObj.Sector_Focus__c!=null && vcNewObj.Sector_Focus__c!=null && isMultiPicklistMatch(''+strNewObj.Sector_Focus__c,vcNewObj.Sector_Focus__c)
                    || strNewObj.Geographical_Focus__c!=null && vcNewObj.Geographical_Focus__c!=null && isGeographicalFocusMatch(strNewObj.Geographical_Focus__c,vcNewObj.Geographical_Focus__c)                
                    || strNewObj.Round__c!=null && vcNewObj.Series__c!=null && isMultiPicklistMatch(''+strNewObj.Round__c,vcNewObj.Series__c)
                    || strNewObj.Investment_Stage__c!=null && vcNewObj.Investment_Stage__c!=null && isMultiPicklistMatch(''+strNewObj.Investment_Stage__c,vcNewObj.Investment_Stage__c)
                    )
                    {
                        Integer finalCount = 0;
						/*
                        if(strNewObj.Industry__c!=null && vcNewObj.Industry__c!=null && isMultiPicklistMatch(''+strNewObj.Industry__c,vcNewObj.Industry__c))
                        {
                            system.debug('<<<Investment_Stage__c>>>>');    
                            finalCount++;
                        }
						*/
                        if(vcNewObj.Investment_Size_From__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_From__c >= strNewObj.Fundraise__c
                           && vcNewObj.Investment_Size_To__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_To__c <= strNewObj.Fundraise__c
                        )
                        {
                            system.debug('<<<Investment_Size_To__c>>>>');    
                            finalCount++;
                        }
                        if(strNewObj.Sector_Focus__c!=null && vcNewObj.Sector_Focus__c!=null && isMultiPicklistMatch(''+strNewObj.Sector_Focus__c,vcNewObj.Sector_Focus__c))
                        {
                            system.debug('<<<Sector_Focus__c>>>>');    
                            finalCount++;
                        }
                        if(strNewObj.Geographical_Focus__c!=null && vcNewObj.Geographical_Focus__c!=null && isGeographicalFocusMatch(strNewObj.Geographical_Focus__c,vcNewObj.Geographical_Focus__c))
                        {
                            system.debug('<<<Geographical_Focus__c>>>>');    
                            finalCount++;
                        }
                        if(strNewObj.Round__c!=null && vcNewObj.Series__c!=null && isMultiPicklistMatch(''+strNewObj.Round__c,vcNewObj.Series__c))
                        {
                            system.debug('<<<Series__c>>>>');    
                            finalCount++;
                        }
                        if(strNewObj.Investment_Stage__c!=null && vcNewObj.Investment_Stage__c!=null && isMultiPicklistMatch(''+strNewObj.Investment_Stage__c,vcNewObj.Investment_Stage__c))
                        {
                            system.debug('<<<Investment_Stage__c>>>>');    
                            finalCount++;
                        }
                        
                        StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(vcNewObj,finalCount,intStrMap.get(finalCount)));
                    }                 
            }
            
            system.debug('##StartupVConnectWrapperFullList>>>>>'+StartupVConnectWrapperFullList);
            system.debug('##StartupVConnectWrapperFullList.size()>>>>>'+StartupVConnectWrapperFullList.size());
            /*
            if(StartupVConnectWrapperFullList.size()<3)
            {
                for(Venture_Connect__c vcNewObj : ventureCTempList)
                {
                    if(StartupVConnectWrapperFullList.size()==3)
                    {
                       // break;
                    }
                     
                    if(!processObjIds.contains(vcNewObj.Id) && 
                    (strNewObj.Industry__c!=null && vcNewObj.Industry__c!=null && isMultiPicklistMatch(''+strNewObj.Industry__c,vcNewObj.Industry__c)
                    || (vcNewObj.Investment_Size_From__c!=null && strNewObj.Total_Investment_In_INR__c!=null && vcNewObj.Investment_Size_From__c >= strNewObj.Total_Investment_In_INR__c
                           && vcNewObj.Investment_Size_To__c!=null && strNewObj.Total_Investment_In_INR__c!=null && vcNewObj.Investment_Size_To__c <= strNewObj.Total_Investment_In_INR__c
                       )
                    || strNewObj.Sector_Focus__c!=null && vcNewObj.Sector_Focus__c!=null && isMultiPicklistMatch(''+strNewObj.Sector_Focus__c,vcNewObj.Sector_Focus__c)
                    || strNewObj.Geographical_Focus__c!=null && vcNewObj.Geographical_Focus__c!=null && strNewObj.Geographical_Focus__c == vcNewObj.Geographical_Focus__c
                    || strNewObj.Round__c!=null && vcNewObj.Series__c!=null && isMultiPicklistMatch(''+strNewObj.Round__c,vcNewObj.Series__c)
                    || strNewObj.Investment_Stage__c!=null && vcNewObj.Investment_Stage__c!=null && isMultiPicklistMatch(''+strNewObj.Investment_Stage__c,vcNewObj.Investment_Stage__c)
                    ))
                    {
                        StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(vcNewObj,1,'f'));
                        processObjIds.add(vcNewObj.Id);
                    }
                }
            }
            */
            
            sortDir = 'desc';
            StartupVConnectWrapperFullList.sort();
            system.debug('## vc StartupVConnectWrapperFullList>>>>>'+StartupVConnectWrapperFullList);
            system.debug('## vc StartupVConnectWrapperFullList.size()>>>>>'+StartupVConnectWrapperFullList.size());
            
            startupVConnectWrapPageMap = getStartupRecordPageMap(StartupVConnectWrapperFullList);
            StartupVConnectWrapperList = startupVConnectWrapPageMap.get(1);
                        
            /*****************************************************************/            
        }
        
        system.debug('sObjStd>>>'+sObjStd);
        system.debug('queryString>>>'+queryString);
    }
    
    global Class StartupVConnectWrapper implements Comparable 
    {
        global Startup__c startup{get;set;}
        global Venture_Connect__c vConnect{get;set;}
        global Integer noOfCriteriaMatch{get;set;}
        global String matchingStringToSort{get;set;}
         
        global StartupVConnectWrapper(sObject st,Integer noOfMatch,String match)
        {
            if(sObjectType=='Venture_Connect__c') 
            { 
                startup = (Startup__c)st;
            }
            else
            {
                vConnect = (Venture_Connect__c)st;
            }
            noOfCriteriaMatch = noOfMatch;
            matchingStringToSort = match;
        }
    
        global Integer compareTo(Object ObjToCompare) {
            //system.debug('sortDir >>>>'+sortDir );         
            if(sortDir =='asc')
            {
                //system.debug('sort direction>>>111');
                return Integer.valueOf(noOfCriteriaMatch - ((StartupVConnectWrapper)objToCompare).noOfCriteriaMatch);
            }
            else
            {
                //system.debug('sort direction>>>222');
                return matchingStringToSort.CompareTo(((StartupVConnectWrapper)ObjToCompare).matchingStringToSort);        
            } 
            
        }
    }
    
    public void toggleSort() {
        system.debug('sortDir000>>>'+sortDir );
        system.debug('sortDirvf >>>'+sortDirvf );
        system.debug('StartupVConnectWrapperList000>>>'+StartupVConnectWrapperFullList);
       
        sortDirvf = sortDirvf.equals('asc') ? 'desc' : 'asc';
        sortDir = sortDirvf;
        
        
        StartupVConnectWrapperFullList.sort();
        currentPageNo = 1;
        system.debug('StartupVConnectWrapperList111>>>'+StartupVConnectWrapperFullList);
        startupVConnectWrapPageMap = getStartupRecordPageMap(StartupVConnectWrapperFullList);
        system.debug('startupVConnectWrapPageMap6666>>'+startupVConnectWrapPageMap);
        
        StartupVConnectWrapperList = startupVConnectWrapPageMap.get(currentPageNo);
                
        system.debug('sortDir>>>'+sortDir );
    }
    
    public Map<Integer,List<StartupVConnectWrapper>> getStartupRecordPageMap(List<StartupVConnectWrapper> stWrapList)
    {
        system.debug('stWrapList>>>'+stWrapList);
            Integer mapKeyCount = 1;
            startupVConnectWrapPageMap = new Map<Integer,List<StartupVConnectWrapper>>();   
            for(StartupVConnectWrapper sp : stWrapList)
            {
                if(!startupVConnectWrapPageMap.containsKey(mapKeyCount))
                {
                    startupVConnectWrapPageMap.put(mapKeyCount,new List<StartupVConnectWrapper>());
                }
                
                if(startupVConnectWrapPageMap.get(mapKeyCount).size()<maxRecordSize)
                {
                    startupVConnectWrapPageMap.get(mapKeyCount).add(sp);
                }
                else
                {
                    mapKeyCount++;
                    startupVConnectWrapPageMap.put(mapKeyCount,new List<StartupVConnectWrapper>()); 
                    startupVConnectWrapPageMap.get(mapKeyCount).add(sp);                   
                }
            }    
            
            system.debug('StartupVConnectWrapperFullList>>>>>>>'+stWrapList.size());
            system.debug('startupVConnectWrapPageMap>>>>>>>'+startupVConnectWrapPageMap.size());
            system.debug('mapKeyCount >>>>>>>'+mapKeyCount );
            //system.debug('Last page >>>>>>>'+startupVConnectWrapPageMap.get(mapKeyCount).size());
            
            if(startupVConnectWrapPageMap.containsKey(mapKeyCount) && startupVConnectWrapPageMap.get(mapKeyCount).size()==0)
            {
                startupVConnectWrapPageMap.remove(mapKeyCount);
                mapKeyCount --;
            }    
            
            system.debug('StartupVConnectWrapperFullList>>>>>>>'+stWrapList.size());
            system.debug('startupVConnectWrapPageMap>>>>>>>'+startupVConnectWrapPageMap.size());
            system.debug('mapKeyCount >>>>>>>'+mapKeyCount );
            //system.debug('Last page >>>>>>>'+startupVConnectWrapPageMap.get(mapKeyCount).size());
        return startupVConnectWrapPageMap;            
    }
    
    Static String getStartUpSOQLStr(sObject obj)
    {
        //Added by Karan queryString updated field Investment_Size_To__c added to queryString
        String queryString = 'select id,name,Type__c,Series__c,Investment_Stage__c,Industry__c,Sector_Focus__c,Geographical_Focus__c,Investment_Size_From__c,Investment_Size_To__c from Venture_Connect__c WHERE ';
        String condition = '';    
        Startup__c vcObj;
        
        vcObj = (Startup__c)obj;
        system.debug('vcObj getSOQLStr>>>'+vcObj);
        
        String geographicalFocusStr = vcObj.Geographical_Focus__c;
        
        if(vcObj.Geographical_Focus__c=='Both')
            geographicalFocusStr = 'Indian,Global';
         
        if(vcObj.Industry__c !=null)
            condition = condition+' Industry__c in '+getMultiPicklistCondition(vcObj.Industry__c)+' OR';
        if(vcObj.Sector_Focus__c!=null)
            condition = condition+' Sector_Focus__c in '+getMultiPicklistCondition(vcObj.Sector_Focus__c)+' OR';
        if(vcObj.Geographical_Focus__c!=null)
            condition = condition+' Geographical_Focus__c in '+getMultiPicklistCondition(geographicalFocusStr)+' OR';
            //condition = condition+' Geographical_Focus__c in '+getMultiPicklistCondition(vcObj.Geographical_Focus__c)+' OR';
        if(vcObj.Fundraise__c!=null)
            condition = condition+' Investment_Size_From__c <= '+vcObj.Fundraise__c+' OR';
        if(vcObj.Fundraise__c!=null)
            condition = condition+' Investment_Size_To__c>= '+vcObj.Fundraise__c+' OR';
        if(vcObj.Round__c !=null)
            condition = condition+' Series__c in '+getMultiPicklistCondition(vcObj.Round__c )+' OR';
        if(vcObj.Investment_Stage__c !=null)
            condition = condition+' Investment_Stage__c in '+getMultiPicklistCondition(vcObj.Investment_Stage__c )+' OR';
                
        condition = condition.removeEnd(' OR');
        
        system.debug('condition>>>'+condition);
         
        if(String.isNotBlank(condition))
            return queryString + condition +' order by LastModifiedDate desc';
        else
            return '';
    }
    
    Static String getVCSOQLStr(sObject obj)
    {
        String queryString = 'select id,Name,Public_Name__c,Sector_Focus__c,Investment_Stage__c,Geographical_Focus__c,Industry__c,Fundraise__c,Round__c,Total_Investment_In_INR__c from Startup__c WHERE ';
        String condition = '';    
        Venture_Connect__c vcObj;
        
        vcObj = (Venture_Connect__c)obj;
        system.debug('vcObj getSOQLStr>>>'+vcObj);
        
        String geographicalFocusStr = vcObj.Geographical_Focus__c;
        
        if(vcObj.Geographical_Focus__c=='Both')
            geographicalFocusStr = 'Indian,Global';
            
        if(vcObj.Industry__c!=null)
            condition = condition+' Industry__c in '+getMultiPicklistCondition(vcObj.Industry__c)+' OR';
        if(vcObj.Sector_Focus__c!=null)
            condition = condition+' Sector_Focus__c in '+getMultiPicklistCondition(vcObj.Sector_Focus__c)+' OR';
        if(vcObj.Geographical_Focus__c!=null)
            condition = condition+' Geographical_Focus__c in '+getMultiPicklistCondition(geographicalFocusStr)+' OR';
            //condition = condition+' Geographical_Focus__c in '+getMultiPicklistCondition(vcObj.Geographical_Focus__c)+' OR';
        if(vcObj.Investment_Size_From__c!=null)
            condition = condition+' Fundraise__c >= '+vcObj.Investment_Size_From__c+' OR';
        if(vcObj.Investment_Size_To__c!=null)
            condition = condition+' Fundraise__c <= '+vcObj.Investment_Size_To__c+' OR';
        if(vcObj.Series__c!=null)
            condition = condition+' Round__c in '+getMultiPicklistCondition(vcObj.Series__c)+' OR';
        if(vcObj.Investment_Stage__c !=null)
            condition = condition+' Investment_Stage__c in '+getMultiPicklistCondition(vcObj.Investment_Stage__c )+' OR';
               
        condition = condition.removeEnd(' OR');
        
        system.debug('condition>>>'+condition);
         
        if(String.isNotBlank(condition))
            return queryString + condition +' order by LastModifiedDate desc';
        else
            return '';
    }
    
    static String getMultiPicklistCondition(String mPicklistval)
    {
        List<String> mPicklistvalList = mPicklistval.split(';');
        system.debug('mPicklistvalList>>>'+mPicklistvalList);
        
        String mpicklistStr = '';
        for(String includeValue :mPicklistvalList)
            mpicklistStr += '\''+includeValue+'\',';
        
        system.debug('mpicklistStr>>>'+mpicklistStr);
        
        mpicklistStr = '('+ mpicklistStr.removeEnd(',') +')';
        
        return mpicklistStr;
    }
    
    public static Boolean isMultiPicklistMatch(String srcStr,String desStr)
    {
        List<String> srcList = srcStr.split(';');
        List<String> desList = desStr.split(';');
        Boolean retFlag = false;
       
        for(String s : srcList)
        {
            //system.debug('Source MultiPicklist val>>>'+s);
            if(desList.contains(s))
            {
                retFlag = true;
                break;
            }
        }
        return retFlag;
    }
    
    public Static Boolean isGeographicalFocusMatch(String srcStr,String desStr)
    {
        Boolean retFlag = false;
        if(!String.isEmpty(srcStr) && !String.isEmpty(desStr) && srcStr=='Both')
            retFlag = true;
        else if(!String.isEmpty(srcStr) && !String.isEmpty(desStr) && srcStr!='Both' && srcStr==desStr)
            retFlag = true;
            
        return retFlag;
    }
   
   
    public PageReference Beginning() { //user clicked beginning
        currentPageNo = 1;
        StartupVConnectWrapperList = startupVConnectWrapPageMap.get(currentPageNo);
        return null;
    }
   
    public PageReference Previous() { //user clicked previous button
        currentPageNo--;
        StartupVConnectWrapperList = startupVConnectWrapPageMap.get(currentPageNo);
        return null;
    }
    
    public PageReference Next() { //user clicked next button
        currentPageNo++;
        StartupVConnectWrapperList = startupVConnectWrapPageMap.get(currentPageNo);
        return null;
    }
    
    public PageReference End() { //user clicked end
        currentPageNo = startupVConnectWrapPageMap.size();
        StartupVConnectWrapperList = startupVConnectWrapPageMap.get(currentPageNo);
        return null;
    }
    
    public Boolean getDisablePrevious() { 
        //this will disable the previous and beginning buttons
        if (currentPageNo>1) return false; else return true;
    }
    public Boolean getDisableNext() { //this will disable the next and end buttons
        if (currentPageNo < startupVConnectWrapPageMap.size()) return false; else return true;
    }
    
   
    
    public Integer getPageNumber() {
        return currentPageNo ;
    }
    
    public Integer getTotalPages() {
         return startupVConnectWrapPageMap.size();
    }
}