public class IPVQuaterlyEmailContent {
    
    
    public class EmailContent {
        public String subjectLine;
        public String body;
        public Blob   attachmentBody;
        public String attachmentName;
    }
    
    
    public static EmailContent generateReferralEmail(
        String        memberName,
        String        rmName,
        String        quarter,
        Id            referringAccountId,
        String        contact,
        List<Account> referredAccounts,
        List<Lead__c> referredLeads
    ) {
        
        if (referredAccounts == null) referredAccounts = new List<Account>();
        if (referredLeads    == null) referredLeads    = new List<Lead__c>();
        
        EmailContent content         = new EmailContent();
        content.subjectLine          = 'IPV Quarterly Referral Report (' + quarter + ')  ' + memberName;
        content.attachmentName       = 'How_To_Earn_Points.pdf';
        content.attachmentBody       = getPdfAttachment();          
        content.body                 =
            buildEmailBody(
                memberName,
                rmName,
                contact,
                quarter,
                buildReferralTable(referringAccountId, memberName, quarter,
                                   referredAccounts, referredLeads)
            );
        
        return content;
    }
    public static EmailContent generateMotivationalEmail(
    String memberName,
    String rmName,
    String rmContact,
    String quarter
) {
    EmailContent content           = new EmailContent();
    content.subjectLine            = 'IPV Quarterly Referral Report (' + quarter + ')  ' + memberName;
    content.attachmentName         = 'How_To_Earn_Points.pdf';
    content.attachmentBody         = getPdfAttachment();  

    content.body =
        'Dear ' + memberName + ',<br/><br/>' +
        'Greetings from IPV!<br/><br/>' +
        'At the outset, we’d like to thank you for being a valuable member of our platform. ' +
        'As we celebrate our 5th year, we’re proud to have backed over 250 startups, with 70 of them securing follow on funding. ' +
        'We’ve also recorded 16 successful exits and 29 partial exits, resulting in remarkable returns for our members.<br/><br/>' +
        'We noticed you haven’t referred any of your friends or family. We’d be happy to help if you would like to invite someone from your network to join the IPV community. ' +
        'Upon receipt, our team will connect with your referrals within 24 hours.<br/><br/>' +
        '<strong>Share &amp; Shine! Grow with every connection.</strong><br/><br/>' +
        '<a href="https://api.whatsapp.com/send?phone=919289150056&text=Share%20%26%20Shine%3A%20Grow%20with%20Every%20Connection%0A%0AYou%20are%20welcome%20to%20share%20multiple%20contact%20cards%20with%20us%20to%20invite%20your%20friends%20%26%20family%20to%20IPV%20on%20this%20WhatsApp%20chat.%20Upon%20receipt%2C%20our%20team%20will%20connect%20with%20your%20referrals%20within%2024%20hours.%0A%0AInterim%2C%20you%20could%20share%20a%20sneak%20peek%20video%20from%20our%20Co-founders%20with%20your%20friends%20%26%20family%20to%20help%20them%20familiarize%20with%20IPV%20%3A%20https%3A%2F%2Fbit.ly%2F4ccoz66%0A%0ANote%3A%20For%20any%20updates%20or%20inquiries%2C%20please%20reach%20out%20to%20your%20Relationship%20Manager/referral">Please click here</a> to invite like minded individuals to join IPV. ' +
        'As a gesture of appreciation, we offer points and an Amazon voucher of INR 3,000/- for every successful referral.<br/><br/>' +
        'In the interim, you could share a sneak peek video from our Co founders with your friends &amp; family to help them familiarise with <br/>' +'IPV'+
        '<a href="https://www.youtube.com/watch?v=xZ11BcfnEMk"> Join Inflection Point Ventures (IPV) for an Exclusive Angel Investing Networking Experience! </a><br/><br/>' +
        'Please refer to the attached document on <strong>“How to Earn Points”</strong> and understand how you can waive off your membership fee.<br/><br/>' +
        'In case of any queries, please feel free to reach out to your Investor Success Manager (ISM), ' +
        rmName + ' – ' + rmContact + '<br/><br/>' +
        'Warm Regards,<br/>Team IPV';

    return content;
}
    
    
    private static String buildEmailBody(
        String memberName,
        String rmName,
        String contact,
        String quarter,
        String referralTableHtml
    ) {
        String body =
            'Dear ' + memberName + ',<br/><br/>' +
            'Greetings for the day!<br/>' +
            'Please find below the status of your Referrals for ' + quarter + ':<br/><br/>' +
            '<strong>"Table Of Referral Report"</strong><br/><br/>' +
            referralTableHtml + '<br/><br/>' +
            '<strong>Share &amp; Shine! Grow with every connection</strong><br/>' +
            'Please click here ' +
            '<a href="https://api.whatsapp.com/send?phone=919289150056&text=Share%20%26%20Shine%3A%20Grow%20with%20Every%20Connection%0A%0AYou%20are%20welcome%20to%20share%20multiple%20contact%20cards%20with%20us%20to%20invite%20your%20friends%20%26%20family%20to%20IPV%20on%20this%20WhatsApp%20chat.%20Upon%20receipt%2C%20our%20team%20will%20connect%20with%20your%20referrals%20within%2024%20hours.%0A%0AInterim%2C%20you%20could%20share%20a%20sneak%20peek%20video%20from%20our%20Co-founders%20with%20your%20friends%20%26%20family%20to%20help%20them%20familiarize%20with%20IPV%20%3A%20https%3A%2F%2Fbit.ly%2F4ccoz66%0A%0ANote%3A%20For%20any%20updates%20or%20inquiries%2C%20please%20reach%20out%20to%20your%20Relationship%20Manager/referral">Inflection Point Venture</a> ' +
            ' to invite like minded individuals to join IPV As a token of appreciation, we <br/>'+'thank you with points and an Amazon voucher of INR 3,000/- for every successful referral and your referral gets a 20% <br/>'+ 'discount on their membership fee.<br/><br/>' +
            'Please refer to the attached document on <strong>“How to earn points”</strong> and understand how you can waive off your membership fee.<br/><br/>' +
            'In case of any queries, please feel free to reach out to your respective Investor Success Manager (ISM) ' +
            rmName + ' – ' + contact + '<br/><br/>' +
            '<strong>Warm Regards,<br/>Team IPV</strong>';
        
        return body;
    }
    
    
    private static String buildReferralTable(
        Id            referringAccountId,
        String        memberName,
        String        quarter,
        List<Account> referredAccounts,
        List<Lead__c> referredLeads
    ) {
        Map<String, String> statusBuckets = new Map<String, String>{
            'On Trial'                            => 'On Trial',
                'On Trial Community'                  => 'On Trial',
                'AIM+ Sales'                          => 'Paid',
                'Paid by IPV Points'                  => 'Paid',
                'Paid by CXO Points'                  => 'Paid',
                'Paid Community'                      => 'Paid',
                'Paid IPV Fee'                        => 'Paid',
                'Exited by own'                       => 'Exited/Rotated',
                'Rotated by IPV'                      => 'Exited/Rotated',
                'Complimentary'                       => 'Complimentary',
                'Add On'                              => 'Complimentary',
                'Asked to call back'                  => 'Lead - Prospects',
                'Reactivated'                         => 'Lead - Prospects',
                'New Lead'                            => 'Lead - Prospects',
                'New Lead (DND)'                      => 'Lead - Prospects',
                'To be reached out'                   => 'Lead - Prospects',
                'Introduction message sent'           => 'Lead - Prospects',
                'Call Scheduled'                      => 'Lead - Prospects',
                'Awaiting Response'                   => 'Lead - Prospects',
                'Call done'                           => 'Lead - Prospects',
                'Payment link to be sent'             => 'Lead - Prospects',
                'IPV RM to reach out'                 => 'Lead - Prospects',
                'Payment link shared'                 => 'Lead - Prospects',
                'No Response - Call 5'                => 'Lead - No Response',
                'No Response - Call 4'                => 'Lead - No Response',
                'No Response - Call 3'                => 'Lead - No Response',
                'No Response - Call 2'                => 'Lead - No Response',
                'No Response - Call 1'                => 'Lead - No Response',
                'Connect Later > 6 months'            => 'Lead - No Response',
                'Follow up sent'                      => 'Lead - No Response',
                'DND Activated'                       => 'Lead - No Response',
                'No Response after Multiple Follow ups' => 'Lead - No Response',
                'Declined'                            => 'Lead - Declined/Duplicate',
                'False lead'                          => 'Lead - Declined/Duplicate',
                'Onboarding done but not moved to trial' => 'Lead - Declined/Duplicate',
                'Duplicate lead'                      => 'Lead - Declined/Duplicate',
                'Invalid number'                      => 'Lead - Declined/Duplicate',
                'Form Refilled'                       => 'Lead - Declined/Duplicate',
                'Startup looking for fund'            => 'Lead - Declined/Duplicate'
                };
                    List<String> allBuckets = new List<String>{
                        'Lead - Prospects',
                            'Lead - No Response',
                            'Lead - Declined/Duplicate',
                            'On Trial',
                            'Complimentary',
                            'Paid',
                            'Exited/Rotated'
                            };
                                Map<String, Integer> bucketCounts = new Map<String, Integer>();
        for (String b : allBuckets) bucketCounts.put(b, 0);
        
        Integer totalReferrals = 0;
        
        for (Account a : referredAccounts) {
            String bucket = statusBuckets.get(a.Membership_Status__c);
            if (bucket != null) {
                bucketCounts.put(bucket, bucketCounts.get(bucket) + 1);
                totalReferrals++;
            }
        }
        
        for (Lead__c l : referredLeads) {
            String bucket = statusBuckets.get(l.Membership_Status__c);
            if (bucket != null) {
                bucketCounts.put(bucket, bucketCounts.get(bucket) + 1);
                totalReferrals++;
            }
        }
        
        String html  = '';
        html += '<table width="100%" cellpadding="0" cellspacing="0" ' +
            'style="border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;font-size:10px;">' +
            '<tr><td colspan="' + (1 + allBuckets.size()) + '" ' +
            'style="background:#002E56;color:#FFFFFF;font-weight:bold;text-align:center;font-size:14px;padding:8px;">' +
            memberName + ' - Referral Report - ' + quarter + '</td></tr></table>';
        
        html += '<table width="100%" cellpadding="4" cellspacing="0" ' +
            'style="font-family:Arial,Helvetica,sans-serif;border:1px solid #000000;font-size:10px;">';
        
        html += '<tr>';
        html += '<th style="background:#FFC000;color:#000;font-weight:bold;border:1px solid #000;">Total Referrals Received</th>';
        for (String bucket : allBuckets) {
            String color = (bucket == 'Paid') ? '#2E7D32' : '#1F6284';
            html += '<th style="background:' + color + ';color:#FFF;font-weight:bold;border:1px solid #000;">' +
                bucket + '</th>';
        }
        html += '</tr>';
        
        html += '<tr>';
        html += '<td style="border:1px solid #000;text-align:center;font-weight:bold;">' + totalReferrals + '</td>';
        for (String bucket : allBuckets) {
            html += '<td style="border:1px solid #000;text-align:center;font-weight:bold;">' +
                bucketCounts.get(bucket) + '</td>';
        }
        html += '</tr></table>';
        
        /* detail rows */
        html += '<table width="100%" cellpadding="4" cellspacing="0" ' +
            'style="border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;border:1px solid #000;margin-top:10px;font-size:10px;">' +
            '<tr><td colspan="3" style="background:#002E56;color:#FFF;font-weight:bold;text-align:center;padding:8px;">Referral Details</td></tr>' +
            '<tr>' +
            '<th style="border:1px solid #000;font-weight:bold;">Referee Name</th>' +
            '<th style="border:1px solid #000;font-weight:bold;">Date of Referring</th>' +
            '<th style="border:1px solid #000;font-weight:bold;">Status</th>' +
            '</tr>';
        
        for (Account a : referredAccounts) {
            String bucket = statusBuckets.get(a.Membership_Status__c);
            if (bucket != null) {
                html += '<tr>' +
                    '<td style="border:1px solid #D9D9D9;">' + a.Name + '</td>' +
                    '<td style="border:1px solid #D9D9D9;text-align:center;">' +
                    (a.Date_of_receiving_lead__c != null ? a.Date_of_receiving_lead__c.format() : '') +
                    '</td>' +
                    '<td style="border:1px solid #D9D9D9;">' + bucket + '</td>' +
                    '</tr>';
            }
        }
        
        for (Lead__c l : referredLeads) {
            String bucket = statusBuckets.get(l.Membership_Status__c);
            if (bucket != null) {
                html += '<tr>' +
                    '<td style="border:1px solid #D9D9D9;">' + l.Name + '</td>' +
                    '<td style="border:1px solid #D9D9D9;text-align:center;">' +
                    (l.Date_of_receiving_lead__c != null ? l.Date_of_receiving_lead__c.format() : '') +
                    '</td>' +
                    '<td style="border:1px solid #D9D9D9;">' + bucket + '</td>' +
                    '</tr>';
            }
        }
        html += '</table>';
        return html;
    }
    
    private static Blob  pdfCache;
    private static Blob getPdfAttachment() {
        if (pdfCache == null) {
            pdfCache = [
                SELECT Body
                FROM   StaticResource
                WHERE  Name = 'How_To_Earn_Points'
                LIMIT  1
            ].Body;
        }
        return pdfCache;
    }
}