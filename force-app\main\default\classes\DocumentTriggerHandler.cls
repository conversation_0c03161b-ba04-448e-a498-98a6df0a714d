public class DocumentTriggerHandler {
    public static void bulkBefore(List<Document__c> docs){
        
        system.debug('in doc trigger ::');
        for(Document__c doc : docs){
            system.debug('doc ::'+doc);
            if(doc.contribution_Agreement__c !=  null && 
               (doc.document_name__c == 'Standard Contribution Agreement' ||  doc.document_name__c == 'Premier Contribution Agreement' ||
                doc.document_name__c == 'Premier+ Contribution Agreement' || doc.document_name__c == 'Initiation Email Sent')){
                    system.debug('document_name__c ::'+doc.document_name__c);
                    doc.AIF_Document_Number__c = '1';
                    Integer countCA = [SELECT Id FROM document__c where contribution_Agreement__c = :doc.Contribution_Agreement__c 
                                       and (document_name__c = 'Standard Contribution Agreement' OR document_name__c = 'Premier Contribution Agreement'
                                            OR document_name__c = 'Premier+ Contribution Agreement')].size();
                    system.debug('coubt ::'+countCA);
                    if(doc.document_name__c.contains('Contribution Agreement') && countCA >= 1){
                        doc.addError('Only one CA document can be created');
                    }
                    Integer countEmailDoc= [SELECT Id FROM document__c where contribution_Agreement__c = :doc.Contribution_Agreement__c and document_name__c = 'Initiation Email Sent'].size();
                    system.debug('coubt ::'+countEmailDoc);
                    if(doc.document_name__c == 'Initiation Email Sent' && countEmailDoc > 1){
                        doc.addError('Only one Initiation Email Sent document can be created');
                    }
                }
            if(doc.contribution_Agreement__c !=  null && (doc.document_name__c == 'Regular Addendum' 
               ||  doc.document_name__c == 'Premier Top up Addendum' || doc.document_name__c == 'Premier+ Top up Addendum')){
                   Integer countCA = [SELECT Id FROM document__c where contribution_Agreement__c = :doc.Contribution_Agreement__c 
                                      and (document_name__c = 'Regular Addendum' OR document_name__c = 'Premier Top up Addendum'
                                           OR document_name__c = 'Premier+ Top up Addendum')].size();
                   system.debug('countCA ::'+countCA);
                   if(countCA == 0){
                       doc.AIF_Document_Number__c = '1';
                   }else{
                       doc.AIF_Document_Number__c = string.valueOf(countCA+1);
                   }
                   
           }
           if(doc.contribution_Agreement__c !=  null && (doc.document_name__c == 'Fatca Form' 
               ||  doc.document_name__c == 'Premier Shift of Class Addendum' || doc.document_name__c == 'Premier+ Shift of Class Addendum')){
                   Integer countCA = [SELECT Id FROM document__c where contribution_Agreement__c = :doc.Contribution_Agreement__c 
                                      and (document_name__c = 'Fatca Form' OR document_name__c = 'Premier Shift of Class Addendum'
                                           OR document_name__c = 'Premier+ Shift of Class Addendum')].size();
                   if(countCA == 0){
                       doc.AIF_Document_Number__c = '1';
                   }else{
                       doc.AIF_Document_Number__c = string.valueOf(countCA+1);
                   }                  
           }
        } 
    }
    
    public static void bulkAfter(List<Document__c> docs){
        // decimal total = 0;
        List<Contribution_Agreement__c> lstCA = new List<Contribution_Agreement__c>();
        Map<Id,Decimal> mapCATotal = new Map<Id,Decimal>();
        set<ID> CAID = new set<ID>();
        for(Document__c doc : docs){
            if(doc.Contribution_Agreement__c != null){
                CAID.add(doc.Contribution_Agreement__c);
            }
        }
        system.debug('CAID ::'+CAID);
        List<Contribution_Agreement__c> listCA = [SELECT Id,Date_of_Signing__c,Total_Contribution_Amount__c,Certificate_Number__c,Class_type__c,
                                                  Membership_Status__c,(select Id,FPC_Signing_Date__c,document_name__c,AIF_Document_Amount__c,Stamp_Paper__c,Contributor_Signing_Date__c FROM Documents__r ORDER BY createdDate asc) 
                                                  FROM Contribution_Agreement__c WHERE Id IN:CAID];
        system.debug('listCA ::'+listCA);
        for(Contribution_Agreement__c CA : listCA){
            decimal total = 0;
            for(document__c doc : CA.documents__r){
                if(doc.document_name__c != null && doc.document_name__c.contains('Contribution Agreement')){
                    if(doc.Stamp_Paper__c != null){
                        CA.Certificate_Number__c = doc.Stamp_Paper__c;                   
                    }
                    system.debug('doc.document_name__c ::'+doc.document_name__c);
                    if(doc.FPC_Signing_Date__c != null){
                        CA.Date_of_Signing__c = doc.FPC_Signing_Date__c;//doc.Contributor_Signing_Date__c;
                    }
                    if(doc.document_name__c == 'Standard Contribution Agreement'){
                        CA.Class_type__c = 'Class D';
                        CA.Membership_Status__c = 'Standard';
                    }else if(doc.document_name__c == 'Premier Contribution Agreement'){
                        CA.Class_type__c = 'Class D/E';
                        CA.Membership_Status__c = 'Premier';
                    }else if(doc.document_name__c == 'Premier+ Contribution Agreement'){
                        CA.Class_type__c = 'Class E';
                        CA.Membership_Status__c = 'Premier+';
                    }else if(doc.document_name__c == 'Premier Select Contribution Agreement'){
                        CA.Class_type__c = 'Class E';
                        CA.Membership_Status__c = 'Premier Select';
                    }
                }
                if(doc.document_name__c == 'Premier Top up Addendum' || doc.document_name__c == 'Premier Shift of Class Addendum'){
                    CA.Class_type__c = 'Class D/E';
                    CA.Membership_Status__c = 'Premier';
                }else if(doc.document_name__c == 'Premier+ Top up Addendum' || doc.document_name__c == 'Premier+ Shift of Class Addendum'){
                    CA.Class_type__c = 'Class E';
                    CA.Membership_Status__c = 'Premier+';
                }else if(doc.document_name__c == 'Deboarding to Class D Addendum'){
                        CA.Class_type__c = 'Class D';
                        CA.Membership_Status__c = 'Premier';
                }else if(doc.document_name__c == 'Premier Select - Shift of Class Addendum'){
                    CA.Class_type__c = 'Class E';
                    CA.Membership_Status__c = 'Premier Select';
                }else if(doc.document_name__c == 'Premier Select - Top up Addendum'){
                    CA.Class_type__c = 'Class E';
                    CA.Membership_Status__c = 'Premier Select';
                }
                
                if(doc.document_name__c != null && doc.document_name__c.contains('Contribution Agreement') && doc.AIF_Document_Amount__c != null){
                    total += doc.AIF_Document_Amount__c;
                }
                if(doc.document_name__c != null && doc.document_name__c == 'Regular Addendum' && doc.AIF_Document_Amount__c != null){
                    total += doc.AIF_Document_Amount__c;
                }
                if(doc.document_name__c != null && doc.document_name__c == 'Premier Top up Addendum' && doc.AIF_Document_Amount__c != null){
                    total += doc.AIF_Document_Amount__c;
                }
                if(doc.document_name__c != null && doc.document_name__c == 'Premier+ Top up Addendum' && doc.AIF_Document_Amount__c != null ){
                    total += doc.AIF_Document_Amount__c;
                }
                CA.Total_Contribution_Amount__c = total;
            }
        }
        update listCA;
    }
}