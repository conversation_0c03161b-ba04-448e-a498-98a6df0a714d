<apex:component >
    <apex:actionStatus onstart="startLoading();" onstop="stopLoading();" id="loadStatus" />
    <style>
        .overlay {
            display: none;
            height: 100%;
            left: 0;
            position: fixed;
            top: 0;
            opacity: 0.3;
            -moz-opacity: 0.3;
            width: 100%;
            -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
            filter: alpha(opacity=30);
            background: #000;
            -khtml-opacity: 0.3;
            z-index: 1000;
        }
 
        .loader {
            background: url('/img/loading32.gif') scroll no-repeat 0 0;
            width: 32px;
            height: 32px;
            position: absolute;
            left: 50%;
        }
    </style>
 
    <div id="load_scrl" class="loadingBox loader" style="display:none"> </div>
    <div class="loadingBox overlay"> </div>
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js"></script>
    <script type="text/javascript">
 
        function startLoading() {
            $('#load_scrl').css('top', $(document).scrollTop() + 200);
            $('.loadingBox').show();
        }
 
        function stopLoading() {
            $('.loadingBox').hide();
        }
    </script>
</apex:component>