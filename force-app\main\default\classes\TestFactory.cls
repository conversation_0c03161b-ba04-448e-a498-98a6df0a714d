@isTest
public class TestFactory {
  
  public static Account createAccount()
  {
        Account accAux = new Account();
        accAux.Name = 'testing';
        accAux.ShippingStreet       = '1 Main St.';
        accAux.ShippingState        = 'VA';
        accAux.ShippingPostalCode   = '12345';
        accAux.ShippingCountry      = 'USA';
        accAux.ShippingCity         = 'Anytown';
        accAux.Description          = 'This is a test account';
        accAux.BillingStreet        = '1 Main St.';
        accAux.BillingState         = 'VA';
        accAux.BillingPostalCode    = '12345';
        accAux.BillingCountry       = 'USA';
        accAux.BillingCity          = 'Anytown';
        accAux.AnnualRevenue        = 10000;
        accAux.ParentId = null;
        accAux.Official_Email__c = '<EMAIL>';
        accAux.Title__c= 'Mr';
        accAux.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        accAux.Membership_Validity__c= date.newInstance(2020, 9, 15);
        accAux.Primary_Country_Code__c= 91;
        accAux.Lead_Source__c= 'Others';
        accAux.Preferred_Email__c= 'Personal';
        accAux.Personal_Email__c = '<EMAIL>';
        AccAux.Secondary_Contact__c ='*********';
        AccAux.Official_Email__c ='<EMAIL>';
      	accAux.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        Integer len = 10;
        String str = string.valueof(Math.abs(Crypto.getRandomLong()));
        String randomNumber =  str.substring(0, len);
        system.debug('Random Number>>>' +  randomNumber );
        
        accAux.Primary_Contact__c= ''+randomNumber ;
        accAux.Primary_Group_Name__c = 'Trial 10';
        accAux.Membership_Status__c = 'Platinum';
        accAux.Relationship_Manager__c = UserInfo.getUserId();
    
        return accAux;
    }
    
    public static Lead__c createLead()
    {
        Lead__c ld = new Lead__c();
        ld.Name = 'Test321';
        //ld.Member_Name__c = 'Test321';
        ld.Lead_Source__c= 'Others';
        ld.Title__c= 'Mr';
        ld.Preferred_Email__c= '<EMAIL>';
        ld.Primary_Country_Code__c= 91;
        Integer len = 10;
        String str = string.valueof(Math.abs(Crypto.getRandomLong()));
        String randomNumber =  str.substring(0, len);
        system.debug('Random Number>>>' +  randomNumber );
        //ld.Relationship_Owner__c = UserInfo.getUserId();
        ld.Primary_Contact__c= ''+randomNumber ;
        ld.Secondary_Contact__c = ''+randomNumber ;
        id LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        ld.RecordTypeId =LeadRecordTypeIdIPV;
        ld.Relationship_Owner__c = UserInfo.getUserId();
        
        return ld;
    }
    public static Startup__c createStartUp()
    {
        Startup__c str = new Startup__c();
        str.Public_Name__c = 'testName';
        str.Legal_Name__c = 'testName';
        return str;
    }
    
    public static Venture_Connect__c createVentureConnect()
    {
        Venture_Connect__c str = new Venture_Connect__c();
        str.Name = 'testName';
        return str;
    }
       
    public static Startup_Round__c createStartUpRound(Id ldAnalystId, Id ldMemberId,Id stUpId)
    {
        Startup_Round__c str = new Startup_Round__c();
        str.Lead_Analyst__c = ldAnalystId;
        str.Lead_Member__c = ldMemberId;
        str.Startup__c = stUpId;
        str.Date_Of_Founders_Call__c = date.newInstance(2020, 9, 15);
        str.Pre_Money_Valuation__c = 10;
        str.Doi_Percent_Fee__c = 11;
        str.Doi_Percent_Equity__c = 12;
        return str;
    }
    
    public static Investment__c createInvestment(Id accId)
    {
        Investment__c inv = new Investment__c();
        inv.Account__c = accId;
        inv.Type__c = 'Invested';
        inv.Investor_Name__c = 'test inv name';
        Inv.Investor__c = System.Label.Investor_IPV_Advisors_Pvt_Ltd;
        
        
        return inv;
    }
    public static Events__c createEvents(String StartupRoundName)
    {
        Events__c ev = new Events__c();
        ev.Name = 'test';
        ev.Startup_Round_Name__c = StartupRoundName;
        return ev;
    }
    public static Attendance__c createAttendance(String priContact,String perContEmail,Id evnId)
    {
        Attendance__c att = new Attendance__c();
        att.Name = 'test';
        att.Primary_Contact__c = priContact; 
        att.Personal_Email__c = perContEmail;
        att.Preferred_Email__c = 'Personal';
        att.Primary_Country_Code__c = 91;
        att.Events__c = evnId;
        att.Lead_Source__c = 'Email';
        att.RecordTypeId = Schema.SObjectType.Attendance__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        return att;
    }
    public static lead CreatStandardlead ()
    {
       lead ld = new lead();
       ld.LastName ='testTom';
       ld.Designation__c='CFO';
       ld.LeadSource = 'Web';
       ld.Email = '<EMAIL>';
       ld.Company ='abc';
       //ld.Are_you_a_first_time_investor__c ='';
       ld.Phone = '*********';
       ld.Status = '';
        return ld;
    }
    
     public static user CreateUser ()
    {
       user u = new user();
       u.Email = '<EMAIL>';
       u.FirstName = 'Test';
       u.LastName ='User1';
       u.Contact_No__c ='*********';
       u.MobilePhone = '1234565432';
	   u.ProfileId = Userinfo.getProfileId();
       u.Alias ='Tuse';
       u.TimeZoneSidKey = 'Asia/Kolkata';
       u.LocaleSidKey ='en_US';
       u.EmailEncodingKey ='ISO-8859-1';
       u.LanguageLocaleKey ='en_US';
       u.Username = '<EMAIL>';
      
       return u;
    }
}