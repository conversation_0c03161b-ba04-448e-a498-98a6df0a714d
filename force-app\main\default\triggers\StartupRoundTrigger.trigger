trigger StartupRoundTrigger on Startup_Round__c (Before Insert,After Insert,After update) {
    
    List<System_Setting__c> settingList = System_Setting__c.getall().values();
    if(settingList!=null && settingList.size()>0 && !settingList[0].StartupRoundTriggerActivated__c)
    {
        system.debug('Returning from StartupRoundTrigger because of custom setting>>>'+settingList);
        return;
    }   
         
    StartupRoundTriggerHandler srth = new StartupRoundTriggerHandler();
    if(trigger.isInsert && trigger.isBefore)
    {
        srth.beforeInsert(trigger.New); 
        //Added by <PERSON><PERSON><PERSON> as we have updated nomeclature logic with this new method. 23-11-2023
        srth.beforeInsertNew(trigger.New);
    }
    
    if(trigger.isInsert && trigger.isAfter)
    {
        srth.afterInsert(trigger.New);
    }
        
    if(trigger.isUpdate && trigger.isAfter)
    {
        srth.afterUpdate(trigger.NewMap,trigger.OldMap);
    }
}