/************************************************************************
    Test Class for CommunicationSummaryTriggerHandler
************************************************************************/
@isTest(SeeAllData=false)
public class CommunicationSummaryTriggerTestClass {

    @isTest
    public static void testHandleBeforeInsert() {
        
        
        Startup__c st = TestFactory.createStartUp();
        st.VC_Connect__c = true;
        st.Portfolio_map_Sector__c = 'Saas';
        st.Sector_Focus__c = 'B2B';
        st.Industry__c = 'Internet';
        st.Round__c = 'Series B';
        st.Fundraise__c = 10;
        st.VC_Connect__c = true;
        insert st;
        system.debug('Startup Object Id>>>>>>>>>>...' + st.Id);
         
        Venture_Connect__c vc = TestFactory.createVentureConnect();
        vc.Investment_Size_From__c = 5;
        vc.Investment_Size_To__c = 20;
        vc.Not_preferred_Sector__c = 'Services';
        vc.Not_preferred_sub_sector__c = 'IT/ ITES';	
        vc.Sub_sector__c = 'Banking/ Financial Services';
        vc.Industry__c = 'Banking/ Financial Services';
        vc.Series__c = 'Pre Series';
        vc.VC_Connect__c = true;

        insert vc;
        
        Venture_Connect__c vcc = [SELECT Id FROM Venture_Connect__c LIMIT 1];
        Startup__c str = [SELECT Id , Startup_Unique_Number__c FROM Startup__c LIMIT 1];
        
        // Test data setup
        List<Communication_Summary__c> testSummaryList = new List<Communication_Summary__c>();
        List<Date> dateList = new List<Date>();
        Date introductionDate1 = Date.today().addDays(-60);
        dateList.add(introductionDate1);
        Date introductionDate2 = Date.today().addDays(-35);
        dateList.add(introductionDate2);
        Date introductionDate3 = Date.today().addDays(-95);
        dateList.add(introductionDate3);
        Date introductionDate4 = Date.today().addDays(-135);
        dateList.add(introductionDate4);
        
        for (Integer i = 0; i < 4; i++) {
            Communication_Summary__c summary = new Communication_Summary__c();
            summary.Date_of_Introduction_to_VC__c = dateList[i];
            summary.Venture_Connect__c = vcc.Id;
            summary.Startup_Name__c = str.Id;
            testSummaryList.add(summary);
        }
        
        Test.startTest();
        CommunicationSummaryTriggerHandler.handleBeforeInsert(testSummaryList);
        insert testSummaryList;
        Test.stopTest();
        
    }
}