public class LeadCustomObjTriggerHandler
{
    public void beforeInsert(List<Lead__c> leadList)
    {  
        Map<ID, Schema.RecordTypeInfo> recordTypeMap = Schema.SObjectType.lead__C.getRecordTypeInfosById();
        update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(leadList); 
        
        for(Lead__c ld: leadList){
            // System.debug('Trigger Before insert lead NAME>>>>'+ld.Name);
            If(ld.RecordTypeId != null && ld.Primary_Contact__c != null)
            {
                ld.Record_Type_Primary_Contact_Unique_Key__c = ''+recordTypeMap.get(ld.RecordTypeId).getDeveloperName() +'_'+ld.Primary_Contact__c;
            }
            
            /* Added By Sahil on 26/03/2024 */
            if(ld.Facebook_Campaign_Name__c != null && ld.Facebook_Campaign_Name__c !='')
            {
                if(isValidCampaign(ld.Facebook_Campaign_Name__c))
                {
                    ld.Campaign__c = ld.Facebook_Campaign_Name__c;
                }                    
            }
            
            //  Added by <PERSON><PERSON><PERSON><PERSON> on 17.05.2024
            if (ld.Referred_By__c != null) {
                ld.Is_this_referral_lead__c = true;
            } else {
                ld.Is_this_referral_lead__c = false;
            }
            
            if(ld.Relationship_Owner__c != null)
            {
                ld.Date_of_Assignment_to_IPV_RM__c = Date.Today();
            }
        }        
    }
    
    
    public void afterInsert(List<Lead__c> leadList)
    {
        Id recordTypeId = Schema.SObjectType.lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        List<Lead__c> idForPoints = new List<Lead__c>();
        Set<Id> leadIDSet = new Set<Id>();
        set<id> IdSetForAppLead = new set<id>();
        for(Lead__c ld : leadList)
        {
            // System.debug('Trigger After insert lead NAME>>>>'+ld.Name);
            if(ld.RecordTypeId == recordTypeId && ld.Is_this_referral_lead__c == true && ld.Referred_By__c != NULL){
                IdSetForAppLead.add(ld.Id);
            }
            
            if(ld.RecordTypeId == recordTypeId){
                system.debug('innn trigger');
                leadIDSet.add(ld.Id);
                if(ld.Lead_Source__c == 'App - Startup Referral' && ld.Membership_Status__c == 'Startup looking for fund'){
                    idForPoints.add(ld);
                    system.debug('innn trigger ::'+idForPoints);
                }
            }
            //  Added By Sahil on 01.04.2024 For Lead Quality Score & Lead Quality Score % Updation. 
            if(ld.Id != null && ld.RecordTypeId == recordTypeId)
            {
                LeadQualityScoreController.getLeadQualityScore(ld.Id);    
            }
            
        }
        
        if(IdSetForAppLead.size() > 0){
            LeadRestAPIController.SendLeadDetails(IdSetForAppLead, true);}
        
        if(idForPoints.size() > 0){
            createPointTransaction(idForPoints);
        }
        
        if(!Test.isRunningTest() && leadIDSet.size()>0 )
        {
            OutboundMessageAPIController.sendWhatsappAPILead(leadIDSet);
            OutboundMessageAPIController.sendWhatsappAPIForNonRefLead(leadIDSet);
        }
        
        updateReferredLeadCounts(leadList);
    }
    /*
@future(callout=true)
public static void sendWhatsappAPI(set<Id> leadIdSet)
{
List<Lead__c> leadList = [select id,name,Full_Primary_Contact__c,Relationship_Owner__r.Name,Personal_Email__c,Date_of_receiving_lead__c from Lead__c where id in :leadIdSet ];
String fromAddress = '917338180839';
String toAddress = '';
String sId = 'HXAP1694460718IN';
String apikey = 'A0da16b6b946124f5b3e6ef6cb7b2e630';
String param1 = 'test11';
String param2 = 'test22';
String param3 = 'test33';
String enddURL = 'https://e2ewebservice20190528111726.azurewebsites.net/api/KORAIWhatsappNotification';

HttpRequest request = new HttpRequest();
request.setEndpoint(enddURL);
request.setHeader('Content-Type','application/json');
request.setMethod('POST');
request.setTimeout(120000);

for(Lead__c ld : leadList)
{
toAddress = ld.Full_Primary_Contact__c;
param3 = ld.Relationship_Owner__r.Name;
String jsonData = '{'+
'"from": "'+fromAddress+'",'+
'"to": "'+toAddress+'",'+
'"type":"", '+
'"template_name":"esigning_pending",'+
'"params":"\\"'+ld.name+'\\",\\"'+ld.Relationship_Owner__r.Name+'\\",\\"'+ld.Personal_Email__c+'\\",\\"'+ld.Date_of_receiving_lead__c+'\\",\\"'+ld.Relationship_Owner__r.Name+'\\"",'+
'"Sid":"'+sId+'",'+
'"apikey":"'+apikey+'"'+
'}';


system.debug('request jsonData>>>'+jsonData);
request.setBody(jsonData);

Http http1 = new Http();
system.debug('request body>>>'+request.getBody());
HTTPResponse res1 = http1.send(request);
system.debug('res1 >>>'+res1 );
}  
}
*/
    
    public void beforeUpdate(List<Lead__c> leadList,Map<Id,Lead__c> trgOldMap)
    {
        Boolean updateReferenceField = false;
        Map<ID, Schema.RecordTypeInfo> recordTypeMap = Schema.SObjectType.lead__c.getRecordTypeInfosById();
        id recordTypeId = Schema.SObjectType.lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        
        for(Lead__c acc : leadList)
        {
            //  System.debug('Trigger Before update lead NAME1>>>>'+acc.Name);
            // if(acc.name != trgOldMap.get(acc.Id).name)
            //   {
            //      System.debug('Name is updated>>>>'+acc.Name);
            //      System.debug('trgOldMap.get(acc.Id).name>>>>'+trgOldMap.get(acc.Id).name);
            //  }
            
            //  if Relationship Owner Changed.  
            Lead__c oldLead = trgOldMap.get(acc.Id);
            
            if(oldLead.Relationship_Owner__c != acc.Relationship_Owner__c)
            {
                acc.Date_of_Assignment_to_IPV_RM__c = Date.Today();
            }
            
            If(acc.RecordTypeId != null && acc.Primary_Contact__c != null)
            {
                acc.Record_Type_Primary_Contact_Unique_Key__c = ''+recordTypeMap.get(acc.RecordTypeId).getDeveloperName() +'_'+acc.Primary_Contact__c;
            }
            
            if(acc.RecordTypeId == recordTypeId)
            {
                if(String.IsNotBlank(acc.Relationship_Manager_Contact__c) && acc.Relationship_Manager_Contact__c!=trgOldMap.get(acc.Id).Relationship_Manager_Contact__c)  
                {
                    updateReferenceField = true;
                }
                if(String.IsNotBlank(acc.Refer_By_Contact_No__c) && acc.Refer_By_Contact_No__c!=trgOldMap.get(acc.Id).Refer_By_Contact_No__c)  
                {
                    updateReferenceField = true;
                }
                if(String.IsNotBlank(acc.Primary_Contact__c) && acc.Primary_Contact__c!=trgOldMap.get(acc.Id).Primary_Contact__c)  
                {
                    updateReferenceField = true;
                }
                
                if(acc.Relationship_Owner__c!=trgOldMap.get(acc.Id).Relationship_Owner__c)
                {
                    acc.OwnerId = acc.Relationship_Owner__c;
                }
                
                /* Added By Sahil on 26/03/2024 */
                if(acc.Facebook_Campaign_Name__c != null && acc.Facebook_Campaign_Name__c !='')
                {
                    if(isValidCampaign(acc.Facebook_Campaign_Name__c))
                    {
                        acc.Campaign__c = acc.Facebook_Campaign_Name__c;
                    }                    
                }
                
                //  Added by Sahilparvat on 17.05.2024
                if (acc.Referred_By__c != null) {
                    acc.Is_this_referral_lead__c = true;
                } else {
                    acc.Is_this_referral_lead__c = false;
                }
                
                if(updateReferenceField)
                    break;
            }
            
            system.debug('updateReferenceField >>>>'+updateReferenceField );
            if(updateReferenceField)
                updateReferenceFieldsCommon(leadList);
            
            //  System.debug('Trigger Before update lead NAME2>>>>'+acc.Name);
            
        }
    }
    
    public void updateReferenceFieldsCommon(List<Lead__c> leadList)
    {
        Map<String,Id> relManagerNoMap = new Map<String,Id>();
        Map<String,Id> referByNoMap = new Map<String,Id>();
        Map<String,Boolean> accountLeadMap = new Map<String,Boolean>();
        
        Map<String,String> recordTypeAccMap = new Map<String,String>();
        Map<Id,String> recordTypeMap = new Map<Id,String>();
        AccountTriggerHandler accHandler = new AccountTriggerHandler();
        recordTypeMap = accHandler.getAccountLeadRecordTypeInfo();
        
        for(Lead__c acc : leadList)
        {
            if(String.IsNotBlank(acc.Relationship_Manager_Contact__c) && acc.Relationship_Owner__c==null)  
            {
                relManagerNoMap.put(acc.Relationship_Manager_Contact__c,null); 
            }
            if(String.IsNotBlank(acc.Refer_By_Contact_No__c) && acc.Referred_By__c==null)  
            {
                referByNoMap.put(acc.Refer_By_Contact_No__c,null); 
            }
            
            if(!acc.Is_Converted_From_Lead__c)
            {
                accountLeadMap.put(acc.Primary_Contact__c,False);
                
                if(recordTypeMap.containsKey(acc.recordtypeId))
                    recordTypeAccMap.put(acc.Primary_Contact__c,recordTypeMap.get(acc.recordtypeId));
                else
                    recordTypeAccMap.put(acc.Primary_Contact__c,null);
            }
        }
        
        system.debug('relManagerNoMap>>>>'+relManagerNoMap);
        system.debug('referByNoMap>>>>'+referByNoMap);
        system.debug('accountLeadMap>>>>'+accountLeadMap);
        
        
        if(accountLeadMap!=null && accountLeadMap.size()>0)
        {
            for(Account acc : [select id,Primary_Contact__c,recordtypeId from Account where Primary_Contact__c in : accountLeadMap.keyset()])
            {
                if(acc.Primary_Contact__c!=null && acc.Primary_Contact__c !='' && recordTypeAccMap.get(acc.Primary_Contact__c)==recordTypeMap.get(acc.recordtypeId))
                    accountLeadMap.put(acc.Primary_Contact__c,True);
            }        
        }
        system.debug('accountLeadMap after>>>>'+accountLeadMap);
        if(relManagerNoMap!=null && relManagerNoMap.size()>0)
        {
            for(user u : [select id,Contact_No__c from user where Contact_No__c in : relManagerNoMap.Keyset()])
            {   
                if(u.Contact_No__c !=null)
                    relManagerNoMap.put(u.Contact_No__c,u.Id);
            }
        }
        
        system.debug('relManagerNoMap11>>>>'+relManagerNoMap);
        if(referByNoMap!=null && referByNoMap.size()>0)
        {
            for(Account existAcc : [select id,Primary_Contact__c from account where Primary_Contact__c in : referByNoMap.Keyset()])
                referByNoMap.put(existAcc.Primary_Contact__c,existAcc.Id);
        }
        system.debug('referByNoMap111>>>>'+referByNoMap);
        
        for(Lead__c acc: leadList)
        {
            if(accountLeadMap.size()>0 && accountLeadMap.ContainsKey(acc.Primary_Contact__c) && accountLeadMap.get(acc.Primary_Contact__c))
            {
                acc.addError('Account is already exist for the same primary contact : '+acc.Primary_Contact__c);
            }    
            if(String.IsNotBlank(acc.Relationship_Manager_Contact__c) && acc.Relationship_Owner__c==null && relManagerNoMap.size()>0 && relManagerNoMap.get(acc.Relationship_Manager_Contact__c)!=null) 
            {
                acc.Relationship_Owner__c = relManagerNoMap.get(acc.Relationship_Manager_Contact__c);
            }
            if(String.IsNotBlank(acc.Refer_By_Contact_No__c) && acc.Referred_By__c ==null && referByNoMap.size()>0 && referByNoMap.get(acc.Refer_By_Contact_No__c)!=null)  
            {
                acc.Referred_By__c = referByNoMap.get(acc.Refer_By_Contact_No__c);
            }
            
            if(acc.Relationship_Owner__c!=null)
                acc.OwnerId = acc.Relationship_Owner__c;
            
            if(acc.Is_Converted_From_Lead__c)
                acc.Is_Converted_From_Lead__c = false;
        }  
    }
    
    // Added by ankush for Activity daily Update.16.8.23
    public void updateAccountCounts(List<Lead__c> leadList)
    {   
        Set<Id> AccountidSet = new Set<Id>();
        List<Account> accountsToUpdate = new List<Account>();
        if(leadList.size() > 0){
            for(Lead__c ld : leadList)
            {
                If(ld.Referred_By__c != null)
                {
                    AccountidSet.add(ld.Referred_By__c); 
                }
            }
            for (Id accountId : AccountidSet) 
            {
                Account acc = new Account(Id = accountId, Referred_leads__c = [SELECT COUNT() FROM Lead__c WHERE Referred_By__c != null AND Referred_By__c = :accountId]);
                accountsToUpdate.add(acc);
            }
            update accountsToUpdate;
        }
    }
    
    //Creating points transactions
    public void createPointTransaction(List<Lead__c> leadList)
    {
        List<Points_Transaction__c> points = new List<Points_Transaction__c>();
        for(Lead__c ld : leadList){
            if(ld.Referred_By__c !=  null){
                Points_Transaction__c point =  new Points_Transaction__c();
                point.Credit_To__c = ld.Referred_By__c;
                point.Debit_From__c = Label.PT_Debit_Account;
                point.Points_Alloted__c = 20;
                point.Founder_Name__c = ld.Name;
                point.Member_Name__c = ld.Referred_By__c;
                point.Manual_Startup_Name__c = ld.Startup_Name__c;
                point.Date_of_Receiving_Lead__c = ld.Date_of_Receiving_Lead__c;
                point.Point_Type__c = 'Refer a Startup';
                points.add(point);
            }                
        }
        if(!points.isEmpty()){
            system.debug('innn trigger points::'+points);
            insert points; 
        }
        
    }
    
    
    // Added by Sahilparvat for Referred Lead Count Update 14.02.2024
    public void updateReferredLeadCounts(List<Lead__c> leadList) {
        Map<Id, Integer> referralCountMap = new Map<Id, Integer>();
        //set<id> IdSetForAppLead = new set<id>();
        Id recordTypeId = Schema.SObjectType.lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        for (Lead__c lead : leadList) {
            if (lead.Referred_By_Lead__c != null) {
                referralCountMap.put(lead.Referred_By_Lead__c, Integer.valueOf(lead.Total_Referred_Account_Leads__c));
            }
            
            /*if(lead.RecordTypeId == recordTypeId && lead.Is_this_referral_lead__c == true){
IdSetForAppLead.add(lead.Id);
}*/
        }
        
        for (AggregateResult result : [SELECT Referred_By_Lead__c, COUNT(Id) counts FROM Lead__c WHERE Referred_By_Lead__c IN :referralCountMap.keySet() GROUP BY Referred_By_Lead__c]) {
            Id referredById = (Id)result.get('Referred_By_Lead__c');
            Integer referralCount = (Integer)result.get('counts');
            referralCountMap.put(referredById, referralCount);
        }
        
        List<Lead__c> leadsToUpdate = new List<Lead__c>();
        for (Id leadId : referralCountMap.keySet()) {
            leadsToUpdate.add(new Lead__c(Id = leadId, Total_Referred_Account_Leads__c = referralCountMap.get(leadId)));
        }
        
        update leadsToUpdate;
        
        /*if(IdSetForAppLead.size() > 0){
LeadRestAPIController.SendLeadDetails(IdSetForAppLead, true);}
*/  
    }
    
    // Added by Sahil on 26.03.2024 Method to check if the Campaign value is valid in the picklist
    private boolean isValidCampaign(String campagin)
    {
        List<String> validCampaigns = new List<String>();
        
        // Describe the Lead object to get the picklist values for the Campaign field
        Schema.DescribeFieldResult fieldResult = Lead__c.Campaign__c.getDescribe();
        
        List<Schema.PicklistEntry> picklistValues = fieldResult.getPicklistValues();
        
        for(Schema.PicklistEntry picklistEntry : picklistValues) {
            validCampaigns.add(picklistEntry.getValue());
        }
        
        System.debug('Camapign >>>>> ' + validCampaigns);
        
        return validCampaigns.contains(campagin);
    }
    //added by jay dabhi 5th septeber 2024 for insert and update a case record in User  Activity object for User Timeline Brd
    
   public void handleLeadActivity(List<Lead__c> leads, Map<Id, Lead__c> Oldmap) {
    List<User_Activity__c> userActivityRecords = new List<User_Activity__c>();

    // Load lead records to ensure we have the necessary fields
    leads = [SELECT Id, Name, Relationship_Manager__r.Name, Membership_Status__c, Referred_By__c, Date_of_receiving_lead__c
             FROM Lead__c
             WHERE Id IN :leads];

    // Prepare list for User IDs if Oldmap is not null (update case)
    List<String> userId = new List<String>();
    if (Oldmap != null) {
        for (Lead__c oldLead : Oldmap.values()) {
            if (oldLead.Relationship_Manager__c != null) {
                userId.add(oldLead.Relationship_Manager__c);
            }
        }
    }

    Map<Id, User> userMap = userId.isEmpty() ? new Map<Id, User>() :
                            new Map<Id, User>([SELECT Id, Name FROM User WHERE Id = :userId]);

    for (Lead__c lead : leads) {
        Lead__c oldLead = (Oldmap != null) ? Oldmap.get(lead.Id) : null; 
        Id oldRmId = (oldLead != null) ? oldLead.Relationship_Manager__c : null;
        Id newRmId = lead.Relationship_Manager__c;
 if (lead.Referred_By__c != null && (oldLead == null || oldLead.Referred_By__c != lead.Referred_By__c)) {
            String ReferredUrl = URL.getOrgDomainURL().toExternalForm() + '/' + lead.Id;
            User_Activity__c userActivity = new User_Activity__c();
            userActivity.Related_Account__c = lead.Referred_By__c;
            userActivity.Activity_Type__c = 'Referred a new lead';
            userActivity.Activity_Detail_RICH__c = 'Lead referred: ' + (ReferredUrl != '' ? '<a href="' + ReferredUrl + '" target="_blank">' + lead.Name + '</a>' : lead.Name);
            userActivity.Time_Stamp__c = lead.Date_of_receiving_lead__c;
            userActivityRecords.add(userActivity);
        }

        if (Oldmap != null && oldLead != null && oldLead.Membership_Status__c != lead.Membership_Status__c) {
            User_Activity__c userActivity = new User_Activity__c();
            userActivity.Related_Lead__c = lead.Id;
            userActivity.Activity_Type__c = 'Membership Status Change';
            userActivity.Activity_Detail_RICH__c = 'Membership status changed from ' + oldLead.Membership_Status__c + ' to ' + lead.Membership_Status__c;
            userActivity.Time_Stamp__c = System.now();
            userActivityRecords.add(userActivity);
        }

        if (Oldmap != null && oldLead != null && oldLead.Relationship_Manager__c != lead.Relationship_Manager__c) {
            String oldRMUrl = oldRmId != null ? URL.getOrgDomainURL().toExternalForm() + '/' + oldRmId : '';
            String newRMUrl = newRmId != null ? URL.getOrgDomainURL().toExternalForm() + '/' + newRmId : '';
            String oldRMName = oldRmId != null ? userMap.get(oldRmId).Name : 'null';
            String newRMName = newRmId != null ? lead.Relationship_Manager__r.Name : 'null';

            User_Activity__c userActivity = new User_Activity__c();
            userActivity.Related_Lead__c = lead.Id;
            userActivity.Activity_Type__c = 'RM Change';
            userActivity.Activity_Detail_RICH__c = 'Relationship Manager changed from ' +
                                                   (oldRMUrl != '' ? '<a href="' + oldRMUrl + '" target="_blank">' + oldRMName + '</a>' : oldRMName) +
                                                   ' to ' +
                                                   (newRMUrl != '' ? '<a href="' + newRMUrl + '" target="_blank">' + newRMName + '</a>' : newRMName);
            userActivity.Time_Stamp__c = System.now();
            userActivityRecords.add(userActivity);
        }
    }

    // Insert all user activities outside the loop
    if (!userActivityRecords.isEmpty()) {
        insert userActivityRecords;
    }
}


}