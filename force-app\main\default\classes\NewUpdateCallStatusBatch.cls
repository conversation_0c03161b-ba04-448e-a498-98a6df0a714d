public class NewUpdateCallStatusBatch implements Database.Batchable<sObject>, Database.Stateful {
    
    private Integer batchSize; 
    Id IPVrecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
    public NewUpdateCallStatusBatch(Integer batchSize) {
        this.batchSize = batchSize;
    }
    
   // Public static boolean IsupdatingAccount = false;

    public Database.QueryLocator start(Database.BatchableContext context) {
        return Database.getQueryLocator([SELECT Id, Date_of_Payment__c, L1_Call_status__c, L2_Call_status__c, L3_Call_status__c, L4_Call_status__c, L5_Call_status__c, L6_Call_status__c , L1_Call_delayed_days__c , L2_Call_delayed_days__c , L3_Call_delayed_days__c , L4_Call_delayed_days__c , L5_Call_delayed_days__c , L6_Call_delayed_days__c FROM Account WHERE Date_of_Payment__c != null AND RecordTypeId =:IPVrecordTypeId]);
    }
    
    public void execute(Database.BatchableContext context, List<Account> scope) {
       
         system.debug('scope ::'+scope);
         Map<Id, account> accountIdToPaymentDateMap = new Map<Id, account>();
         Map<id, Account> uniqueAccountsMap = new Map<id, Account> ();
         final Integer L1_DAYS = 15;
    	 final Integer L2_DAYS = 60;
    	 final Integer L3_DAYS = 120;
    	 final Integer L4_DAYS = 180;
    	 final Integer L5_DAYS = 270;
    	 final Integer L6_DAYS = 335;
        
        for (Account acc :scope ) {
			accountIdToPaymentDateMap.put(acc.Id, acc);
        }
		//List<Account> recordsToProcess = scope.subList(0, Math.min(batchSize, scope.size()));
        
        for(Account Acc :scope){
          Id accountId = acc.Id;
			Date paymentDate = accountIdToPaymentDateMap.get(accountId).Date_of_Payment__c;
			Account relatedAccount = new Account(Id = accountId);
           // uniqueId.add(accountId);
			
            Date L1DueDate = paymentDate.addDays(L1_DAYS);
            Date L2DueDate = paymentDate.addDays(L2_DAYS);
            Date L3DueDate = paymentDate.addDays(L3_DAYS);
            Date L4DueDate = paymentDate.addDays(L4_DAYS);
            Date L5DueDate = paymentDate.addDays(L5_DAYS);
            Date L6DueDate = paymentDate.addDays(L6_DAYS);
			
			//	If Not "COMPLETED" Then Check For "OVERDUE"
			if(accountIdToPaymentDateMap.get(accountId).L1_Call_status__c != 'Completed' && Date.today() > L1DueDate)
			{
				relatedAccount.L1_Call_status__c = 'Overdue';
				relatedAccount.L1_Call_delayed_days__c = L1DueDate.daysBetween(Date.today());
			}
			 if(accountIdToPaymentDateMap.get(accountId).L2_Call_status__c != 'Completed' && Date.today() > L2DueDate)
			{
				relatedAccount.L2_Call_status__c = 'Overdue';
				relatedAccount.L2_Call_delayed_days__c = L2DueDate.daysBetween(Date.today());
			}
			 if(accountIdToPaymentDateMap.get(accountId).L3_Call_status__c != 'Completed' && Date.today() > L3DueDate)
			{
				relatedAccount.L3_Call_status__c = 'Overdue';
				relatedAccount.L3_Call_delayed_days__c = L3DueDate.daysBetween(Date.today());
			}
			 if(accountIdToPaymentDateMap.get(accountId).L4_Call_status__c != 'Completed' && Date.today() > L4DueDate)
			{
				relatedAccount.L4_Call_status__c = 'Overdue';
				relatedAccount.L4_Call_delayed_days__c = L4DueDate.daysBetween(Date.today());
			}
			 if(accountIdToPaymentDateMap.get(accountId).L5_Call_status__c != 'Completed' && Date.today() > L5DueDate)
			{
				relatedAccount.L5_Call_status__c = 'Overdue';
				relatedAccount.L5_Call_delayed_days__c = L5DueDate.daysBetween(Date.today());
			}
			 if(accountIdToPaymentDateMap.get(accountId).L6_Call_status__c != 'Completed' && Date.today() > L6DueDate)
			{
				relatedAccount.L6_Call_status__c = 'Overdue';
				relatedAccount.L6_Call_delayed_days__c = L6DueDate.daysBetween(Date.today());
			}
			
			
			//	Already "OVERDUE" Then Increase DUE Days.
			if(accountIdToPaymentDateMap.get(accountId).L1_Call_status__c == 'Overdue'&& Date.today() > L1DueDate)
			{
				relatedAccount.L1_Call_delayed_days__c = L1DueDate.daysBetween(Date.today());
			}
			if(accountIdToPaymentDateMap.get(accountId).L2_Call_status__c == 'Overdue'&& Date.today() > L2DueDate)
			{
				relatedAccount.L2_Call_delayed_days__c = L2DueDate.daysBetween(Date.today());
			}
			if(accountIdToPaymentDateMap.get(accountId).L3_Call_status__c == 'Overdue'&& Date.today() > L3DueDate)
			{
				relatedAccount.L3_Call_delayed_days__c = L3DueDate.daysBetween(Date.today());
			}
			if(accountIdToPaymentDateMap.get(accountId).L4_Call_status__c == 'Overdue' && Date.today() > L4DueDate)
			{
				relatedAccount.L4_Call_delayed_days__c = L4DueDate.daysBetween(Date.today());
			}
			if(accountIdToPaymentDateMap.get(accountId).L5_Call_status__c == 'Overdue' && Date.today() > L5DueDate)
			{
				relatedAccount.L5_Call_delayed_days__c = L5DueDate.daysBetween(Date.today());
			}
			if(accountIdToPaymentDateMap.get(accountId).L6_Call_status__c == 'Overdue' && Date.today() > L6DueDate)
			{
				relatedAccount.L6_Call_delayed_days__c = L6DueDate.daysBetween(Date.today());
			}
			
			if (!uniqueAccountsMap.containsKey(accountId)) {
              	uniqueAccountsMap.put(accountId, relatedAccount);
        	}
			
        }
		 List<Account> accountsToUpdate = new List<Account>(uniqueAccountsMap.values());
		System.Debug('Account ID >>>>> '+ accountsToUpdate);
        
		// Update the Account records in bulk
        if (!accountsToUpdate.isEmpty() && accountsToUpdate.size() > 0) {
            update accountsToUpdate;
           }  
        
         //IsupdatingAccount = True;
    }

    public void finish(Database.BatchableContext context) {
      
    }
}