@RestResource(urlMapping='/AttachPDF/*')
global class CAPDFRestController
{
    @HttpGet
    global static void AttachPDFtoRecordREST()
    {
        
        RestRequest req = RestContext.request;
        id recordId = req.requestURI.substring(req.requestURI.lastIndexOf('/')+1);
        system.debug('recordID ::'+recordId);
        PageReference pdfPage = new PageReference('/apex/GeneratePDF?Id='+recordId);
        pdfPage.getParameters().put('id',recordId);
        Blob pdf = pdfPage.getContentAsPdf(); //!Test.isRunningTest() ? pdfPage.getContentAsPdf() : Blob.ValueOf('dummy text');
        Contribution_Agreement__c ca = [Select Id,Name FROM Contribution_Agreement__c WHERE Id = :recordId limit 1]; 
        //Create the email attachment
        Messaging.EmailFileAttachment efa = new Messaging.EmailFileAttachment();
        efa.setFileName(ca.Name+'.pdf');//Pleace ad pdf name from contact name project and oppty concatinate
        efa.setContentType('application/pdf');
        efa.setBody(pdf);
        
        // Create the Singal Email Message
        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
        email.setSubject('CA files');
        email.setToAddresses(new String[] { '<EMAIL>' });//add account email id
        email.setPlainTextBody( 'Please Find Attachment your financial report Attached with this email.');
        email.setFileAttachments(new Messaging.EmailFileAttachment[] {efa});
        
        // Sends the email
        Messaging.SendEmailResult [] r = Messaging.sendEmail(new Messaging.SingleEmailMessage[] {email});
        system.debug('result '+r);
        
    }
    
    // call this method from your Batch Apex
    global static void attachPdfToRecord( Id recordId, String sessionId )
    {
        System.debug('recordId==>'+recordId);
        System.debug('sessionId==>'+sessionId);
        System.debug('Iam in REST API Call method');
        String endpoint = 'https://d0l000000dxkreas--devsb1.sandbox.my.salesforce.com/services/apexrest/AttachPDF/' + recordId;
        HttpRequest req = new HttpRequest();
        req.setEndpoint(endpoint);
        req.setMethod('GET');
        req.setHeader('Authorization', 'OAuth ' + UserInfo.getSessionId());
        
        Http http = new Http();
        HttpResponse response = http.send(req);  
    }
}