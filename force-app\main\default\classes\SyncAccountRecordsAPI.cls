@RestResource(urlMapping='/getDataFromSalesforceOrg/*')
global with sharing class SyncAccountRecordsAPI {
    @HttpPost
    global static String getAccounts() {
        List<Account> accList = [SELECT Name, Membership_Status__c, Personal_Email__c, Primary_Contact__c, Primary_Country_Code__c, Membership_Validity__c, Is_Tenant__c, L1_manager__r.Name, Partner_type__c, Org_RM__r.Name,
                                  Partner_parent_account__r.Name, B2B_App_Tenant_Id__c, Sync_flag__c, Sync_error__c, B2B_User_Type__c
                                  FROM Account WHERE Sync_flag__c = false AND RecordTypeId = '0120l000001PyTPAA0' AND Id = '0010l00001Agc4ZAAR'];

        System.debug('AccRecords===>' + accList);
        
        JSONGenerator jsonGen = JSON.createGenerator(true);
        jsonGen.writeStartArray();
        
        if(accList.size() > 0 && accList != null){
			for (account acc : accList){
				// Start array element
				jsonGen.writeStartObject();
            
				jsonGen.writeStringField('tenantId_salesforceId', acc.Partner_parent_account__c != null ? acc.Partner_parent_account__c : acc.Id);
            
				if(acc.Partner_parent_account__c != null){
				jsonGen.writeFieldName('parentAc');
				jsonGen.writeStartObject();
				Account parentAcc = [SELECT Id , Name , Primary_Contact__c , Primary_Country_Code__C , Personal_Email__c , Preferred_Email__c FROM Account WHERE Id =: acc.Partner_parent_account__c LIMIT 1 ];
				
				jsonGen.writeStringField('salesforceId', parentAcc.Id);
				if(parentAcc.Name != null){
					jsonGen.writeStringField('MemberName'  , parentAcc.Name);
				}
				if(parentAcc.Primary_Contact__c != null){
				jsonGen.writeStringField('pcontact', parentAcc.Primary_Contact__c);
				}
				if(parentAcc.Primary_Country_Code__C != null){
				jsonGen.writeStringField('pcountryCode', String.valueOf(parentAcc.Primary_Country_Code__C));
				}
				if(parentAcc.Personal_Email__c != null){
				jsonGen.writeStringField('Pemail', parentAcc.Personal_Email__c);
				}
				else if(parentAcc.Preferred_Email__c != null){
				jsonGen.writeStringField('Pemail', parentAcc.Preferred_Email__c);	
				}
				jsonGen.writeEndObject();
				}
				
				if(acc.L1_manager__c != null){
				jsonGen.writeFieldName('L1Manager');
				jsonGen.writeStartObject();
				Account L1ManagerAcc = [SELECT Id , Name , Primary_Contact__c , Primary_Country_Code__C , Personal_Email__c , Preferred_Email__c FROM Account WHERE Id =: acc.L1_manager__c LIMIT 1 ];
				
				jsonGen.writeStringField('salesforceId', L1ManagerAcc.Id);
				if(L1ManagerAcc.Name != null){
				jsonGen.writeStringField('MemberName', L1ManagerAcc.Name);
				}
				if(L1ManagerAcc.Primary_Contact__c != null){
				jsonGen.writeStringField('pcontact', L1ManagerAcc.Primary_Contact__c);
				}
				if(L1ManagerAcc.Primary_Country_Code__C != null){
				jsonGen.writeStringField('pcountryCode', String.valueOf(L1ManagerAcc.Primary_Country_Code__C));				
				}
				if(L1ManagerAcc.Personal_Email__c != null){
				jsonGen.writeStringField('Pemail', L1ManagerAcc.Personal_Email__c);
				}
				else if(L1ManagerAcc.Preferred_Email__c != null){
				jsonGen.writeStringField('Pemail', L1ManagerAcc.Preferred_Email__c);				
				}
				
				jsonGen.writeEndObject();
				}
            
				if(acc.Org_RM__c != null){
				jsonGen.writeFieldName('OrgRM');
				jsonGen.writeStartObject();
				Account OrgRMacc = [SELECT Id , Name , Primary_Contact__c , Primary_Country_Code__C , Personal_Email__c , Preferred_Email__c FROM Account WHERE Id =: acc.Org_RM__c LIMIT 1 ];
				
				jsonGen.writeStringField('salesforceId', OrgRMacc.Id );
				if(OrgRMacc.Name != null){
				jsonGen.writeStringField('MemberName', OrgRMacc.Name);
				}
				if(OrgRMacc.Primary_Contact__c != null){
				jsonGen.writeStringField('pcontact', OrgRMacc.Primary_Contact__c);
				}
				if(OrgRMacc.Primary_Country_Code__C != null){
				jsonGen.writeStringField('pcountryCode', String.valueOf(OrgRMacc.Primary_Country_Code__C));				
				}
				if(OrgRMacc.Personal_Email__c != null){
				jsonGen.writeStringField('Pemail', OrgRMacc.Personal_Email__c);
				}
				else if(OrgRMacc.Preferred_Email__c != null){
				jsonGen.writeStringField('Pemail', OrgRMacc.Preferred_Email__c);				
				}
				
				jsonGen.writeEndObject();
				}
				
				if(acc.B2B_App_Tenant_Id__c != null){
				jsonGen.writeStringField('tenentId', acc.B2B_App_Tenant_Id__c);
				}
				
				jsonGen.writeStringField('partnerRM', '');
            
				if(acc.B2B_User_Type__c != null){
				jsonGen.writeStringField('b2bUserType', acc.B2B_User_Type__c);
				}
				
				if(acc.Is_Tenant__c != null){
				jsonGen.writeBooleanField('isTenant', acc.Is_Tenant__c);
				}
				
				if(acc.Name != null){
				jsonGen.writeStringField('MemberName', acc.Name);
				}
				if(acc.Primary_Contact__c != null){
				jsonGen.writeStringField('pcontact', acc.Primary_Contact__c);
				}
				if(acc.Primary_Country_Code__C != null){
				jsonGen.writeStringField('pcountryCode', String.valueOf(acc.Primary_Country_Code__C));				
				}
				if(acc.Personal_Email__c != null){
				jsonGen.writeStringField('Pemail', acc.Personal_Email__c);
				}
				jsonGen.writeEndObject();
			}
			}
            
            jsonGen.writeEndArray();
            
            String jsonData = jsonGen.getAsString();
            System.debug('Json Data ===> ' + jsonData);
			
        return jsonData;
       
    }
}