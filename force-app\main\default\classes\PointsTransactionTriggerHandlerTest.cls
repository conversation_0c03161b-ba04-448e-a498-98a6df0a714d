@isTest
public class PointsTransactionTriggerHandlerTest {

    // Helper method to create test data for Points_Transaction__c
    private static Points_Transaction__c createPointsTransaction(String pointType, Id creditToId, Id debitFromId, Decimal pointsAlloted) {
        Points_Transaction__c pt = new Points_Transaction__c(
            Point_Type__c = pointType,
            Credit_To__c = creditToId,
            Debit_From__c = debitFromId,
            Points_Alloted__c = pointsAlloted,
            Startup_Count__c = 10

        );
        insert pt;
        return pt;
    }

    // Helper method to create test data for Account
    private static Account createAccount() {
         account acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Platinum';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        acc.App_City__c = 'BANDA';
        acc.Personal_Email__c = '<EMAIL>';
        acc.Bot_Communication_Country_Code__c = null;
        acc.Bot_Communication_Number__c = '';
        insert acc;
        return acc;
    }
    
    @isTest
    public static void testBulkAfter() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();

        // Create Points_Transaction__c records
        Points_Transaction__c pt1 = createPointsTransaction('Help as SME during DD and present findings on the Investor Call', account1.Id, account2.Id, 10);
        
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();

        // Query accounts to check if points have been updated
        Account updatedAccount1 = [SELECT Id, Total_Refer_a_Startup_Points__c, Total_Investor_Call_Points__c FROM Account WHERE Id = :account1.Id];
        Account updatedAccount2 = [SELECT Id, Total_Refer_a_Startup_Points__c, Total_Investor_Call_Points__c FROM Account WHERE Id = :account2.Id];

        // Assert that points have been correctly updated
        //System.assertEquals(updatedAccount1.Total_Refer_a_Startup_Points__c, 90, 'Account 1 points for "Refer a Startup" should be updated.');
        //System.assertEquals(updatedAccount2.Total_Investor_Call_Points__c, 30, 'Account 2 points for "Investor Call" should be updated.');

    }

    @isTest
    public static void testBulkAfter1() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();

        // Create Points_Transaction__c records
       // Points_Transaction__c pt1 = createPointsTransaction('Help as SME during DD and present findings on the Investor Call', account1.Id, account2.Id, 10);
        Points_Transaction__c pt2 = createPointsTransaction('Investor Call', account2.Id, account1.Id, 20);
        
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    
    @isTest
    public static void testBulkAfter2() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('Worked as a Lead Member', account2.Id, account1.Id, 20);       
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    
    @isTest
    public static void testBulkAfter3() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('Form Complete', account2.Id, account1.Id, 20);       
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter4() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('Help Build Communities', account2.Id, account1.Id, 20);       
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter5() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('External Event', account2.Id, account1.Id, 20);   
        Points_Transaction__c pt4 = createPointsTransaction('External Event', account1.Id, account2.Id, 20);   
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter6() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('Business Connect', account2.Id, account1.Id, 20);       
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter7() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('Adding Angel Investor on LinkedIn', account2.Id, account1.Id, 20);       
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter8() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('Help as SME during DD', account2.Id, account1.Id, 20);       
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter10() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('LetsGrow Goal Achieved', account2.Id, account1.Id, 20);    
        Points_Transaction__c pt4 = createPointsTransaction('Points Redeemed For Renewal', account1.Id, account2.Id, 20);    
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        system.debug('****'+ptList);
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter12() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();    
        Points_Transaction__c pt4 = createPointsTransaction('Points Redeemed For Renewal', account1.Id, account2.Id, 20);    
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        system.debug('****'+ptList);
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter11() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('Invest 5L and above', account2.Id, account1.Id, 20);   
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        system.debug('****'+ptList);
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter13() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('Worked as a Co-lead', account2.Id, account1.Id, 20);   
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        system.debug('****'+ptList);
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter14() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('Startup Voting', account2.Id, account1.Id, 20);   
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        system.debug('****'+ptList);
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter15() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('LetsGrow SIP Call', account2.Id, account1.Id, 20);   
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        system.debug('****'+ptList);
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    @isTest
    public static void testBulkAfter16() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();
        Points_Transaction__c pt3 = createPointsTransaction('LetsGrow Review Deck', account2.Id, account1.Id, 20);   
        // Call the bulkAfter method to simulate the trigger
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c];
        system.debug('****'+ptList);
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();
    }
    
    @isTest
    public static void testBeforeDelete() {
        // Create test accounts
        Account account1 = createAccount();
        Account account2 = createAccount();

        // Create Points_Transaction__c records with Point_Type__c = 'Backed Out'
        Points_Transaction__c pt1 = createPointsTransaction('Backed Out', account1.Id, account2.Id, 50);
        Points_Transaction__c pt2 = createPointsTransaction('Backed Out', account2.Id, account1.Id, 50);

        // Simulate delete of Points_Transaction__c records
        Test.startTest();
        delete [SELECT Id FROM Points_Transaction__c WHERE Id IN :new Set<Id>{pt1.Id, pt2.Id}];
        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c FROM Points_Transaction__c WHERE Id IN :new Set<Id>{pt1.Id, pt2.Id}];
        PointsTransactionTriggerHandler.beforeDelete(ptList);
        Test.stopTest();

        // Query accounts to check if points have been updated
        Account updatedAccount1 = [SELECT Id, Total_Backed_Out_Points__c FROM Account WHERE Id = :account1.Id];
        Account updatedAccount2 = [SELECT Id, Total_Backed_Out_Points__c FROM Account WHERE Id = :account2.Id];

        // Assert that Total_Backed_Out_Points__c has been correctly updated (e.g., incremented by 200)
       // System.assertEquals(updatedAccount1.Total_Backed_Out_Points__c, 200, 'Account 1 backed out points should be updated.');
       // System.assertEquals(updatedAccount2.Total_Backed_Out_Points__c, 200, 'Account 2 backed out points should be updated.');
    }

    @isTest
    public static void testNoUpdates() {
        // Create test account without any Points_Transaction__c records
        Account account1 = createAccount();

        // Call bulkAfter method without any points transactions
        Test.startTest();
        List<Points_Transaction__c> ptList = [SELECT Id FROM Points_Transaction__c WHERE Id = :new Set<Id>{ }];
        PointsTransactionTriggerHandler.bulkAfter(ptList);
        Test.stopTest();


}

    @isTest
    public static void testEdgeCase() {
        // Test edge case with no accounts or points transactions
        Test.startTest();
        PointsTransactionTriggerHandler.bulkAfter(new List<Points_Transaction__c>());
        Test.stopTest();
    }

    @isTest
    public static void deletedPointTransactionScenario(){
        
        /*
        
        // Create test accounts
        Account account1 = new Account();
        account1.Name = 'testing1';
        account1.ShippingStreet       = '1 Main St.';
        account1.ShippingState        = 'VA';
        account1.ShippingPostalCode   = '12345';
        account1.ShippingCountry      = 'USA';
        account1.ShippingCity         = 'Anytown';
        account1.Description          = 'This is a test account';
        account1.BillingStreet        = '1 Main St.';
        account1.BillingState         = 'VA';
        account1.BillingPostalCode    = '12345';
        account1.BillingCountry       = 'USA';
        account1.BillingCity          = 'Anytown';
        account1.AnnualRevenue        = 10000;
        account1.ParentId = null;
        account1.Official_Email__c = '<EMAIL>';
        account1.Title__c= 'Mr';
        account1.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        account1.Membership_Validity__c= date.newInstance(2020, 9, 15);
        account1.Primary_Country_Code__c= 91;
        account1.Lead_Source__c= 'Others';
        account1.Preferred_Email__c= 'Personal';
        account1.Personal_Email__c = '<EMAIL>';
        account1.Secondary_Contact__c ='*********';
        account1.Official_Email__c ='<EMAIL>';
      	account1.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        Integer len = 10;
        String str = string.valueof(Math.abs(Crypto.getRandomLong()));
        String randomNumber =  str.substring(0, len);
        account1.Primary_Contact__c= ''+randomNumber ;
        account1.Primary_Group_Name__c = 'Trial 10';
        account1.Membership_Status__c = 'Platinum';
        account1.Relationship_Manager__c = UserInfo.getUserId();
        account1.Are_They_Part_of_Other_Platform__c = 'Yes';
        account1.App_signup_date__c = Date.Today();
        account1.Referred_leads__c = 0;
        account1.Total_Number_of_Founder_Calls__c = 4;
        account1.Total_Number_of_Investor_Calls__c = 5;
        account1.Total_Numbe_of_Offline_Online_event__c = 3;
        account1.College_Tier__c = 'Tier 1';
        account1.Designation_Band__c = 'Band 1';
        account1.Bot_Input__c = 'Interacted -Chat with RM';
        account1.lead_Source__c = 'Referral'; 
        insert account1;
        
        
        Account account2 = new Account();
        account2.Name = 'testing2';
        account2.ShippingStreet       = '1 Main St.';
        account2.ShippingState        = 'VA';
        account2.ShippingPostalCode   = '12345';
        account2.ShippingCountry      = 'USA';
        account2.ShippingCity         = 'Anytown';
        account2.Description          = 'This is a test account';
        account2.BillingStreet        = '1 Main St.';
        account2.BillingState         = 'VA';
        account2.BillingPostalCode    = '12345';
        account2.BillingCountry       = 'USA';
        account2.BillingCity          = 'Anytown';
        account2.AnnualRevenue        = 10000;
        account2.ParentId = null;
        account2.Official_Email__c = '<EMAIL>';
        account2.Title__c= 'Mr';
        account2.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        account2.Membership_Validity__c= date.newInstance(2020, 9, 15);
        account2.Primary_Country_Code__c= 91;
        account2.Lead_Source__c= 'Others';
        account2.Preferred_Email__c= 'Personal';
        account2.Personal_Email__c = '<EMAIL>';
        account2.Secondary_Contact__c ='*********';
        account2.Official_Email__c ='<EMAIL>';
        account2.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        account2.Primary_Contact__c= ''+randomNumber ;
        account2.Primary_Group_Name__c = 'Trial 10';
        account2.Membership_Status__c = 'Platinum';
        account2.Relationship_Manager__c = UserInfo.getUserId();
        account2.Are_They_Part_of_Other_Platform__c = 'No';
        account2.App_signup_date__c = null;
        account2.Referred_leads__c = null;
        account2.Referred_Accounts__c = null;
        account2.Total_Number_of_Founder_Calls__c = 0;
        account2.Total_Number_of_Investor_Calls__c = 0;
        account2.ParentId = account1.id;
        account2.Total_Numbe_of_Offline_Online_event__c = 9;
        account2.College_Tier__c = 'Tier 2';
        account2.Designation_Band__c = 'Band 2';
        account2.Bot_Input__c = 'Interacted -Chat with RM';
        account2.lead_Source__c = 'Linkedin';
        insert account2;

        


        // Create Points_Transaction__c records with Point_Type__c = 'Backed Out'
        Points_Transaction__c pt1 = createPointsTransaction('Refer a Startup', account1.Id, account2.Id, 50);
        Points_Transaction__c pt2 = createPointsTransaction('Investor Call', account1.Id, account2.Id, 50);
        Points_Transaction__c pt4 = createPointsTransaction('Form Complete', account1.Id, account2.Id, 50);
        Points_Transaction__c pt5 = createPointsTransaction('Referal Conversions', account1.Id, account2.Id, 50);
        Points_Transaction__c pt6 = createPointsTransaction('Renew Membership', account1.Id, account2.Id, 50);
        Points_Transaction__c pt7 = createPointsTransaction('Help Build Communities', account1.Id, account2.Id, 50);
        Points_Transaction__c pt8 = createPointsTransaction('External Event', account1.Id, account2.Id, 50);
        Points_Transaction__c pt9 = createPointsTransaction('Help as SME', account1.Id, account2.Id, 50);
        Points_Transaction__c pt10 = createPointsTransaction('Worked as a Lead Member', account1.Id, account2.Id, 50);
        Points_Transaction__c pt11 = createPointsTransaction('Worked as a Co-lead', account1.Id, account2.Id, 50);
        Points_Transaction__c pt12 = createPointsTransaction('Write a thought leadership article to be published on IPV website', account1.Id, account2.Id, 50);
        Points_Transaction__c pt13 = createPointsTransaction('Startup Voting', account2.Id, account1.Id, 50);
        Points_Transaction__c pt14 = createPointsTransaction('LetsGrow SIP Call', account2.Id, account1.Id, 50);
        Points_Transaction__c pt15 = createPointsTransaction('LetsGrow Review Deck', account2.Id, account1.Id, 50);
        Points_Transaction__c pt16 = createPointsTransaction('LetsGrow Goal Achieved', account2.Id, account1.Id, 50);
        Points_Transaction__c pt17 = createPointsTransaction('LetsGrow Fund Raised', account2.Id, account1.Id, 50);
        Points_Transaction__c pt18 = createPointsTransaction('LetsGrow Business Connected', account2.Id, account1.Id, 50);
        Points_Transaction__c pt19 = createPointsTransaction('LetsGrow Complete', account2.Id, account1.Id, 50);
        Points_Transaction__c pt20 = createPointsTransaction('Invest 5L and above', account2.Id, account1.Id, 50);
        Points_Transaction__c pt21 = createPointsTransaction('Bring a friend/family member along to IPV Events', account2.Id, account1.Id, 50);
        Points_Transaction__c pt22 = createPointsTransaction('Achieve Goals for LetsGrow program for a startup', account2.Id, account1.Id, 50);
        Points_Transaction__c pt23 = createPointsTransaction('Business Connect', account2.Id, account1.Id, 50);
        Points_Transaction__c pt24 = createPointsTransaction('Points Redeemed For Renewal', account2.Id, account1.Id, 50);
        Points_Transaction__c pt25 = createPointsTransaction('Points redeemed for investment fee', account2.Id, account1.Id, 50);
        Points_Transaction__c pt26 = createPointsTransaction('Adding Angel Investor on LinkedIn', account2.Id, account1.Id, 50);
        Points_Transaction__c pt27 = createPointsTransaction('Investor Recording Video Testimonial', account2.Id, account1.Id, 50);
        Points_Transaction__c pt28 = createPointsTransaction('Help as SME during DD', account2.Id, account1.Id, 50);
        Points_Transaction__c pt29 = createPointsTransaction('Help as SME during DD and present findings on the Investor Call', account2.Id, account1.Id, 50);
        

        Test.startTest();

        List<Points_Transaction__c> ptList = [SELECT Id, Point_Type__c, Credit_To__c, Debit_From__c, Points_Alloted__c FROM Points_Transaction__c WHERE Id IN :new Set<Id>{pt1.Id, pt2.Id, pt4.Id, pt5.Id, pt6.Id, pt7.Id, pt8.Id, pt9.Id, pt10.Id, pt11.Id, pt12.Id, pt13.Id, pt14.Id, pt15.Id, pt16.Id, pt17.Id, pt18.Id, pt19.Id, pt20.Id, pt21.Id, pt22.Id, pt23.Id, pt24.Id, pt25.Id, pt26.Id, pt27.Id,pt28.Id ,pt29.id}];
        System.debug('The Size of The BeforeDelete List Is : >>> ' + ptList.size());
        System.debug(ptList[0].Points_Alloted__c);
        PointsTransactionTriggerHandler.beforeDelete(ptList);
        Test.stopTest();
        */
    }
}