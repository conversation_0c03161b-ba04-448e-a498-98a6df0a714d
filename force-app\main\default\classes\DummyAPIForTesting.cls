public class DummyAPIForTesting {

    public static void callingExternalAPI()
    {
        List<Account> accountList = [SELECT Id From Account LIMIT 100];
        List<Lead__c> leadList = [SELECT Id From Lead__c LIMIT 100];

        List<Id> idsToSend = new List<Id>();

        for(Account account : accountList)
        {
            idsToSend.add(account.Id);
        }

        for(Lead__c lead : leadList)
        {
            idsToSend.add(lead.Id);
        }

        System.debug('The Number of Ids : ' + idsToSend.size());

        String startDate = String.valueOf(Date.Today());
        String endDate = String.valueOf(Date.Today().addDays(-1));

        System.debug('Start Date : ' + startDate);
        System.debug('End Date : ' + endDate);

        if (!idsToSend.isEmpty()) 
        {
            DummyExternalAPISync.callExternalApi(idsToSend ,startDate , endDate);
        } 
        else {
            System.debug('Nothing Happened! :(');
        }
    }
}