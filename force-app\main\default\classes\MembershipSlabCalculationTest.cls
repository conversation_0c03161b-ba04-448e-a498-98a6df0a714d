/*******************************************************************************************************************

Test Class For MembershipSlabCalculationBatch & MembershipSlabCalculationScheduable & MembershipSlabEmailNotification

********************************************************************************************************************/

@isTest
public class MembershipSlabCalculationTest {

    @isTest
    public static void membershipSlabCalculationTest()
    {
        List<Account> accountList = new List<Account>();
        
        Account account1 = TestFactory.createAccount();
        account1.Membership_Status__c = 'Complimentary';
        account1.Date_of_Addition__c = Date.Today();
        account1.Date_of_Payment__c = Date.Today();
        account1.Total_Refer_a_Startup_Points__c = 480;
        accountList.add(account1);
        
        Account account2 = TestFactory.createAccount();
        account2.Membership_Status__c = 'Complimentary';
        account2.Date_of_Addition__c = Date.Today();
        account2.Date_of_Payment__c = Date.Today();
        account2.Total_Refer_a_Startup_Points__c = 600;
        accountList.add(account2);
        
        Account account3 = TestFactory.createAccount();
        account3.Membership_Status__c = 'Complimentary';  
        account3.Date_of_Addition__c = Date.Today();
        account3.Date_of_Payment__c = Date.Today();
        account3.Total_Refer_a_Startup_Points__c = 999;
        accountList.add(account3);
        
        Account account4 = TestFactory.createAccount();
        account4.Membership_Status__c = 'Complimentary';
        account4.Date_of_Addition__c = Date.Today();
        account4.Date_of_Payment__c = Date.Today();
        account4.Total_Refer_a_Startup_Points__c = 1999;
        accountList.add(account4);
        
        Account account5 = TestFactory.createAccount();
        account5.Membership_Status__c = 'Complimentary';
        account5.Date_of_Addition__c = Date.Today();
        account5.Date_of_Payment__c = Date.Today();
        account5.Total_Refer_a_Startup_Points__c = 0;
        accountList.add(account5);
        
        Account account6 = TestFactory.createAccount();        
        account6.Membership_Status__c = 'Complimentary';
        account6.Date_of_Addition__c = Date.Today();
        account6.Date_of_Payment__c = Date.Today();
        account6.Total_Refer_a_Startup_Points__c = 480;
        account6.Membership_Slab__c = 'Gold';
        account6.Date_of_Slab_Updation__c = Date.Today().addYears(-3);
        account6.Membership_Slab_Validity_Upto__c = Date.Today().addYears(-2);
        accountList.add(account6);
		
        Account account7 = TestFactory.createAccount();        
        account7.Membership_Status__c = 'Complimentary';
        account7.Date_of_Addition__c = Date.Today();
        account7.Date_of_Payment__c = Date.Today();
        account7.Total_Refer_a_Startup_Points__c = 480;
        account7.Membership_Slab__c = 'Silver';
        account7.Date_of_Slab_Updation__c = Date.Today().addYears(-3);
        account7.Membership_Slab_Validity_Upto__c = Date.Today().addYears(-2);
        accountList.add(account7);
        
        Account account8 = TestFactory.createAccount();        
        account8.Membership_Status__c = 'Complimentary';
        account8.Date_of_Addition__c = Date.Today();
        account8.Date_of_Payment__c = Date.Today();
        account8.Total_Refer_a_Startup_Points__c = 10480;
        account8.Membership_Slab__c = 'Gold';
        account8.Date_of_Slab_Updation__c = Date.Today().addYears(-3);
        account8.Membership_Slab_Validity_Upto__c = Date.Today().addYears(-2);
        accountList.add(account8);
        
        Account account9 = TestFactory.createAccount();        
        account9.Membership_Status__c = 'Complimentary';
        account9.Date_of_Addition__c = Date.Today();
        account9.Date_of_Payment__c = Date.Today();
        account9.Total_Refer_a_Startup_Points__c = 580;
        account9.Membership_Slab__c = 'Silver';
        account9.Date_of_Slab_Updation__c = Date.Today().addYears(-3);
        account9.Membership_Slab_Validity_Upto__c = Date.Today().addYears(-2);
        accountList.add(account9);

        Account account10 = TestFactory.createAccount();        
        account10.Membership_Status__c = 'Complimentary';
        account10.Date_of_Addition__c = Date.Today();
        account10.Date_of_Payment__c = Date.Today();
        account10.Total_Refer_a_Startup_Points__c = 10480;
        account10.Membership_Slab__c = 'Silver';
        account10.Date_of_Slab_Updation__c = Date.Today().addYears(-3);
        account10.Membership_Slab_Validity_Upto__c = Date.Today().addDays(10);
        accountList.add(account10);
        
        insert accountList;
    	
        Startup__c startup1 = new Startup__c();
        startup1.Public_Name__c = 'Demo Startup1';
        startup1.Legal_Name__c = 'Dummy Startup1';
        insert startup1;
        
        Startup_Round__c startupRound1 = new Startup_Round__c();
        startupRound1.Name = 'Startup Round 1';
        startupRound1.Doi_Percent_Fee__c = 10;
        startupRound1.Doi_Percent_Equity__c = 10;
        startupRound1.Pre_Money_Valuation__c = 100000;
        startupRound1.Lead_Member__c = account1.Id;
        startupRound1.Lead_Analyst__c = account2.Id;
        startupRound1.Date_of_sending_out_call_for_money__c = Date.Today().addDays(-15);
        startupRound1.Date_Of_Founders_Call__c = Date.Today();	
        startupRound1.Date_of_Investor_Call__c = Date.Today();
        startupRound1.PLT_CFM_Yet_to_Go_Amount__c = 100;
        startupRound1.Startup__c = startup1.Id;
        
        insert startupRound1;
        
        List<Contact> investorList = new List<contact>(); 
        Contact investor1 =new Contact();
        investor1.LastName= 'Investor 1'; 
        investor1.AccountId=account1.Id;
        investor1.Investor_s_PAN__c='**********';
        investorList.add(investor1);
        
        Contact investor2 =new Contact();
        investor2.LastName= 'Investor 2'; 
        investor2.AccountId=account2.Id;
        investor2.Investor_s_PAN__c='**********';
        investorList.add(investor2);
        
        Contact investor3 =new Contact();
        investor3.LastName= 'Investor 3'; 
        investor3.AccountId=account3.Id;
        investor3.Investor_s_PAN__c='**********';
        investorList.add(investor3);
        
        Contact investor4 =new Contact();
        investor4.LastName= 'Investor 4'; 
        investor4.AccountId=account4.Id;
        investor4.Investor_s_PAN__c='**********';
        investorList.add(investor4);
                         
        Contact investor5 =new Contact();
        investor5.LastName= 'Investor 5'; 
        investor5.AccountId=account5.Id;
        investor5.Investor_s_PAN__c='**********';
        investorList.add(investor5);  
        
        insert investorList;
        
        
		 List<Investment__c> investmentList = new List<Investment__c>();
        
       
        Investment__c investment1 = TestFactory.createInvestment(account1.Id);
        investment1.Account__c =  account1.Id;
        investment1.Investment_Amount_Due__c = 1000;
        investment1.Buyer_Name_Inv__c = investor1.Id;
        investment1.Startup_Round__c = startupRound1.Id;
        investment1.Investment_Amount__c = *********;
        investment1.Investor_s_PAN__c = '**********';
        investmentList.add(investment1);
        
        Investment__c investment2 = TestFactory.createInvestment(account2.Id);
        investment2.Account__c =  account2.Id;
        investment2.Startup_Round__c = startupRound1.Id;
        investment2.Buyer_Name_Inv__c = investor2.Id;
        investment2.Investment_Amount_Due__c = 1000;
        investment2.Investor_s_PAN__c = '**********';
        investment2.Investment_Amount__c = 10000;
        investmentList.add(investment2);
        
        Investment__c investment3 = TestFactory.createInvestment(account3.Id);
        investment3.Account__c =  account3.Id;
        investment3.Investment_Amount_Due__c = 1000;
        investment3.Startup_Round__c = startupRound1.Id;
        investment3.Buyer_Name_Inv__c = investor3.Id;
        investment3.Investment_Amount__c = 1200000;
        investment3.Investor_s_PAN__c = '**********';
        investmentList.add(investment3);
        
        Investment__c investment4 = TestFactory.createInvestment(account4.Id);
        investment4.Account__c =  account4.Id;
        investment4.Startup_Round__c = startupRound1.Id;
        investment4.Buyer_Name_Inv__c = investor4.Id;
        investment4.Investment_Amount_Due__c = 1000;
        investment4.Investment_Amount__c = 500;
        investment4.Investor_s_PAN__c = '**********';
        investmentList.add(investment4);
        
        Investment__c investment5 = TestFactory.createInvestment(account5.Id);
        investment5.Account__c = account5.Id;
        investment5.Investment_Amount_Due__c = 1000;
        investment5.Startup_Round__c = startupRound1.Id;
        investment5.Buyer_Name_Inv__c = investor5.Id;
        investment5.Investment_Amount__c = 999;
        investment5.Investor_s_PAN__c = '**********';
        investmentList.add(investment5);
        
        insert investmentList;
        
        Test.startTest();
        MembershipSlabCalculationScheduable m = new MembershipSlabCalculationScheduable();
        m.execute(null);
        Test.stopTest();    
    }
    
     @isTest
    public static void membershipSlabEmailNotificationTest()
    {
        List<Account> accountList = new List<Account>();
        
        Account account1 = TestFactory.createAccount();
        account1.Membership_Slab__c = 'Gold';
        account1.Date_of_Slab_Updation__c = Date.Today();
        account1.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        //account1.Relationship_Manager__c = UserInfo.getUserId();
        accountList.add(account1);
        
        Account account2 = TestFactory.createAccount();
        account2.Membership_Slab__c = 'Silver';
        account2.Date_of_Slab_Updation__c = Date.Today();
        account2.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        accountList.add(account2);
        
        Account account3 = TestFactory.createAccount();
        account3.Membership_Slab__c = 'Bronze';
        account3.Date_of_Slab_Updation__c = Date.Today();
        account3.Membership_Slab_Validity_Upto__c = null;
        accountList.add(account3);
        
        insert accountList;
        
        account1.Membership_Slab__c = 'Silver';
        account2.Membership_Slab__c = 'Bronze';
        account2.Membership_Slab_Validity_Upto__c = null;
        account3.Membership_Slab__c = 'Gold';
        account3.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        
        List<Account> accountsToUpdate = new List<Account>();
        accountsToUpdate.add(account1);
        accountsToUpdate.add(account2);
        accountsToUpdate.add(account3);
        
        update accountsToUpdate;
        
        MembershipSlabEmailNotification m = new MembershipSlabEmailNotification();
        m.execute(null);
    }
}