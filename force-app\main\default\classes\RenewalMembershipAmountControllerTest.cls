@isTest
public class RenewalMembershipAmountControllerTest {

    @isTest
    public static void renewalMembershipAmountTest()
    {
        List<Account> accountList = listOfAccounts();
        
        System.debug('Account Id 1 >>>> ' + accountList[0].Id);
        System.debug('Account Id 2 >>>> ' + accountList[1].Id);
        System.debug('Account Id 3 >>>> ' + accountList[2].Id);
        Test.startTest();
        RenewalMembershipAmountController.renewalMembershipAmount(accountList[0].Id);
        RenewalMembershipAmountController.renewalMembershipAmount(accountList[1].Id);
        RenewalMembershipAmountController.renewalMembershipAmount(accountList[2].Id);
        //System.debug('Result for Account: ' + account.Id + ' - ');
        Test.stopTest();

        /*Account account1 = TestFactory.createAccount();
        account1.Membership_Slab__c = 'Bronze';
        account1.Date_of_Slab_Updation__c = Date.Today();
        account1.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        account1.Additional_Discount__c = 5;
        // account1.Total_Investor_Call_Points__c = 0;

        Account account2 = TestFactory.createAccount();
        account2.Membership_Slab__c = 'Gold';
        account2.Date_of_Slab_Updation__c = Date.Today();
        account2.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        account2.Additional_Discount__c = 10;
        account2.Total_Investor_Call_Points__c = 100;

        Account account3 = TestFactory.createAccount();
        account3.Membership_Slab__c = 'Silver';
        account3.Date_of_Slab_Updation__c = Date.Today();
        account3.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        account3.Additional_Discount__c = 1;
        account3.Total_Investor_Call_Points__c = 5000;

        accountList.add(account1);
        accountList.add(account2);
        accountList.add(account3);

        insert accountList;

        Test.startTest();
        RenewalMembershipAmountController.renewalMembershipAmount(accountList[0].Id);
        RenewalMembershipAmountController.renewalMembershipAmount(accountList[1].Id);
        RenewalMembershipAmountController.renewalMembershipAmount(accountList[2].Id);
        Test.stopTest();
        */
    }

    public static List<Account> listOfAccounts()
    {


        List<Account> accountList = new List<Account>();
        Account acc1 = new Account();
        Account acc2 = new Account();
        Account acc3 = new Account();

        acc1.Name = 'testing1';
        acc1.ShippingStreet       = '1 Main St.';
        acc1.ShippingState        = 'VA';
        acc1.ShippingPostalCode   = '12345';
        acc1.ShippingCountry      = 'USA';
        acc1.ShippingCity         = 'Anytown';
        acc1.Description          = 'This is a test account';
        acc1.BillingStreet        = '1 Main St.';
        acc1.BillingState         = 'VA';
        acc1.BillingPostalCode    = '12345';
        acc1.BillingCountry       = 'USA';
        acc1.BillingCity          = 'Anytown';
        acc1.AnnualRevenue        = 10000;
        acc1.ParentId = null;
        acc1.Official_Email__c = '<EMAIL>';
        acc1.Title__c= 'Mr';
        acc1.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc1.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc1.Primary_Country_Code__c= 91;
        acc1.Lead_Source__c= 'Others';
        acc1.Preferred_Email__c= 'Personal';
        acc1.Personal_Email__c = '<EMAIL>';
        acc1.Secondary_Contact__c ='*********';
        acc1.Official_Email__c ='<EMAIL>';
      	acc1.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        Integer len = 10;
        String str = string.valueof(Math.abs(Crypto.getRandomLong()));
        String randomNumber =  str.substring(0, len);
        acc1.Primary_Contact__c= ''+randomNumber ;
        acc1.Primary_Group_Name__c = 'Trial 10';
        acc1.Membership_Status__c = 'Platinum';
        acc1.Relationship_Manager__c = UserInfo.getUserId();
        acc1.Are_They_Part_of_Other_Platform__c = 'Yes';
        acc1.App_signup_date__c = Date.Today();
        acc1.Referred_leads__c = 0;
        acc1.Total_Number_of_Founder_Calls__c = 4;
        acc1.Total_Number_of_Investor_Calls__c = 5;
        acc1.Total_Numbe_of_Offline_Online_event__c = 3;
        acc1.College_Tier__c = 'Tier 1';
        acc1.Designation_Band__c = 'Band 1';
        acc1.Bot_Input__c = 'Interacted -Chat with RM';
        acc1.lead_Source__c = 'Referral'; 
        acc1.Membership_Slab__c = 'Bronze';
        acc1.Date_of_Slab_Updation__c = Date.Today();
        acc1.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        acc1.Additional_Discount__c = 5;
        acc1.Date_of_Payment__c = Date.newInstance(2023, 11, 10);
        accountList.add(acc1);


        acc2.Name = 'testing2';
        acc2.ShippingStreet       = '1 Main St.';
        acc2.ShippingState        = 'VA';
        acc2.ShippingPostalCode   = '12345';
        acc2.ShippingCountry      = 'USA';
        acc2.ShippingCity         = 'Anytown';
        acc2.Description          = 'This is a test account';
        acc2.BillingStreet        = '1 Main St.';
        acc2.BillingState         = 'VA';
        acc2.BillingPostalCode    = '12345';
        acc2.BillingCountry       = 'USA';
        acc2.BillingCity          = 'Anytown';
        acc2.AnnualRevenue        = 10000;
        acc2.ParentId = null;
        acc2.Official_Email__c = '<EMAIL>';
        acc2.Title__c= 'Mr';
        acc2.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc2.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc2.Primary_Country_Code__c= 91;
        acc2.Lead_Source__c= 'Others';
        acc2.Preferred_Email__c= 'Personal';
        acc2.Personal_Email__c = '<EMAIL>';
        acc2.Secondary_Contact__c ='*********';
        acc2.Official_Email__c ='<EMAIL>';
      	acc2.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc2.Primary_Contact__c= ''+randomNumber ;
        acc2.Primary_Group_Name__c = 'Trial 10';
        acc2.Membership_Status__c = 'Platinum';
        acc2.Relationship_Manager__c = UserInfo.getUserId();
        acc2.Are_They_Part_of_Other_Platform__c = 'No';
        acc2.App_signup_date__c = null;
        acc2.Referred_leads__c = null;
        acc2.Referred_Accounts__c = null;
        acc2.Total_Number_of_Founder_Calls__c = 0;
        acc2.Total_Number_of_Investor_Calls__c = 0;
        acc2.ParentId = acc2.id;
        acc2.Total_Numbe_of_Offline_Online_event__c = 9;
        acc2.College_Tier__c = 'Tier 2';
        acc2.Designation_Band__c = 'Band 2';
        acc2.Bot_Input__c = 'Interacted -Chat with RM';
        acc2.lead_Source__c = 'Linkedin';
        acc2.Membership_Slab__c = 'Gold';
        acc2.Date_of_Slab_Updation__c = Date.Today();
        acc2.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        acc2.Additional_Discount__c = 10;
        acc2.Total_Investor_Call_Points__c = 100;
        acc1.Date_of_Payment__c = Date.newInstance(2023, 11, 10);
        accountList.add(acc2);

        acc3.Name = 'testing3';
        acc3.ShippingStreet       = '1 Main St.';
        acc3.ShippingState        = 'VA';
        acc3.ShippingPostalCode   = '12345';
        acc3.ShippingCountry      = 'USA';
        acc3.ShippingCity         = 'Anytown';
        acc3.Description          = 'This is a test account';
        acc3.BillingStreet        = '1 Main St.';
        acc3.BillingState         = 'VA';
        acc3.BillingPostalCode    = '12345';
        acc3.BillingCountry       = 'USA';
        acc3.BillingCity          = 'Anytown';
        acc3.AnnualRevenue        = 10000;
        acc3.ParentId = null;
        acc3.Official_Email__c = '<EMAIL>';
        acc3.Title__c= 'Mr';
        acc3.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc3.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc3.Primary_Country_Code__c= 91;
        acc3.Lead_Source__c= 'Others';
        acc3.Preferred_Email__c= 'Personal';
        acc3.Personal_Email__c = '<EMAIL>';
        acc3.Secondary_Contact__c ='*********';
        acc3.Official_Email__c ='<EMAIL>';
      	acc3.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc3.Primary_Contact__c= ''+randomNumber ;
        acc3.Primary_Group_Name__c = 'Trial 10';
        acc3.Membership_Status__c = 'Platinum';
        acc3.Relationship_Manager__c = UserInfo.getUserId();
        acc3.Are_They_Part_of_Other_Platform__c = null;
        acc3.App_signup_date__c = null;
        acc3.Referred_leads__c = 1;
        acc3.Total_Number_of_Founder_Calls__c = 30;
        acc3.ParentId = acc3.id;
        acc3.Total_Number_of_Investor_Calls__c = 20;
        acc3.Total_Numbe_of_Offline_Online_event__c = 0;
        acc3.College_Tier__c = 'Tier 3';
        acc3.Designation_Band__c = 'Band 3';
        acc3.Bot_Input__c = null;
        acc3.Bot_Journey_Stage__c = null;
        acc3.lead_Source__c = 'Facebook';
        acc3.Membership_Slab__c = 'Silver';
        acc3.Date_of_Slab_Updation__c = Date.Today();
        acc3.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        acc3.Additional_Discount__c = 1;
        acc3.Total_Investor_Call_Points__c = 5000;
        acc1.Date_of_Payment__c = Date.newInstance(2023, 11, 10);
        accountList.add(acc3);

        insert accountList;

        return accountList;
    }  
}