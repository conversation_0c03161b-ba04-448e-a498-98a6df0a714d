@IsTest
public class AppFutureCreateCaseAPIMock implements HttpCalloutMock {
    
    public static HttpResponse respond(HttpRequest request){
        //List<Case> c = [Select Id,CaseNumber,Date_Issue_Raised__c,Issue_raised_By__c,Issue_raised_By__r.Primary_Contact__c,Issue_raised_By__r.Primary_Country_Code__c,Complaint_Updates__c,Description , Status, Origin, Turnaround_Time__c,Date_Issue_resolved__c from Case];
        HttpResponse response = new HttpResponse();
        JSONGenerator gen = JSON.createGenerator(true);    
            gen.writeStartObject();      
            gen.writeStringField('salesforce_case_number ', '00009963');
            gen.writeStringField('description','This is Test');
            gen.writeStringField('status','Whatsapp');
            gen.writeStringField('origin','WIP');
            //gen.writeNumberField('tat',c.Turnaround_Time__c);
            //gen.writeString<PERSON>ield('complaint_description',c.Complaint_Updates__c);
            gen.writeEndObject();  
        String jsonData = gen.getAsString();
        response.setBody(jsonData);
        return response;
    }
}