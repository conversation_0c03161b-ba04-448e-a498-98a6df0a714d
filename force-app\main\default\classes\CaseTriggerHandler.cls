public class CaseTriggerHandler {
    public static void handleCaseAssignmentClosure(Boolean isInsert,List<Case> newCasesList, Map<Id, Case> oldMap) {
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        Set<String> cType = new set<String>{'Complaint','Information/ Guidance','Feedback/Suggestion','Internal Complaint'};
        Set<Id> ownerIds = new Set<Id>();
        
        for(Case caseObj : newCasesList){
            ownerIds.add(caseObj.Responsibility_to_Solve__c);
            ownerIds.add(caseObj.Responsibility_to_solve_Internal__c);
            ownerIds.add(caseObj.Responsibility_to_solve_Internal_Support__c);
        }
        Map<Id, String> userEmailMap = new Map<Id, String>();
        for (User user : [SELECT Id, Email FROM User WHERE Id IN :ownerIds]) {
            userEmailMap.put(user.Id, user.Email);
        }
        
        if(isInsert)
        {
            for(Case caseObj : newCasesList)
            {
                Messaging.SingleEmailMessage email;                
                if(cType.contains(caseObj.Complaint_Type__c) && caseObj.Relevant_Team__c!=null){
                    email = createCaseAssignmentEmail(caseObj,userEmailMap,'CASEASSIGNMENT');
                    //emails.add(createCaseAssignmentEmail(caseObj,userEmailMap,'CASEASSIGNMENT'));
                }
                if(caseObj.Status=='Closed'){
                    email = createCaseAssignmentEmail(caseObj,userEmailMap,'CASERESOLVED');
                    //emails.add(createCaseAssignmentEmail(caseObj,userEmailMap,'CASERESOLVED'));
                }
                if(email!=null)
                    emails.add(email);
            } 
        }
        else
        {
            for(Case caseObj : newCasesList)
            {
                Messaging.SingleEmailMessage email;
                if(cType.contains(caseObj.Complaint_Type__c) && caseObj.Relevant_Team__c!=null && (caseObj.Complaint_Type__c!=oldMAp.get(caseObj.Id).Complaint_Type__c || caseObj.Relevant_Team__c !=oldMAp.get(caseObj.Id).Relevant_Team__c)){
                    email = createCaseAssignmentEmail(caseObj,userEmailMap,'CASEASSIGNMENT');
                    //emails.add(createCaseAssignmentEmail(caseObj,userEmailMap,'CASEASSIGNMENT'));
                }
                if(caseObj.Status=='Closed' && caseObj.Status!=oldMAp.get(caseObj.Id).Status){
                    email = createCaseAssignmentEmail(caseObj,userEmailMap,'CASERESOLVED');
                    //emails.add(createCaseAssignmentEmail(caseObj,userEmailMap,'CASERESOLVED'));
                }
                
                if(caseObj.Due_date_for_closure__c!=oldMAp.get(caseObj.Id).Due_date_for_closure__c)
                {
                    email = createCaseAssignmentEmail(caseObj,userEmailMap,'CASEDUEDATEREVISED');
                    //caseObj.Old_Due_date_for_closure__c = oldMAp.get(caseObj.Id).Due_date_for_closure__c;
                    //emails.add(createCaseAssignmentEmail(caseObj,userEmailMap,'CASEDUEDATEREVISED'));
                }
                if(email!=null)
                    emails.add(email);
            } 
        }
        // Send emails
        if (!emails.isEmpty() && emails.size()>0) {
            EmailUtility eCls = new EmailUtility();
            eCls.sendEmailBulk(emails);
        }
    }
    
    public static Messaging.SingleEmailMessage createCaseAssignmentEmail(Case caseObj,Map<Id, String> userEmailMap,String emailName) {
        Id templateId;
        //String emailName = 'CASERESOLVED';
        EmailUtility eCls = new EmailUtility();
        List<String> ccAddrList = new List<String>();
        List<String> toAddrList = new List<String>();
        Map<String, Case_escalation_setting__mdt> metadataMap = new Map<String, Case_escalation_setting__mdt>();
        Messaging.SingleEmailMessage email;
        
        //if(!isClosure)
        //    emailName = 'CASEASSIGNMENT';
        System.debug('emailName>>>>'+emailName);
        templateId = EmailUtility.getTemplateId(emailName);
        metadataMap = eCls.getCaseEscalationSetting();
        
        If(Test.isRunningTest())
        {
            ccAddrList.add('<EMAIL>');
            toAddrList.add('<EMAIL>');
        }
        else
        {
            toAddrList.addAll(eCls.getCustomerSuccessSPOCEmail(caseObj,metadataMap));
            //Commented by Bharat as per discussion with mauli on 11-03-2025
            //ccAddrList.addAll(eCls.getFunHead(caseObj,metadataMap));
            if(userEmailMap!=null && userEmailMap.containsKey(caseObj.Responsibility_to_solve_Internal__c))
            {
                ccAddrList.add(userEmailMap.get(caseObj.Responsibility_to_solve_Internal__c));
            }  
        }
        
        Id conId = ecls.getDummyContactId();
              
        system.debug('toAddrList>>>>>>>>>'+toAddrList);
        system.debug('ccAddrList>>>>>>>>>'+ccAddrList);
        if(toAddrList!=null && toAddrList.size()>0)
        {
            email = eCls.createMailTemplateObj(caseObj.Id,templateId,toAddrList,ccAddrList,null,null,conId);
        }   
        return email;
    }
  
    
}