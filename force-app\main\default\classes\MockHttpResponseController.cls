@isTest
global class MockHttpResponseController implements HttpCalloutMock {
    // Implement this interface method
    global HTTPResponse respond(HTTPRequest req) {
        Map<String,Object> credMap = new Map<String,Object>();
        Map<String,Object> credMapForToken = new Map<String,Object>();
        credMapForToken.put('token','tokenString');
        credMap.put('data',credMapForToken);
        credMap.put('password','test');
        credMap.put('mobile','8866104284');
        credMap.put('country_code','+91');
        String JSONData = JSON.serialize(credMap);
        
        HTTPResponse res = new HTTPResponse();
        res.setHeader('Content-Type', 'text/json');
        res.setBody(JSONData);
        res.setStatusCode(200);
        return res;
    }
}