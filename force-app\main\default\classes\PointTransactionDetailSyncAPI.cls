@RestResource(urlMapping='/PointTransactionDetailSyncAPI/*')
global with sharing class PointTransactionDetailSyncAPI {

    @HttpPost
    global static void getPointTransactionDetails() {
        RestResponse res = RestContext.response;
        ResponseWrapper wResponse = new ResponseWrapper();
        
        try {
            RestRequest req = RestContext.request;
            String jsonReqString = req.requestBody.toString();
            RequestWrapper wRequest = (RequestWrapper) JSON.deserialize(jsonReqString, RequestWrapper.class);
            
            Set<String> primaryNumberSet = new Set<String>();
            Set<Integer> countryCodeSet = new Set<Integer>();
            
            for (ObjPrimaryContactWrapper primaryContact : wRequest.primaryContactList) {
                if (String.isNotBlank(primaryContact.primaryNumber)) {
                    primaryNumberSet.add(primaryContact.primaryNumber);
                }
                
                if (String.isNotBlank(primaryContact.countryCode) && primaryContact.countryCode.isNumeric()) {
                    countryCodeSet.add(Integer.valueOf(primaryContact.countryCode.trim()));
                }
            }
            
            if (primaryNumberSet.isEmpty()) {
                wResponse = createResponse(false, 'No primary numbers provided', null);
                res.responseBody = Blob.valueOf(JSON.serialize(wResponse));
                res.statusCode = 400;
                return;
            }
            
            Id IPVRecordType = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
            
            List<Account> accountList = new List<Account>();
            accountList = [SELECT Id, Name, Primary_Contact__c, Primary_Country_Code__c ,Total_Point__c, Total_Amount_Invested_in_Last_12_Months__c,
                           Membership_Slab__c, Date_of_Slab_Updation__c, Membership_Slab_Validity_Upto__c , Total_Points_as_of__c
                           FROM Account
                           WHERE Primary_Contact__c IN :primaryNumberSet AND Primary_Country_Code__c IN :countryCodeSet AND RecordTypeId = :IPVRecordType];
            
            Map<Id, List<Map<String, Object>>> responseMap = new Map<Id, List<Map<String, Object>>>();
            if (!accountList.isEmpty()) {
                Set<Id> accountIds = new Set<Id>();
                for (Account acc : accountList) {
                    accountIds.add(acc.Id);
                    responseMap.put(acc.Id, new List<Map<String, Object>>());
                }
                
                System.debug('Last Sync Time >>>>> ' + wRequest.lastSyncTime);

                // Set<String> notExpectedPointType = new Set<String>{'Refer a Startup','Investor Call','Form Complete','Startup Voting','LetsGrow SIP Call','LetsGrow Review Deck'};
                
                String transactionQuery = 'SELECT Id, Name, Startup__c, Startup__r.Public_Name__c,Startup_Name__c, Referee_Name__c,Manual_Startup_Name__c , Event_Date__c , Date_of_Startup_Voting__c , Date_of_SIP_Call__c , Date_of_Receiving_Lead__c , Credit_To__c, Credit_To__r.Name, ' +
                                          'Debit_From__c, Debit_From__r.Name, Point_Type__c, Points_Alloted__c, CreatedDate ' +
                                          'FROM Points_Transaction__c ' +
                                          'WHERE (Credit_To__c IN :accountIds OR Debit_From__c IN :accountIds)';
                
                if (wRequest.lastSyncTime != null) {
                    DateTime lastSyncTime = wRequest.lastSyncTime;//.addHours(5).addMinutes(30);
                    transactionQuery += ' AND CreatedDate > :lastSyncTime';
                }
                List<Points_Transaction__c> transactionList = Database.query(transactionQuery);
                
                // String transactionQueryForReferStartup = 'SELECT Id, Name, Startup__c, Startup__r.Public_Name__c,Referee_Name__c,Startup_Name__c, Manual_Startup_Name__c , Event_Date__c , Date_of_Receiving_Lead__c , Credit_To__c, Credit_To__r.Name, ' +
                //                                          'Debit_From__c, Debit_From__r.Name, Point_Type__c, Points_Alloted__c, CreatedDate ' +
                //                                          'FROM Points_Transaction__c ' +
                //                                          'WHERE Point_Type__c = \'Refer a Startup\' AND ' +
                //     					                 '(Credit_To__c IN :accountIds OR Debit_From__c IN :accountIds)';
                
                // if (wRequest.lastSyncTime != null) {
                //     Date lastSyncTime = wRequest.lastSyncTime.Date().addDays(-1);//.addHours(5).addMinutes(30);
                //     transactionQueryForReferStartup += ' AND Date_of_Receiving_Lead__c > :lastSyncTime';
                // }
                                
                // String transactionQueryForInvestorCall = 'SELECT Id, Name, Startup__c, Startup__r.Public_Name__c,Referee_Name__c,Startup_Name__c, Manual_Startup_Name__c , Event_Date__c , Date_of_Receiving_Lead__c , Credit_To__c, Credit_To__r.Name, ' +
                //                                          'Debit_From__c, Debit_From__r.Name, Point_Type__c, Points_Alloted__c, CreatedDate ' +
                //                                          'FROM Points_Transaction__c ' +
                //                                          'WHERE Point_Type__c IN (\'Investor Call\' , \'Form Complete\') AND ' +
                //     					                 '(Credit_To__c IN :accountIds OR Debit_From__c IN :accountIds)';
                
                // if (wRequest.lastSyncTime != null) {
                //     Date lastSyncTime = wRequest.lastSyncTime.Date().addDays(-1);//.addHours(5).addMinutes(30);
                //     transactionQueryForInvestorCall += ' AND Event_Date__c > :lastSyncTime';
                // }
                                
                // String transactionQueryForStartupVoting = 'SELECT Id, Name, Startup__c, Startup__r.Public_Name__c,Referee_Name__c,Startup_Name__c, Manual_Startup_Name__c , Event_Date__c , Date_of_Startup_Voting__c , Date_of_SIP_Call__c , Date_of_Receiving_Lead__c , Credit_To__c, Credit_To__r.Name, ' +
                //                                          'Debit_From__c, Debit_From__r.Name, Point_Type__c, Points_Alloted__c, CreatedDate ' +
                //                                          'FROM Points_Transaction__c ' +
                //                                          'WHERE Point_Type__c IN (\'Startup Voting\') AND ' +
                //     					                 '(Credit_To__c IN :accountIds OR Debit_From__c IN :accountIds)';
                
                // if (wRequest.lastSyncTime != null) {
                //     Date lastSyncTime = wRequest.lastSyncTime.Date().addDays(-1);//.addHours(5).addMinutes(30);
                //     transactionQueryForStartupVoting += ' AND Date_of_Startup_Voting__c > :lastSyncTime';
                // }
                
                // String transactionQueryForLetsGrow = 'SELECT Id, Name, Startup__c, Startup__r.Public_Name__c,Referee_Name__c,Startup_Name__c, Manual_Startup_Name__c , Event_Date__c , Date_of_Startup_Voting__c , Date_of_SIP_Call__c , Date_of_Receiving_Lead__c , Credit_To__c, Credit_To__r.Name, ' +
                //                                          'Debit_From__c, Debit_From__r.Name, Point_Type__c, Points_Alloted__c, CreatedDate ' +
                //                                          'FROM Points_Transaction__c ' +
                //                                          'WHERE Point_Type__c IN (\'LetsGrow SIP Call\' , \'LetsGrow Review Deck\') AND ' +
                //     					                 '(Credit_To__c IN :accountIds OR Debit_From__c IN :accountIds)';
                
                // if (wRequest.lastSyncTime != null) {
                //     Date lastSyncTime = wRequest.lastSyncTime.Date().addDays(-1);//.addHours(5).addMinutes(30);
                //     transactionQueryForLetsGrow += ' AND Date_of_SIP_Call__c > :lastSyncTime';
                // }
                // List<Points_Transaction__c> transactionListOfInvestorCall = Database.query(transactionQueryForInvestorCall);
                // List<Points_Transaction__c> transactionListOfReferStartup = Database.query(transactionQueryForReferStartup);
                // List<Points_Transaction__c> transactionListOfStartupVoting = Database.query(transactionQueryForStartupVoting);
                // List<Points_Transaction__c> transactionListOfLetsGrow = Database.query(transactionQueryForLetsGrow);
                
				// if(transactionListOfInvestorCall.Size() > 0)
                // {
                // 	transactionList.addAll(transactionListOfInvestorCall);
                // }

				// if(transactionListOfReferStartup.Size() > 0)
                // {
                // 	transactionList.addAll(transactionListOfReferStartup);
                // }

				// if(transactionListOfStartupVoting.Size() > 0)
                // {
                // 	transactionList.addAll(transactionListOfStartupVoting);
                // }

				// if(transactionListOfLetsGrow.Size() > 0)
                // {
                // 	transactionList.addAll(transactionListOfLetsGrow);
                // }
				
                
                for (Points_Transaction__c pt : transactionList) {
                    Map<String, Object> ptMap = constructPointTransactionMap(pt);
                    if (responseMap.containsKey(pt.Credit_To__c)) {
                        responseMap.get(pt.Credit_To__c).add(ptMap);
                    }
                    if (responseMap.containsKey(pt.Debit_From__c)) {
                        responseMap.get(pt.Debit_From__c).add(ptMap);
                    }
                }
            }
            
            List<Map<String, Object>> responseList = new List<Map<String, Object>>();
            
            for (Account acc : accountList) {
                Map<String, Object> accMap = constructAccountMap(acc);
                List<Map<String, Object>> pointTransactionList = responseMap.get(acc.Id);
                
                if (pointTransactionList == null) {
                    pointTransactionList = new List<Map<String, Object>>();
                }
                
                responseList.add(new Map<String, Object>{
                    'Account' => accMap,
                    'pointHistory' => pointTransactionList
                });
            }
            
            if (!responseList.isEmpty()) {
                wResponse = createResponse(true, 'Point transaction details synced successfully', responseList);
                res.responseBody = Blob.valueOf(JSON.serialize(wResponse));
                res.statusCode = 200;
            } else {
                wResponse = createResponse(false, 'Records Not Found', null);
                res.responseBody = Blob.valueOf(JSON.serialize(wResponse));
                res.statusCode = 404;
            }
            
        } catch (Exception e) {
            wResponse = createResponse(false, 'Exception: ' + e.getMessage(), null);
            res.responseBody = Blob.valueOf(JSON.serialize(wResponse));
            res.statusCode = 500;
        }
    }
    
    
    private static Map<String, Object> constructAccountMap(Account account) {
        Map<String, Object> accountMap = new Map<String, Object>();
        accountMap.put('Id', account.Id);
        accountMap.put('Name', account.Name);
        accountMap.put('Primary_Contact__c', account.Primary_Contact__c);
        accountMap.put('Primary_Country_Code__c', account.Primary_Country_Code__c);
        accountMap.put('Total_Point__c', account.Total_Point__c);
        accountMap.put('Total_Amount_Invested_in_Last_12_Months__c', account.Total_Amount_Invested_in_Last_12_Months__c);
        accountMap.put('Date_of_Slab_Updation__c', account.Date_of_Slab_Updation__c);
        accountMap.put('Membership_Slab_Validity_Upto__c', account.Membership_Slab_Validity_Upto__c);
        accountMap.put('Total_Points_as_of__c', account.Total_Points_as_of__c);
        
        if (account.Membership_Slab__c != null) {
            if (account.Membership_Slab__c == 'Gold') {
                accountMap.put('Membership_Slab__c', 'Gold');
                accountMap.put('Max_Points', 0);
                accountMap.put('Max_Investment', 0);
            } else if (account.Membership_Slab__c == 'Silver') {
                accountMap.put('Membership_Slab__c', 'Silver');
                accountMap.put('Max_Points', 1000);
                accountMap.put('Max_Investment', 3000000);
            } else if (account.Membership_Slab__c == 'Bronze') {
                accountMap.put('Membership_Slab__c', 'Bronze');
                accountMap.put('Max_Points', 500);
                accountMap.put('Max_Investment', 1000000);
            }
        } else {
            accountMap.put('Membership_Slab__c', '');
            accountMap.put('Max_Points', 0);
            accountMap.put('Max_Investment', 0);
        }
        
        return accountMap;
        
    }
    
    private static Map<String, Object> constructPointTransactionMap(Points_Transaction__c pointTransaction) {
        Map<String, Object> pointTransactionMap = new Map<String, Object>();
        pointTransactionMap.put('Id', pointTransaction.Id);
        pointTransactionMap.put('Name', pointTransaction.Name);
        
        if(pointTransaction.Point_Type__c == 'Refer a Startup' && pointTransaction.Date_of_Receiving_Lead__c != null)
        {
            if(pointTransaction.Date_of_Receiving_Lead__c != null)
            {
            	pointTransactionMap.put('LastModifiedDate', pointTransaction.Date_of_Receiving_Lead__c);
            }
            else
            {
                pointTransactionMap.put('LastModifiedDate', pointTransaction.CreatedDate);
            }
        }
        else if((pointTransaction.Point_Type__c == 'Investor Call' || pointTransaction.Point_Type__c == 'Form Complete') && pointTransaction.Event_Date__c != null)
        {   
            if(pointTransaction.Event_Date__c != null)
            {
	            pointTransactionMap.put('LastModifiedDate', pointTransaction.Event_Date__c);    
            }
            else
            {
            	pointTransactionMap.put('LastModifiedDate', pointTransaction.CreatedDate);
            }
        }
        else if(pointTransaction.Point_Type__c == 'Startup Voting' && pointTransaction.Date_of_Startup_Voting__c != null)
        {
            if(pointTransaction.Date_of_Startup_Voting__c != null)
            {
            	pointTransactionMap.put('LastModifiedDate', pointTransaction.Date_of_Startup_Voting__c);
            }
            else
            {
                pointTransactionMap.put('LastModifiedDate', pointTransaction.CreatedDate);
            }
        }
		else if((pointTransaction.Point_Type__c == 'LetsGrow SIP Call' || pointTransaction.Point_Type__c == 'LetsGrow Review Deck') && pointTransaction.Date_of_SIP_Call__c != null)
        {
            if(pointTransaction.Date_of_SIP_Call__c != null)
            {
                pointTransactionMap.put('LastModifiedDate', pointTransaction.Date_of_SIP_Call__c);
            }
            else
            {
                pointTransactionMap.put('LastModifiedDate', pointTransaction.CreatedDate);
            }
        }
        else
        {
            pointTransactionMap.put('LastModifiedDate', pointTransaction.CreatedDate);
        }
        
        if(pointTransaction.Point_Type__c == 'Refer a Startup' || pointTransaction.Point_Type__c == 'LetsGrow Goal Achieved' || pointTransaction.Point_Type__c == 'LetsGrow Fund Raised' || pointTransaction.Point_Type__c == 'LetsGrow Business Connected' || pointTransaction.Point_Type__c == 'LetsGrow SIP Call' || pointTransaction.Point_Type__c == 'LetsGrow Review Deck' || pointTransaction.Point_Type__c == 'LetsGrow Complete')
        {
            pointTransactionMap.put('Startup__c', pointTransaction.Manual_Startup_Name__c != null ? pointTransaction.Manual_Startup_Name__c : null);
        }
        else if(pointTransaction.Point_Type__c == 'Referal Conversions')
        {
            pointTransactionMap.put('Startup__c', pointTransaction.Referee_Name__c != null ? pointTransaction.Referee_Name__c : null);
        }
        else if(pointTransaction.Point_Type__c == 'Investor Call' || pointTransaction.Point_Type__c == 'Form Complete' || pointTransaction.Point_Type__c == 'Backed Out' || pointTransaction.Point_Type__c == 'Help as SME during DD' || pointTransaction.Point_Type__c == 'Help as SME' || pointTransaction.Point_Type__c == 'Help as SME during DD and present findings on the Investor Call' || pointTransaction.Point_Type__c == 'Worked as a Lead Member' || pointTransaction.Point_Type__c == 'Worked as a Co-lead' || pointTransaction.Point_Type__c == 'Invest 5L and above')
        {
            pointTransactionMap.put('Startup__c', pointTransaction.Startup__c != null ? pointTransaction.Startup__r.Public_Name__c : null);
        }
        else
        {
            pointTransactionMap.put('Startup__c', null);
        }
        
       
        pointTransactionMap.put('Debit_From__c', pointTransaction.Debit_From__c != null ? pointTransaction.Debit_From__r.Name : null);
        pointTransactionMap.put('Credit_To__c', pointTransaction.Credit_To__c != null ? pointTransaction.Credit_To__r.Name : null);
        pointTransactionMap.put('Point_Type__c', pointTransaction.Point_Type__c);
        pointTransactionMap.put('Points_Alloted__c', pointTransaction.Points_Alloted__c);
        
        return pointTransactionMap;
        
    }
    
    private static ResponseWrapper createResponse(Boolean isSuccess, String message, List<Map<String, Object>> dataList) {
        ResponseWrapper res = new ResponseWrapper();
        res.isSuccess = isSuccess;
        res.message = message;
        
        if (dataList != null && !dataList.isEmpty()) {
            res.dataList = dataList;
        }
        System.debug('Response >>>> ' + res);
        return res;
    }
    
    global class ObjPrimaryContactWrapper {
        global String countryCode;
		global String primaryNumber;
    }
     
    global class RequestWrapper {
        global List<String> columns;
        global List<ObjPrimaryContactWrapper> primaryContactList;
        global DateTime lastSyncTime;
    }
    
    global class ResponseWrapper {
        global Boolean isSuccess;
        global String message;
        global List<Map<String, Object>> dataList;

        global ResponseWrapper() {
            isSuccess = false;
            message = '';
            dataList = new List<Map<String, Object>>();
        }
    }
}