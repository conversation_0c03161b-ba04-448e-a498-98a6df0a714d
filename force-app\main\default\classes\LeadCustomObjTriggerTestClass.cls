/************************************************************************
    Test Class for LeadCustomObjTrigger AND LeadRestAPIController
************************************************************************/

@isTest(SeeAllData=false)
private class LeadCustomObjTriggerTestClass{
    
     private class RestMock implements HttpCalloutMock {
        
        public HTTPResponse respond(HTTPRequest req) {
            String fullJson = 'your Json Response';
            Map<String,Object> credMAp1 = new Map<String,Object>();
            credMAp1.put('token','token111');
            
            Map<String,Object> credMAp = new Map<String,Object>();
            credMAp.put('data',credMAp1);
            credMAp.put('mobile','**********');
            credMAp.put('country_code','+91');
            credMAp.put('password','hello');	
            fullJson = JSON.serialize(credMAp);
            
            HTTPResponse res = new HTTPResponse();
            res.setHeader('Content-Type', 'text/json');
            res.setBody(fullJson);
            res.setStatusCode(200);
            return res;
        }
    }
    
    @testSetup static void setup() {
        account acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Platinum';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        insert acc;
        
    }
    
    static testMethod void testLeadInsert()
    {
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseController());
        test.startTest();
        lead__c LeadList = TestFactory.createLead();
        LeadList.Primary_contact__c ='*********';
        LeadList.Facebook_Campaign_Name__c = '19th May Exclusive Masterclass';
        insert leadList;
        id recordTypeId = Schema.SObjectType.lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        Account acc = [select id,Primary_Contact__c from account limit 1];    
        lead__c leadObj = new Lead__c();
        leadObj = TestFactory.createLead(); 
        leadObj.Referred_By__c = Acc.Id;
        leadObj.Refer_By_Contact_No__c = acc.Primary_Contact__c;
       //leadObj.Referred_By_Lead__c = 'a0FF3000006MeW5MAK';
        leadObj.Referred_By_Lead__c = LeadList.id;
        leadObj.RecordTypeId = recordTypeId;
        leadObj.Facebook_Campaign_Name__c = '19th May Exclusive Masterclass';
        leadObj.Is_this_referral_lead__c = true;
        leadObj.Refer_By_Contact_No__c =  acc.Primary_Contact__c;
         Profile p = [SELECT Id FROM Profile WHERE Name='Standard User']; 
            User u = new User(Alias = 'standt', Email='<EMAIL>', 
            EmailEncodingKey='UTF-8', LastName='Testing', LanguageLocaleKey='en_US', 
            LocaleSidKey='en_US', ProfileId = p.Id, 
            TimeZoneSidKey='America/Los_Angeles', UserName='<EMAIL>',Contact_No__c='1234567');

          System.runAs(u) {
              leadObj.Relationship_Manager_Contact__c = u.Contact_No__c;
          }
        
        insert leadObj;
        //Added for leadrestAPIController. 19.10.23
        Set<Id> leadIdSet = new Set<Id>{leadObj.Id};
        LeadRestAPIController.SendLeadDetails(leadIdSet, true);
        
        acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Platinum';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        acc.Primary_Contact__c = '**********';
        insert acc;
       
        leadObj.Referred_By__c = null;
        leadObj.Relationship_Manager__c =UserInfo.getUserId();
        leadObj.Membership_Status__c='On Trial';
        update leadObj; 
        
        Lead__c ldc2 = TestFactory.createLead();
        ldc2.Primary_Contact__c = '**********';
        ldc2.Lead_Source__c = 'App - Startup Referral';
        ldc2.Membership_Status__c = 'Startup looking for fund';
        ldc2.Referred_By__c = Acc.Id;
        
        insert ldc2;
         test.stopTest();
    }
}