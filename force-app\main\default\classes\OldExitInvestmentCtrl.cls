Public class OldExitInvestmentCtrl {
 
    Public List<Startup__c > startupList{get;set;}
    Public List<InvesmentWrapper> InvestmentList{get;set;}
    Public List<Selectoption> startupOptionList{get;set;}
    Public List<Selectoption> exitRoundOptionList{get;set;}
    Public List<Selectoption> transferRoundOptionList{get;set;}
    public integer invLimitToProcess = 50;
    
    Public List<Selectoption> exitTypeList{get;set;}
    Public List<Selectoption> eventTypeList{get;set;}
    Public string selectedstartup{get;set;}
    Public string selectedExitRound{get;set;}
    Public string selectedTransferRound{get;set;}
    Public string selectedExitType{get;set;}
    Public string selectedEventType{get;set;}
    public Account acc {get; set;}
    public Boolean isInvSelected = false;
    Public String JOINTSOUTSIDESTR = 'Joint A/c - Outside';
    Public String EXITROUNDSEPARATOR = '_E';
    Public String INTTRANSROUNDSEPARATOR = '_IT';
    
    //Pagination variables
    private integer counter=0;  //keeps track of the offset
    public integer list_size=5; //sets the page size or number of rows
    public integer total_size; //used to show user the total size of the list
    Public Static String EXITSTR = 'Exit';
    Public Static String INTTRANSFER= 'Internal Transfers';
    
    Public OldExitInvestmentCtrl(){
        acc = new Account();
        investmentList = new List<InvesmentWrapper >();
        startupOptionList = new List<Selectoption>(); 
        exitRoundOptionList = new List<Selectoption>(); 
        transferRoundOptionList = new List<Selectoption>(); 
        
        exitTypeList= new List<Selectoption>();
        eventTypeList= new List<Selectoption>();
        startupOptionList.add(new selectOption('', '- None -'));
        exitRoundOptionList.add(new selectOption('', '- None -'));
        transferRoundOptionList.add(new selectOption('', '- None -'));
        exitTypeList.add(new selectOption('', '- None -'));
        eventTypeList.add(new selectOption('', '- None -'));
        startupList = new  List<Startup__c >();
        startupList = [SELECT id,Public_Name__c,Date_of_Investor_Call__c FROM Startup__c where Total_Investment_In_INR__c>0 order by Public_Name__c];
        
        for(Startup__c sr : startupList ){
            startupOptionList.add(new selectoption(sr.id,sr.Public_Name__c));
        }
        
        exitTypeList.add(new selectoption('Exit','Exit'));
        exitTypeList.add(new selectoption('Partial Exit','Partial Exit'));
        exitTypeList.add(new selectoption('Internal Transfer','Internal Transfer'));
        exitTypeList.add(new selectoption('Internal Transfer - IPV','Internal Transfer - IPV'));
        
        eventTypeList.add(new selectoption('Full Exit (Outside)','Full Exit (Outside)'));
        eventTypeList.add(new selectoption('Partial Exit (Outside)','Partial Exit (Outside)'));
        eventTypeList.add(new selectoption('Full Redemption of Units','Full Redemption of Units'));
        eventTypeList.add(new selectoption('Partial Redemption of Units','Partial Redemption of Units'));
        eventTypeList.add(new selectoption('Internal Transfer of Units','Internal Transfer of Units'));
        eventTypeList.add(new selectoption('Internal Transfer of Shares','Internal Transfer of Shares'));
    
    } 
    
    public PageReference getInvesment() {
        investmentList = new List<InvesmentWrapper >();
        
        if(selectedstartup!=null && selectedExitType!=null && (((selectedExitType=='Internal Transfer' || selectedExitType=='Internal Transfer - IPV') 
                                                                && acc.Amount_Paid__c!=null && acc.Date_of_Payment__c!=null ) || (acc.Amount_Paid__c!=null && acc.Date_of_Payment__c!=null && acc.Designation__c!=null)))
        {
            for(Investment__c invObj : [select id,Exit_Date__c,Type__c,Issue_Type__c,Membership_status__c,GPOA_Taken__c,Is_Primary__c,Investment_in_Own_Name_Family_Member__c,AIF_Contributor__c,Primary_Holder_Contact__c,Startup_Round__c,Startup_Round__r.Startup__c,Startup_Round__r.Issue_Price__c,Startup_Round__r.Name,Startup_Round__r.Issue_Price_FnF_Val__c,Investor_Name__c,Investor__c,Internal_Transfer_Investor__c,Investor__r.Name,Investor__r.Investor_Full_Name__c,Account__r.name,Number_of_Units_Held__c,Number_Of_Shares__c,Startup_Round__r.Round_Type__c from Investment__c where Startup_Round__r.Startup__c =:selectedstartup and Type__c in ('Invested','Invested - Shadow') order by Account__r.name])//  limit :list_size offset :counter])
            {
                investmentList.add(new InvesmentWrapper(invObj,selectedExitType));
            }
        }
        else
        {
            String sBreak = '</br>';
            apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,'Below inputs are mandatory.'));
            apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,'Startup Name.' ));
            apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,'Exit Price.' ));
            apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,'Date Of Exit.' ));
            
            if(selectedExitType!='Internal Transfer' && selectedExitType!='Internal Transfer - IPV')
                apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,'Buyer Name.' ));
        }
        return null;
    }
    
    public PageReference startupChange() {
        system.debug('startupChange>>>>'+selectedstartup);
        List<Startup_Round__c> roundList = [select id,name,Startup_Name__c,Round_Type__c from Startup_Round__c where Startup__c = :selectedstartup];// and (Round_Type__c = 'Exit' OR Round_Type__c = 'Internal Transfers')];
        exitRoundOptionList = new List<Selectoption>(); 
        exitRoundOptionList.add(new selectOption('', '- None -'));

        for(Startup_Round__c round : roundList)
        {
            if(round.Round_Type__c == 'Exit')
                exitRoundOptionList.add(new selectOption(''+round.Id, round.Startup_Name__c+' > '+round.name));
            else if(round.Round_Type__c == 'Internal Transfers')
                transferRoundOptionList.add(new selectOption(''+round.Id, round.Startup_Name__c+' > '+round.name));
        }    
        return null;
    }
    
    public PageReference updateEntries() {
        isInvSelected = false;
        if(acc.NumberOfEmployees!=null && acc.NumberOfEmployees> 0)
        {
            for(InvesmentWrapper invObj : investmentList)
            {
                if(invObj.checked)
                {
                    invObj.shareToBeExit = acc.NumberOfEmployees;
                    isInvSelected = true;
                }
            }
            
            if(!isInvSelected)
                apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,'Please select at least one investment record.' ));
        }
        else{
            apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,'Please enter valid Share To Be Exited(Mass).' ));
        }
        
        return null;
    }
    
    public PageReference saveExit() {
        List<Investment__c> updateInvList = new List<Investment__c>();
        List<Investment__c> insertInvList = new List<Investment__c>();
        Map<Id,Investment__c> invMap = new Map<Id,Investment__c>();
        Set<Id> invIdSet = new Set<Id>();
        Investment__c invClone;
        Startup_Round__c exitRoundObj;
        Startup_Round__c transferRoundObj;
        List<Startup_Round__c> sRoundUpsertList = new List<Startup_Round__c>();
        String soqlStartup = 'Select ';
        String soqlInv = 'Select id,';
        isInvSelected = false;
        Boolean isTransferInvBlank = false;
        Map<Id,Contact> investorMap = new Map<Id,Contact>();
        Integer totalSelectedRec = 0;
        Set<Id> selectedRoundSet = new Set<Id>();
        String existingRoundName='';
        //try{
            system.debug('acc>>>>>>>'+acc);
            
            for(InvesmentWrapper invObj : investmentList)
            {
                if(invObj.checked)
                {
                    selectedRoundSet.add(invObj.inv.Startup_Round__c);
                    if(invObj.shareToBeExit>0 || (invObj.inv.Number_Of_Shares__c==0 && invObj.shareToBeExit==0))
                    {
                        invIdSet.add(invObj.inv.Id);
                        isInvSelected = true;
                    }
                    if(selectedExitType=='Internal Transfer') 
                    {
                        if(invObj.inv.Internal_Transfer_Investor__c!=null)
                        {
                            investorMap.put(invObj.inv.Internal_Transfer_Investor__c,null);
                            
                        }
                        else
                        {
                            isTransferInvBlank = true;
                        }
                    }
                    totalSelectedRec++;
                }
            }
            
            if(invLimitToProcess < totalSelectedRec)
            {
                apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,'Maximum number of records you can process at a time is '+invLimitToProcess+'. Please select less records then '+invLimitToProcess+'.' ));
                return null;
            }
                
            if(!isInvSelected)
            {
                apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,'Please select at least one investment record.' ));
                return null;
            }
            //system.debug('selectedRoundSet>>>>>>>>>>>>>>>>>'+selectedRoundSet);
            if(selectedRoundSet.size()>1){
                apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,'Please select the investment records for only one startup round.' ));
                return null;
            }
            
            if(selectedExitType=='Internal Transfer' && isTransferInvBlank)
            {
                apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,'Please select investor to transfer for the transfer request.' ));
                return null;
            }
            
            system.debug('investorMap>>>>>>>'+investorMap);
            if(investorMap.size()>0)
            {
                investorMap = new Map<ID, Contact>([select id,name,accountId,Investor_s_PAN__c,Email,Postal_Address__c,Investment_in_Own_Name_Family_Member__c,Phone from contact where id in : investorMap.keyset()]);
            }
            system.debug('investorMap>>>>>>>'+investorMap);
           
            system.debug('acc.Date_of_Payment__c>>>>>>>'+acc.Date_of_Payment__c);
            if(invIdSet!=null && invIdSet.size()>0 && selectedExitType !='Internal Transfer - IPV')
            {
                List<Startup_Round__c> sRoundList = new List<Startup_Round__c>();
            
                if(selectedExitRound!=null && selectedExitRound!='')
                {
                    soqlStartup = 'Select id,Exit_Price__c,Date_Of_Exit__c,Parent_Round_Id__c,Buyer_Name__c,Startup_Investment_Type__c,Issue_Price__c from Startup_Round__c where id=:selectedExitRound' ;
                    system.debug('soqlStartup>>>>>>>'+soqlStartup );
                    sRoundList = Database.query(soqlStartup);
                    exitRoundObj  = sRoundList[0];
                }
                else
                {
                    soqlStartup = soqlStartup + string.join(getFields('Startup_Round__c'),',');
                    soqlStartup = soqlStartup +' from Startup_Round__c where id in : selectedRoundSet limit 1' ;
                    system.debug('soqlStartup>>>>>>>'+soqlStartup );
                    sRoundList = Database.query(soqlStartup);
                    exitRoundObj  = sRoundList[0].clone(false, false, false, false);
                    existingRoundName = exitRoundObj.Startup_name_auto__c!= null? exitRoundObj.Startup_name_auto__c: exitRoundObj.name;
                    exitRoundObj.Startup_name_auto__c = getRoundUpdatedName(selectedstartup,existingRoundName,EXITSTR,EXITROUNDSEPARATOR,acc.Amount_Paid__c,acc.Date_of_Payment__c);
                }
                exitRoundObj.Type_of_Event__c = selectedEventType;
                exitRoundObj.Exit_Price__c = acc.Amount_Paid__c;
                exitRoundObj.Date_Of_Exit__c = acc.Date_of_Payment__c;
                exitRoundObj.Buyer_Name__c = acc.Designation__c;
                exitRoundObj.Exit_Valuation__c = acc.Total_Investment_for_Membership_Slab__c;
                exitRoundObj.Round_Type__c = 'Exit';
                exitRoundObj.Parent_Round_Id__c = exitRoundObj.Parent_Round_Id__c != null? exitRoundObj.Parent_Round_Id__c +'>'+ new List<ID>(selectedRoundSet)[0] : ''+new List<ID>(selectedRoundSet)[0];
                exitRoundObj.Portfolio_Statement_Type__c = null;
                sRoundUpsertList.add(exitRoundObj);
                
                If(selectedExitType =='Internal Transfer')
                {
                    soqlStartup = 'Select ';
                    if(selectedTransferRound!=null && selectedTransferRound!='')
                    {
                        soqlStartup = 'Select id,Exit_Price__c,Date_Of_Exit__c,Parent_Round_Id__c,Buyer_Name__c,Startup_Investment_Type__c,Issue_Price__c from Startup_Round__c where id=:selectedTransferRound' ;
                        system.debug('soqlStartup111>>>>>>>'+soqlStartup );
                        sRoundList = Database.query(soqlStartup);                         exitRoundObj  = sRoundList[0];
                    }
                    else
                    {
                        soqlStartup = soqlStartup + string.join(getFields('Startup_Round__c'),',');
                        soqlStartup = soqlStartup +' from Startup_Round__c where id in : selectedRoundSet limit 1' ;
                        system.debug('soqlStartup111>>>>>>>'+soqlStartup );
                        sRoundList = Database.query(soqlStartup);
                        exitRoundObj  = sRoundList[0].clone(false, false, false, false);
                        
                        existingRoundName = exitRoundObj.Startup_name_auto__c!= null? exitRoundObj.Startup_name_auto__c: exitRoundObj.name;
                        exitRoundObj.Startup_name_auto__c = getRoundUpdatedName(selectedstartup,existingRoundName,INTTRANSFER,INTTRANSROUNDSEPARATOR,acc.Amount_Paid__c,acc.Date_of_Payment__c);
                        
                    }
                    
                    exitRoundObj.Type_of_Event__c = selectedEventType;
                    exitRoundObj.Exit_Price__c = acc.Amount_Paid__c;
                    exitRoundObj.Date_Of_Exit__c = acc.Date_of_Payment__c;
                    exitRoundObj.Buyer_Name__c = acc.Designation__c;
                    exitRoundObj.Exit_Valuation__c = acc.Total_Investment_for_Membership_Slab__c;
                    exitRoundObj.Round_Type__c = 'Internal Transfers';
                    //Added as per Yashika's request || 22-05-2023
                    exitRoundObj.Issue_Price__c = acc.Amount_Paid__c;
                    exitRoundObj.Parent_Round_Id__c = exitRoundObj.Parent_Round_Id__c != null? exitRoundObj.Parent_Round_Id__c +'>'+ new List<ID>(selectedRoundSet)[0] : ''+new List<ID>(selectedRoundSet)[0];
                    
                    //existingRoundName = exitRoundObj.Startup_name_auto__c!= null? exitRoundObj.Startup_name_auto__c: exitRoundObj.name;
                    exitRoundObj.Portfolio_Statement_Type__c = null;
                    sRoundUpsertList.add(exitRoundObj);
                }
                
                
                //upsert exitRoundObj;
                if(sRoundUpsertList.size()>0)
                {
                    upsert sRoundUpsertList;
                    exitRoundObj = sRoundUpsertList[0];
                }
                system.debug('sRoundUpsertList>>>>>>>'+sRoundUpsertList);
                system.debug('exitRoundObj>>>>>>>'+exitRoundObj);
            }
                        
            soqlInv  =soqlInv + string.join(getFields('Investment__c'),',');
            soqlInv = soqlInv +' from Investment__c where id in:invIdSet';
            system.debug('soqlInv>>>>>>>'+soqlInv);
            
            for(Investment__c inv : Database.query(soqlInv))
            {
                invMap.put(inv.Id,inv); 
            }
            
            for(InvesmentWrapper invObj : investmentList)
            {
                if(invObj.checked)
                {
                    system.debug('invObj.invObj.inv.Id>>>>>'+invObj.inv.Id);
                    system.debug('invObj.shareToBeExit>>>>>'+invObj.shareToBeExit);
                    system.debug('invObj.inv.Number_Of_Shares__c>>>>>'+invObj.inv.Number_Of_Shares__c);
                     
                    if(selectedExitType =='Internal Transfer - IPV')
                    {
                        Decimal pricePerShare = 0;
                        if(invObj.inv.Issue_Type__c == 'Primary' || invObj.inv.Issue_Type__c == 'Primary - AIF' || invObj.inv.Issue_Type__c == 'IPV Employee'
                            || invObj.inv.Issue_Type__c == 'Friends & Family T1 - AIF'
                            || invObj.inv.Issue_Type__c == 'Friends & Family T2 - AIF'
                        )
                        {
                            if(invObj.inv.Startup_Round__r.Issue_Price__c!=null)
                                pricePerShare = invObj.inv.Startup_Round__r.Issue_Price__c;  
                        }
                        else if(invObj.inv.Issue_Type__c == 'Friends & Family T1' || invObj.inv.Issue_Type__c == 'Friends & Family T2')
                        {   
                             if(invObj.inv.Startup_Round__r.Issue_Price_FnF_Val__c!=null)
                                 pricePerShare = invObj.inv.Startup_Round__r.Issue_Price_FnF_Val__c;  
                        }
                        
                       
                        invObj.inv.Type__c = 'Internal Transfers';
                        invObj.inv.Type_of_Event__c = selectedEventType;
                        insertInvList.add(invObj.inv); //update
                         
                        Investment__c invCloneNew = invMap.get(invObj.inv.Id).clone(false, false, false, false);
                        invCloneNew.Type__c = 'Invested';
                        invCloneNew.Id=null;
                        invCloneNew.Number_Of_Shares__c = invObj.shareToBeExit;
                        invCloneNew.Parent_Investment__c = invObj.inv.Id;
                        invCloneNew.Investment_Amount__c = invObj.shareToBeExit * pricePerShare;
                        invCloneNew.Type_of_Event__c = selectedEventType;
                        invCloneNew.Investment_Amount_Due__c= invCloneNew.Investment_Amount__c;
                        invCloneNew = clearFields(invCloneNew);
                        
                        /*
                        if(invObj.inv.Startup_Round__r.Round_Type__c == 'Internal Transfers')
                        {
                            invCloneNew = clearInternalTransferFields(invCloneNew);
                        }
                        else{
                            invCloneNew = clearFields(invCloneNew);
                        }
                       
                        invCloneNew.Committed_Amount__c= null;
                        invCloneNew.Investment_Amount_Due__c= null;
                        invCloneNew.Investment_Amount_in_Inr__c= null;
                        invCloneNew.Investment_Fee_Due__c= null;
                        invCloneNew.Investment_Fee_Received__c= null;
                        invCloneNew.Final_Commitment_Amount__c = null;
                        */
                        system.debug('insert2>>>>'+invCloneNew);
                        insertInvList.add(invCloneNew);

                    }
                    else
                    {
                        if((invObj.inv.Number_Of_Shares__c==0 && invObj.shareToBeExit==0) || (invObj.shareToBeExit>0 && invObj.inv.Number_Of_Shares__c <= invObj.shareToBeExit))
                        {
                            invObj.inv.Type__c = 'Exit';
                            
                            If(selectedExitType=='Internal Transfer')
                            {
                                invObj.inv.Type__c = 'Internal Transfers';
                                //invObj.inv.Investment_Amount__c = invObj.inv.Number_Of_Shares__c * exitRoundObj.Exit_Price__c;
                            }
                            invObj.inv.Type_of_Event__c = selectedEventType;
                            insertInvList.add(invObj.inv); //update
                            
                            invClone = invMap.get(invObj.inv.Id).clone(false, false, false, false);
                            invClone.Startup_Round__c = exitRoundObj.Id;
                            invClone.Type__c = 'Exit';
                            invClone.Number_Of_Shares__c = invObj.shareToBeExit;
                            invClone.Id=null;
                            invClone.Parent_Investment__c = invObj.inv.Id;
                            invClone.Investment_Amount_Due__c= exitRoundObj.Issue_Price__c * invObj.shareToBeExit;

                            invClone = clearFields(invClone);
                            
                            /*
                            invClone.Committed_Amount__c= null;
                            invClone.Investment_Amount_Due__c= null;
                            invClone.Investment_Amount_in_Inr__c= null;
                            invClone.Investment_Fee_Due__c= null;
                            invClone.Investment_Fee_Received__c= null;
                            invClone.Parent_Investment__c = invObj.inv.Id;
                            
                            invClone.Final_Commitment_Amount__c = null;
                            */
                            
                            if(invObj.inv.Number_Of_Shares__c>0)
                                invClone.Exit_amount_to_be_transferred__c = invObj.inv.Number_Of_Shares__c * exitRoundObj.Exit_Price__c;
                            else
                                invClone.Exit_amount_to_be_transferred__c = 0;
                            
                            system.debug('11111invClone.Number_Of_Shares__c>>>'+invClone.Number_Of_Shares__c);
                            system.debug('2222invexitRoundObj.Exit_Price__c>>>'+exitRoundObj.Exit_Price__c);
                            if(invClone.Number_Of_Shares__c>0)
                                invClone.Exit_amount_to_be_transferred__c = invClone.Number_Of_Shares__c * exitRoundObj.Exit_Price__c;
                            else
                                invClone.Exit_amount_to_be_transferred__c = 0;
                                
                            invClone.Exit_Date__c = exitRoundObj.Date_Of_Exit__c;
                            invClone.Exit_Date__c = acc.Date_of_Payment__c;
                            invClone.Exit_Price__c = exitRoundObj.Exit_Price__c;
                            
                            // Changes were made by Ankush For Investment Amount, changed exit_price with issue price.
                            invClone.Investment_Amount__c = invClone.Number_Of_Shares__c * exitRoundObj.Issue_Price__c;
                            invClone.Final_Commitment_Amount__c = null;
                            invClone.Type_of_Event__c = selectedEventType;
                            
                            system.debug('insert3>>>>'+invClone);
                            insertInvList.add(invClone);  //insert
                            
                            //If type = transfer
                            //Create transfer record under transfer sr
                            If(selectedExitType=='Internal Transfer')
                            {
                                invClone = invMap.get(invObj.inv.Id).clone(false, false, false, false);
                                invClone.Startup_Round__c = sRoundUpsertList[1].Id;
                                invClone.Type__c = 'Invested';
                                invClone.Number_Of_Shares__c = invObj.shareToBeExit;
                                
                                invClone.Id=null;
                                
                                invClone.Parent_Investment__c = invObj.inv.Id;
                                invClone.Investor__c = invObj.inv.Internal_Transfer_Investor__c;
                                invClone = clearInternalTransferFields(invClone);

                                invClone.Postal_Address__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Postal_Address__c;
                                invClone.Account__c= investorMap.get(invObj.inv.Internal_Transfer_Investor__c).AccountId;
                                invClone.Investor_s_PAN__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Investor_s_PAN__c;
                                invClone.Investor_Name__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Name;
                                invClone.Email_ID__c= investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Email;
                                invClone.Investment_in_Own_Name_Family_Member__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Investment_in_Own_Name_Family_Member__c;
                                
                                //if(invClone.Investment_in_Own_Name_Family_Member__c == JOINTSOUTSIDESTR)
                                //    invClone.Is_Primary__c = false; //Joint account will have only 1 primary contact
                                    
                                invClone.Phone_No__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Phone;
                                invClone.Primary_Holder_Contact__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Phone;
                                system.debug('investorMap111>>>>>>>>'+investorMap);
                                system.debug('invClone.Primary_Holder_Contact__c>>>>>>>>'+invClone.Primary_Holder_Contact__c);

                                invClone.Investment_Amount__c = invClone.Number_Of_Shares__c * exitRoundObj.Exit_Price__c;
                                //Added by Ankush
                                invClone.Investment_Amount_Due__c= invClone.Investment_Amount__c;
                                
                                invClone.Type_of_Event__c = selectedEventType;

                                system.debug('insert4>>>>'+invClone);
                                insertInvList.add(invClone);  //insert
                            }
                            
                        }
                        else if(invObj.shareToBeExit>0 && invObj.inv.Number_Of_Shares__c > invObj.shareToBeExit)
                        {
                            invObj.inv.Type__c = 'Partial Exit';
                            
                            If(selectedExitType=='Internal Transfer')
                            {
                                invObj.inv.Type__c = 'Internal Transfers';
                                //invObj.inv.Investment_Amount__c = invObj.inv.Number_Of_Shares__c * exitRoundObj.Exit_Price__c;
                            }
                            invObj.inv.Type_of_Event__c = selectedEventType;
                            
                            //Added by HD || Date: 24-10-2024 || Desc: For Capital Gain & Loss Statement
                            invObj.inv.Exit_Date__c = acc.Date_of_Payment__c;
                            insertInvList.add(invObj.inv);  //update
                            
                            system.debug('invObj>>>>111>>>>'+invObj);
                            Decimal pricePerShare = 0;
                            if(invObj.inv.Issue_Type__c == 'Primary' || invObj.inv.Issue_Type__c == 'Primary - AIF' || invObj.inv.Issue_Type__c == 'IPV Employee'
                            || invObj.inv.Issue_Type__c == 'Friends & Family T1 - AIF'
                            || invObj.inv.Issue_Type__c == 'Friends & Family T2 - AIF'
                            )
                            {
                                if(invObj.inv.Startup_Round__r.Issue_Price__c!=null)
                                    pricePerShare = invObj.inv.Startup_Round__r.Issue_Price__c;  
                            }
                            else if(invObj.inv.Issue_Type__c == 'Friends & Family T1' || invObj.inv.Issue_Type__c == 'Friends & Family T2')
                            {   
                                if(invObj.inv.Startup_Round__r.Issue_Price_FnF_Val__c!=null)
                                    pricePerShare = invObj.inv.Startup_Round__r.Issue_Price_FnF_Val__c;  
                            }
                           
                            system.debug('pricePerShare>>>>111>>>>'+pricePerShare);
                            Investment__c invCloneNew = invMap.get(invObj.inv.Id).clone(false, false, false, false);
                            invCloneNew.Type__c = 'Invested';
                            invCloneNew.Id=null;
                            invCloneNew.Number_Of_Shares__c = invObj.inv.Number_Of_Shares__c - invObj.shareToBeExit;
                            invCloneNew.Parent_Investment__c = invObj.inv.Id;
                            invCloneNew.Investment_Amount__c = invCloneNew.Number_Of_Shares__c * pricePerShare;
                            invCloneNew.Investment_Amount_Due__c= invCloneNew.Investment_Amount__c;
                            
                            invCloneNew = clearFields(invCloneNew);
                            //Added by HD || Date: 24-10-2024 || Desc: For Capital Gain & Loss Statement
                            invCloneNew.Exit_Date__c = acc.Date_of_Payment__c;
                            
                            system.debug('selectedExitType>>>>111>>>>'+selectedExitType);
                            If(selectedExitType=='Internal Transfer')
                            {
                                invCloneNew.Startup_Round__c = sRoundUpsertList[1].Id;
                                //IF type is internal transer then create inv record unser internal transfer round with new inv
                                invCloneNew.Parent_Investment__c = invObj.inv.Id;
                                invCloneNew.Investor__c = invObj.inv.Internal_Transfer_Investor__c; 
                                invCloneNew = clearInternalTransferFields(invCloneNew);
                                
                                invCloneNew.Number_Of_Shares__c = invObj.shareToBeExit;
                                invCloneNew.Investor__c = invObj.inv.Internal_Transfer_Investor__c;
                                invCloneNew.Postal_Address__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Postal_Address__c;
                                invCloneNew.Account__c= investorMap.get(invObj.inv.Internal_Transfer_Investor__c).AccountId;
                                invCloneNew.Investor_s_PAN__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Investor_s_PAN__c;
                                invCloneNew.Investor_Name__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Name;
                                invCloneNew.Email_ID__c= investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Email;
                                invCloneNew.Investment_in_Own_Name_Family_Member__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Investment_in_Own_Name_Family_Member__c;
                                
                                //if(invCloneNew.Investment_in_Own_Name_Family_Member__c == JOINTSOUTSIDESTR)
                                //    invCloneNew.Is_Primary__c = false; //Joint account will have only 1 primary contact
                                
                                invCloneNew.Phone_No__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Phone;
                                invCloneNew.Primary_Holder_Contact__c = investorMap.get(invObj.inv.Internal_Transfer_Investor__c).Phone;
                                system.debug('investorMap222>>>>>>>>'+investorMap);
                                system.debug('invCloneNew.Primary_Holder_Contact__c>>>>>>>>'+invCloneNew.Primary_Holder_Contact__c);

                                //invCloneNew.Investment_Amount__c = invCloneNew.Number_Of_Shares__c * pricePerShare;
                                invCloneNew.Investment_Amount__c = invCloneNew.Number_Of_Shares__c * exitRoundObj.Exit_Price__c;
                                //Added by HD || Date: 24-10-2024 || Desc: For Capital Gain & Loss Statement
                                invCloneNew.Exit_Date__c = acc.Date_of_Payment__c;
                            
                                system.debug('invObj.inv.Internal_Transfer_Investor__c>>>>111>>>>'+invObj.inv.Internal_Transfer_Investor__c);
                                system.debug('invCloneNew>>>>111>>>>'+invCloneNew);
                                
                                //Remaining share should be consider as a investment for orignal investor
                                Investment__c invCloneTransferNew = invMap.get(invObj.inv.Id).clone(false, false, false, false);
                                invCloneTransferNew.Type__c = 'Invested';
                                invCloneTransferNew.Id=null;
                                invCloneTransferNew.Number_Of_Shares__c = invObj.inv.Number_Of_Shares__c - invObj.shareToBeExit;
                                invCloneTransferNew.Parent_Investment__c = invObj.inv.Id;
                                invCloneTransferNew.Investment_Amount__c = invCloneTransferNew.Number_Of_Shares__c * pricePerShare;
                                //invCloneTransferNew.Investment_Amount__c = invCloneTransferNew.Number_Of_Shares__c * exitRoundObj.Exit_Price__c;
                                
                                //Added by Ankush 15/9/2022
                                invCloneTransferNew.Investment_Amount_Due__c = invCloneTransferNew.Investment_Amount__c;
                                invCloneTransferNew.Type_of_Event__c = selectedEventType;                                
                                invCloneTransferNew = clearFields(invCloneTransferNew);
                                
                                //if(invCloneTransferNew.Investment_in_Own_Name_Family_Member__c == JOINTSOUTSIDESTR)
                                //    invCloneTransferNew.Is_Primary__c = false; //Joint account will have only 1 primary contact  
                                
                                //Added by HD || Date: 24-10-2024 || Desc: For Capital Gain & Loss Statement
                                invCloneTransferNew.Exit_Date__c = acc.Date_of_Payment__c;
                                system.debug('insert5>>>>'+invCloneTransferNew);
          
                                insertInvList.add(invCloneTransferNew);  //insert
                                
                            }
                            system.debug('insert1>>>>'+invCloneNew);
                            insertInvList.add(invCloneNew);  //insert
                            
                            invClone = invMap.get(invObj.inv.Id).clone(false, false, false, false);
                            invClone.Startup_Round__c = exitRoundObj.Id;
                            invClone.Type__c = 'Exit';
                            invClone.Number_Of_Shares__c = invObj.shareToBeExit;
                            invClone.Id=null;
                            invClone.Parent_Investment__c = invObj.inv.Id;
                            invClone.Committed_Amount__c= null;
                            //invClone.Investment_Amount_Due__c= null;
                            invClone.Investment_Amount_in_Inr__c= null;
                            invClone.Investment_Fee_Due__c= null;
                            invClone.Investment_Fee_Received__c= null;
                            invClone.Final_Commitment_Amount__c = null;
                            //invClone.Exit_amount_to_be_transferred__c = invObj.inv.Number_Of_Shares__c * exitRoundObj.Exit_Price__c;
                            invClone.Exit_Date__c = exitRoundObj.Date_Of_Exit__c;
                            invClone.Exit_Date__c = acc.Date_of_Payment__c;
                            invClone.Exit_Price__c = exitRoundObj.Exit_Price__c;
                            
                            system.debug('77777invClone.Number_Of_Shares__c>>>'+invClone.Number_Of_Shares__c);
                            system.debug('88888invexitRoundObj.Exit_Price__c>>>'+exitRoundObj.Exit_Price__c);
                            
                            if(invClone.Number_Of_Shares__c>0)
                                invClone.Exit_amount_to_be_transferred__c = invClone.Number_Of_Shares__c * exitRoundObj.Exit_Price__c;
                            else
                                invClone.Exit_amount_to_be_transferred__c = 0;
                            // Changes were made by Ankush For Investment Amount, changed exit_price with issue price.    
                            invClone.Investment_Amount__c = invClone.Number_Of_Shares__c * exitRoundObj.Issue_Price__c;
                            InvClone.Final_Commitment_Amount__c = null;
                            invClone.Investment_Amount_Due__c= InvClone.Investment_Amount__c;
                            invClone.Type_of_Event__c = selectedEventType;
                            
                            invClone = clearFields(invClone);
                            
							//if(invClone.Investment_in_Own_Name_Family_Member__c == JOINTSOUTSIDESTR)
                            //        invClone.Is_Primary__c = false; //Joint account will have only 1 primary contact  
                            system.debug('insert6>>>>'+invClone);
                            insertInvList.add(invClone); //insert
                            
                            //New entry with no of share - share to be exit
                            
                        }
                    }        
                }
            }
            
            if(insertInvList.size()>0)
            {
                system.debug('insertInvList>>>>>'+insertInvList);
                upsert insertInvList;
                //selectedstartup = null;
                system.debug('insertInvList111>>>>>'+insertInvList);
                investmentList = new List<InvesmentWrapper >();

                apexpages.addMessage(new ApexPages.message(Apexpages.Severity.INFO,'Saved successfully'));
    
            }
        /*}
        catch(Exception e){
            apexpages.addMessage(new ApexPages.message(Apexpages.Severity.ERROR,''+e.getMessage()));
        }
        */
        return null;
    }
    
    Public Investment__c clearInternalTransferFields(Investment__c invObj)
    {
        //Set<String> fieldLabels = new Set<String>{'INVI Status','Distinctive No.(To)','Distictive No (From)','Investment in (Own Name/ Family Member)','Residential Status','Phone No','Email ID','Investor Father/Husband\'s Name','Postal Address','Added to WA Status','Added to App Backend','Added to App Investor Group','Remarks','Is Primary','Primary Holder Contact','Investment Type (App)','Actual Commitment Timestamp','Internal Commitment Timestamp','Reason for waitlist','Incomplete Status','Date of transaction (App)','Contribution Agreement','Sent Email Communications','Personal Email','Official Email','Invested number of share','IPVHQ Shadow Balance Share','IPVFnF Shadow Balance share','Family Balance Share','Intransfer balance share','Date of FnF CFM','Call For Money Sent','POA received','Bank Remittance Letter','PAN Card','Date of Allotment (App)','SAF hard copy','Aadhar Card','PAS - 4 : Investor Bankdetails soft copy','SAF soft copy (App)','Transfer Deed- soft copy (App)','PAS - 4 : Investor Bankdetails hard copy','Deed of Adherence - soft copy (App)','DOA stamp paper - soft copy','Transfer Deed- hard copy (App)','FIRC','Deed of Adherence - hard copy (App)','Consent - AIF','DOA stamp paper - hard copy','Date of Unit Allotment','Date of Drawdown sent','FCGPR Required','Date of Invi Filing','FCGPR Filing Done','FCGPR RBI Approval Received','NRD Given','Exit amount transferred','Exit fee %','Scans of Documents Received','Exit fee received','Executed Documents emailed','Buyer Name(Venture Connect)','IRR% Realised New','Date on Transfer Deed','Hard Copy of Documents Received','Hard Copy of Documents sent to Founder','Buyer Name','Buyer Name(Investor)','Old - Number of Shares','Refund Amount on Conversion','Old - Investment Amount Remitted','Tranches','Committed Amount','Final Commitment Amount','Investment Fee Due','Investment Fee Received (App)','Investment Amount (in INR)','Investment fee write-off','Funds Cheque (App)','Investment Fee %','Fund Transfer From (App)','IPV Fees Cheque','Tracking Id','Repatriation (App)','Get Physical Certificate (App)','Courier Partner','Remitters Bank Name','Documents Required','Remitters Bank Account Number','Transfer from','Remitters Address - (Foreign)','Transaction ID','Bank Statement remittance reference Info','Status','Foreign Domicile Country','INVI Number','Short INVI Number','Investment Premier Status','Investment AIF Class','Investment Carry %','Date On Share Certificate','Certificate Number','Share Certificates Scan received','Share Certificates Scan Sent (App)','Share Certificates (Hard Copy) Location','Stamp duty paid','Share Certificate location description','Share Certificates Uploaded On App','Share Certificates Tracking ID (App)','Amount Paid - Special Relationship','Date of Payment - Special Relationship','Channel Partner','Reason for Non Filing INVI'}; 
        Set<String> fieldLabels = new Set<String>{'Unit credited status','INVI Status','Distinctive No.(To)','Distictive No (From)','Investment in (Own Name/ Family Member)','Residential Status','Phone No','Email ID','Investor Father/Husband\'s Name','Postal Address','Added to WA Status','Added to App Backend','Added to App Investor Group','Remarks','Is Primary','Primary Holder Contact','Actual Commitment Timestamp','Internal Commitment Timestamp','Reason for waitlist','Incomplete Status','Date of transaction (App)','Contribution Agreement','Sent Email Communications','Personal Email','Official Email','Invested number of share','IPVHQ Shadow Balance Share','IPVFnF Shadow Balance share','Family Balance Share','Intransfer balance share','Date of FnF CFM','Call For Money Sent','POA received','Bank Remittance Letter','PAN Card','Date of Allotment (App)','SAF hard copy','Aadhar Card','PAS - 4 : Investor Bankdetails soft copy','SAF soft copy (App)','Transfer Deed- soft copy (App)','PAS - 4 : Investor Bankdetails hard copy','Deed of Adherence - soft copy (App)','DOA stamp paper - soft copy','Transfer Deed- hard copy (App)','FIRC','Deed of Adherence - hard copy (App)','Consent - AIF','DOA stamp paper - hard copy','Date of Unit Allotment','Date of Drawdown sent','FCGPR Required','Date of Invi Filing','FCGPR Filing Done','FCGPR RBI Approval Received','NRD Given','Exit amount transferred','Exit fee %','Scans of Documents Received','Exit fee received','Executed Documents emailed','Buyer Name(Venture Connect)','IRR% Realised New','Date on Transfer Deed','Hard Copy of Documents Received','Hard Copy of Documents sent to Founder','Buyer Name','Buyer Name(Investor)','Old - Number of Shares','Refund Amount on Conversion','Old - Investment Amount Remitted','Tranches','Committed Amount','Final Commitment Amount','Investment Fee Due','Investment Fee Received (App)','Investment Amount (in INR)','Investment fee write-off','Funds Cheque (App)','Investment Fee %','Fund Transfer From (App)','IPV Fees Cheque','Tracking Id','Repatriation (App)','Get Physical Certificate (App)','Courier Partner','Remitters Bank Name','Documents Required','Remitters Bank Account Number','Transfer from','Remitters Address - (Foreign)','Transaction ID','Bank Statement remittance reference Info','Status','Foreign Domicile Country','INVI Number','Short INVI Number','Investment Premier Status','Investment Carry %','Date On Share Certificate','Certificate Number','Share Certificates Scan received','Share Certificates Scan Sent (App)','Share Certificates (Hard Copy) Location','Stamp duty paid','Share Certificate location description','Share Certificates Uploaded On App','Share Certificates Tracking ID (App)','Amount Paid - Special Relationship','Date of Payment - Special Relationship','Channel Partner','Reason for Non Filing INVI'}; 
        
        Map<String, String> labelToAPINameMap = new Map<String, String>();
        Schema.DescribeSObjectResult objDescribe = Schema.getGlobalDescribe().get('Investment__c').getDescribe();   
        Map<String, Schema.SObjectField> fieldMap = objDescribe.fields.getMap();
        Set<String> checkboxSet = new Set<String>();
        
        for (String fieldName : fieldMap.keySet()) {
            Schema.DescribeFieldResult fieldDescribe = fieldMap.get(fieldName).getDescribe();
            if(fieldDescribe.isUpdateable())
                labelToAPINameMap.put(fieldDescribe.getLabel(), fieldDescribe.getName());
            
            if(fieldDescribe.getType() == Schema.DisplayType.Boolean)
                checkboxSet.add(fieldDescribe.getLabel());
        }
            
        for(String fieldlabel : fieldLabels) 
        {
            if(labelToAPINameMap.containsKey(fieldlabel))
            {
                if(checkboxSet.contains(fieldlabel))
                    invObj.put(labelToAPINameMap.get(fieldlabel), false);
                else 
                    invObj.put(labelToAPINameMap.get(fieldlabel), null);
            }
        }   
        system.debug('clearInternalTransferFields invObj>>>>>'+invObj);
         
        return invObj;
    }
    
    Public Investment__c clearFields(Investment__c invObj)
    {
        invObj.Contribution_Agreement__c = null;
        invObj.Committed_Amount__c = null;
        invObj.Final_Commitment_Amount__c = null;
        invObj.Investment_Fee_Due__c = null;
        invObj.Investment_Fee_Received__c = null;
        invObj.Investment_Amount_in_Inr__c = null;
        invObj.Investment_fee_write_off__c = null;
        //added by bharat for stop replication of field on child object on 05-03-2025
        invObj.Unit_credited_status__c = null;
        //invObj.Investor_Type__c = null;
        //invObj.Investor_Type__c = null;
        //invObj.Investment_AIF_Class__c = null;
        return invObj;
    }
    
    public static List<string> getFields(String selectedObject){
        List<String> reqFields = new List<String>();
        Map <String,Schema.SObjectType> gd = Schema.getGlobalDescribe();
        Schema.SObjectType sobjType = gd.get(selectedObject);
        Schema.DescribeSObjectResult r = sobjType.getDescribe();
        Map<String, Schema.SObjectField> MapofField = r.fields.getMap();
        
        for(String fieldName : MapofField.keySet()) {
            Schema.SObjectField field = MapofField.get(fieldName);
            Schema.DescribeFieldResult F = field.getDescribe();
            //System.debug('field-->'+field );
            if(F.isCreateable())
                reqFields.add(''+field);
        }
        System.debug(reqFields);
        return reqFields;
    }
    
    public PageReference Beginning() { //user clicked beginning
        counter = 0;
        return null;
    }
    
    public PageReference Previous() { //user clicked previous button
        counter -= list_size;
        return null;
    }
    
    public PageReference Next() { //user clicked next button
        system.debug('1  counter ::'+counter+' :list_size:'+list_size);
        counter += list_size;
        system.debug('counter ::'+counter+' :list_size:'+list_size);
        return null;
    }
    
    public PageReference End() { //user clicked end
        if(Test.isRunningTest())
        {
            total_size = 50;
            list_size = 5;
        }
        system.debug('total_size>>'+total_size+'<<>>'+list_size);
        system.debug('total_size111>>'+math.mod(total_size, list_size));
        counter = total_size - math.mod(total_size, list_size);
        system.debug('counter END>>'+counter);
        
        if(counter == total_size)
            counter = total_size - list_size;
            
        system.debug('counter END After>>'+counter);
        return null;
    }
    
    public Boolean getDisablePrevious() { 
        //this will disable the previous and beginning buttons
        if (counter>0) return false; else return true;
    }
    public Boolean getDisableNext() { //this will disable the next and end buttons
        if (counter + list_size < total_size) return false; else return true;
    }
    
    public Integer getTotal_size() {
        return total_size;
    }
    
    public Integer getPageNumber() {
        return counter/list_size + 1;
    }
    
    public Integer getTotalPages() {
        if (math.mod(total_size, list_size) > 0) {
            return total_size/list_size + 1;
        } else {
            return (total_size/list_size);
        }
    }
    
    Public String getRoundUpdatedName(Id startupId,String existingRName,String rType,String separator,Decimal exitPrice, Date exitDate){
        List<Startup_Round__c> startupRoundList = new List<Startup_Round__c>();
        startupRoundList = [SELECT id,name,createddate,Round_type__c,Startup__c,Startup__r.Public_Name__c,Pre_Emptive_Deal__c,Startup_name_auto__c,Exit_Price__c,Date_Of_Exit__c FROM Startup_Round__c where Startup__c =:startupId AND round_type__c =: rType order by createddate desc];
        Map<Id,Map<String,Integer>> startupChildPreEmptiveCountMap = new Map<Id,Map<String,Integer>>();
        Date recordCreatedDate = Date.today();
        //String publicStr = startupRoundList[0].Startup__r.Public_Name__c; 
        Integer lastRowCount = 0;
        String sRoundName = '';
        System.debug('startupRoundList>>>>>>>'+startupRoundList);
        Set<Integer> seqSet = new Set<Integer>();
        
        if(startupRoundList.size()>0){
            for(Startup_Round__c sr : startupRoundList)
            {
                system.debug('For Loop>>>>>>>>>>'+sr.ID);
                if(exitPrice == sr.Exit_Price__c && exitDate==sr.Date_Of_Exit__c)
                {
                    lastRowCount = getLastSequence(sr.Startup_name_auto__c!= null? sr.Startup_name_auto__c: sr.name,separator);
                    seqSet = new Set<Integer>();
                    break;
                }
                seqSet.add(getLastSequence(sr.Startup_name_auto__c!= null? sr.Startup_name_auto__c: sr.name,separator));
            }
            
            if(seqSet.size()>0){
                lastRowCount  = findGreatestPostfix(seqSet);
                lastRowCount = lastRowCount + 1;
            }
        }
        
        if(lastRowCount==0)
            lastRowCount =1;
        
        system.debug('existingRName123>>>>>>>>'+existingRName);   
        /* Commented by Hemant as Yashika requested to have nested naming max length of this field will be 255. 
        String rName = existingRName.substringBefore('_');
        String lastPart = existingRName.substringAfterLast('_');
        system.debug('firstPart>>>>>>>>'+rName+'<<<<<lastPart>>>>'+lastPart);   
        
        If(lastPart!=null && lastPart!='')
            rName = rName+'_'+lastPart;
        
        sRoundName = rName +separator+lastRowCount; 
        */
        
        sRoundName = existingRName +separator+lastRowCount; 
        system.debug('sRoundName>>>>>>>>'+sRoundName);            
        return sRoundName;
    }
    
    Public Integer getLastSequence(String strName,String separator){
        Integer ret = 0;        
        if(strName!=null && strName!='' && strName.contains(separator))
        {
            String lastStr = strName.substringAfterLast(separator);
            if(lastStr.isNumeric())
                ret = Integer.valueOf(lastStr);
        }
        return  ret;
    }
    public class InvesmentWrapper {

        public Boolean checked{ get; set; }
        public Investment__c inv{ get; set;}
        Public Decimal shareToBeExit{get;set;}
        Public String investorName{get;set;}
        
        public InvesmentWrapper (){
            inv= new Investment__c ();
            checked = false;
            shareToBeExit = 0;
            investorName = '';
        }
    
        public InvesmentWrapper (Investment__c invObj,String exitType){
            inv = invObj;
            checked = false;
            shareToBeExit = 0;
            
            if(invObj.Number_Of_Shares__c==null)
                invObj.Number_Of_Shares__c = 0;
                
            if(invObj.Investor__r.Name!=null)
                investorName = invObj.Investor__r.Name;
            else
                investorName = invObj.Investor_Name__c;   
            system.debug('InvestorName999>>>>>>>>'+investorName); 
            if(exitType =='Exit')
                shareToBeExit = invObj.Number_Of_Shares__c;
        }
    }
    
    
    public static Integer findGreatestPostfix(Set<Integer> seqList){
        Integer maxInteger = null;
        for(Integer postfix : seqList){
            try{
                if (maxInteger == null || postfix > maxInteger){
                    maxInteger = postfix ;
                }
            }
            catch (exception e){
            }
        }
        return maxInteger;
    }
    
}