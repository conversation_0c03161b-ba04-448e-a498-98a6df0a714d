global with sharing class PromoCodeAP<PERSON> {
	private static string CLASS_NAME = 'PromoCodeAPI';
    
    @future (Callout=True)
    public static void sendCampaignDetails (set<Id> cmpIdSet){
	
        try{
            String endURLSetting;
            String accessToken;
            List<API_Setting__c> settingList = API_Setting__c.getall().values();
            system.debug('API_Setting__c>>>'+settingList);
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].End_URL__c))
            {
                endURLSetting = ''+settingList[0].End_URL__c;
            }
            system.debug('--endURLSetting--'+endURLSetting);
            
            List<Campaign> cmpList = [SELECT Id  ,Name , Campaign_Code__c, Campaign__c, Campaign_logo_url__c, Discount_Percentage__c , Visibility__c , Status_of_Campaign_Code__c , Final_Amount_to_be_paid_incl_GST__c, StartDate , EndDate, Headline_Text__c, Description FROM Campaign WHERE Id In:cmpIdSet ];
            List<Map<String , Object>> cmpMapList = New List<Map<String , Object>>();
            
            for (campaign cmp : cmpList){
                Map<String , object> cmpMap = New Map<String , Object>();
                
                if (cmp.Id != null) {
                    cmpMap.put('SalesforceId', cmp.Id);
                }
                if (cmp.Campaign_Code__c != null) {
                    cmpMap.put('Campaign_Code', cmp.Campaign_Code__c);
                }
                if (cmp.Campaign__c != null) {
                    cmpMap.put('Campaign', cmp.Campaign__c);
                }
                if (cmp.Discount_Percentage__c != null) {
                    cmpMap.put('Discount_Percentage', cmp.Discount_Percentage__c);
                }
                if (cmp.StartDate != null) {
                    cmpMap.put('Date_of_Activation', cmp.StartDate);
                }
                if (cmp.EndDate != null) {
                    cmpMap.put('Date_of_Expiry', cmp.EndDate);
                }
                if (cmp.Campaign_logo_url__c != null) {
                    cmpMap.put('Campaign_logo_url', cmp.Campaign_logo_url__c);
                }
                if (cmp.Headline_Text__c != null) {
                    cmpMap.put('Headline_Text', cmp.Headline_Text__c);
                }
                if (cmp.Description != null) {
                    cmpMap.put('Description', cmp.Description);
                }
                if (cmp.Visibility__c != null) {
                    cmpMap.put('Visibility', cmp.Visibility__c);
                }
                if (cmp.Status_of_Campaign_Code__c != null) {
                    cmpMap.put('Status_of_Campaign_Code', cmp.Status_of_Campaign_Code__c);
                }
                if (cmp.Final_Amount_to_be_paid_incl_GST__c != null) {
                    cmpMap.put('Final_Amount_to_be_paid_incl_GST', cmp.Final_Amount_to_be_paid_incl_GST__c);
                }
                if (cmp.Name != null) {
                    cmpMap.put('Campaign_Name', cmp.Name);
                }
                cmpMapList.add(cmpMap);
            }
            
            Map<String , Object> finalDataMap = new Map<String , Object>();
            finalDataMap.put('Campaigns' , cmpMapList);
            
            String jsonData = JSON.serialize(finalDataMap);
            System.debug('Campaign Json Data >>>>>>>>>>>>>>>>>>>>.' + jsonData);
            
            // uncomented by karan to check data flow for app side 29/08/2024
            accessToken = restLoginController.loginExternalSystem();
            if(accessToken != null){
                String enddURL = endURLSetting;
                //enddURL += '/promoCode/addBulkPromoCode';
                enddURL += '/referAndBuyMembership/promo-code/addBulkPromoCode';
                system.debug('endURL --->'+enddURL);
                HttpRequest request = new HttpRequest();
  
                request.setEndpoint(enddURL);
                request.setMethod('POST');
                request.setHeader('Authorization','Bearer ' + accessToken);
                request.setHeader('Content-Type','application/json');
                request.setTimeout(120000);
                request.setBody(jsonData);
                Http http1 = new Http();
                
                HTTPResponse res1 = http1.send(request);
                
                System.debug(res1.getStatusCode());
                System.debug('res--add-'+res1.getBody());
                System.debug('STATUS:'+res1.getStatus());
            }
			
        }
        catch(Exception ex) {
        	system.debug('Error '+ex);
            Error_Log__c log =  new Error_Log__c();
            log.Class_Name__c = CLASS_NAME;
            log.method__c = 'sendCampaignDetails' ;
            log.Error_Message__c = ex.getMessage();
            insert log;
            System.debug('Error while sending promocode details : ' + log.Error_Message__c);       
        }
	}
}