@isTest 
global  class CSVFileReadMock implements HttpCalloutMock{
        // Implement this interface method
        global HTTPResponse respond(HTTPRequest req) {
            Map<String,Object> credMap = new Map<String,Object>();
            Map<String,Object> credMapForToken = new Map<String,Object>();
            credMapForToken.put('token','tokenString');
            credMap.put('verified',true);
            credMap.put('error_msg','test');
            credMap.put('id','**********');
            credMap.put('country_code','+91');
            credMap.put('beneficiary_name_with_bank','Test name');
            String JSONData = JSON.serialize(credMap);
            
            HTTPResponse res = new HTTPResponse();
            res.setHeader('Content-Type', 'text/json');
            res.setBody(JSONData);
            res.setStatusCode(200);
            return res;
        }    
}