public class LeadWhatsappScheduler implements Schedulable {
    private String scheduleExpression;
    private String scheduleName;
    private set<Id> leads;
    public LeadWhatsappScheduler(String name, String expression,set<ID> leadList) {
        scheduleName = name;
        scheduleExpression = expression;
        leads = leadList;
    }
    
    public void execute(SchedulableContext context) {       
       OutboundMessageAPIController.sendWhatsappAPIForNonRefLead(leads);  
       System.debug('Scheduled a job!');
    }
    
    public static void scheduleJob(String name, String expression, set<Id> leads) {
        LeadWhatsappScheduler scheduler = new LeadWhatsappScheduler(name, expression, leads);
        String jobId = System.schedule(name, expression, scheduler);
        System.debug('Job scheduled with ID: ' + jobId);
    }
}