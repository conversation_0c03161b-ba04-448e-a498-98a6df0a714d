<template>
    <lightning-card title="Account Activities">
        <!-- Top-right corner buttons -->
        <div class="slds-grid slds-grid_align-end slds-p-around_medium">
            <lightning-button-icon icon-name="utility:filterList" alternative-text="Filter" onclick={openModal} class="slds-m-right_small"></lightning-button-icon>
            <lightning-button-icon icon-name="utility:download" alternative-text="Download" onclick={handleDownload}></lightning-button-icon>
        </div>

        <!-- Modal for Filters -->
        <template if:true={isModalOpen}>
            <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container">
                    <header class="slds-modal__header">
                        <button class="slds-button slds-button_icon slds-modal__close" title="Close" onclick={closeModal} style="background-color: white;">
                            <lightning-icon icon-name="utility:close" alternative-text="Close" size="small"></lightning-icon>
                            <span class="slds-assistive-text">Close</span>
                        </button>
                        <h2 class="slds-text-heading_medium">Filter Activities</h2>
                    </header>
                    <div class="slds-modal__content slds-p-around_medium">
                        <lightning-tabset>
                            <!-- Tab 1: CRM Activity -->
                            <lightning-tab label="CRM Activity" active={isTab1} data-tab="tab1" onclick={handleTabChange}>
                                <lightning-layout multiple-rows>
                                    <lightning-layout-item>
                                        <lightning-checkbox-group
                                            options={checkboxOptionsGroup}
                                            value={selectedValues}
                                            onchange={handleCheckboxChange}
                                            style="columns: 3;">
                                        </lightning-checkbox-group>
                                    </lightning-layout-item>

                                    <!-- Buttons moved to the bottom -->
                                    <lightning-layout-item class="slds-p-top_small slds-grid slds-grid_align-center">
                                        <lightning-button class="slds-m-right_small" variant="neutral" label="Select all" onclick={selectAllCRM}></lightning-button>
                                        <lightning-button variant="neutral" label="Clear all" onclick={clearAllCRM}></lightning-button>
                                    </lightning-layout-item>
                                </lightning-layout>
                            </lightning-tab>

                            <!-- Tab 2: Date Range -->
                            <lightning-tab label="Date Range" active={isTab2} data-tab="tab2" onclick={handleTabChange}>
                                <lightning-input type="date" name="startDate" label="Start Date" value={tempStartDate} onchange={handleDateChange}></lightning-input>
                                <lightning-input type="date" name="endDate" label="End Date" value={tempEndDate} onchange={handleDateChange}></lightning-input>
                                <lightning-button class="slds-p-top_small slds-grid lds-m-right_small " variant="neutral" label="Clear all" onclick={clearAllDate}></lightning-button>
                            </lightning-tab>

                            <!-- Tab 3: App Activity -->
                            <lightning-tab label="App Activity" active={isTab3} data-tab="tab3" onclick={handleTabChange}>
                                <lightning-layout multiple-rows>
                                    <lightning-layout-item>
                                        <lightning-checkbox-group
                                            options={checkboxOptionsappGroup}
                                            value={selectedValuesApp}
                                            onchange={handleCheckboxAppChange}
                                            style="columns: 3;">
                                        </lightning-checkbox-group>
                                    </lightning-layout-item>

                                    <!-- Buttons moved to the bottom -->
                                    <lightning-layout-item class="slds-p-top_small slds-grid slds-grid_align-center">
                                        <lightning-button class="slds-m-right_small" variant="neutral" label="Select all" onclick={selectAllApp}></lightning-button>
                                        <lightning-button variant="neutral" label="Clear all" onclick={clearAllApp}></lightning-button>
                                    </lightning-layout-item>
                                </lightning-layout>
                            </lightning-tab>
                        </lightning-tabset>
                    </div>
                    <footer class="slds-modal__footer">
                        <lightning-button variant="brand" label="Apply" onclick={applyFilters}></lightning-button>
                    </footer>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </template>

        <!-- Display Activities -->
        <template if:true={groupedActivities}>
            <div class="slds-vertical-line">
                <template for:each={groupedActivities} for:item="activityGroup">
                    <lightning-accordion key={activityGroup.date} allow-multiple-sections-open="true" active-section-name={activityGroup.date}>
                        <lightning-accordion-section name={activityGroup.date} label={activityGroup.date} key={activityGroup.date}>
                            <template for:each={activityGroup.activities} for:item="activity">
                                <div key={activity.id} class="slds-p-around_medium activity-item">
                                    <lightning-icon class="vertical-line" icon-name={activity.iconName} alternative-text="activity icon" size="x-small"></lightning-icon>&nbsp;
                                    <div class="activity-detail">
                                        <!-- Displaying rich text content -->
                                        <lightning-formatted-rich-text value={activity.activityDetail}></lightning-formatted-rich-text>
                                    </div>
                                    
                                </div>
                            </template>
                        </lightning-accordion-section>
                    </lightning-accordion>
                </template>
            </div>
        </template>

        <template if:false={groupedActivities}>
            <p>No activities found.</p>
        </template>

        <!-- Pagination -->
        <div class="slds-grid slds-grid_align-end slds-p-around_medium">
            <lightning-button label="Previous" icon-name="utility:chevronleft" class="slds-p-around_medium" data-type="previous" onclick={handlePageChange} disabled={disablePrevious}></lightning-button>
            <lightning-button label="Next" icon-name="utility:chevronright" class="slds-p-around_medium" data-type="next" onclick={handlePageChange} disabled={disableNext}></lightning-button>
        </div>
    </lightning-card>
</template>