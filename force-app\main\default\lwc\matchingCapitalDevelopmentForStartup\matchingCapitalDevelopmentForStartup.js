import { LightningElement, wire, api, track } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import getMatchingStartups from '@salesforce/apex/MatchingCapitalDevelopmentController.getMatchingCapitalDevelopments';

const columns = [
    {
        label: 'Name',
        fieldName: 'NavigateToRecordUrl',
        type: 'url',
        typeAttributes: {
            label: { fieldName: 'Name' },
            target: '_blank',
            tooltip: 'Open Record'
        }
    },
    { label: 'Industry', fieldName: 'Industry_Sector__c', type: 'text' },
    { label: 'Fundraise Amount (Min) ', fieldName: 'Minimum_Transaction_Size__c', type: 'number' },
    { label: 'Fundraise Amount (Max) ', fieldName: 'Maximum_Transaction_Size__c', type: 'text' }
];

export default class MatchingCapitalDevelopmentForStartup extends NavigationMixin(LightningElement) {

    @api recordId;
    @track capitalDevelopmentData;

    connectedCallback() {
        this.getCapitalDevelopments();
    }

    getCapitalDevelopments() {
        if (this.recordId) {
            getMatchingStartups({ startupId: this.recordId })
                .then(result => {
                    console.log('Capital Development :', result); // Log the complete records to inspect the fields
                    this.capitalDevelopmentData = { data: this.mapDataForNavigation(result), error: undefined };
                })
                .catch(error => {
                    console.error('Error fetching Capital Development :', error);
                    this.capitalDevelopmentData = { data: undefined, error: 'Error fetching startups' };
                });
        } else {
            console.error('Record ID is missing.');
        }
    }

    mapDataForNavigation(data) {
        return data.map(record => {
            return {
                ...record,
                NavigateToRecordUrl: `/${record.Id}`,
            };
        });
    }

    handlePublicNameClick(event) {
        const recordId = event.detail.row.Id;
        // Use NavigationMixin to navigate to the record page
        this[NavigationMixin.GenerateUrl]({
            type: 'standard__recordPage',
            attributes: {
                recordId: recordId,
                actionName: 'view'
            }
        }).then(url => {
            window.open(url, '_blank');
        }).catch(error => {
            console.error('Error navigating to the record:', error);
        });
    }

    get columns() {
        return columns;
    }

}