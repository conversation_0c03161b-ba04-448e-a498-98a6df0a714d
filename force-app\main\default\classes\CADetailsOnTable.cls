public with sharing class CADetailsOnTable {
  @AuraEnabled(cacheable=true)
  public static List<ContributionAgreementWrapper> getContributionAgreementsForAccountObject(
    Id accountId
  ) {
    try {
      Id JointRecordTypeId = Schema.SObjectType.Contribution_Agreement__c.getRecordTypeInfosByName()
        .get('Joint')
        .getRecordTypeId();

      List<Contact> investors = [
        SELECT Id, Name
        FROM Contact
        WHERE AccountId = :accountId
      ];

      Map<Id, String> contactIdToName = new Map<Id, String>();
      Set<Id> investorIds = new Set<Id>();

      for (Contact c : investors) {
        contactIdToName.put(c.Id, c.Name);
        investorIds.add(c.Id);
      }

      List<Contribution_Agreement__c> agreements = [
        SELECT
          Id,
          Name,
          Investor1__c,
          Investor2__c,
          Investor1__r.Name,
          Investor2__r.Name,
          Fund_Onboarded_On__r.Name,
          Type__c,
          Total_Contribution_Amount__c,
          Total_Drawdowns__c,
          Balance_Amounts__c,
          Carry__c,
          Class_type__c,
          Premier_Premier_Onboarding_Date__c,
          Fund_Onboarded_on__r.Fund_Currency__c,
          RecordTypeId
        FROM Contribution_Agreement__c
        WHERE
          Investor1__c IN :contactIdToName.keySet()
          OR Investor2__c IN :contactIdToName.keySet()
        ORDER BY CreatedDate DESC
      ];

      List<ContributionAgreementWrapper> results = new List<ContributionAgreementWrapper>();
      for (Contribution_Agreement__c ca : agreements) {
        if (investorIds.contains(ca.Investor1__c)) {
          results.add(
            new ContributionAgreementWrapper(
              ca.Id,
              ca.Name,
              ca.Investor1__c,
              ca.Investor1__r.Name,
              ca.Fund_Onboarded_On__r.Name,
              ca.Type__c,
              ca.Total_Contribution_Amount__c,
              ca.Total_Drawdowns__c,
              ca.Balance_Amounts__c,
              ca.Carry__c,
              ca.Class_type__c,
              ca.Premier_Premier_Onboarding_Date__c,
              ca.Fund_Onboarded_on__r.Fund_Currency__c
            )
          );
        }

        if (investorIds.contains(ca.Investor2__c)) {
          results.add(
            new ContributionAgreementWrapper(
              ca.Id,
              ca.Name,
              ca.Investor2__c,
              ca.Investor2__r.Name,
              ca.Fund_Onboarded_On__r.Name,
              ca.Type__c,
              ca.Total_Contribution_Amount__c,
              ca.Total_Drawdowns__c,
              ca.Balance_Amounts__c,
              ca.Carry__c,
              ca.Class_type__c,
              ca.Premier_Premier_Onboarding_Date__c,
              ca.Fund_Onboarded_on__r.Fund_Currency__c
            )
          );
        }
      }

      return results;
    } catch (Exception e) {
      throw new AuraHandledException('Error fetching data: ' + e.getMessage());
    }
  }

  @AuraEnabled(cacheable=true)
  public static List<ContributionAgreementWrapper> getContributionAgreementsForInvestorObject(
    Id investorId
  ) {
    try {
      List<Contact> investors = [
        SELECT Id, Name
        FROM Contact
        WHERE Id = :investorId
      ];

      Set<Id> investorIds = new Set<Id>();
      Map<Id, String> contactIdToName = new Map<Id, String>();

      for (Contact c : investors) {
        contactIdToName.put(c.Id, c.Name);
        investorIds.add(c.Id);
      }

      List<Contribution_Agreement__c> agreements = [
        SELECT
          Id,
          Name,
          Investor1__c,
          Investor2__c,
          Investor1__r.Name,
          Investor2__r.Name,
          Fund_Onboarded_On__r.Name,
          Type__c,
          Total_Contribution_Amount__c,
          Total_Drawdowns__c,
          Balance_Amounts__c,
          Carry__c,
          Class_type__c,
          Premier_Premier_Onboarding_Date__c,
          Fund_Onboarded_on__r.Fund_Currency__c,
          RecordTypeId
        FROM Contribution_Agreement__c
        WHERE
          Investor1__c IN :contactIdToName.keySet()
          OR Investor2__c IN :contactIdToName.keySet()
        ORDER BY CreatedDate DESC
      ];

      List<ContributionAgreementWrapper> results = new List<ContributionAgreementWrapper>();
      for (Contribution_Agreement__c ca : agreements) {
        if (investorIds.contains(ca.Investor1__c)) {
          results.add(
            new ContributionAgreementWrapper(
              ca.Id,
              ca.Name,
              ca.Investor1__c,
              ca.Investor1__r.Name,
              ca.Fund_Onboarded_On__r.Name,
              ca.Type__c,
              ca.Total_Contribution_Amount__c,
              ca.Total_Drawdowns__c,
              ca.Balance_Amounts__c,
              ca.Carry__c,
              ca.Class_type__c,
              ca.Premier_Premier_Onboarding_Date__c,
              ca.Fund_Onboarded_on__r.Fund_Currency__c
            )
          );
        }

        if (investorIds.contains(ca.Investor2__c)) {
          results.add(
            new ContributionAgreementWrapper(
              ca.Id,
              ca.Name,
              ca.Investor2__c,
              ca.Investor2__r.Name,
              ca.Fund_Onboarded_On__r.Name,
              ca.Type__c,
              ca.Total_Contribution_Amount__c,
              ca.Total_Drawdowns__c,
              ca.Balance_Amounts__c,
              ca.Carry__c,
              ca.Class_type__c,
              ca.Premier_Premier_Onboarding_Date__c,
              ca.Fund_Onboarded_on__r.Fund_Currency__c
            )
          );
        }
      }

      return results;
    } catch (Exception e) {
      throw new AuraHandledException('Error fetching data: ' + e.getMessage());
    }
  }

  public class ContributionAgreementWrapper {
    @AuraEnabled
    public Id caId;
    @AuraEnabled
    public String caName;
    @AuraEnabled
    public Id investorId;
    @AuraEnabled
    public String investorName;
    @AuraEnabled
    public String fundOnboardedOn;
    @AuraEnabled
    public String type;
    @AuraEnabled
    public Decimal totalContribution;
    @AuraEnabled
    public Decimal totalDrawdown;
    @AuraEnabled
    public Decimal balanceAmount;
    @AuraEnabled
    public Decimal carryPercent;
    @AuraEnabled
    public String classValue;
    @AuraEnabled
    public Date premierOnboardingDate;
    @AuraEnabled
    public String currencyField;


    public ContributionAgreementWrapper(
      Id caId,
      String caName,
      Id investorId,
      String investorName,
      String fundOnboardedOn,
      String type,
      Decimal totalContribution,
      Decimal totalDrawdown,
      Decimal balanceAmount,
      Decimal carryPercent,
      String classValue,
      Date premierOnboardingDate,
      String currencyField
    ) {
      this.caId = caId;
      this.caName = caName;
      this.investorId = investorId;
      this.investorName = investorName;
      this.fundOnboardedOn = fundOnboardedOn;
      this.type = type;
      this.totalContribution = totalContribution;
      this.totalDrawdown = totalDrawdown;
      this.balanceAmount = balanceAmount;
      this.carryPercent = carryPercent;
      this.classValue = classValue;
      this.premierOnboardingDate = premierOnboardingDate;
      this.currencyField = currencyField;
    }
  }
}