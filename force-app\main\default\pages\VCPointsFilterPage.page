<apex:page standardController="Venture_Connect__c" extensions="VCPointsFilterCtrl" lightningStylesheets="true" >
    <apex:form >
        <apex:pageBlock >
            <!--
            <apex:pageBlockButtons location="bottom">
                <apex:outputPanel id="myButtons" style="align:canter;">
                    <apex:commandButton action="{!Beginning}" title="Beginning" value="<<" disabled="{!disablePrevious}" reRender="myPanel,myButtons"/>
                    <apex:commandButton action="{!Previous}" title="Previous" value="<" disabled="{!disablePrevious}" reRender="myPanel,myButtons"/>        
                    <apex:commandButton action="{!Next}" title="Next" value=">"  disabled="{!disableNext}" reRender="myPanel,myButtons"/>
                    <apex:commandButton action="{!End}" title="End" value=">>" disabled="{!disableNext}" reRender="myPanel,myButtons"/>  
                </apex:outputPanel>
                </apex:pageBlockButtons>
             -->
             <apex:outputPanel id="myPanel">
                <apex:pageBlockTable value="{!StartupVConnectWrapperList}" var="stObj">
                    
                    <apex:column headerValue="Startup No.">
                        <apex:outputLink value="/{!stObj.startup.Id}">{!stObj.startup.Name}</apex:outputLink>
                    </apex:column>
                    <apex:column value="{!stObj.startup.Public_Name__c}"/>
                    <apex:column value="{!stObj.startup.Portfolio_map_Sector__c}"/>
                    <apex:column value="{!stObj.startup.Industry__c}"/>  
                    <apex:column value="{!stObj.startup.Fundraise__c}"/>
                    <apex:column value="{!stObj.startup.Round__c}"/>
               <!--     <apex:column value="{!stObj.startup.Round__c}"/>
                    <apex:column value="{!stObj.startup.Fundraise__c}"/> -->
                    <!-- <apex:column value="{!stObj.noOfCriteriaMatch}" headerValue="No of criteria match"/> -->
                    
                    <apex:column >
                         <apex:facet name="header">   
                           <apex:commandLink action="{!toggleSort}" value="No of criteria match{!IF(sortDirvf ='asc','▼','▲')}" id="cmdSort" reRender="myPanel,myButtons">
                           </apex:commandLink>
                         </apex:facet>
                         <apex:outputtext value="{!stObj.noOfCriteriaMatch}"></apex:outputtext>
                    </apex:column> 
                    
                </apex:pageBlockTable>
                
            </apex:outputPanel>
            <apex:pageBlockButtons location="bottom">
                <apex:outputPanel id="myButtons" style="align:canter;">
                    <apex:commandButton action="{!Beginning}" title="First" value="<<" disabled="{!disablePrevious}" reRender="myPanel,myButtons"/>
                    <apex:commandButton action="{!Previous}" title="Previous" value="<" disabled="{!disablePrevious}" reRender="myPanel,myButtons"/>        
                    <apex:commandButton action="{!Next}" title="Next" value=">"  disabled="{!disableNext}" reRender="myPanel,myButtons"/>
                    <apex:commandButton action="{!End}" title="Last" value=">>" disabled="{!disableNext}" reRender="myPanel,myButtons"/>  
                </apex:outputPanel>
                </apex:pageBlockButtons>
        </apex:pageBlock>        
    </apex:form>
</apex:page>