// Author: Akash <PERSON>
public class AWSSignature {

    String region = '';
    String service = 'lambda';
    String key = ''; //AWS key
    String secret = ''; //AWS Secret key
    String host = 'bqdcji6ua3munvcdj7mu5xt3ka0etojt.lambda-url.ap-south-1.on.aws';
    string resource='';
    string method;
    string payload;
    String amzDate = Datetime.now().formatGMT('yyyyMMdd\'T\'HHmmss\'Z\'');
    String dateStamp = Datetime.now().formatGMT('yyyyMMdd');
    string payloadHash;
    
    private string createCanonicalRequest(){
        String canonicalUri = '/'+resource; 
        
        String canonicalQueryString = '';
        
        string canonicalHeaders=canonicalHeaders();
        
        String signedHeaders =signedHeaders();
       
        String canonicalRequest = method + '\n' 
            + canonicalUri + '\n'  
            + canonicalQueryString + '\n' 
            + canonicalHeaders + '\n' 
            + signedHeaders + '\n' 
            + payloadHash;
        return canonicalRequest;
    }
    
    private string createStringToSign(string canonicalRequest){
        String algorithm = algorithm();
        
        String credentialScope = credentialScope();
        
        String stringToSign = algorithm + '\n' +  amzdate + '\n' +  credentialScope + '\n' + 
            EncodingUtil.convertToHex(Crypto.generateDigest('sha256', Blob.valueOf(canonicalRequest)));
       
		return stringToSign;
    }
    
    private string calculateSignature(string stringToSign){
        Blob signingKey = createSigningKey(secret);
        
        String signature =  createSignature(stringToSign, signingKey); 
        
        return signature;
    }
    
    private string algorithm(){
        return 'AWS4-HMAC-SHA256';
    }
    private string credentialScope(){
        return datestamp + '/' + region + '/' + service + '/' + 'aws4_request';
    }
    private string signedHeaders(){
        return 'host;x-amz-content-sha256;x-amz-date';
    }
    private string canonicalHeaders(){
        return 'host:' + host + '\n' + 'x-amz-content-sha256:' + payloadHash + '\n' + 'x-amz-date:' + amzdate + '\n';
    }
    private string endpoint(){
        system.debug('endpoint: '+ 'https://' + host);
        return 'https://' + host ;
    }
    private Blob createSigningKey(String secretKey){
        Blob dateKey = signString(Blob.valueOf(datestamp),Blob.valueOf('AWS4'+secretKey));
        Blob dateRegionKey = signString(Blob.valueOf(region),dateKey);
        Blob dateRegionServiceKey = signString(Blob.valueOf(service),dateRegionKey);
        return signString(Blob.valueOf('aws4_request'),dateRegionServiceKey);
    }
    
    private Blob signString(Blob msg, Blob key){
        return Crypto.generateMac('HMACSHA256', msg, key);
    } 
    
    private String createSignature(String stringToSign, Blob signingKey){        
        return EncodingUtil.convertToHex(Crypto.generateMac('HMACSHA256', blob.valueof(stringToSign), signingKey));
    }
    
    public void setAWS(string servce, string k, string sct){
        region='ap-south-1';
        service=servce;
        key=k;
        secret=sct;
    }
    
    public void setUrl(string hst){
        host=hst;
    }
    
    public void setMethod(string mthd){
        method=mthd;
    }
    
    public void setPayloadString(String pyld){
        payload=pyld;
		Blob payloadBlob = Blob.valueOf(payload);
		payloadHash = EncodingUtil.convertToHex(Crypto.generateDigest('SHA-256', payloadBlob));
    }
    
    public HttpRequest createSigntaureRequest(){
        
        HttpRequest req = new HttpRequest();
        
        req.setMethod(method);
        string canonicalRequest=createCanonicalRequest();
		string stringToSign=createStringToSign(canonicalRequest);
        string calculatedSignature=calculateSignature(stringToSign);
        String authorizationHeader = algorithm() + ' ' 
            + 'Credential=' + key + '/' 
            + credentialScope() + ', ' 
            +  'SignedHeaders=' + signedHeaders() + ', ' 
            + 'Signature=' + calculatedSignature;
        
        req.setHeader('Authorization',authorizationHeader);
        req.setHeader('x-amz-date', amzdate);
        req.setHeader('x-amz-content-sha256', payloadHash);
        req.setEndpoint(endpoint());
        req.setBody(payload);
        
        return req;
    }
    
}