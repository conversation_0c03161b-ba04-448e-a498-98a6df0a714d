/****************************************************
    test class for CaseTrigger trigger
****************************************************/
@isTest(SeeAllData=false)
public class CaseTriggerTestClass{
    
    @testSetup static void setup() {
                  id recordTypeIdIPV = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
            List<Account> accList = new List<Account>();
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            for(Account accAux: accList)
            {
                accAux.RecordTypeId = recordTypeIdIPV;
                accAux.ShippingStreet       = '';
                accAux.ShippingState        = '';
                accAux.ShippingPostalCode   = '';
                accAux.ShippingCountry      = '';
                accAux.ShippingCity         = '';
                accAux.Description          = '';
                accAux.BillingStreet        = '';
                accAux.BillingState         = '';
                accAux.BillingPostalCode    = '';
                accAux.BillingCountry       = '';
                accAux.BillingCity          = '';
                accAux.Designation__c ='Test';
                accAux.Company__c = 'Codiot';
                accAux.Are_you_a_first_time_investor_c__c ='';
                accAux.Preferred_Email__c = 'Personal';
                accAux.Personal_Email__c = '<EMAIL>';
            }
            insert accList;
            System.debug('invTrigger Acct insert....13' + accList);    
        
            List<Contact> conList = new List<Contact>();
            integer i = 1;
            for(Account acc : accList)
            {
                Contact cont = new Contact();
                cont.FirstName='Test';
                cont.LastName='Test';
                cont.Email = i+'<EMAIL>';
                cont.Accountid= acc.id;
                cont.Investor_s_PAN__c = 'ABCDE'+i+'173D';
                cont.Investment_in_Own_Name_Family_Member__c = 'Own Name';
                cont.Send_Auto_Com_Email__c = true;
                conList.add(cont);
                i++;
            }
            insert conList;
          Account a = [select id from account limit 1];
          Case c = new Case();
         c.Priority = 'High';
         c.Status ='WIP';
         c.Issue_raised_By__c = a.Id;
         c.Date_Issue_Raised__c = date.today();
         c.Description = 'test desc';

         Profile p = [SELECT Id FROM Profile WHERE Name='Standard User']; 
            User u = new User(Alias = 'standt', Email='<EMAIL>', 
            EmailEncodingKey='UTF-8', LastName='Testing', LanguageLocaleKey='en_US', 
            LocaleSidKey='en_US', ProfileId = p.Id, 
            TimeZoneSidKey='America/Los_Angeles', UserName='<EMAIL>',Contact_No__c='1234567');

          System.runAs(u) {
              c.Responsibility_to_Solve__c = userinfo.getuserid();
          }
          
          insert c;
    }
    
    public TestMethod static void TestCaseTrigger()
    {

         Test.startTest();
        Test.setMock(HttpCalloutMock.class, new AppFutureCreateCaseAPIMock());
        Account a = [select id from account limit 1];

         Case c = new Case();
         c.Priority = 'High';
         c.Status ='WIP';
         c.Issue_raised_By__c = a.Id;
         c.Date_Issue_Raised__c = date.today();
         c.Description = 'test desc';

         Profile p = [SELECT Id FROM Profile WHERE Name='Standard User']; 
            User u = new User(Alias = 'standt', Email='<EMAIL>', 
            EmailEncodingKey='UTF-8', LastName='Testing', LanguageLocaleKey='en_US', 
            LocaleSidKey='en_US', ProfileId = p.Id, 
            TimeZoneSidKey='America/Los_Angeles', UserName='<EMAIL>',Contact_No__c='1234567');

          System.runAs(u) {
              c.Responsibility_to_Solve__c = userinfo.getuserid();
          }
          
          insert c;
        
          User usr= [select id from User where Profile.name = 'system administrator' and IsActive = true limit 1];
        SYSTEM.DEBUG('USR>>>>'+usr);
          c.Relevant_Team__c = 'Tech Related';
          c.Description ='Closed';
          c.Status ='Closed';
          c.Responsibility_to_Solve__c=userInfo.getUSerId();
          c.Responsibility_to_solve_Internal__c=userInfo.getUSerId();
          
          update c;

          Test.stopTest();
          delete c;
    }
   
}