public class GeneratePDFController {
    public List<Contact> cont{get;set;}
    public Contribution_Agreement__c con{get;set;}
    public List<Investment__c> invList{get;set;}
    public List<Investment__c> redList{get;set;}
    public List<Investment__c> offMarketSales{get;set;}
    public List<Investment__c> offMarketPurchase{get;set;}
    public List<Investment__c> feeList{get;set;}
    public List<Transaction__c> transList{get;set;}
    public List<Investment__c> rePaymentList{get;set;}
    public String currentRecordId{get;set;}
    public String pan{get;set;}
    public String CADate{get;set;}
    public String resStatus{get;set;}
    public String phone{get;set;}
    public date fromDate;
    public date toDate{get;set;}
    public String toDateWallet{get;set;}
    public decimal totalCommitAmt = 0;
    public decimal totalAddendumAmt = 0;
    public decimal totalCAAmt{get;set;}
    public decimal totalDrawdown{get;set;}
    public decimal balanceDrawdown{get;set;}
    public decimal walletBalance{get;set;}
    public decimal totalFeeAmt = 0;
    public decimal totalInvAmt = 0;
    public decimal totalIP = 0;
    public decimal totalIS = 0;
    public decimal totalInvestment = 0;
    public decimal totalTrans = 0;
    public decimal totalRedAmt = 0;
    set<Id> setCAId = new set<Id>();
    Map<Id,List<Contact>> mapCAContact = new Map<Id,List<Contact>>();
    Map<Id,List<Investment__c>> mapCAInvestment = new Map<Id,List<Investment__c>>();
    Map<Id,List<Investment__c>> mapCAExitInvestment = new Map<Id,List<Investment__c>>();
    Map<Id,List<Transaction__c>> mapCATransaction = new Map<Id,List<Transaction__c>>();
    
    public GeneratePDFController(){
        system.debug('called::');
        if(date.today().day() <15){
            CADate = DateTime.newInstance(date.today().year(), date.today().month()-1, 15).format('dd-MMM-yyyy');
        }else
            CADate = DateTime.newInstance(date.today().year(), date.today().month(), 15).format('dd-MMM-yyyy');
        currentRecordId  = ApexPages.CurrentPage().getparameters().get('id');
        if(ApexPages.CurrentPage().getparameters().get('fromDate') != null)
            fromDate = Date.valueOF(ApexPages.CurrentPage().getparameters().get('fromDate'));
        if(ApexPages.CurrentPage().getparameters().get('toDate') != null)
            toDate  = Date.valueOF(ApexPages.CurrentPage().getparameters().get('toDate'));
        system.debug('fromDate::'+fromDate);
        system.debug('toDate::'+toDate);
        system.debug('CADate::'+CADate);
        if(toDate == null){
            toDate = date.today();
            system.debug('innnnnnnnnnnnnnnnnnnnn::'+toDate);
        }
        if(fromDate == null || fromDate == Date.newInstance(2000, 01, 01)){
            CADate = 'As on ' + DateTime.newInstance(
                toDate.year(), toDate.month(), toDate.day()).format('dd-MMM-yyyy');
            fromDate = Date.newInstance(2000, 01, 01);
            system.debug('CADate::'+CADate);
        }else{
            system.debug('toDate::'+toDate.year());
            CADate = 'From ' + DateTime.newInstanceGmt(
                fromDate.year(), fromDate.month(), fromDate.day()).format('dd-MMM-yyyy') 
                + ' To ' + DateTime.newInstanceGmt(
                    toDate.year(), toDate.month(), toDate.day()).format('dd-MMM-yyyy');
            system.debug('CADate::'+CADate);
        }
        invList =  new List<investment__c>();
        redList =  new List<investment__c>();
        offMarketSales =  new List<investment__c>();
        offMarketPurchase =  new List<investment__c>();
        feeList =  new List<investment__c>();
        rePaymentList =  new List<investment__c>();
        system.debug('fromDate::'+fromDate);
        system.debug('toDate::'+toDate);
        toDateWallet = DateTime.newInstance(
                    toDate.year(), toDate.month(), toDate.day()).format('dd-MMM-yyyy');
        system.debug('currentRecordId::'+currentRecordId);
        if(currentRecordId != null && String.isNotBlank(currentRecordId)){
            updateCACommittTotal(currentRecordId,toDate);
            con = [SELECT Id,Name,Email_ID__c,Address__c,Foreingn_Address__c,Total_Drawdowns__c,Total_Contribution_Amount__c,
                   Wallet_Balances__c,Balance_Amounts__c,Nominee_1__c,Nominee_2__c,Nominee_3__c,Investor1__r.Name,Investor2__r.Name,
                   Bank_Name__c,Account_Number__c,Account_IFSC__c,
                   Virtual_Accounts__c,Virtual_IFSC__c,Investor1__r.Account.Relationship_Manager__r.NAme
                   FROM Contribution_Agreement__c WHERE ID = :currentRecordId];
            system.debug('con::'+con);  
            List<Investment__c> invLists = CARelatedListController.getInvestments(currentRecordId);
            if(invLists != null && !invLists.isEmpty()){
                for(Investment__c inv : invLists){
                    if(inv.Startup_Round__r.Round_Type__c == 'Internal Transfers' && inv.Type__c == 'Invested'){
                        if(inv.Date_on_Transfer_Deed__c >= fromDate && inv.Date_on_Transfer_Deed__c <= toDate){
                            invList.add(inv);
                        }
                    }                      	
                    else if(inv.Date_of_transaction__c >= fromDate && inv.Date_of_transaction__c <= toDate){
                        invList.add(inv);
                    }
                }
            }
            system.debug('invList::'+invList);
            List<Investment__c> redLists = CARelatedListController.getInvestmentsRedemption(currentRecordId);
            //List<Investment__c> redInv = CARelatedListController.getInvestmentsRedemption(currentRecordId);
            if(redLists != null && !redLists.isEmpty()){
                for(Investment__c inv : redLists){
                    if(inv.Date_of_SH_4__c >= fromDate && inv.Date_of_SH_4__c <= toDate){
                        redList.add(inv);
                    }
                }
            }
            system.debug('redList::'+redList);
            List<Investment__c> feeLists = CARelatedListController.getExit(currentRecordId);
            system.debug('feeLists::'+feeLists);
            if(feeLists != null && !feeLists.isEmpty()){
                for(Investment__c inv : feeLists){
                    if(inv.Date_of_transaction__c >= fromDate && inv.Date_of_transaction__c <= toDate ){                    
                        //if(inv.Type__c == 'Exit' || inv.Type__c == 'Partial Exit'){
                            //inv.Investment_Fee_Received__c = inv.Exit_Fee_received__c;
                       //// }
                        feeList.add(inv);
                    }
                }
            }
            system.debug('feeList::'+feeList);
            
            
             List<Investment__c> rePaymentLists = CARelatedListController.getRePayments(currentRecordId);
            if(rePaymentLists != null && !rePaymentLists.isEmpty()){
                for(Investment__c inv : rePaymentLists){
                    if(inv.Date_of_SH_4__c >= fromDate && inv.Date_of_SH_4__c <= toDate){                      
                        rePaymentList.add(inv);
                    }
                }
            }
            system.debug('rePaymentList::'+rePaymentList);
            
            List<Investment__c> offMarkets = CARelatedListController.getOffMarketInv(currentRecordId);
            if(offMarkets != null && !offMarkets.isEmpty()){
                for(Investment__c inv : offMarkets){
                    if(inv.Date_on_Transfer_Deed__c >= fromDate && inv.Date_on_Transfer_Deed__c <= toDate
                       && inv.Startup_Round__r.Round_Type__c == 'Exit'){
                           offMarketSales.add(inv);
                       }
                    if(inv.Date_on_Transfer_Deed__c >= fromDate && inv.Date_on_Transfer_Deed__c <= toDate
                       && inv.Startup_Round__r.Round_Type__c == 'Internal Transfers'){
                           offMarketPurchase.add(inv);
                       }
                }
            }
            system.debug('offMArkets::'+offMArketS);
            system.debug('offMArket::'+offMArketPurchase);
            DateTime toDateForTrans = DateTime.newInstance(toDate.year(), toDate.month(), toDate.Day(), 23, 59, 59);
            system.debug('toDateForTrans   ::'+toDateForTrans);
            transList = [SELECT Id,Transaction_Type__c,Transaction_Date__c,Amount__c
                         FROM Transaction__c WHERE Contribution_Agreement__c = :currentRecordId
                         AND Transaction_Date__c >= :fromDate and Transaction_Date__c <= :toDateForTrans];
           
            //added by vidhi 
            cont = [SELECT Id,Name,Contribution_Agreement__r.Investor1__r.Investor_s_PAN__c,phone,Residential_Status__c FROM Contact WHERE Contribution_Agreement__c = :currentRecordId];
            if(!cont.isEmpty()){
                pan = cont[0].Contribution_Agreement__r.Investor1__r.Investor_s_PAN__c;
                resStatus = cont[0].Residential_Status__c;
                if(resStatus == 'NRI'){
                    con.Address__c = con.Foreingn_Address__c;
                }
                phone = cont[0].phone;
            }
        }
        
    }
    
    @AuraEnabled
    public static String sendPdf(String recordId) {
        system.debug('recordId ::'+recordId);
        String CADate = DateTime.newInstance(
            date.today().year(), date.today().month(), date.today().day()).format('dd-MMM-yyyy');
        //recordId = 'a0P0l0000080zsTEAQ';        
        PageReference pdf = new pagereference('/apex/GeneratePDF?id='+recordId);
        string CANumber = [SELECT Id,Name FROM Contribution_Agreement__c WHERE ID = :recordId].Name;
        pdf.getParameters().put('id', recordId);
        system.debug('CADate ::'+CADate);
        Blob body;
        try {
            // returns page as a PDF
            body = pdf.getContent();
        } catch (Exception e) {
            body = Blob.valueOf('data');
        }
        String fileName = 'FirstPortal Capital - Unit Statement - ' + CANumber + ' - ' + CADate+'.pdf';
        Messaging.EmailFileAttachment attach = new Messaging.EmailFileAttachment();
        attach.setContentType('application/pdf');
        attach.setFileName(fileName);
        attach.Body = body;
        
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setToAddresses(new String[] {'<EMAIL>','<EMAIL>'});
        //mail.setToAddresses(new String[] {'<EMAIL>'});
        mail.setSubject('Contribution Agreement Details');
        mail.setHtmlBody('Contribution Agreement Details');
        mail.setFileAttachments(new Messaging.EmailFileAttachment[] {attach}); 
        // Send the email
        Messaging.sendEmail(new Messaging.SingleEmailMessage[] {mail});
        return 'Email sent Successfully with PDF.';
    }
    
    @AuraEnabled
    public static string sendMailPDF(String type,List<String> listCANumbers,date fromDate,date toDate){
        List<Contribution_Agreement__c> listCA = new List<Contribution_Agreement__c>();
        system.debug('type--- '+type+fromDate+toDate);
        if(fromDate == null){
            fromDate = Date.newInstance(2000, 01, 01);
        }
        if(toDate == null){
            toDate = date.today();
        }
        String CADate = DateTime.newInstance(
            date.today().year(), date.today().month(), date.today().day()).format('dd-MMM-yyyy');
        system.debug('type--- '+type+fromDate+toDate);
        system.debug('CADate--- '+CADate);
        if(type == 'all'){
            system.debug('listCANumbers--- '+listCANumbers+':fromDate:'+fromDate+':todate:'+toDate);
            listCA = [SELECT Id FROM Contribution_Agreement__c where Name IN : listCANumbers];
            //PageReference pdf = new pagereference('/apex/GeneratePDF?id='+listCA[0].Id+'&fromDate='+fromDate+'&toDate='+toDate);
            //pdf.getParameters().put('id', listCA[0].ID);
            system.debug('list  listCA--- '+listCA);
            return '/apex/GeneratePDF?id='+listCA[0].Id+'&fromDate='+fromDate+'&toDate='+toDate;
        }else if(type == 'exclude'){
            system.debug('listCANumbers--- '+listCANumbers);
            if(listCANumbers != null && !listCANumbers.isEmpty()){
                listCA = [SELECT Id FROM Contribution_Agreement__c WHERE Name NOT IN :listCANumbers ];
            }else{
                listCA = [SELECT Id FROM Contribution_Agreement__c limit 50000 ];                
            } 
            system.debug('list  listCA--- '+listCA.size());   
            //return null;
            
        }else if(type == 'include'){
            system.debug('listCANumbers--- '+listCANumbers);
            listCA = [SELECT Id FROM Contribution_Agreement__c WHERE Name IN :listCANumbers ];
            system.debug('list  listCA--- '+listCA);            
        }
        //String CADate = DateTime.newInstance(
        //   date.today().year(), date.today().month(), 15).format('dd-MMM-YYYY');
        List<Messaging.Email> messages = new List<Messaging.Email>();
        for(Contribution_Agreement__c CA : listCA){            
            //recordId = 'a0P0l0000080zsTEAQ';        
            PageReference pdf = new pagereference('/apex/GeneratePDF?id='+CA.ID);
            pdf.getParameters().put('id', CA.ID);
            
            Blob body;
            try {
                // returns page as a PDF
                body = pdf.getContent();
            } catch (Exception e) {
                body = Blob.valueOf('data');
            }
            Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
            String fileName = 'FirstPortal Capital - Unit Statement - ' + toDate+'.pdf';
            Messaging.EmailFileAttachment attach = new Messaging.EmailFileAttachment();
            attach.setContentType('application/pdf');
            attach.setFileName(fileName);
            attach.Body = body;
            
            
            mail.setToAddresses(new String[] {'<EMAIL>','<EMAIL>'});
            //mail.setToAddresses(new String[] {'<EMAIL>'});
            mail.setSubject('Contribution Agreement Details');
            mail.setHtmlBody('Contribution Agreement Details');
            mail.setFileAttachments(new Messaging.EmailFileAttachment[] {attach});         
            messages.add(mail);
        }
        Messaging.sendEmail(messages);
        return 'Email sent Successfully with PDF.';
    }
    
    @AuraEnabled
    public static string getPassword(String password){
        CA_Email_settings__c setting = CA_Email_settings__c.getInstance();
        if(setting.Password__c == password){
            return 'SUCCESS';
        }else{
            return 'FAIL';
        }
    }
    
    @AuraEnabled
    public static List<Contribution_Agreement__c> getAllCA(String searchText){
        searchText ='\'%' + searchText + '%\'';
        system.debug('serachText '+searchText);
        string query = 'SELECT Name from Contribution_Agreement__c where Name Like   ' + searchText ;
        //List<Contribution_Agreement__c> listCA = [SELECT Name from Contribution_Agreement__c where Name Like : searchText];
        system.debug('query ::'+query);
        List<Contribution_Agreement__c> listCA = database.query(query);
        return listCA;
    }
    
    @AuraEnabled
    public static void sendEmailToAll(List<String> caNumber,date fromDate,date toDate){
        if(fromDate == null){
            fromDate = Date.newInstance(2000, 01, 01);
        }
        if(toDate == null){
            toDate = date.today();
        }
        system.debug('caNumber ::'+caNumber);
        system.debug('toDate ::'+toDate);
        List<Contribution_Agreement__c> lstCA = [SELECT Id,Total_Committed_Amount__c,Total_Interse_Purchase__c,Total_Interse_Sale__c,Total_Fees__c,Total_Investment_Amount__c,Total_Transfer__c,
                                                 (SELECT Id,Amount__c from Transactions__r ),
                                                 (SELECT Id,AIF_Document_Amount__c,Document_Name__c from Documents__r ),
                                                 (SELECT Id,Name,Type__c,Startup_Round__r.Round_Type__c,Date_of_transaction__c,Date_on_Transfer_Deed__c,
                                                  Exit_Fee_received__c,Investment_Amount_Balance__c,Startup_Round__r.Type_of_Event__c,Parent_Investment__c,
                                                  Investment_Amount__c,Considered_Investment_Amount__c,Final_Commitment_Amount__c,Investor_Type__c,Investment_Fee_Received__c FROM Investment__r Where Investor_Type__c = 'Via AIF')
                                                 FROM Contribution_Agreement__c WHERE Name IN :caNumber];
        system.debug('lstCA ::'+lstCA);
        CAPDFBatch caBatch = new CAPDFBatch();
        caBatch.sessionId = UserInfo.getSessionId();
        caBatch.caIds = caNumber;
        caBatch.fromDate = fromDate;
        caBatch.toDate = toDate;
     	Database.executeBatch(caBatch);  
    }
    
    Public void updateCACommittTotal(String CAID,date toDate)
    {
        totalCAAmt = 0;
        totalDrawdown= 0;
        balanceDrawdown= 0;
        walletBalance= 0;
        totalRedAmt = 0;
        system.debug('CAID.keySet() ::'+CAID);
        DateTime toDateForTrans = DateTime.newInstance(toDate.year(), toDate.month(), toDate.Day(), 23, 59, 59);
        List<Contribution_Agreement__c> lstCA = [SELECT Id,Total_Drawdowns__c,Wallet_Balances__c,Total_Committed_Amount__c,Total_Interse_Purchase__c,Total_Interse_Sale__c,Total_Fees__c,Total_Investment_Amount__c,Total_Transfer__c,
                                                 (SELECT Id,Amount__c from Transactions__r where transaction_date__c <= :toDateForTrans),
                                                 (SELECT Id,AIF_Document_Amount__c,Document_Name__c from Documents__r where FPC_Signing_Date__c <= :toDate),
                                                 (SELECT Id,Name,Type__c,Startup_Round__r.Round_Type__c,Date_of_transaction__c,Date_on_Transfer_Deed__c,
                                                  Exit_Fee_received__c,Investment_Amount_Balance__c,Startup_Round__r.Type_of_Event__c,Parent_Investment__c,Date_of_SH_4__c,
                                                  Investment_Amount__c,Considered_Investment_Amount__c,Final_Commitment_Amount__c,Investor_Type__c,Investment_Fee_Received__c FROM Investment__r Where Investor_Type__c = 'Via AIF')
                                                 FROM Contribution_Agreement__c WHERE Id = :CAID];
        system.debug('lstCA ::'+lstCA);
        for(Contribution_Agreement__c ca : lstCA){            
            for(Document__c doc : ca.Documents__r){
                if(doc.Document_Name__c.contains('Contribution Agreement') && doc.AIF_Document_Amount__c != null){
                    totalCommitAmt += doc.AIF_Document_Amount__c;
                }
                if(doc.Document_Name__c.contains('Addendum') && doc.AIF_Document_Amount__c != null){
                    totalAddendumAmt += doc.AIF_Document_Amount__c;
                }
            }
            totalCAAmt = totalCommitAmt + totalAddendumAmt;
            for(Investment__c inv : ca.Investment__r){
                if(inv.Parent_Investment__c == null  && inv.Date_of_transaction__c <= toDate 
                  	&& ((((inv.Type__c == 'Invested' || inv.Type__c == 'Internal Transfers') 
                                          			&& inv.Investor_Type__c == 'Via AIF') 
                                          && (inv.Startup_Round__r.Round_Type__c == 'Raise' || inv.Startup_Round__r.Round_Type__c == 'Pre-emptive'))
                                              || 
                                              ((inv.Type__c == 'Exit' || inv.Type__c == 'Partial Exit') && inv.Investor_Type__c == 'Via AIF' && inv.Startup_Round__r.Round_Type__c == 'Raise' 
                                              )) 
                  ){
                      if(inv.Type__c == 'Exit' && inv.Exit_Fee_received__c != null){
                          totalFeeAmt += inv.Exit_Fee_received__c;
                      }else if(inv.Investment_Fee_Received__c != null){
                          totalFeeAmt += inv.Investment_Fee_Received__c;
                      }
                }
                if(inv.Date_of_transaction__c <= toDate && ((inv.Type__c == 'Invested' && inv.Investor_Type__c == 'Via AIF') && (inv.Startup_Round__r.Round_Type__c == 'Raise' || inv.Startup_Round__r.Round_Type__c == 'Pre-emptive'))){
                    if(inv.Investment_Amount__c != null)
                    	totalInvAmt += inv.Investment_Amount__c;
                }
               /* if(inv.Date_of_transaction__c <= toDate && (((inv.Type__c == 'Exit' || inv.Type__c == 'Partial Exit' || inv.Type__c == 'Invested' || inv.Type__c == 'Internal Transfers' ) && inv.Investor_Type__c == 'Via AIF') 
                                                            && (inv.Startup_Round__r.Round_Type__c == 'Raise' || inv.Startup_Round__r.Round_Type__c == 'Pre-emptive'))
                  ){
                      
                      if(inv.Parent_Investment__c == null && inv.Investment_Fee_Received__c != null){
                          totalFeeAmt += inv.Investment_Fee_Received__c;
                      }
                      
                      
                      if(inv.Type__c == 'Invested' ){                          
                          if(inv.Investment_Amount__c != null){
                              totalInvAmt += inv.Investment_Amount__c;     
                          }
                          
                      }                
                  }*/
                if(inv.Date_on_Transfer_Deed__c <= toDate && (inv.Type__c == 'Invested' && inv.Investor_Type__c == 'Via AIF' && inv.Startup_Round__r.Round_Type__c == 'Internal Transfers' && inv.Startup_Round__r.Type_of_Event__c == 'Internal Transfer of Units' )){
                    if(inv.Considered_Investment_Amount__c != null)
                        totalIP += inv.Considered_Investment_Amount__c;
                }
                if(inv.Date_of_SH_4__c <= toDate && (inv.Type__c == 'Exit' && inv.Investor_Type__c == 'Via AIF' && inv.Startup_Round__r.Round_Type__c == 'Exit' && (inv.Startup_Round__r.Type_of_Event__c == 'Full Redemption of Units' || inv.Startup_Round__r.Type_of_Event__c == 'Partial Redemption of Units'))){
                    system.debug('toDate :'+inv.Investment_Amount__c);
                    totalRedAmt += inv.Investment_Amount__c;
                }
                if(inv.Date_on_Transfer_Deed__c <= toDate && (inv.Type__c == 'Exit' && inv.Investor_Type__c == 'Via AIF' && inv.Startup_Round__r.Round_Type__c == 'Exit' && inv.Startup_Round__r.Type_of_Event__c == 'Internal Transfer of Units' )){
                    if(inv.Investment_Amount__c != null)
                        totalIS += inv.Investment_Amount__c;
                }
                
            } 
            for(Transaction__c trans : ca.Transactions__r){
                if(trans.amount__c != null)
                    totalTrans += trans.amount__c;
            }
            totalDrawdown = totalInvAmt + totalFeeAmt + totalIP + totalRedAmt;
            BalanceDrawdown = totalCAAmt - totalDrawdown;
            walletBalance = totalTrans - totalInvAmt - totalFeeAmt - totalIS - totalRedAmt;
            system.debug('lstCA ::'+lstCA);
        }
    }
    
    /*@AuraEnabled
    public static void sendAllMail(String csvFileBody){
        system.debug('csvFileBody ::'+csvFileBody);
        String csvAsString = Blob.valueOF(csvFileBody).toString();
        system.debug('csvAsString ::'+csvAsString);
        // Split CSV String to lines
        csvAsString = csvAsString.replaceAll('(\r\n|\r)','\n');
        String[] csvFileLines = csvAsString.split('\n');
        system.debug('csvFileLines ::'+csvFileLines.size());
        for(Integer i=1; i <= csvFileLines.size() - 1; i++){
            String[] csvRecordData = csvFileLines[i].split(',');    
            system.debug('csvRecordData[0] ::'+csvRecordData[0]+'::');
        }
    }*/
    
    
   /* public static PageReference downloadAll(){
        ContentFolder newFolder = new ContentFolder(
            Name = 'Test--'+ String.valueOf(Math.random() * 10)
        );
        insert newFolder;List<Id> contentDocumentIds = new List<Id>();
        Id contentFolderId = newFolder.Id;
        List<Contribution_Agreement__c> listCA = [SELECT Id,Name FROM Contribution_Agreement__c limit 5 ];
        //PageReference pdf = new pagereference('/apex/GeneratePDF?id='+listCA[0].Id+'&fromDate='+fromDate+'&toDate='+toDate);
        //pdf.getParameters().put('id', listCA[0].ID);
        system.debug('list  listCA--- '+listCA);
        List<contentVersion> contentDocumentsToInsert = new List<contentVersion>();
        
        List<Attachment> attachmentsToInsert = new List<Attachment>();
        for(Contribution_Agreement__c ca : listCA){
            system.debug('list  ca--- '+ca);
            //return new pagereference('/apex/GeneratePDF?id='+ca.Id);
            PageReference PDf =  Page.GeneratePDF;//Replace attachmentPDf with the page you have rendered as PDF
            PDf.getParameters().put('id',ca.Id);
            PDf.setRedirect(true);
            Blob pdfFileData = PDf.getContent(); // Replace with your PDF file data
            String pdfFileName = 'CA --'+ca.Name+'.pdf'; // Replace with your PDF file name
            
            ContentVersion contentVersion = new ContentVersion(
                Title = pdfFileName,
                PathOnClient = pdfFileName,
                VersionData = pdfFileData,
                IsMajorVersion = true
            );
            contentDocumentsToInsert.add(contentVersion);
        }
        insert contentDocumentsToInsert;
        
        List<ContentDocumentLink> contentDocumentLinksToInsert = new List<ContentDocumentLink>();
        for(ContentVersion cv : contentDocumentsToInsert){
            Id conDocId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id =:cv.Id].ContentDocumentId;
            ContentDocumentLink cdl = New ContentDocumentLink();
            cdl.LinkedEntityId = contentFolderID;
            cdl.ContentDocumentId = conDocId;
            cdl.shareType = 'V';
            contentDocumentLinksToInsert.add(cdl);
        }
        insert contentDocumentLinksToInsert;
        
        List<ContentDocumentLink> contentLinks = [
            SELECT ContentDocumentId
            FROM ContentDocumentLink
            WHERE LinkedEntityId = :contentFolderId
        ];
        system.debug('contentLinks ::'+contentLinks);
        for(ContentDocumentLink cdl : contentLinks) {
                contentDocumentIds.add(cdl.ContentDocumentId);
            }
        List<String> downloadUrls = new List<String>();
        for (Id docId : contentDocumentIds) {
            String downloadUrl = URL.getSalesforceBaseUrl().toExternalForm() +
                '/sfc/servlet.shepherd/version/download/' + docId;
            downloadUrls.add(downloadUrl);
        }
        PageReference redirectPage = new PageReference(downloadUrls[0]); // Redirect to the first URL
        redirectPage.setRedirect(true);
        return redirectPage;      
    
    }*/
}