@isTest(SeeAllData=false)
private class B2BuserOnboardingAPITest {
    
    @testSetup
    static void setup() {
        
        // Create parent account
        Account spocAcc = new Account(
            Name = 'Parent Account',
            Personal_Email__c = '<EMAIL>',
            Primary_Country_Code__c = 91,
            Primary_Contact__c = '**********',
            Preferred_Email__c = 'Personal',
            Lead_Source__c = 'ORAI',
            Relationship_Manager__c = UserInfo.getUserId(),
            Date_of_addition__C = date.today(),
            Primary_Group_Name__c ='IPV 1',
            Title__c = 'Mr'
        );
        insert spocAcc;
        
        Account parentAccount = new Account(
            Name = 'Parent Account',
            Is_Tenant__c = true,
            Tenant_SPOC_Account__c = spocAcc.Id,
            Personal_Email__c = '<EMAIL>',
            Partner_Type__c = 'Individual',
            Primary_Country_Code__c = 91,
            Primary_Contact__c = '**********',
            Preferred_Email__c = 'Personal',
            Lead_Source__c = 'ORAI',
            Relationship_Manager__c = UserInfo.getUserId(),
            Date_of_addition__C = date.today(),
            Primary_Group_Name__c ='IPV 1',
            Title__c = 'Mr'
        );
        insert parentAccount;
        
        // Create L1 manager account
        Account l1ManagerAccount = new Account(
            Name = 'L1 Manager Account',
            Partner_parent_account__c = parentAccount.Id,
            Personal_Email__c = '<EMAIL>',
            Primary_Contact__c = '**********',
            Primary_Country_Code__c = 91,
            Preferred_Email__c = 'Personal',
            Lead_Source__c = 'ORAI',
            B2B_User_Type__c = 'L1',
            Relationship_Manager__c = UserInfo.getUserId(),
            Title__c = 'Mr',
            Date_of_addition__C = date.today(),
            Primary_Group_Name__c ='IPV 1',
            ParentId = parentAccount.Id
        );
        insert l1ManagerAccount;
        
        // Create Org RM account
        Account orgRMAccount = new Account(
            Name = 'Org RM Account',
            Partner_parent_account__c = parentAccount.Id,
            Personal_Email__c = '<EMAIL>',
            Primary_Contact__c = '**********',
            Primary_Country_Code__c = 91,
            B2B_User_Type__c = 'L1',
            Preferred_Email__c = 'Personal',
            Lead_Source__c = 'ORAI',
            Relationship_Manager__c = UserInfo.getUserId(),
            Date_of_addition__C = date.today(),
            Primary_Group_Name__c ='IPV 1',
            Title__c = 'Mr'
        );
        insert orgRMAccount;
        
        // Create child account associated with the parent, L1 manager, and Org RM accounts
        Account childAccount = new Account(
            Name = 'Child Account',
            Partner_parent_account__c = parentAccount.Id,
            L1_manager__c = l1ManagerAccount.Id,
            Org_RM__c = orgRMAccount.Id,
            Personal_Email__c = '<EMAIL>',
            Primary_Contact__c = '**********',
            Primary_Country_Code__c = 91,
            B2B_User_Type__c = 'L2',
            Is_Tenant__c = false,
            Preferred_Email__c = 'Personal',
            Lead_Source__c = 'ORAI',
            Relationship_Manager__c = UserInfo.getUserId(),
            Date_of_addition__C = date.today(),
            Primary_Group_Name__c ='IPV 1',
            Title__c = 'Mr'
        );
        insert childAccount;   
        
        Account childAccount2 = new Account(
            Name = 'Child Account 2',
            Partner_parent_account__c = parentAccount.Id,
            L1_manager__c = l1ManagerAccount.Id,
            Org_RM__c = orgRMAccount.Id,
            Personal_Email__c = '<EMAIL>',
            Primary_Contact__c = '**********',
            Primary_Country_Code__c = 91,
            B2B_User_Type__c = 'Customer',
            Is_Tenant__c = false,
            Preferred_Email__c = 'Personal',
            Lead_Source__c = 'ORAI',
            Relationship_Manager__c = UserInfo.getUserId(),
            Date_of_addition__C = date.today(),
            Primary_Group_Name__c ='IPV 1',
            Title__c = 'Mr'
        );
        insert childAccount2;  
    }
    
    @isTest
    static void testSendAccountDetails() {
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseController());
        
        List<Account> childAccounts = [SELECT Id, Name FROM Account WHERE Name = 'Child Account'];
        Set<Id> accountIds = new Set<Id>();
        for(Account acc : childAccounts) {
            accountIds.add(acc.Id);
        }
        
        List<Account> childAccount2 = [SELECT Id FROM Account WHERE Name = 'Child Account 2'];
        Set<Id> accIds = new Set<Id>();
        for(Account acc : childAccount2) {
            accIds.add(acc.Id);
        }
        
        Test.startTest();
        B2BuserOnboardingAPI.sendAccountDetails(accountIds, true); 
        B2BuserOnboardingAPI.sendAccountDetails(accIds, true); 
        Test.stopTest();
    }
}