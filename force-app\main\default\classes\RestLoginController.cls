global class RestLoginController {
    
    public static string loginExternalSystem(){
        Map<String,String> credMAp = new Map<String,String>();
        credMAp.put('password','hello');
        credMAp.put('mobile','8866104284');
        credMAp.put('country_code','+91');
        String endURLSetting;

        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        //system.debug('RestLoginController API_Setting__c>>>'+settingList);
        if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].End_URL__c))
        {
            endURLSetting = ''+settingList[0].End_URL__c;
        }
        if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Username__c))
        {
            credMAp.put('mobile',''+settingList[0].Mobile_Username__c);
        }
        if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Password__c))
        {
            credMAp.put('password',''+settingList[0].Mobile_Password__c);
        }
        
        //system.debug('--credMAp--'+credMAp);
        system.debug('--endURLSetting--'+endURLSetting);

        HttpRequest req = new HttpRequest();
        String enddURL = endURLSetting+'/userAuthentication/login-admin';            
        String body =  JSON.serialize(credMAp);            
        req.setEndpoint(enddURL);
        req.setHeader('Content-Type','application/json');
        req.setMethod('POST');
        req.setTimeout(120000);
        req.setBody(body);
        
        Http http = new Http();
        HTTPResponse res = http.send(req);
        System.debug('res---'+res.toString());
        if(res.getStatusCode() == 200){
                system.debug('Data sent'+res.getBody());
                String jsonstr = JSON.serialize(res.getBody());
                JSONParser parser = JSON.createParser(jsonStr);
                Map<String, Object> results = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                String tkn = JSON.serialize(results.get('data'));
                Map<String, Object> token = (Map<String, Object>) JSON.deserializeUntyped(tkn);
                String accessToken = String.valueOF(token.get('token'));                
                system.debug('accessToken in add  '+accessToken);
            return accessToken;
        }
        return null;
    }
    
    @future(callout=true)
    public static void genericBulkDeleteAPI(set<ID> idsToDelete,string fieldJSONName,string endURL){
    Try{
        String accessToken = loginExternalSystem();
        system.debug('token ---'+accessToken);
        system.debug('--set of ids :'+JSON.serialize(idsToDelete));
        String ids = JSON.serialize(idsToDelete);
        /*JSONGenerator jsonGen = JSON.createGenerator(true);
        jsonGen.writeStartObject(); 
         jsonGen.writeObjectField(fieldJSONName,'["a0B0l000008YmWREA0","a0B0l000008s8lmEAA"]');                              
        jsonGen.writeEndObject(); 
        string genString = jsonGen.getAsString();
        genString = genString.replace('\\"', '"');        
        system.debug('--delete body :'+genString);*/
        
        String endURLSetting;

        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        //system.debug('RestLoginController API_Setting__c>>>'+settingList);
        if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].End_URL__c))
        {
            endURLSetting = ''+settingList[0].End_URL__c;
        }
        system.debug('--endURLSetting--'+endURLSetting);
        
        
        HttpRequest req = new HttpRequest();
        endURL = endURLSetting+'/'+endURL;   
        system.debug('--URL :'+endURL);            
        req.setEndpoint(endURL);
        req.setHeader('Authorization','Bearer ' + accessToken);
        req.setHeader('Content-Type','application/json');
        req.setMethod('DELETE');
        req.setTimeout(120000);
        string body = '{\r\n "'+fieldJSONName+'" : '+ids+'\r\n }';
        system.debug(body);
        req.setBody(body);
        Http http = new Http();
        HTTPResponse res = http.send(req);
        system.debug(res.toString());
        
        }
        catch(Exception ex){
            system.debug('Error '+ex);
            Error_Log__c log =  new Error_Log__c();
            log.Class_Name__c = 'restLoginController';
            log.method__c = 'genericBulkDeleteAPI' ;
            log.Error_Message__c = ex.getMessage();
            insert log;
        }
    }
}