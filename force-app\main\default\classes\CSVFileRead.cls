public class CSVFileRead {
    
    public Blob csvFileBody{get;set;}
    public String csvAsString{get;set;}
    public String[] csvFileLines{get;set;}
    public List<account> accountlist{get;set;}
    public List<String> failedList;
    public Map<string,String> accountMap{get;set;}
    public Map<string,String> beneficiaryNameMap{get;set;}
    public Map<string,String> verifiedDateMap{get;set;}
    public String acNumber{get;set;}
    public String IFSCNo{get;set;}
    public String acName{get;set;}
    public String bankName{get;set;}
    public String contriNo{get;set;}
    
    public CSVFileRead(ApexPages.StandardController controller){
        accountMap = new Map<string,String>();
        beneficiaryNameMap = new Map<string,String>();
        verifiedDateMap = new Map<string,String>();
        acName = '';
    }
    
    public void importSingleData(){
        verifyBankData(acNumber,IFSCNo,acName);
        String transId;        
        String reason = '';
        failedList = new List<String> ();
        map<String,Bank_Account__c> mapBankAcc = new map<String,Bank_Account__c>();
        
        List<Bank_Account__c> banks = [select Id,Bank_name__c,Bank_Transaction_Id__c,Date_of_PD__c,Contribution_Agreement__c,Contribution_Agreement__r.Name,Account_Number__c, Reason_for_failure__c, Penny_Drop__c from Bank_Account__c  
                                       WHERE Contribution_Agreement__r.Name =: contriNo];
        List<Contribution_Agreement__c> ca = [select id from Contribution_Agreement__c where name = :contriNo];
        system.debug('');
        if(ca.isEmpty()){
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error,'Please Enter Valid CA :'));
            return;
        }
        if(accountMap.containsKey(acNumber)){ 
            if(accountMap.get(acNumber).contains(':')){
                transId = accountMap.get(acNumber).SubStringBefore(':');
                reason = accountMap.get(acNumber).SubStringAfter(':');
            }else{
                transId = accountMap.get(acNumber); 
            }
        }
        system.debug('transId ::'+transId);
        //system.debug('banks ::'+banks);
        if(!banks.isEmpty()){
            for(Bank_Account__c bank : banks){
                mapBankAcc.put(bank.Account_Number__c,bank);            
            } 
        }
        
        bank_Account__c bank = new bank_Account__c();
        bank.Account_Number__c = acNumber;
        bank.Bank_Name__c = bankName;
        system.debug('beneficiaryNameMap.get(acNumber) ::'+beneficiaryNameMap.get(acNumber));
        bank.Account_Holder_Name__c = beneficiaryNameMap.get(acNumber);
        if(mapBankAcc != null && mapBankAcc.containsKey(acNumber)){
            bank = mapBankAcc.get(acNumber);
        }        
        //system.debug('bank ::'+bank);
        if(reason != ''){    
            bank.Penny_Drop__c = 'Failed';
            bank.Reason_for_failure__c = reason;
            failedList.add(contriNo);
        }else{
            bank.Penny_Drop__c = 'Yes';
            bank.Reason_for_failure__c = '';
        }
        bank.IFSC__c = IFSCNo;
        if(ca.size()>0)
            bank.Contribution_Agreement__c = ca[0].id;
        bank.Bank_Transaction_Id__c = transId;
        bank.Date_of_PD__c = Date.valueOf(verifiedDateMap.get(acNumber));
        try{
            system.debug('bank ::'+bank);            
            upsert bank;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.CONFIRM,'successfull transaction'));
        }
        catch(exception ex){
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.INFO,'Failed records :'+failedList));
        }       
        if(!failedList.isEmpty()){
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.INFO,'Failed records :'+failedList));
        }    
    }    
    
    public void importBankData(){
        set<Double> accNoSet = new set<double>();
        set<String> CASet = new set<String>();
        failedList = new List<String> ();
        List<Bank_Account__c> bankList = new List<Bank_Account__c>();
        accountMap = new Map<string,String>();
        Map<String,List<double>> accCAMap = new Map<String,List<double>>();
        Map<String,List<Bank_Account__c>> accCABankMap = new Map<String,List<Bank_Account__c>>();
        csvAsString = csvFileBody.toString();
        // Split CSV String to lines
        csvAsString = csvAsString.replaceAll('(\r\n|\r)','\n');
        csvFileLines = csvAsString.split('\n');
        //system.debug('csvFileLines ::'+csvFileLines.size());
        for(Integer i=1; i <= csvFileLines.size() - 1; i++){
            String[] csvRecordData = csvFileLines[i].split(',');
            
            system.debug('csvRecordData[0] ::'+csvRecordData.size()+'::');
            //system.debug('csvRecordData[2] ::'+csvRecordData[2]+'::');
            //system.debug('csvRecordData[3] ::'+csvRecordData[3]+'::');
            string accName = null;
            string bankkName = null;
            string caNo = String.valueOf(csvRecordData[0]);
            string accNumber = csvRecordData[1];
            string IFSC = csvRecordData[2];
            if(csvRecordData.size()-1 == 3){
                accName = csvRecordData[3];
            }
            if(csvRecordData.size()-1 == 4){
                bankkName = csvRecordData[4];
            }
            
            Bank_Account__c bankObj = new Bank_Account__c();
            bankObj.Account_Holder_Name__c = accName;
            bankObj.Account_Number__c = accNumber;
            bankObj.IFSC__c = IFSC;
            bankObj.Bank_Name__c = bankkName;
            system.debug('CASet::'+CASet+'::');
            CASet.add(caNo);
            system.debug('caNo::'+caNo+'::');
            system.debug('CASet::'+CASet+'::');
            system.debug('IFSC::'+IFSC+'::');
            system.debug('accName::'+accName+'::');
            verifyBankData(accNumber,IFSC,accName);
            accNoSet.add(double.valueOf(accNumber));
            if(accCAMap.containsKey(csvRecordData[0])){
                List<Bank_Account__c> acclist = accCABankMap.get(csvRecordData[0]);
                accList.add(bankObj);
                //accList.add(double.valueOf(accNumber));
                accCABankMap.put(csvRecordData[0],accList);
            }
            else{
                accCABankMap.put(csvRecordData[0],new List<Bank_Account__c>{bankObj});
                //accCAMap.put(csvRecordData[0],new List<double>{double.valueOf(accNumber)});
            }
            
        }
        
        system.debug('CASet ::'+CASet.size()+' '+CASet);
        map<string,ID> mapCA = new map<String,ID>();
        List<Contribution_Agreement__c> caList = [SELECT id,Name FROM Contribution_Agreement__c WHERE Name IN : CASet];
        for(Contribution_Agreement__c ca : caList){
            mapCA.put(ca.name,ca.id);
        }
        system.debug('mapCA ::'+mapCA);
        map<String,Bank_Account__c> mapBankAcc = new map<String,Bank_Account__c>();
        List<Bank_Account__c> banks = [select Id,Bank_Name__c,Date_of_PD__c,Contribution_Agreement__r.Name,Contribution_Agreement__c,Account_Number__c, Reason_for_failure__c, Penny_Drop__c from Bank_Account__c  
                                       WHERE Contribution_Agreement__r.Name IN : CASet];
        //system.debug('banks ::'+banks);
        for(Bank_Account__c bank : banks){
            mapBankAcc.put(bank.Account_Number__c,bank);            
        }
        
        for(String ca : accCABankMap.keySet()){
            List<double> lstAccNumber = accCAMap.get(ca);
            List<bank_account__c> bankss = accCABankMap.get(ca);
            for(bank_account__c accNo : bankss){
                String transId;
                String reason = '';
                if(accountMap.containsKey(accNo.Account_Number__c)){ 
                    system.debug('--------'+accountMap.get(accNo.Account_Number__c));
                    if(accountMap.get(accNo.Account_Number__c).contains(':')){
                        transId = accountMap.get(accNo.Account_Number__c).SubStringBefore(':');
                        reason = accountMap.get(accNo.Account_Number__c).SubStringAfter(':');
                    }else{
                        transId = accountMap.get(accNo.Account_Number__c); 
                    }
                }
                if(mapBankAcc.containsKey(accNo.account_number__c)){
                    bank_Account__c  bank = mapBankAcc.get(accNo.account_number__c);
                    //system.debug('bank ::'+bank);
                    if(reason != ''){    
                        bank.Penny_Drop__c = 'Failed';
                        bank.Reason_for_failure__c = reason;
                        failedList.add(mapBankAcc.get(accNo.account_number__c).Contribution_Agreement__r.Name);
                    }else{                    
                        bank.Date_of_PD__c = Date.valueOf(verifiedDateMap.get(accNo.account_number__c));
                        bank.Penny_Drop__c = 'Yes';
                        bank.Reason_for_failure__c = '';
                    }
                    bank.Account_holder_Name__c = beneficiaryNameMap.get(accNo.account_number__c);
                    bank.Bank_Transaction_Id__c = transId;
                    bank.Bank_Name__c = accNo.Bank_Name__c;
                    bankList.add(bank);
                }else{
                    //system.debug('mapCA.get(ca); ::'+mapCA.get(ca));
                    bank_Account__c bank = new bank_Account__c();
                    bank.Contribution_Agreement__c = mapCA.get(ca);
                    bank.Account_Holder_Name__c = beneficiaryNameMap.get(accNo.Account_Number__c);
                    bank.IFSC__c = accNo.IFSC__c;
                    bank.Account_Number__c = accNo.Account_Number__c;
                    bank.Bank_Transaction_Id__c = transId;
                    bank.Bank_Name__c = accNo.Bank_Name__c;
                    if(reason != ''){                
                        bank.Reason_for_failure__c = reason;
                        bank.Penny_Drop__c = 'Failed';                        
                    }else{
                        bank.Penny_Drop__c = 'yes';
                        bank.Reason_for_failure__c = '';
                        bank.Date_of_PD__c = Date.valueOf(verifiedDateMap.get(accNo.account_number__c));
                    }
                    bankList.add(bank);
                }
            }
        }
        system.debug('bankList; ::'+bankList);
        try{
            upsert bankList; 
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.CONFIRM,'Transaction Successful..'));
        }catch(Exception e){
            System.Debug('error :'+e.getLineNumber()+'::'+e.getMessage());
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error,'Transaction Not Successful'));
        }
        if(!failedList.isEmpty()){
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.INFO,'Failed records :'+failedList));
        }
        
    }
    
    public void importCSVFile(){
        //try{
        // Read CSV file body and store it in variable
        DateTime currentDate = date.today().addDays(10);
        String curDate = currentDate.format('dd.MM.yyyy');
        csvAsString = csvFileBody.toString();
        // Split CSV String to lines
        csvFileLines = csvAsString.split('\r');
        String[] Addresses;
        List<Id> docId = new List<Id>();
        List<String> Names = new List<String>();
        List<Document> lstDoc = new List<Document>([SELECT Id,Name,Body,ContentType FROM Document WHERE DeveloperName  = 'Individual_documents_Part_2' 
                                                    OR DeveloperName  = 'Individual_documents_Part_1' OR DeveloperName  = 'Individual_documents_Part_3'
                                                    OR DeveloperName  = 'Individual_documents_Part_4']);
        //docId.addAll(docMap.keySet());
        List<document> lstDocNonI = [SELECT Id,Name,Body,ContentType FROM Document WHERE DeveloperName  = 'Non_Individual_documents_Part_2' 
                                     OR DeveloperName  = 'Non_Individual_documents_Part_1' OR DeveloperName  = 'Non_Individual_documents_Part_3'
                                     OR DeveloperName  = 'Non_Individual_documents_Part_4' OR DeveloperName  = 'Non_Individual_documents_Part_5'
                                     OR DeveloperName  = 'Non_Individual_documents_Part_6'];
        
        EmailTemplate et = [SELECT Id,HTMLValue FROM EmailTemplate WHERE DeveloperName = 'Onboarding_Email_Tamplate'];
        EmailTemplate etJoin = [SELECT Id,HTMLValue FROM EmailTemplate WHERE DeveloperName = 'Onboarding_Email_Tamplate_for_Join'];
        List<Messaging.SingleEmailMessage> mails = new List<Messaging.SingleEmailMessage>();
        // Iterate CSV file lines and retrieve one column at a time.
        
        for(Integer i=1; i < csvFileLines.size() - 1; i++){
            String[] csvRecordData = csvFileLines[i].split(',');             
            String type = csvRecordData[5];
            if(type != null && type.equalsIgnoreCase('Individual')){ 
                List<Messaging.EmailFileAttachment> attachments = new List<Messaging.EmailFileAttachment>();
                for (document doc: lstDoc) {
                    Messaging.EmailFileAttachment efa = new Messaging.EmailFileAttachment();
                    efa.setFileName(doc.Name);
                    efa.setBody(doc.Body);
                    efa.setContentType(doc.ContentType);
                    attachments.add(efa);
                }
                Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
                mail.setTemplateId(et.Id);
                mail.setFileAttachments(attachments);
                String body = et.HTMLValue;
                body = Body.replace('{Name}',csvRecordData[0]);
                body = Body.replace('{RM Name}',csvRecordData[2]);
                body = Body.replace('{RM Number}',csvRecordData[3]);
                body = body.replace('{currentDate}', curDate);
                system.debug('---'+String.valueOf(currentDate));
                mail.setReplyTo(csvRecordData[4]);
                mail.setToAddresses(new String[] {csvRecordData[1]});
                mail.setSubject('AIF Onboarding_'+csvRecordData[0]);
                mail.setHTMLBody(body);
                mails.add(mail);
            }else if(type.equalsIgnoreCase('Non-Individual')){
                system.debug('---');
                List<Messaging.EmailFileAttachment> attachments = new List<Messaging.EmailFileAttachment>();
                for (document doc: lstDocNonI) {
                    Messaging.EmailFileAttachment efa = new Messaging.EmailFileAttachment();
                    efa.setFileName(doc.Name);
                    efa.setBody(doc.Body);
                    efa.setContentType(doc.ContentType);
                    attachments.add(efa);
                }
                Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
                mail.setTemplateId(etJoin.Id);
                mail.setReplyTo(csvRecordData[4]);                
                mail.setFileAttachments(attachments);
                String body = etJoin.HTMLValue;
                body = Body.replace('{RM Name}',csvRecordData[2]);
                body = Body.replace('{RM Number}',csvRecordData[3]);
                mail.setToAddresses(new String[] {csvRecordData[1]});
                mail.setSubject('AIF Onboarding_'+csvRecordData[0]);
                mail.setHTMLBody(body);
                mails.add(mail);
            }           
        }
        Messaging.sendEmail(mails);       
    }
    
    
    public Map<string,String> verifyBankData(string accNo,String IFSC, String accName){
        system.debug('accountMap :'+accountMap);
        string clientId = 'AI2EPAINEFIN4P145R9ESJ6FFDL6ZAPR';
        string clientSecret = 'VH8YKIKTEZ86FCNO2MN2BCTJW5IIJGZ7';
        String myBase64String = EncodingUtil.base64Encode(Blob.valueof(clientId + ':' + clientSecret));
        String authorizationHeaderString = 'Basic QUlSNDVJVDk4SFMxSDJBNkJUNVFSSVhSSjZEUVpKRUY6WFg2U0xTTElMRTE1OTlUTlpRVFhJUjFZT1MxRjdURDQ='; //+ myBase64String;
        system.debug('authorizationHeaderString :'+authorizationHeaderString);
        String endURL = 'https://api.digio.in/client/verify/bank_account';
        Map<String,String> bodyMap = new Map<String,String>();
        bodyMap.put('beneficiary_account_no', accNo);
        bodyMap.put('beneficiary_ifsc', IFSC);
        bodyMap.put('beneficiary_name', accName);
        HttpRequest req = new HttpRequest();
        HttpResponse response = new HttpResponse();
        String bodyy =  JSON.serialize(bodyMap);
        system.debug('---'+bodyy);
        req.setEndpoint(endURL);
        req.setHeader('Authorization',authorizationHeaderString);
        req.setHeader('Content-Type','application/json');
        req.setMethod('POST');
        req.setTimeout(120000);
        req.setBody(bodyy);
        Http http1 = new Http();
        HTTPResponse res = http1.send(req);
        system.debug('Response ::'+res);
        Map<String, Object> results = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
        system.debug('results ::'+results);
        system.debug('results ::'+results.get('message'));        
        if(res.getStatusCode() != 200){
            string errorMsg = ':'+String.valueOf(results.get('message'));	        	        
            accountMap.put(accNo,errorMsg);
            return accountMap;
        }else{
            if(boolean.valueOf(results.get('verified'))){
                accountMap.put(accNo,String.valueOf(results.get('id')));
                system.debug('beneficiaryNameMap ::'+results.get('beneficiary_name_with_bank')); 
                string str = (results.get('beneficiary_name_with_bank')).toString();
                beneficiaryNameMap.put(accNo,str);
                verifiedDateMap.put(accNo,String.valueOf(results.get('verified_at')));
                system.debug('beneficiaryNameMap ::'+beneficiaryNameMap);  
            }else{
                accountMap.put(accNo,String.valueOf(results.get('id'))+':'+String.valueOf(results.get('error_msg')));
            }
            return accountMap;
        }     
        
    }
    
}