@isTest
public with sharing class InvestorAIFMeterAPITest {
    @isTest
    static void testValidInput() {
        // Setup test data
        Account testAccount1= TestFactory.createAccount();
        testAccount1.Name ='Test Account 1';
        testAccount1.Primary_Contact__c = '**********';
        testAccount1.Primary_Country_Code__c= 91;
        testAccount1.Membership_Slab__c = 'Gold';
        insert testAccount1;

        List<Contact> investorList = new List<Contact>();
        Contact investor1 = new Contact();
        investor1.LastName = 'Test Investor 1';
        investor1.AIF_Contributor__c = true;
        investor1.AccountId = testAccount1.Id;
        investor1.Investor_s_PAN__c = '**********';
        investorList.add(investor1);

        Contact investor2 = new Contact();
        investor2.LastName = 'Test Investor 2';
        investor2.AIF_Contributor__c = true;
        investor2.AccountId = testAccount1.Id;
        investor2.Investor_s_PAN__c = '**********';
        investorList.add(investor2);
        
        Contact investor3 = new Contact();
        investor3.AIF_Contributor__c = true;
        investor3.LastName = 'Test Investor 3';
        investor3.AccountId = testAccount1.Id;
        investor3.Investor_s_PAN__c = '**********';
        investorList.add(investor3);

        insert investorList;

        Id IndividualrecordTypeId = Schema.SObjectType.Contribution_Agreement__c.getRecordTypeInfosByName().get('Individual').getRecordTypeId();
		Id JointrecordTypeId = Schema.SObjectType.Contribution_Agreement__c.getRecordTypeInfosByName().get('Joint').getRecordTypeId();

        List<Contribution_Agreement__c> agreeList = new List<Contribution_Agreement__c>();

        Contribution_Agreement__c agree1 = new Contribution_Agreement__c();
        agree1.Investor1__c = investor1.Id;
        agree1.Total_Contribution_Amount__c = 999;
        agree1.Total_Committed_Amount__c = 90;
        agree1.RecordTypeId = IndividualrecordTypeId;
        agreeList.add( agree1);
        
        Contribution_Agreement__c agree2 = new Contribution_Agreement__c();
        agree2.Investor1__c = investor2.Id;
        agree2.Investor2__c = investor1.Id;
        agree2.Total_Contribution_Amount__c = 999;
        agree2.Total_Committed_Amount__c = 90;
        agree2.RecordTypeId = JointrecordTypeId;
        agreeList.add( agree2);
        
        insert agreeList;

        InvestorAIFMeterAPI.RequestWrapper requestBody = new InvestorAIFMeterAPI.RequestWrapper();
        InvestorAIFMeterAPI.ObjPrimaryContactWrapper contact = new InvestorAIFMeterAPI.ObjPrimaryContactWrapper();
        contact.primaryNumber = '**********'; // Non-existing primary number
        contact.countryCode = '91';
        requestBody.primaryContactList = new List<InvestorAIFMeterAPI.ObjPrimaryContactWrapper>{contact};
    
        RestContext.request = new RestRequest();
        RestContext.response = new RestResponse();
        RestContext.request.requestBody = Blob.valueOf(JSON.serialize(requestBody));
    
        // Call the method
        Test.startTest();
        InvestorAIFMeterAPI.getPointTransactionDetails();
        Test.stopTest();
    
        // Validate response
        RestResponse response = RestContext.response;
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response.responseBody.toString());
    }
}