trigger PointsTransactionTrigger on Points_Transaction__c (after insert,after update, before delete) {
    List<System_Setting__c> settingList = System_Setting__c.getall().values();
    
    if(settingList!=null && settingList.size()>0 && !settingList[0].CATriggerActivated__c)
    {
        system.debug('Returning from CA Trigger because of custom setting>>>'+settingList);
        return;
    }
    
    if(trigger.isInsert || trigger.isUpdate){
    	PointsTransactionTriggerHandler.bulkAfter(Trigger.new);
    }
    if(trigger.isdelete && trigger.isBefore){
        PointsTransactionTriggerHandler.beforeDelete(Trigger.old);
    }
    
}