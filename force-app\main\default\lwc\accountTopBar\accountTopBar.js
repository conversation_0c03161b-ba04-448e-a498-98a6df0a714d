// import { LightningElement, wire, api } from 'lwc';
// import { getRecord, getRecordNotifyChange, getFieldValue } from 'lightning/uiRecordApi';
// import { getRecord as getUserRecord } from 'lightning/uiRecordApi';
// import User_Name from '@salesforce/schema/User.Name';

// // Define fields to retrieve from the Account object
// const FIELDS = [
//     'Account.Primary_Contact__c',
//     'Account.BillingCity',
//     'Account.Account_Premier_Status__c',
//     'Account.Membership_Slab__c',
//     'Account.Total_Amount_Committed__c',
//     'Account.Referred_Accounts__c',
//     'Account.Total_Point__c',
//     'Account.Linkedin__c',
//     'Account.Relationship_Manager__c',
//     'Account.Membership_Status__c',
//     'Account.AIM__c',
//     'Account.Other_Membership_type__c',
//     'Account.Total_Amount_Invested__c',
//     'Account.Referred_leads__c',
//     'Account.Lead_Source__c',
//     'Account.App_Status__c',
//     'Account.Lead_Quality_Score__c',
//     'Account.Lead_Quality_Score_Percentage__c',
//     'Account.Name',
//     'Account.Referrals_in_Trials__c',
//     'Account.Referrals_Rotated__c',
//     'Account.Referrals_Paid__c',
//     'Account.Date_of_Addition__c'
//     // Add more fields as needed
// ];

// export default class accTopBar extends LightningElement {
//     @api recordId; // This will contain the Id of the account record passed to this component
//     relationshipManagerId

//     @wire(getRecord, { recordId: '$recordId', fields: FIELDS })
//     account;

//     // Function to refresh the account data when needed
//     refreshAccount() {
//         getRecordNotifyChange([{ recordId: this.recordId }]);
//     }

//     // Wire method to handle the response and set variables accordingly
//     handleRecordResponse({ error, data }) {
//         if (data) {
//             this.relationshipManagerId = data.fields.Relationship_Manager__c.value;
//         } else if (error) {
//             console.error('Error loading record', error);
//         }
//     }

//     // Wire method to call handleRecordResponse when the data changes
//     @wire(getRecord, { recordId: '$recordId', fields: FIELDS })
//     wiredRecord({ error, data }) {
//         if (data) {
//             this.handleRecordResponse({ data });
//         } else if (error) {
//             this.handleRecordResponse({ error });
//         }
//     }

//     @wire(getRecord, { recordId: '$relationshipManagerId', fields: [User_Name] })
//     user;

//     get relationshipManagerName() {
//         return this.user.data ? getFieldValue(this.user.data, User_Name) : '';
//     }

//     // Getter function to format number with commas
//     get formattedTotalAmountCommitted() {
//         return this.formatNumberWithCommas(this.account.data.fields.Total_Amount_Committed__c.value);
//     }

//     get formattedTotalAmountInvested() {
//         return this.formatNumberWithCommas(this.account.data.fields.Total_Amount_Invested__c.value);
//     }

//     get formattedTotalPoints() {
//         return this.formatNumberWithCommas(this.account.data.fields.Total_Point__c.value);
//     }

//     // Define a getter function to format the "Lead Quality Score %"
//     get formattedLeadQualityScore() {
//         const leadQualityScore = this.account.data.fields.Lead_Quality_Score_Percentage__c.value;
//         // Check if leadQualityScore is a valid number
//         if (!isNaN(leadQualityScore) && leadQualityScore !== null) {
//             // Format the number with two decimal places and append the percentage symbol
//             return leadQualityScore.toFixed(2) + "%";
//         } else {
//             return ""; // Return an empty string if leadQualityScore is not a valid number
//         }
//     }
//     formatNumberWithCommas(number) {
//         return number ? number.toLocaleString() : '';
//     }

// }
import { LightningElement, wire, api } from 'lwc';
import getAccountData from '@salesforce/apex/accounTopBarCtrl.getAccountData';

export default class AccTopBar extends LightningElement {
    @api recordId; // This will contain the Id of the account record passed to this component
    accountData = {};
    error;

    @wire(getAccountData, { accountId: '$recordId' })
    wiredAccount({ error, data }) {
        if (data) {
            this.accountData = data;            
        } else if (error) {
            this.error = error;
            this.accountData = {};
        }
    }

    // Getter function to format number with commas
    get formattedTotalAmountCommitted() {
        return this.accountData.Total_Amount_Committed__c ? this.formatNumberWithCommas(this.accountData.Total_Amount_Committed__c) : '';
    }

    get formattedTotalAmountInvested() {
        return this.accountData.Total_Amount_Invested__c ? this.formatNumberWithCommas(this.accountData.Total_Amount_Invested__c) : '';
    }

    get formattedTotalPoints() {
        return this.accountData.Total_Point__c ? this.formatNumberWithCommas(this.accountData.Total_Point__c) : '';
    }

    get formattedLeadQualityScore() {
        const leadQualityScore = this.accountData.Lead_Quality_Score_Percentage__c;
        if (!isNaN(leadQualityScore) && leadQualityScore !== null) {
            return parseFloat(leadQualityScore).toFixed(2) + "%";
        } else {
            return "";
        }
    }

    formatNumberWithCommas(number) {
        return number ? parseFloat(number).toLocaleString() : '';
    }

    get formattedDateOfAddition() {
        return this.accountData.Date_of_Addition__c ? this.formatDate(this.accountData.Date_of_Addition__c) : '';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based in JS
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    }
}