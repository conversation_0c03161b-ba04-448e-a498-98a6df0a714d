public with sharing class accounTopBarCtrl {
    @AuraEnabled(cacheable=true)
    public static Map<String, String> getAccountData(Id accountId) {
        Integer referralPaidCount = 0;
        Integer referralTrailCount = 0;
        Integer referralRotatedCount = 0;
        id IPVRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        Account acc = [
            SELECT 
                Primary_Contact__c, 
                BillingCity,
                Account_Premier_Status__c,
                Membership_Slab__c,
                Total_Amount_Committed__c,
                Referred_Accounts__c,
                Total_Point__c,
                Linkedin__c,
                Relationship_Manager__r.Name,
                Membership_Status__c,
                AIM__c,
                Other_Membership_type__c,
                Total_Amount_Invested__c,
                Referred_leads__c,
                Lead_Source__c,
                App_Status__c,
                Lead_Quality_Score__c,
                Lead_Quality_Score_Percentage__c,
                Name,
                Date_of_Addition__c
            FROM Account
            WHERE Id = :accountId
        ];
        System.debug('parentId>>>>>>>>.' + accountId);
        for (AggregateResult result : [SELECT ParentId, COUNT(Id) counts FROM Account WHERE (Date_of_Payment__c != null OR Membership_Status__c = 'Complimentary') AND ParentId =: accountId AND RecordTypeId =: IPVRecordTypeId GROUP BY ParentId]) {
            referralPaidCount = (Integer)result.get('counts');
        }
        System.debug('ACC.referralCountPaidMember>>>>>>>>' + referralPaidCount );
        for (AggregateResult result : [SELECT ParentId, COUNT(Id) counts FROM Account WHERE Membership_Status__c IN ('On Trial' , 'On Trial Community') AND ParentId =: accountId AND RecordTypeId =: IPVRecordTypeId GROUP BY ParentId]) {
            referralTrailCount = (Integer)result.get('counts');
        }
        System.debug('ACC.referralCountTrailMember>>>>>>>>' +  referralTrailCount);
        
        for (AggregateResult result : [SELECT ParentId, COUNT(Id) counts FROM Account WHERE (Membership_Status__c = 'Exited by own' OR Membership_Status__c = 'Rotated by IPV') AND ParentId =: accountId AND RecordTypeId =: IPVRecordTypeId GROUP BY ParentId]) {
            referralRotatedCount = (Integer)result.get('counts');
        }
        System.debug('ACC.referralCountRotatedMember>>>>>>>>' + referralRotatedCount );
        
        Map<String, String> accDataMap = new Map<String, String>();
        accDataMap.put('Primary_Contact__c', acc.Primary_Contact__c);
        accDataMap.put('BillingCity', acc.BillingCity);
        accDataMap.put('Account_Premier_Status__c', acc.Account_Premier_Status__c);
        accDataMap.put('Membership_Slab__c', acc.Membership_Slab__c);
        accDataMap.put('Total_Amount_Committed__c', String.valueOf(acc.Total_Amount_Committed__c));
        accDataMap.put('Referred_Accounts__c', String.valueOf(acc.Referred_Accounts__c));
        accDataMap.put('Total_Point__c', String.valueOf(acc.Total_Point__c));
        accDataMap.put('Linkedin__c', acc.Linkedin__c);
        accDataMap.put('Relationship_Manager__c', acc.Relationship_Manager__r.Name);
        accDataMap.put('Membership_Status__c', acc.Membership_Status__c);
        accDataMap.put('AIM__c', acc.AIM__c);
        accDataMap.put('Other_Membership_type__c', acc.Other_Membership_type__c);
        accDataMap.put('Total_Amount_Invested__c', String.valueOf(acc.Total_Amount_Invested__c));
        accDataMap.put('Referred_leads__c', String.valueOf(acc.Referred_leads__c));
        accDataMap.put('Lead_Source__c', acc.Lead_Source__c);
        accDataMap.put('App_Status__c', acc.App_Status__c);
        accDataMap.put('Lead_Quality_Score__c', String.valueOf(acc.Lead_Quality_Score__c));
        accDataMap.put('Lead_Quality_Score_Percentage__c', String.valueOf(acc.Lead_Quality_Score_Percentage__c));
        accDataMap.put('Name', acc.Name);
        accDataMap.put('Date_of_Addition__c', String.valueOf(acc.Date_of_Addition__c));
        accDataMap.put('Referrals_in_Trials__c', String.valueOf(referralTrailCount) );
        accDataMap.put('Referrals_Rotated__c', String.valueOf(referralRotatedCount));
        accDataMap.put('Referrals_Paid__c', String.valueOf(referralPaidCount));
        return accDataMap;
    }
}