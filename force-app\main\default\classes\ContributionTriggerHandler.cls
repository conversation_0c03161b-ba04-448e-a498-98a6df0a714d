public class ContributionTriggerHandler {
    public static void bulkBefore(List<Contribution_Agreement__c> CAList) {
        /*Defines a static method bulkBefore that takes a list of Contribution_Agreement__c records. It's typically used in a before insert or before update trigger context. */
        set<ID> conId = new Set<ID>();
        /*Initializes a set to collect unique Contact IDs from the agreement records. */
        Map<ID, Contact> mapContact = new Map<ID, Contact>();
        for (Contribution_Agreement__c ca : CAList) {
            if (ca.Investor1__c != null) {
                conId.add(ca.Investor1__c);
            }
            if (ca.Investor2__c != null) {
                conId.add(ca.Investor2__c);
            }
        }
        /*Loops through each agreement to extract and collect the Contact IDs from Investor1__c and Investor2__c fields if they exist. */
        List<contact> conList = [
            SELECT
            Id,
            Email,
            Account.Bot_Communication_Country_Code__c,
            Account.Bot_Communication_Number__c
            FROM Contact
            WHERE ID IN :conId
        ];
        /*SOQL query: retrieves Email and Account-related phone details for the collected Contact IDs. */
        for (contact con : conList) {
            mapContact.put(con.Id, con);
        }
        /*Populates the map with queried contacts for easy reference. */
        for (Contribution_Agreement__c ca : CAList) {
            /*Iterates again through the input agreement list. */
            if (
                ca.Investor1__c != null &&
                mapContact != null &&
                mapContact.containsKey(ca.Investor1__c)
            ) {
                ca.Email_ID__c = mapContact.get(ca.Investor1__c).Email;
                ca.Phone_Number__c = mapContact.get(ca.Investor1__c)
                    .Account.Bot_Communication_Number__c;
                ca.Country_Code__c = mapContact.get(ca.Investor1__c)
                    .Account.Bot_Communication_Country_Code__c;
            }
            /*If Investor1__c is present and exists in the map, populate:
Email
Phone
Country code from the related contact’s account */
            if (
                ca.Investor2__c != null &&
                mapContact != null &&
                mapContact.containsKey(ca.Investor2__c)
            ) {
                ca.Email_ID_2__c = mapContact.get(ca.Investor2__c).Email;
            }
        }
    }
    /*If Investor2__c is present and exists in the map, only update the secondary email. */
    public static void bulkAfter(
        List<Contribution_Agreement__c> CAList,
        List<Contribution_Agreement__c> oldCAList
    ) {
        /*Handles after insert logic, assuming oldCAList is null during insert. */
        //public static void bulkAfter(List<Contribution_Agreement__c> CAList)
        /*if(trigger.isUpdate){
set<ID> oldInvId = new set<ID>();
for(Contribution_Agreement__c CA : oldCAList){
if(CA.Investor1__c != null){
oldInvId.add(CA.Investor1__c);
}
if(CA.Investor2__c != null){
oldInvId.add(CA.Investor2__c);
}
}
if(oldInvId != null){
List<Investment__c> lstInvestment = [SELECT Id,Type__c,Investor__c,Committed_Amount__c,Contribution_Agreement__c FROM Investment__c WHERE Investor__c IN :oldInvId];
if(!lstInvestment.isEmpty()){
for(Investment__c inv :  lstInvestment){
inv.Contribution_Agreement__c = null;
}
update lstInvestment;
}
}
}*/
        system.debug('in CA trigger **');
        set<ID> conId = new Set<ID>();
        Map<Id, Id> mapCAInvestor = new Map<Id, Id>();
        /*conId: holds agreement IDs mapCAInvestor: maps Investor ID → Agreement ID */
        Map<Id, Contribution_Agreement__c> mapOfCA = new Map<Id, Contribution_Agreement__c>();
        
        List<Fund_Module__c> fundModuleList = [SELECT Id , Name FROM Fund_Module__c];
        Map<Id, String> fundModuleMap = new Map<Id, String>();

        for (Fund_Module__c fmc : fundModuleList) {
            fundModuleMap.put(fmc.Id, fmc.Name);
        }

        for (Contribution_Agreement__c CA : CAList) {
            conId.add(CA.Id);
            if (CA.Investor1__c != null) {
                mapCAInvestor.put(CA.Investor1__c, CA.Id);
            }
            if (CA.Investor2__c != null) {
                mapCAInvestor.put(CA.Investor2__c, CA.Id);
            }
            mapOfCA.put(CA.Id, CA);
        }
        system.debug('in mapCAInvestor **' + mapCAInvestor);
        /*Builds a map of Investors to their Agreement, using both Investor1__c and Investor2__c. */
        if (oldCAList == null) {
            /*Ensures the logic only runs during insert, not update. */
            List<contact> lstInvestors = [
                SELECT Id, Contribution_Agreement__c , Contribution_Agreement_IPV_International__c , Contribution_Agreement_Fund_B__c, Contribution_Agreement_Fund_A__c
                FROM contact
                WHERE Id IN :mapCAInvestor.keySet()
            ];
            system.debug('in lstInvestors **' + lstInvestors);
            /*Fetches the investors who are referenced in agreements. */
            if (!lstInvestors.isEmpty()) {
                for (contact con : lstInvestors) {
                    // added by sahilparvat on 06.05.2025 for Gift Scope BRD.
                    Contribution_Agreement__c conAgree = mapOfCA.get(
                        mapCAInvestor.get(con.Id)
                    );
                    
                    if (mapCAInvestor.containsKey(con.Id)) {
                        System.debug('Inside INVESTOR :::: ');
                        String fundName = fundModuleMap.get(conAgree.Fund_Onboarded_on__c);
                        System.debug('Fund Name :::: ' + fundName);

                        if (
                            fundName == 'IPV International' &&
                            con.Contribution_Agreement_IPV_International__c == null
                        ) {
                            con.Contribution_Agreement_IPV_International__c = mapCAInvestor.get(
                                con.Id
                            );
                        } else if (
                            fundName == 'Fund B' &&
                            con.Contribution_Agreement_Fund_B__c == null
                        ) {
                            con.Contribution_Agreement_Fund_B__c = mapCAInvestor.get(con.Id);
                        } else if (
                            fundName == 'Fund A' &&
                            con.Contribution_Agreement_Fund_A__c == null
                        ) {
                            con.Contribution_Agreement_Fund_A__c = mapCAInvestor.get(con.Id);
                            System.debug('Inside >>> ');
                        } else {
                            con.Contribution_Agreement__c = mapCAInvestor.get(con.Id);
                        }
                    }
                }
                update lstInvestors;
            }
            system.debug('in lstInvestors **' + mapCAInvestor.keySet());
            /*For each investor contact, sets their Contribution_Agreement__c reference and updates them. */
            List<Investment__c> lstInvestment = [
                SELECT
                Id,
                Type__c,
                Investor__c,
                Committed_Amount__c,
                Contribution_Agreement__c
                FROM Investment__c
                WHERE
                Investor__c IN :mapCAInvestor.keySet()
                AND Investor_Type__c = 'Via AIF'
            ];
            system.debug('in lstInvestment **' + lstInvestment);
            /*Fetches all "Via AIF" investment records tied to the investors. */
            if (!lstInvestment.isEmpty()) {
                for (Investment__c inv : lstInvestment) {
                    if (mapCAInvestor.containsKey(inv.Investor__c)) {
                        if (inv.Contribution_Agreement__c == null)
                            inv.Contribution_Agreement__c = mapCAInvestor.get(
                                inv.Investor__c
                            );
                    }
                }
                update lstInvestment;
            }
        }
        /*Updates each Investment to link the correct Contribution_Agreement__c, only if it's currently null. */
    }
    
    @future
    public static void updateCACommittTotal(set<Id> CAID) {
        /*Future method (runs asynchronously) to update aggregate fields on agreements. */
        system.debug('CAID.keySet() ::' + CAID);
        List<Contribution_Agreement__c> lstCA = [
            SELECT
            Id,
            Total_Committed_Amount__c,
            Total_Redemption_Amount__c,
            Total_Interse_Purchase__c,
            Total_Interse_Sale__c,
            Total_Fees__c,
            Total_Investment_Amount__c,
            Total_Transfer__c,
            (SELECT Id, Amount__c FROM Transactions__r),
            (
                SELECT
                Id,
                Name,
                Type__c,
                Startup_Round__r.Round_Type__c,
                Exit_Fee_received__c,
                Investment_Amount_Balance__c,
                Parent_Investment__c,
                Startup_Round__r.Type_of_Event__c,
                Investment_Amount__c,
                Considered_Investment_Amount__c,
                Final_Commitment_Amount__c,
                Investor_Type__c,
                Investment_Fee_Received__c
                FROM Investment__r
                WHERE Investor_Type__c = 'Via AIF'
            )
            FROM Contribution_Agreement__c
            WHERE Id IN :CAID
        ];
        system.debug('lstCA ::' + lstCA);
        /*Loads the agreements and their related Investment__r and Transactions__r records. */
        for (Contribution_Agreement__c ca : lstCA) {
            decimal total = 0;
            decimal totalFee = 0;
            decimal totalIP = 0;
            decimal totalIS = 0;
            decimal totalInvestment = 0;
            decimal totalTrans = 0;
            decimal totalRedAmt = 0;
            /*Initializes variables to accumulate totals. */
            for (Investment__c inv : ca.Investment__r) {
                /*Loops through related investments. */
                /*if((((inv.Type__c == 'Invested' || inv.Type__c == 'Internal Transfers' ) && inv.Investor_Type__c == 'Via AIF') && (inv.Startup_Round__r.Round_Type__c == 'Raise' || inv.Startup_Round__r.Round_Type__c == 'Pre-emptive') && inv.Parent_Investment__c == null)
|| (inv.Type__c == 'Exit' && inv.Investor_Type__c == 'Via AIF' && inv.Startup_Round__r.Round_Type__c == 'Exit' && (inv.Startup_Round__r.Type_of_Event__c == 'Full Redemption of Units' || inv.Startup_Round__r.Type_of_Event__c == 'Partial Redemption of Units'))){
*/
                if (
                    inv.Parent_Investment__c == null &&
                    ((((inv.Type__c == 'Invested' ||
                        inv.Type__c == 'Internal Transfers') &&
                       inv.Investor_Type__c == 'Via AIF') &&
                      (inv.Startup_Round__r.Round_Type__c == 'Raise' ||
                       inv.Startup_Round__r.Round_Type__c == 'Pre-emptive')) ||
                     ((inv.Type__c == 'Exit' ||
                       inv.Type__c == 'Partial Exit') &&
                      inv.Investor_Type__c == 'Via AIF' &&
                      inv.Startup_Round__r.Round_Type__c == 'Raise'))
                ) {
                    /*Handles fee aggregation logic for specific investment types and conditions. */
                    if (inv.Type__c == 'Exit' && inv.Exit_Fee_received__c != null) {
                        totalFee += inv.Exit_Fee_received__c;
                    } else if (inv.Investment_Fee_Received__c != null) {
                        totalFee += inv.Investment_Fee_Received__c;
                    }
                }
                /*Adds investment or exit fees. */
                if (
                    (inv.Type__c == 'Exit' &&
                     inv.Investor_Type__c == 'Via AIF' &&
                     inv.Startup_Round__r.Round_Type__c == 'Exit' &&
                     (inv.Startup_Round__r.Type_of_Event__c ==
                      'Full Redemption of Units' ||
                      inv.Startup_Round__r.Type_of_Event__c ==
                      'Partial Redemption of Units'))
                ) {
                    totalRedAmt += inv.Investment_Amount__c;
                }
                /*Adds redemption amounts for exit events. */
                if (
                    (inv.Type__c == 'Invested' &&
                     inv.Investor_Type__c == 'Via AIF' &&
                     inv.Startup_Round__r.Round_Type__c == 'Internal Transfers')
                ) {
                    if (inv.Considered_Investment_Amount__c != null)
                        totalIP += inv.Considered_Investment_Amount__c;
                }
                /*Adds internal purchase amounts. */
                if (
                    (inv.Type__c == 'Exit' &&
                     inv.Investor_Type__c == 'Via AIF' &&
                     inv.Startup_Round__r.Round_Type__c == 'Exit' &&
                     inv.Startup_Round__r.Type_of_Event__c == 'Internal Transfer of Units')
                ) {
                    if (inv.Investment_Amount__c != null)
                        totalIS += inv.Investment_Amount__c;
                }
                /*Adds internal sale amounts. */
                if (
                    ((inv.Type__c == 'Invested' && inv.Investor_Type__c == 'Via AIF') &&
                     (inv.Startup_Round__r.Round_Type__c == 'Raise' ||
                      inv.Startup_Round__r.Round_Type__c == 'Pre-emptive'))
                ) {
                    if (inv.Investment_Amount__c != null)
                        totalInvestment += inv.Investment_Amount__c;
                }
                /*Adds investment amounts. */
                if (
                    inv.Type__c == 'Committed' &&
                    inv.Investor_Type__c == 'Via AIF' &&
                    inv.Final_Commitment_Amount__c != null
                ) {
                    total += inv.Final_Commitment_Amount__c;
                }
                
                system.debug('total ::' + total);
                /*Adds committed amounts. */
            }
            for (Transaction__c trans : ca.Transactions__r) {
                if (trans.amount__c != null)
                    totalTrans += trans.amount__c;
            }
            /*Adds up all transaction amounts. */
            ca.Total_Committed_Amount__c = total;
            ca.Total_Interse_Purchase__c = totalIP;
            ca.Total_Interse_Sale__c = totalIS;
            ca.Total_Fees__c = totalFee;
            ca.Total_Investment_Amount__c = totalInvestment;
            ca.Total_Transfer__c = totalTrans;
            ca.Total_Redemption_Amount__c = totalRedAmt;
            system.debug('TotalRedAmount: ' + totalRedAmt);
        }
        update lstCA;
        system.debug('lstCA ::' + lstCA);
        /*Applies the totals to each record and updates them. */
    }
    
    // Added by Sahilparvat on 27.05.2025 for IPV International - Gift City BRD.
    public static void fundModuleRollUpSummary(
    List<Contribution_Agreement__c> conAgreeList,
    Map<Id, Contribution_Agreement__c> oldMap
) {
    Set<Id> fundIds = new Set<Id>();

    for (Contribution_Agreement__c newCA : conAgreeList) {
        fundIds.add(newCA.Fund_Onboarded_on__c);
        if (oldMap != null && oldMap.containsKey(newCA.Id)) {
            Contribution_Agreement__c oldCA = oldMap.get(newCA.Id);
            if (oldCA.Fund_Onboarded_on__c != newCA.Fund_Onboarded_on__c) {
                fundIds.add(oldCA.Fund_Onboarded_on__c); // Include old Fund Module
            }
        }
    }

    // Clean nulls
    fundIds.remove(null);
    
    Map<Id, Decimal> totalCASignedAmount = new Map<Id, Decimal>();
    Map<Id, Decimal> totalDrawdownAmount = new Map<Id, Decimal>();
    Map<Id, Decimal> totalFundBalance = new Map<Id, Decimal>();
    Map<Id, Integer> totalSignedCAs = new Map<Id, Integer>();

    // Query for rollup totals from all CA grouped by Fund Module
    for (AggregateResult result : [
        SELECT
            Fund_Onboarded_on__c fundId,
            SUM(Total_Contribution_Amount__c) totalCA,
            SUM(Total_Drawdowns__c) totalDrawdown,
            SUM(Balance_Amounts__c) totalBalance,
            COUNT(Id) totalCAs
        FROM Contribution_Agreement__c
        WHERE Fund_Onboarded_on__c IN :fundIds
        GROUP BY Fund_Onboarded_on__c
    ]) {
        Id fundId = (Id) result.get('fundId');
        totalCASignedAmount.put(fundId, (Decimal) result.get('totalCA'));
        totalDrawdownAmount.put(fundId, (Decimal) result.get('totalDrawdown'));
        totalSignedCAs.put(fundId, (Integer) result.get('totalCAs'));
        totalFundBalance.put(fundId, (Decimal) result.get('totalBalance'));
    }

    List<Fund_Module__c> fundModuleToUpdate = new List<Fund_Module__c>();

    for (Id fundId : fundIds) {
        Fund_Module__c fm;
        fm = new Fund_Module__c(Id = fundId);
        fm.Total_CA_Signed_Amount__c = totalCASignedAmount.containsKey(fundId) ? totalCASignedAmount.get(fundId) : null;
        fm.Total_Drawdown_Amount__c = totalDrawdownAmount.containsKey(fundId) ? totalDrawdownAmount.get(fundId) : null;
        fm.Signed_CAs__c = totalSignedCAs.containsKey(fundId) ? totalSignedCAs.get(fundId) : 0;
        fm.Total_Fund_Balance__c = totalFundBalance.containsKey(fundId) ? totalFundBalance.get(fundId) : null;
        fundModuleToUpdate.add(fm);
    }

    if (!fundModuleToUpdate.isEmpty()) {
        update fundModuleToUpdate;
    }
}

    
    
    public static void assignFundSpecificNumbers(
        List<Contribution_Agreement__c> newCAs,
        Map<Id, Contribution_Agreement__c> oldMap
    ) {
        // Map of CAs that need new numbers
        Map<Id, Contribution_Agreement__c> caWithFundMap = new Map<Id, Contribution_Agreement__c>();
        Set<Id> fundModuleIds = new Set<Id>();

        Boolean isInsert = (oldMap == null);
        Boolean isUpdate = !isInsert;

        for (Contribution_Agreement__c ca : newCAs) {
            Id newFundId = ca.Fund_Onboarded_on__c;
            Id oldFundId = isUpdate ? oldMap.get(ca.Id).Fund_Onboarded_on__c : null;

            Boolean fundChanged = isInsert || (newFundId != null && newFundId != oldFundId);

            if (fundChanged && newFundId != null) {
                caWithFundMap.put(ca.Id, ca);
                fundModuleIds.add(newFundId);
            }
        }

        if (fundModuleIds.isEmpty()) return;

        // Step 1: Get Fund Identifiers for each Fund Module
        Map<Id, String> fundIdToIdentifierMap = new Map<Id, String>();
        for (Fund_Module__c fm : [
            SELECT Id, Fund_Identifier__c
            FROM Fund_Module__c
            WHERE Id IN :fundModuleIds
        ]) {
            if (String.isNotBlank(fm.Fund_Identifier__c)) {
                fundIdToIdentifierMap.put(fm.Id, fm.Fund_Identifier__c);
            }
        }

        // Step 2: Prepare CA grouped by Fund Identifier
        Map<String, List<Contribution_Agreement__c>> identifierToCAsMap = new Map<String, List<Contribution_Agreement__c>>();
        for (Contribution_Agreement__c ca : caWithFundMap.values()) {
            String identifier = fundIdToIdentifierMap.get(ca.Fund_Onboarded_on__c);
            if (identifier != null) {
                if (!identifierToCAsMap.containsKey(identifier)) {
                    identifierToCAsMap.put(identifier, new List<Contribution_Agreement__c>());
                }
                identifierToCAsMap.get(identifier).add(ca);
            }
        }

        // Step 3: Get current max CA numbers for each Fund Identifier
        Set<String> allIdentifiers = identifierToCAsMap.keySet();
        Map<String, Integer> identifierToMaxNumberMap = new Map<String, Integer>();

        List<AggregateResult> results = [
            SELECT Fund_Onboarded_on__r.Fund_Identifier__c identifier,
                   MAX(Fund_Specific_CA_Number__c) maxCA
            FROM Contribution_Agreement__c
            WHERE Fund_Onboarded_on__r.Fund_Identifier__c IN :allIdentifiers
              AND Fund_Specific_CA_Number__c != null
            GROUP BY Fund_Onboarded_on__r.Fund_Identifier__c
        ];

        for (AggregateResult ar : results) {
            String identifier = (String) ar.get('identifier');
            String maxCANum = (String) ar.get('maxCA');
            if (String.isNotBlank(maxCANum)) {
                String numPart = maxCANum.replaceAll('[^0-9]', '');
                Integer maxNum = Integer.valueOf(numPart);
                identifierToMaxNumberMap.put(identifier, maxNum);
            }
        }

        // Step 4: Assign new CA numbers
        for (String identifier : identifierToCAsMap.keySet()) {
            List<Contribution_Agreement__c> cas = identifierToCAsMap.get(identifier);
            Integer nextNumber = identifierToMaxNumberMap.containsKey(identifier)
                ? identifierToMaxNumberMap.get(identifier) + 1  
                : 1;

            for (Contribution_Agreement__c ca : cas) {
                String paddedNumber = String.valueOf(nextNumber);
                while (paddedNumber.length() < 4) {
                    paddedNumber = '0' + paddedNumber;
                }
                ca.Fund_Specific_CA_Number__c = identifier + paddedNumber;
                System.debug('Assigned CA Number: ' + ca.Fund_Specific_CA_Number__c);
                nextNumber++;
            }
        }
    }
}