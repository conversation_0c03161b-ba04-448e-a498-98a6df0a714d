trigger ContentVersionTrigger on ContentVersion (After insert,before insert,after update) {
    
   Blob csvFileBody;
   String csvFullData;
   system.debug('trigger.isInsert>>>>'+trigger.isInsert);
   system.debug('trigger.isBefore>>>>'+trigger.isBefore);
   system.debug('trigger.isupdate>>>>'+trigger.isUpdate);

   for(ContentVersion c : trigger.New)
   {
       system.debug('VersionData9999>>>>'+c.VersionData);

   }
    system.debug('trigger.New[0] SIZE>>>>'+trigger.New.size());
    system.debug('trigger.New[0].VersionData>>>>'+trigger.New[0].VersionData);
    csvFileBody = trigger.New[0].VersionData;
    system.debug('csvFileBody >>>>'+csvFileBody );
    //csvFullData = ''+csvFileBody.toString();
    system.debug('csvFullDatahjhj>>>>'+csvFullData);
    
}