trigger AccountTrigger on Account (before insert,before update,after insert,after update) {
    public class customException extends Exception {}
    
    List<System_Setting__c> settingList = System_Setting__c.getall().values();
    
    if(settingList!=null && settingList.size()>0 && !settingList[0].AccountTriggerActivated__c)
    {
        system.debug('Returning from Account Trigger because of custom setting>>>'+settingList);
        return;
    }
    
    AccountTriggerHandler handler = new AccountTriggerHandler();
    //try
    //{
        if(trigger.isInsert && trigger.IsBefore)
        {
            
            handler.beforeInsert(trigger.New);
            handler.updateReferredCounts(trigger.New);
        }
        else if(trigger.isUpdate && trigger.IsBefore)
        {
            handler.beforeUpdate(trigger.New,trigger.OldMap);
            //  Uncommented By Sahil on 26th April 2024
            //handler.updateReferredCounts(trigger.new);
         
        }
        else if(trigger.isInsert && trigger.IsAfter)
        {
            handler.afterInsert(trigger.New);
            //handler.updateReferredCounts(trigger.New);
            handler.handleAccountActivitiesInsert(trigger.New);
           
            
            
        }
        else if(trigger.isUpdate && trigger.IsAfter)
        {
            handler.afterUpdate(trigger.New,trigger.oldmap);
            //handler.updateReferredCounts(trigger.new);            
            handler.createPointTransaction(trigger.New,trigger.OldMap);
            //added by jay dabhi on 4th september 2024 for Customer Wise Timeline Chart
            handler.handleAccountActivitiesUpdate(trigger.New,trigger.OldMap);
        }
        
        /*if(Test.isRunningTest()){
            throw new customException('This is bad');
        }
   //}
    
    catch(exception e)
    {
        if(!Test.isRunningTest())
            trigger.New[0].addError(e.getMessage());
    } 
    */
 }