trigger StartupTrigger on Startup__c (before insert,after Update, after Insert) {

    List<System_Setting__c> settingList = System_Setting__c.getall().values();
    
    if(settingList!=null && settingList.size()>0 && !settingList[0].StartupTriggerActivated__c)
    {
        system.debug('Returning from StartupTrigger because of custom setting>>>'+settingList);
        return;
    }
    
    if(trigger.isBefore && trigger.isInsert)
    {
        StartupTriggerHandler handler = new StartupTriggerHandler();
        handler.beforeInsert(trigger.New);
    }
    if(trigger.isAfter && trigger.isInsert)
    {
        StartupTriggerHandler handler = new StartupTriggerHandler();
        handler.afterInsert(trigger.New);
    }
    if(trigger.isAfter && trigger.isUpdate)
    {
        StartupTriggerHandler handler = new StartupTriggerHandler();
        handler.afterUpdate(trigger.New,trigger.Oldmap);
    }
    
}