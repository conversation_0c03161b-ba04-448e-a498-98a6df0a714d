/************************************************************************
    Test Class for ConvertLeadCtrl to Deploy.
************************************************************************/

@isTest(SeeAllData=false)
private class ConvertLeadCtrlTestClass01{////////
	    
    @testSetup static void setup() {
        account acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Platinum';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        insert acc;  
        id IPVrecordTypeId = Schema.SObjectType.lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead__c leadObj = new Lead__c();
        leadObj = TestFactory.createLead(); 
        leadObj.RecordTypeId = IPVrecordTypeId;
        leadObj.Relationship_Owner__c = UserInfo.getUserId();
        leadObj.Membership_Status__c = 'On Trial';
        leadObj.utm_campaign__c = 'test Camp';
        leadObj.utm_medium__c = 'Test medium';
        leadObj.Type_of_Lead__c = 'Hot Lead';
        leadObj.Personal_Email__c = '<EMAIL>';
        leadObj.Are_you_a_first_time_investor__c ='';
        leadObj.Company__c='test';
        leadObj.Gain_from_Joining__c = 'I want to Generate Wealth through curated startup investments';
        leadObj.Date_of_Assignment_to_FiveS_RM__c = date.today();
        leadobj.Date_of_Assignment_to_IPV_RM__c = date.today();
        leadObj.utm_source__c ='Test';        
        leadObj.RecordTypeId = IPVrecordTypeId;
        leadObj.Date_of_Addition__c = date.today();
        //leadObj.Primary_Group_Name__c = 'Trial 10';
        leadObj.City__c = 'agra';
        leadobj.App_Age__c=23;
        leadobj.App_Address__c ='testaddress';
        leadobj.App_Designation__c ='CTO';
        leadobj.App_City__c ='Mumbai';
        leadobj.Company__c ='Codiot';
        leadobj.App_Email__c ='<EMAIL>';
        leadobj.App_Postal_Code__c = 1234;
        leadobj.App_PAN_Number__c ='**********';
        leadobj.Preferred_Sectors_to_Invest__c ='D2C';
        leadObj.Date_of_receiving_lead__c = date.today();
        leadObj.Referred_By__c = acc.Id;
        leadObj.Referral_Taken_By__c = acc.Id;
        leadObj.Lead_Quality_Score__c = 47;
        leadObj.Lead_Quality_Score_Percentage__c = 100; 
        leadObj.College_Tier__c = 'Tier 1';
        leadObj.Designation_Band__c = 'Band 1';
        leadObj.Are_They_Part_of_Other_Platform__c = 'Yes';
        leadObj.Bot_Input__c = 'testing';
        leadObj.App_Company__c = 'CODIOT';
        leadObj.App_Industry__c = 'demo testing';
        leadObj.App_Household_Income__c = 10000;
        leadObj.App_Country__c = 'India';
        leadObj.App_Status__c = 'Yes';
        leadObj.App_Expertise__c = 'demo testing';
        leadObj.App_Full_Name__c = 'App Full Name';
        leadObj.App_LinkedIn_ID__c = 'www.google.com';
        leadObj.Preferred_Sub_Sectors_to_Invest__c = 'sub sector';
        leadObj.Facebook_Campaign_Name__c = 'A';
        leadObj.Bot_Journey_Stage__c = 'First Message Initiated';
             	
        insert leadObj;
        system.debug('LeadObject>>' +leadObj);
        
        Startup__c st = TestFactory.createStartUp();
        insert st; 
        system.debug('Startup ST>>' +st);
        Startup_Round__c strObj = TestFactory.createStartUpRound( acc.Id,acc.Id, st.Id);
        insert strObj;
        system.debug('Roundss>>' +strObj);
        
        Events__c evObj = TestFactory.createEvents(''+strobj.Name);
        evObj.Startup_Round__c =strobj.id;
        insert evObj;
        system.debug('eventss>>' +evObj);
        
        Attendance__c att =new attendance__C();
        att.Events__c = evObj.id;
        att.Lead__c = leadObj.id;   
        att.Primary_Contact__c = leadObj.Primary_Contact__c;
        att.Primary_Country_Code__c = leadObj.Primary_Country_Code__c;
        att.Personal_Email__c = leadObj.Personal_Email__c;
        att.Lead_Source__c = leadObj.Lead_Source__c;
        att.Name = 'Test';
        
        Insert att;
        system.debug('Attendence Att>>' +att);
        
        
       User_Activity__c usr = new User_Activity__c();
        usr.Activity_Type__c = 'Agenda Tab';
         usr.Activity_Detail_RICH__c = 'Agenda Tab >>>>> 10';
         usr.Time_Stamp__c= System.now();
        usr.Related_Account__c =acc.id;
        insert usr;
        


        
        Task tt = new task();
        tt.Subject = 'Call';
        tt.WhatId = acc.id;
        tt.OwnerId = userInfo.getUserId();
        insert tt;
        system.debug('Task TT>>' +tt);
    }
    
    static testMethod void testAccountToLead()
    {
        test.startTest();
        
            Account parAcc = [select id,Primary_Contact__c,Personal_Email__c from account limit 1];
            PageReference pageRef = Page.ConvertLeadVfPage;
            test.setCurrentPageReference(pageRef);
            pageRef.getParameters().put('id',parAcc.Id);
            
            ConvertLeadCtrl ctrl = new ConvertLeadCtrl();  
            ctrl.convert();
            ctrl.cancelAction();        
         test.stopTest();
    }
    
    static testMethod void testLeadToAccount()
    {
        test.startTest();
        
            lead__c parAcc = [select id,Primary_Contact__c, recordTypeid,Personal_Email__c from lead__c limit 1];
        	// Added by karan
       		Task task = new Task(Subject = 'Whatsapp', WhatId = parAcc.Id);
    		insert task;
            List<lead__c> accList = new List<lead__c>();
            accList.add(parAcc);  
            update accList;
        	
        	system.debug('Account List>>' +accList);
        	system.debug('ParAcc List>>' +parAcc);
            PageReference pageRef = Page.ConvertLeadVfPage;
            test.setCurrentPageReference(pageRef);
            pageRef.getParameters().put('id',parAcc.Id);
            ConvertLeadCtrl ctrl = new ConvertLeadCtrl();  
            List<Task> updatedTasks = [SELECT Id, WhatId FROM Task WHERE WhatId = :parAcc.Id];
    		System.assertEquals(1, updatedTasks.size());
    		System.assertEquals(parAcc.Id, updatedTasks[0].WhatId);
            ctrl.convert();
            ctrl.cancelAction();        
         test.stopTest();
    }
    static testMethod void testMassLeadToAccount()
    {
        test.startTest();
            lead__c parAcc = [select id,Primary_Contact__c, recordTypeid,Personal_Email__c,Is_Converted_From_Lead__c from lead__c limit 1];
            List<Lead__c> leadList = new List<Lead__c>();
            leadList.add(parAcc);  
            update leadList;
        	system.debug('LeadListToUpdate>>>>'+leadList);
            List<Task> taskList = new List<Task>();
            taskList = [select id,WhatId from Task where WhatId in:leadList];
                system.debug('taskList>>>>'+taskList);
                
                if(taskList!=null && taskList.size()>0)
                {
                    for(Task tObj : taskList)
                    {
                        tObj.WhatId = ''+leadList[0];
                    }
                    update taskList;
                    system.debug('taskListToUpdate>>>>'+taskList);
                }
            List<Attendance__c> AttenList = new list<Attendance__c>();
            AttenList = [SELECT Id, Account__c,Lead__c,Personal_Email__c  FROM Attendance__c WHERE Lead__c IN :leadList];
            Map<Id, Attendance__c> updatedAttenMap = new Map<Id, Attendance__c>();
            for (Attendance__c atten : attenList) {
                if(atten.Personal_Email__c != null){
                    update AttenList;
                }
            }
        System.debug('Atten list'+ AttenList);
        
            ApexPages.StandardSetController stdSetController = new ApexPages.StandardSetController(leadList);
            stdSetController.setSelected(leadList);
            ConvertLeadsToAccountsCtrl ctrl = new ConvertLeadsToAccountsCtrl(stdSetController);
            
            ctrl.convert();
            ctrl.cancelBtn();
         test.stopTest();
    }
   
}