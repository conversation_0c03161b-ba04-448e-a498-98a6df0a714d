// created by b<PERSON>t to call API of campaign for sending a rpomocode details to app side 
trigger CampaignTrigger on Campaign (before insert , after insert , before update , after update ) {
    
    campaignTriggerHandler handler = new campaignTriggerHandler();
    
    if(trigger.isInsert && trigger.IsAfter){
        handler.afterInsert(trigger.new);
    }
    
    if(trigger.isUpdate && trigger.IsAfter){
        handler.afterUpdate(trigger.new);
    }
}