global class UserActivityTimelineBatchForLead implements Database.Batchable<SObject>,Database.AllowsCallouts {

    global Database.QueryLocator start(Database.BatchableContext bc) {
      
        String query = 'SELECT Id FROM Lead__c WHERE App_signup_date__c != null';
         
        return Database.getQueryLocator(query);
       
    }

    global void execute(Database.BatchableContext bc, List<SObject> scope) {
        List<Id> recordIds = new List<Id>();
        String endDate = system.now().format('dd-MM-yyyy');
        String startDate = system.now().addDays(-1).format('dd-MM-yyyy');

        for (SObject obj : scope) {
            Lead__c lead = (Lead__c)obj;
            recordIds.add(lead.Id);
                   }
        if (!recordIds.isEmpty()) {
            UserActivityRestService.callExternalApi(recordIds, startDate, endDate);
        } 
    }

    global void finish(Database.BatchableContext bc) {
        System.debug('Finished processing Lead batch.');
    }
}