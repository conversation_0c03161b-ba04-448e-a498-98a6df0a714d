@isTest
public class LeadQualityScoreAccountControllerTest {

    @isTest
    public static void testLeadQualityScoreAccountObject()
    {
        List<Account> accounts = listOfAccounts();
        
       
        LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(accounts[0].Id);
        LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(accounts[1].Id);
		LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(accounts[2].Id);
        LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(accounts[3].Id);
        LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(accounts[4].Id);
        LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(accounts[5].Id);
        LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(accounts[6].Id);
        LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(accounts[7].Id);
        LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(accounts[8].Id);
        LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(accounts[9].Id);
        LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(accounts[10].Id);
        
    }
    
    public static List<Account> listOfAccounts()
    {
        
        List<Account> accountList = new List<Account>();
        Account acc1 = new Account();
        Account acc2 = new Account();
        Account acc3 = new Account();
        Account acc4 = new Account();
        Account acc5 = new Account();
        Account acc6 = new Account();
        Account acc7 = new Account();
        Account acc8 = new Account();        
        Account acc9 = new Account();
        Account acc10 = new Account();
        Account acc11 = new Account();
        
        
        Startup__c startup = new Startup__c();
        Startup_Round__c startupRound = new Startup_Round__c();
        
        List<Investment__c> investList = new List<Investment__c>();
        Investment__c inv1 = new Investment__c();
        Investment__c inv2 = new Investment__c();
        Investment__c inv3 = new Investment__c();
        Investment__c inv4 = new Investment__c();
        
        
        acc1.Name = 'testing1';
        acc1.ShippingStreet       = '1 Main St.';
        acc1.ShippingState        = 'VA';
        acc1.ShippingPostalCode   = '12345';
        acc1.ShippingCountry      = 'USA';
        acc1.ShippingCity         = 'Anytown';
        acc1.Description          = 'This is a test account';
        acc1.BillingStreet        = '1 Main St.';
        acc1.BillingState         = 'VA';
        acc1.BillingPostalCode    = '12345';
        acc1.BillingCountry       = 'USA';
        acc1.BillingCity          = 'Anytown';
        acc1.AnnualRevenue        = 10000;
        acc1.ParentId = null;
        acc1.Official_Email__c = '<EMAIL>';
        acc1.Title__c= 'Mr';
        acc1.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc1.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc1.Primary_Country_Code__c= 91;
        acc1.Lead_Source__c= 'Others';
        acc1.Preferred_Email__c= 'Personal';
        acc1.Personal_Email__c = '<EMAIL>';
        acc1.Secondary_Contact__c ='*********';
        acc1.Official_Email__c ='<EMAIL>';
      	acc1.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        Integer len = 10;
        String str = string.valueof(Math.abs(Crypto.getRandomLong()));
        String randomNumber =  str.substring(0, len);
        acc1.Primary_Contact__c= ''+randomNumber ;
        acc1.Primary_Group_Name__c = 'Trial 10';
        acc1.Membership_Status__c = 'Platinum';
        acc1.Relationship_Manager__c = UserInfo.getUserId();
        acc1.Are_They_Part_of_Other_Platform__c = 'Yes';
        acc1.App_signup_date__c = Date.Today();
        acc1.Referred_leads__c = 0;
        acc1.Total_Number_of_Founder_Calls__c = 4;
        acc1.Total_Number_of_Investor_Calls__c = 5;
        acc1.Total_Numbe_of_Offline_Online_event__c = 3;
        acc1.College_Tier__c = 'Tier 1';
        acc1.Designation_Band__c = 'Band 1';
        acc1.Bot_Input__c = 'Interacted -Chat with RM';
        acc1.lead_Source__c = 'Referral'; 
        insert acc1;
        
        
        acc2.Name = 'testing2';
        acc2.ShippingStreet       = '1 Main St.';
        acc2.ShippingState        = 'VA';
        acc2.ShippingPostalCode   = '12345';
        acc2.ShippingCountry      = 'USA';
        acc2.ShippingCity         = 'Anytown';
        acc2.Description          = 'This is a test account';
        acc2.BillingStreet        = '1 Main St.';
        acc2.BillingState         = 'VA';
        acc2.BillingPostalCode    = '12345';
        acc2.BillingCountry       = 'USA';
        acc2.BillingCity          = 'Anytown';
        acc2.AnnualRevenue        = 10000;
        acc2.ParentId = null;
        acc2.Official_Email__c = '<EMAIL>';
        acc2.Title__c= 'Mr';
        acc2.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc2.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc2.Primary_Country_Code__c= 91;
        acc2.Lead_Source__c= 'Others';
        acc2.Preferred_Email__c= 'Personal';
        acc2.Personal_Email__c = '<EMAIL>';
        acc2.Secondary_Contact__c ='*********';
        acc2.Official_Email__c ='<EMAIL>';
      	acc2.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc2.Primary_Contact__c= ''+randomNumber ;
        acc2.Primary_Group_Name__c = 'Trial 10';
        acc2.Membership_Status__c = 'Platinum';
        acc2.Relationship_Manager__c = UserInfo.getUserId();
        acc2.Are_They_Part_of_Other_Platform__c = 'No';
        acc2.App_signup_date__c = null;
        acc2.Referred_leads__c = null;
        acc2.Referred_Accounts__c = null;
        acc2.Total_Number_of_Founder_Calls__c = 0;
        acc2.Total_Number_of_Investor_Calls__c = 0;
        acc2.ParentId = acc2.id;
        acc2.Total_Numbe_of_Offline_Online_event__c = 9;
        acc2.College_Tier__c = 'Tier 2';
        acc2.Designation_Band__c = 'Band 2';
        acc2.Bot_Input__c = 'Interacted -Chat with RM';
        acc2.lead_Source__c = 'Linkedin';
        accountList.add(acc2);
        
        
        acc3.Name = 'testing3';
        acc3.ShippingStreet       = '1 Main St.';
        acc3.ShippingState        = 'VA';
        acc3.ShippingPostalCode   = '12345';
        acc3.ShippingCountry      = 'USA';
        acc3.ShippingCity         = 'Anytown';
        acc3.Description          = 'This is a test account';
        acc3.BillingStreet        = '1 Main St.';
        acc3.BillingState         = 'VA';
        acc3.BillingPostalCode    = '12345';
        acc3.BillingCountry       = 'USA';
        acc3.BillingCity          = 'Anytown';
        acc3.AnnualRevenue        = 10000;
        acc3.ParentId = null;
        acc3.Official_Email__c = '<EMAIL>';
        acc3.Title__c= 'Mr';
        acc3.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc3.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc3.Primary_Country_Code__c= 91;
        acc3.Lead_Source__c= 'Others';
        acc3.Preferred_Email__c= 'Personal';
        acc3.Personal_Email__c = '<EMAIL>';
        acc3.Secondary_Contact__c ='*********';
        acc3.Official_Email__c ='<EMAIL>';
      	acc3.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc3.Primary_Contact__c= ''+randomNumber ;
        acc3.Primary_Group_Name__c = 'Trial 10';
        acc3.Membership_Status__c = 'Platinum';
        acc3.Relationship_Manager__c = UserInfo.getUserId();
        acc3.Are_They_Part_of_Other_Platform__c = null;
        acc3.App_signup_date__c = null;
        acc3.Referred_leads__c = 1;
        acc3.Total_Number_of_Founder_Calls__c = 30;
        acc3.ParentId = acc3.id;
        acc3.Total_Number_of_Investor_Calls__c = 20;
        acc3.Total_Numbe_of_Offline_Online_event__c = 0;
        acc3.College_Tier__c = 'Tier 3';
        acc3.Designation_Band__c = 'Band 3';
        acc3.Bot_Input__c = null;
        acc3.Bot_Journey_Stage__c = null;
        acc3.lead_Source__c = 'Facebook';
        accountList.add(acc3);



        acc4.Name = 'testing4';
        acc4.ShippingStreet       = '1 Main St.';
        acc4.ShippingState        = 'VA';
        acc4.ShippingPostalCode   = '12345';
        acc4.ShippingCountry      = 'USA';
        acc4.ShippingCity         = 'Anytown';
        acc4.Description          = 'This is a test account';
        acc4.BillingStreet        = '1 Main St.';
        acc4.BillingState         = 'VA';
        acc4.BillingPostalCode    = '12345';
        acc4.BillingCountry       = 'USA';
        acc4.BillingCity          = 'Anytown';
        acc4.AnnualRevenue        = 10000;
        acc4.ParentId = null;
        acc4.Official_Email__c = '<EMAIL>';
        acc4.Title__c= 'Mr';
        acc4.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc4.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc4.Primary_Country_Code__c= 91;
        acc4.Lead_Source__c= 'Others';
        acc4.Preferred_Email__c= 'Personal';
        acc4.Personal_Email__c = '<EMAIL>';
        acc4.Secondary_Contact__c ='*********';
        acc4.Official_Email__c ='<EMAIL>';
      	acc4.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc4.Primary_Contact__c= ''+randomNumber ;
        acc4.Primary_Group_Name__c = 'Trial 10';
        acc4.Membership_Status__c = 'Platinum';
        acc4.Relationship_Manager__c = UserInfo.getUserId();
        acc4.Are_They_Part_of_Other_Platform__c = null;
        acc4.App_signup_date__c = null;
        acc4.Referred_leads__c = 7;
        acc4.Total_Number_of_Founder_Calls__c = 7;
        acc4.Total_Number_of_Investor_Calls__c = 3;
        acc4.Total_Numbe_of_Offline_Online_event__c = 0;
        acc4.College_Tier__c = null;
        acc4.Designation_Band__c = 'Band 4';
        acc4.Bot_Input__c = null;
        acc4.Bot_Journey_Stage__c = 'First Message Initiated';
        acc4.lead_Source__c = 'App';
        accountList.add(acc4);  
        

        
        acc5.Name = 'testing5';
        acc5.ShippingStreet       = '1 Main St.';
        acc5.ShippingState        = 'VA';
        acc5.ShippingPostalCode   = '12345';
        acc5.ShippingCountry      = 'USA';
        acc5.ShippingCity         = 'Anytown';
        acc5.Description          = 'This is a test account';
        acc5.BillingStreet        = '1 Main St.';
        acc5.BillingState         = 'VA';
        acc5.BillingPostalCode    = '12345';
        acc5.BillingCountry       = 'USA';
        acc5.BillingCity          = 'Anytown';
        acc5.AnnualRevenue        = 10000;
        acc5.ParentId = null;
        acc5.Official_Email__c = '<EMAIL>';
        acc5.Title__c= 'Mr';
        acc5.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc5.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc5.Primary_Country_Code__c= 91;
        acc5.Lead_Source__c= 'Others';
        acc5.Preferred_Email__c= 'Personal';
        acc5.Personal_Email__c = '<EMAIL>';
        acc5.Secondary_Contact__c ='*********';
        acc5.Official_Email__c ='<EMAIL>';
      	acc5.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc5.Primary_Contact__c= ''+randomNumber ;
        acc5.Primary_Group_Name__c = 'Trial 10';
        acc5.Membership_Status__c = 'Platinum';
        acc5.Relationship_Manager__c = UserInfo.getUserId();
        acc5.Are_They_Part_of_Other_Platform__c = null;
        acc5.App_signup_date__c = null;
        acc5.Referred_leads__c = 4;
        acc5.Total_Number_of_Founder_Calls__c = 13;
        acc5.Total_Number_of_Investor_Calls__c = 3;
        acc5.ParentId = acc5.id;
        acc5.Total_Numbe_of_Offline_Online_event__c = 0;
        acc5.College_Tier__c = null;
        acc5.Designation_Band__c = null;
        acc5.Bot_Input__c = 'Interacted -Chat with RM';
        acc5.lead_Source__c = 'App Referral';
        accountList.add(acc5);
        
        
        acc6.Name = 'testing6';
        acc6.ShippingStreet       = '1 Main St.';
        acc6.ShippingState        = 'VA';
        acc6.ShippingPostalCode   = '12345';
        acc6.ShippingCountry      = 'USA';
        acc6.ShippingCity         = 'Anytown';
        acc6.Description          = 'This is a test account';
        acc6.BillingStreet        = '1 Main St.';
        acc6.BillingState         = 'VA';
        acc6.BillingPostalCode    = '12345';
        acc6.BillingCountry       = 'USA';
        acc6.BillingCity          = 'Anytown';
        acc6.AnnualRevenue        = 10000;
        acc6.ParentId = null;
        acc6.Official_Email__c = '<EMAIL>';
        acc6.Title__c= 'Mr';
        acc6.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc6.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc6.Primary_Country_Code__c= 91;
        acc6.Lead_Source__c= 'Others';
        acc6.Preferred_Email__c= 'Personal';
        acc6.Personal_Email__c = '<EMAIL>';
        acc6.Secondary_Contact__c ='*********';
        acc6.Official_Email__c ='<EMAIL>';
      	acc6.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc6.Primary_Contact__c= ''+randomNumber ;
        acc6.Primary_Group_Name__c = 'Trial 10';
        acc6.Membership_Status__c = 'Platinum';
        acc6.Relationship_Manager__c = UserInfo.getUserId();
        acc6.Are_They_Part_of_Other_Platform__c = null;
        acc6.App_signup_date__c = null;
        acc6.Referred_leads__c = 12;
        acc6.Total_Number_of_Founder_Calls__c = 13;
        acc6.Total_Number_of_Investor_Calls__c = 3;
        acc6.ParentId = Acc6.id;
        acc6.Primary_Country_Code__c = 91;
        acc6.Total_Numbe_of_Offline_Online_event__c = 0;
        acc6.College_Tier__c = null;
        acc6.Designation_Band__c = null;
        acc6.Bot_Input__c = null;
        acc6.Bot_Journey_Stage__c = 'First Message Initiated';
        acc6.lead_Source__c = 'B2B-Luxury Brands';
        accountList.add(acc6);
        
        
        acc7.Name = 'testing7';
        acc7.ShippingStreet       = '1 Main St.';
        acc7.ShippingState        = 'VA';
        acc7.ShippingPostalCode   = '12345';
        acc7.ShippingCountry      = 'USA';
        acc7.ShippingCity         = 'Anytown';
        acc7.Description          = 'This is a test account';
        acc7.BillingStreet        = '1 Main St.';
        acc7.BillingState         = 'VA';
        acc7.BillingPostalCode    = '12345';
        acc7.BillingCountry       = 'USA';
        acc7.BillingCity          = 'Anytown';
        acc7.AnnualRevenue        = 10000;
        acc7.ParentId = null;
        acc7.Official_Email__c = '<EMAIL>';
        acc7.Title__c= 'Mr';
        acc7.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc7.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc7.Primary_Country_Code__c= 91;
        acc7.Lead_Source__c= 'Others';
        acc7.Preferred_Email__c= 'Personal';
        acc7.Personal_Email__c = '<EMAIL>';
        acc7.Secondary_Contact__c ='*********';
        acc7.Official_Email__c ='<EMAIL>';
      	acc7.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc7.Primary_Contact__c= ''+randomNumber ;
        acc7.Primary_Group_Name__c = 'Trial 10';
        acc7.Membership_Status__c = 'Platinum';
        acc7.Relationship_Manager__c = UserInfo.getUserId();
        acc7.Are_They_Part_of_Other_Platform__c = null;
        acc7.App_signup_date__c = null;
        acc7.Referred_leads__c = 1;
        acc7.Primary_Country_Code__c = 1;
        acc7.Total_Number_of_Founder_Calls__c = 13;
        acc7.Total_Number_of_Investor_Calls__c = 3;
        acc7.ParentId = acc1.id;
        acc7.Total_Numbe_of_Offline_Online_event__c = 0;
        acc7.College_Tier__c = null;
        acc7.Designation_Band__c = null;
        acc7.Bot_Input__c = 'Interacted -Chat with RM';
        acc7.lead_Source__c = 'Offline Events';
        accountList.add(acc7);
        
        
        acc8.Name = 'testing8';
        acc8.ShippingStreet       = '1 Main St.';
        acc8.ShippingState        = 'VA';
        acc8.ShippingPostalCode   = '12345';
        acc8.ShippingCountry      = 'USA';
        acc8.ShippingCity         = 'Anytown';
        acc8.Description          = 'This is a test account';
        acc8.BillingStreet        = '1 Main St.';
        acc8.BillingState         = 'VA';
        acc8.BillingPostalCode    = '12345';
        acc8.BillingCountry       = 'USA';
        acc8.BillingCity          = 'Anytown';
        acc8.AnnualRevenue        = 10000;
        acc8.ParentId = null;
        acc8.Official_Email__c = '<EMAIL>';
        acc8.Title__c= 'Mr';
        acc8.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc8.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc8.Primary_Country_Code__c= 91;
        acc8.Lead_Source__c= 'Others';
        acc8.Preferred_Email__c= 'Personal';
        acc8.Personal_Email__c = '<EMAIL>';
        acc8.Secondary_Contact__c ='*********';
        acc8.Official_Email__c ='<EMAIL>';
      	acc8.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc8.Primary_Contact__c= ''+randomNumber ;
        acc8.Primary_Group_Name__c = 'Trial 10';
        acc8.Membership_Status__c = 'Platinum';
        acc8.Relationship_Manager__c = UserInfo.getUserId();
        acc8.Are_They_Part_of_Other_Platform__c = null;
        acc8.App_signup_date__c = null;
        acc8.Referred_leads__c = 1;
        acc8.Primary_Country_Code__c = 1;
        acc8.Total_Number_of_Founder_Calls__c = 13;
        acc8.Total_Number_of_Investor_Calls__c = 3;
        acc8.ParentId = acc1.id;
        acc8.Total_Numbe_of_Offline_Online_event__c = 0;
        acc8.College_Tier__c = null;
        acc8.Designation_Band__c = null;
        acc8.Bot_Input__c = 'Interacted -Chat with RM';
        acc8.lead_Source__c = 'Organic-social media';
        accountList.add(acc8);
        
        
        acc9.Name = 'testing9';
        acc9.ShippingStreet       = '1 Main St.';
        acc9.ShippingState        = 'VA';
        acc9.ShippingPostalCode   = '12345';
        acc9.ShippingCountry      = 'USA';
        acc9.ShippingCity         = 'Anytown';
        acc9.Description          = 'This is a test account';
        acc9.BillingStreet        = '1 Main St.';
        acc9.BillingState         = 'VA';
        acc9.BillingPostalCode    = '12345';
        acc9.BillingCountry       = 'USA';
        acc9.BillingCity          = 'Anytown';
        acc9.AnnualRevenue        = 10000;
        acc9.ParentId = null;
        acc9.Official_Email__c = '<EMAIL>';
        acc9.Title__c= 'Mr';
        acc9.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc9.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc9.Primary_Country_Code__c= 91;
        acc9.Lead_Source__c= 'Others';
        acc9.Preferred_Email__c= 'Personal';
        acc9.Personal_Email__c = '<EMAIL>';
        acc9.Secondary_Contact__c ='*********';
        acc9.Official_Email__c ='<EMAIL>';
      	acc9.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc9.Primary_Contact__c= ''+randomNumber ;
        acc9.Primary_Group_Name__c = 'Trial 10';
        acc9.Membership_Status__c = 'Platinum';
        acc9.Relationship_Manager__c = UserInfo.getUserId();
        acc9.Are_They_Part_of_Other_Platform__c = null;
        acc9.App_signup_date__c = null;
        acc9.Referred_leads__c = 1;
        acc9.Primary_Country_Code__c = 1;
        acc9.Total_Number_of_Founder_Calls__c = 13;
        acc9.Total_Number_of_Investor_Calls__c = 3;
        acc9.ParentId = acc1.id;
        acc9.Total_Numbe_of_Offline_Online_event__c = 0;
        acc9.College_Tier__c = null;
        acc9.Designation_Band__c = null;
        acc9.Bot_Input__c = 'Interacted -Chat with RM';
        acc9.lead_Source__c = 'B2B-Partnerships';
        accountList.add(acc9);
        
        acc10.Name = 'testing7';
        acc10.ShippingStreet       = '1 Main St.';
        acc10.ShippingState        = 'VA';
        acc10.ShippingPostalCode   = '12345';
        acc10.ShippingCountry      = 'USA';
        acc10.ShippingCity         = 'Anytown';
        acc10.Description          = 'This is a test account';
        acc10.BillingStreet        = '1 Main St.';
        acc10.BillingState         = 'VA';
        acc10.BillingPostalCode    = '12345';
        acc10.BillingCountry       = 'USA';
        acc10.BillingCity          = 'Anytown';
        acc10.AnnualRevenue        = 10000;
        acc10.ParentId = null;
        acc10.Official_Email__c = '<EMAIL>';
        acc10.Title__c= 'Mr';
        acc10.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc10.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc10.Primary_Country_Code__c= 91;
        acc10.Lead_Source__c= 'Others';
        acc10.Preferred_Email__c= 'Personal';
        acc10.Personal_Email__c = '<EMAIL>';
        acc10.Secondary_Contact__c ='*********';
        acc10.Official_Email__c ='<EMAIL>';
      	acc10.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc10.Primary_Contact__c= ''+randomNumber ;
        acc10.Primary_Group_Name__c = 'Trial 10';
        acc10.Membership_Status__c = 'Platinum';
        acc10.Relationship_Manager__c = UserInfo.getUserId();
        acc10.Are_They_Part_of_Other_Platform__c = null;
        acc10.App_signup_date__c = null;
        acc10.Referred_leads__c = 1;
        acc10.Primary_Country_Code__c = 1;
        acc10.Total_Number_of_Founder_Calls__c = 13;
        acc10.Total_Number_of_Investor_Calls__c = 3;
        acc10.ParentId = acc1.id;
        acc10.Total_Numbe_of_Offline_Online_event__c = 0;
        acc10.College_Tier__c = null;
        acc10.Designation_Band__c = null;
        acc10.Bot_Input__c = 'Interacted -Chat with RM';
        acc10.lead_Source__c = 'B2B-Corporates';
        accountList.add(acc10);
        
        
        acc11.Name = 'testing11';
        acc11.ShippingStreet       = '1 Main St.';
        acc11.ShippingState        = 'VA';
        acc11.ShippingPostalCode   = '12345';
        acc11.ShippingCountry      = 'USA';
        acc11.ShippingCity         = 'Anytown';
        acc11.Description          = 'This is a test account';
        acc11.BillingStreet        = '1 Main St.';
        acc11.BillingState         = 'VA';
        acc11.BillingPostalCode    = '12345';
        acc11.BillingCountry       = 'USA';
        acc11.BillingCity          = 'Anytown';
        acc11.AnnualRevenue        = 10000;
        acc11.ParentId = null;
        acc11.Official_Email__c = '<EMAIL>';
        acc11.Title__c= 'Mr';
        acc11.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        acc11.Membership_Validity__c= date.newInstance(2020, 9, 15);
        acc11.Primary_Country_Code__c= 91;
        acc11.Lead_Source__c= 'Others';
        acc11.Preferred_Email__c= 'Personal';
        acc11.Personal_Email__c = '<EMAIL>';
        acc11.Secondary_Contact__c ='*********';
        acc11.Official_Email__c ='<EMAIL>';
      	acc11.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        acc11.Primary_Contact__c= ''+randomNumber ;
        acc11.Primary_Group_Name__c = 'Trial 10';
        acc11.Membership_Status__c = 'Platinum';
        acc11.Relationship_Manager__c = UserInfo.getUserId();
        acc11.Are_They_Part_of_Other_Platform__c = null;
        acc11.App_signup_date__c = null;
        acc11.Referred_leads__c = 1;
        acc11.Primary_Country_Code__c = 1;
        acc11.Total_Number_of_Founder_Calls__c = null;
        acc11.Total_Number_of_Investor_Calls__c = null;
        acc11.ParentId = acc1.id;
        acc11.Total_Numbe_of_Offline_Online_event__c = null;
        acc11.College_Tier__c = null;
        acc11.Designation_Band__c = null;
        acc11.Bot_Input__c = 'Interacted -Chat with RM';
        acc11.lead_Source__c = 'CXO Genie Form';
        accountList.add(acc11);
        
        
        
        insert accountList;
        accountList.add(acc1);
        
        /*
        
	// Created These Startup , Startup Round & Investment To Cover The "Total Invested Amount" Scenario of Account. 
	// Commented a Line on InvestmentTriggerHandler Line No. --> 1022
	
		startup.Public_Name__c = 'Demo Startup';
        startup.Legal_Name__c = 'Demo Testing';
        insert startup;
        
        startupRound.Lead_Analyst__c = acc1.Id;
        startupRound.Lead_Member__c = acc1.Id;
        startupRound.Startup__c = startup.Id;
        startupRound.Date_of_Investor_Call__c = Date.Today();
        startupRound.Date_Of_Founders_Call__c = date.newInstance(2020, 9, 15);
        startupRound.Pre_Money_Valuation__c = 10;
        startupRound.Doi_Percent_Fee__c = 11;
        startupRound.Doi_Percent_Equity__c = 12;
        
        insert startupRound;
        
        inv1.Account__c = acc1.Id;
        inv1.Type__c = 'Invested';
        inv1.Investor_Name__c = 'test inv name1';
        inv1.Investor__c = System.Label.Investor_IPV_Advisors_Pvt_Ltd;
        inv1.Investment_Amount__c = 1000;
        inv1.Investor_Type__c = 'Via LLP';
        inv1.Investor_s_PAN__c = '**********';
        inv1.Startup_Round__c = startupRound.Id;
        investList.add(inv1);

        inv2.Account__c = acc2.Id;
        inv2.Type__c = 'Invested';
        inv2.Investor_Name__c = 'test inv name2';
        inv2.Investor__c = System.Label.Investor_IPV_Advisors_Pvt_Ltd;
        inv2.Investment_Amount__c = 650000;
        inv2.Investor_Type__c = 'Via LLP';
        inv2.Investor_s_PAN__c = '**********';
        inv2.Startup_Round__c = startupRound.Id;
        investList.add(inv2);

        inv3.Account__c = acc3.Id;
        inv3.Type__c = 'Invested';
        inv3.Investor_Name__c = 'test inv name3';
        inv3.Investor__c = System.Label.Investor_IPV_Advisors_Pvt_Ltd;
        inv3.Investment_Amount__c = 1650000;
        inv3.Investor_Type__c = 'Via LLP';
        inv3.Investor_s_PAN__c = '**********';
        inv3.Startup_Round__c = startupRound.Id;
        investList.add(inv3);
        
        inv4.Account__c = acc4.Id;
        inv4.Type__c = 'Invested';
        inv4.Investor_Name__c = 'test inv name4';
        inv4.Investor__c = System.Label.Investor_IPV_Advisors_Pvt_Ltd;
        inv4.Investment_Amount__c = 2650000;
        inv4.Investor_Type__c = 'Via LLP';
        inv4.Investor_s_PAN__c = '**********';
        inv4.Startup_Round__c = startupRound.Id;
        investList.add(inv4);
        
        insert investList;
        
        */
        
        return accountList;
    }
}