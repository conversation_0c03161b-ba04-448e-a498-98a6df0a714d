trigger LeadTrigger on Lead (before insert,After insert) {
    
    if(Trigger.isBefore && Trigger.isInsert){
        
        system.debug('BeforeInsert'+ Trigger.new);
        leadStdTriggerHandler ldHandler = new leadStdTriggerHandler();
        ldHandler.beforeInsert(Trigger.new);
       
    }
    else if(trigger.isAfter && trigger.isinsert)
    {  
        system.debug('AfterInsert'+ Trigger.new);
        leadStdTriggerHandler ldHandler = new leadStdTriggerHandler();
        ldHandler.AfterInsert(Trigger.new);  
    }
}