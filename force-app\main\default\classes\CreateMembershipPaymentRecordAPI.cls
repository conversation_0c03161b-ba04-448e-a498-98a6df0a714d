@RestResource(urlMapping='/CreateMembershipPaymentRecordAPI/*')
Global with sharing class CreateMembershipPaymentRecordAPI {
    
    @HttpPost
    global Static ResponseWrapper createTransaction()
    {
        responseWrapper wResponse = new responseWrapper();
        try
        {
            RestRequest req = RestContext.request;
            RestResponse res = Restcontext.response;
            string jsonReqString=req.requestBody.tostring();
            List<Transaction__c> transactionInsertList = new List<Transaction__c>();
            Set<Integer> countryCodeSet = new Set<Integer>();
            Set<String> primaryNumberSet = new Set<String>();
            id IPVrecordTypeId = Schema.SObjectType.lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
            id IPVAccRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
            Id recordId;
            String sourceObjectName = '';
            
            requestWrapper wResp=(requestWrapper) JSON.deserialize(jsonReqString,requestWrapper.class);
            System.Debug('wResp>>>>>>>>>>>>>' + wResp);
            
            If(wResp!=null && wResp.dataList != null)
            {
                for(transactionRequestWrapper trw : wResp.dataList)
                {
                    if(trw.is_renew == false)
                    {
                        Transaction__c trc = new Transaction__c();
                        if(trw.payment_amount != null){
                            trc.Amount__c = trw.payment_amount;
                        }
                        if(trw.payment_id != null){
                            trc.Razorpay_Payment_ID__c = trw.payment_id;
                        }
                        if(trw.order_id != null){
                            trc.Order_ID__c = trw.order_id;
                        }
                        if(trw.transaction_at != null){
                            trc.Transaction_Date__c = trw.transaction_at;
                        }
                        trc.Transaction_Type__c = 'New Membership Payment';
                        if((trw.promo_code != null && trw.is_promo_code == TRUE)){
                            trc.Coupon_Code_Used__c = trw.promo_code;
                        }
                        if((trw.refer_code != null && trw.is_refer_code == TRUE)){
                            trc.Coupon_Code_Used__c = trw.refer_code;
                        }
                        
                        if(trw.payment_discount != null)
                        {
                            trc.Total_Discount__c = trw.payment_discount;
                        }
                        
                        If(trw.salesforce_user_account_id != null)
                        {
                            recordId = trw.salesforce_user_account_id;
                            sourceObjectName = recordId.getSObjectType().getDescribe().getName();
                            System.Debug('recordId>>>>>>' + recordId);
                            System.Debug('sourceObjectName>>>>>>' + sourceObjectName);
                            
                            if(sourceObjectName == 'Lead__c'){
                                trc.Leads_Name__c = trw.salesforce_user_account_id;
                            }else If(sourceObjectName == 'Account'){
                                trc.Account_Name__c = trw.salesforce_user_account_id;
                            }	
                        }else{
                            Lead__c paymentAcc = [SELECT Id , Name , Primary_Contact__c , Primary_Country_Code__c FROM Lead__c WHERE Primary_Contact__c =: String.valueOf(trw.contact) AND Primary_Country_Code__c =: Integer.valueOf(trw.countryCode) AND recordTypeId =: IPVrecordTypeId];
                            If(paymentAcc != null){
                                trc.Leads_Name__c = paymentAcc.Id ;
                            }
                        }
                        transactionInsertList.add(trc);
                    }
                    else if(trw.is_renew == true)
                    {
                        Transaction__c trc = new Transaction__c();
                        
                        if(trw.payment_amount != null){
                            trc.Amount__c = trw.payment_amount;
                        }
                        
                        if(trw.payment_id != null){
                            trc.Razorpay_Payment_ID__c = trw.payment_id;
                        }
                        
                        if(trw.order_id != null){
                            trc.Order_ID__c = trw.order_id;
                        }
                        
                        if(trw.transaction_at != null){
                            trc.Transaction_Date__c = trw.transaction_at;
                        }
                        
                        trc.Transaction_Type__c = 'Renewal Membership';

                        if(trw.total_used_points != null)
                        {
                            trc.Total_Point_Used__c = trw.total_used_points;
                        }
                        
                        if(trw.total_year_of_payment != null && trw.total_year_of_payment == 1)
                        {
                            trc.Total_Year_of_Payment__c = '1 Year';
                        }
                        else if(trw.total_year_of_payment != null && trw.total_year_of_payment == 2)
                        {
                            trc.Total_Year_of_Payment__c = '2 Years';
                        }
                        else if (trw.total_year_of_payment != null && trw.total_year_of_payment == 3)
                        {
                            trc.Total_Year_of_Payment__c = '3 Years';
                        }
                        else if (trw.total_year_of_payment != null && trw.total_year_of_payment == 4)
                        {
                            trc.Total_Year_of_Payment__c = '4 Years';
                        }
                        else if (trw.total_year_of_payment != null && trw.total_year_of_payment == 5)
                        {
                            trc.Total_Year_of_Payment__c = '5 Years';
                        }

                        if(trw.payment_discount != null)
                        {
                            trc.Total_Discount__c = trw.payment_discount;
                        }

                        if(trw.salesforce_user_account_id != null)
                        {
                            recordId = trw.salesforce_user_account_id;
                            sourceObjectName = recordId.getSObjectType().getDescribe().getName();
                            System.Debug('recordId>>>>>>' + recordId);
                            System.Debug('sourceObjectName>>>>>>' + sourceObjectName);
                            
                           if(sourceObjectName == 'Account'){
                                trc.Account_Name__c = trw.salesforce_user_account_id;
                            }	
                        }

                        transactionInsertList.add(trc);
                    }
                }
                if(transactionInsertList != null && transactionInsertList.size() > 0)
                {
                    insert transactionInsertList;
                    for(Transaction__c tc: transactionInsertList)
                    {
                        System.debug('NEW TRANSACTION ID>>>>>'+tc.Id);
                        System.debug('NEW TRANSACTION Number>>>>>'+ tc.Name);
                        System.debug('NEW TRANSACTION>>>>>'+tc);
                    }
                }
                wResponse.status = true;
                wResponse.message= 'Transaction records are created successfully';
            }else{
                wResponse.status = false;
                wResponse.message= 'Transaction list is null';
            }
        }
        catch(exception e)
        {
            wResponse.status = false;
            wResponse.message= 'Exception:'+e.getMessage();
        }
        return wResponse;
    }
    
    global class transactionRequestWrapper{
        
        global Decimal payment_amount ;
        global boolean is_promo_code;
        global boolean is_refer_code;
        global String refer_code ;
        global String payment_id;
        global boolean is_renew;
        global DateTime transaction_at;
        global Integer total_year_of_payment;
        global String promo_code;
        global string contact;
        global string countryCode;
        global String order_id;
        global Double payment_discount;
        global String payment_status;
        global String salesforce_user_account_id;
        global Integer total_used_points;
    }
    
    global class requestWrapper{
        global List<transactionRequestWrapper> dataList;
    }
    global class responseWrapper{
        global boolean status;
        global string message;
    }
}