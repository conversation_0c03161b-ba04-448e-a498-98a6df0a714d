public class AccountExcelUploader {
    public static void uploadExcelToAccount(Id accountId, List<Map<String, String>> failedTransactions, String fileName) {
        try {
            // Generate Excel XML content
            String xmlContent = generateExcelXML(failedTransactions);

            // Convert XML to Blob
            Blob excelBlob = Blob.valueOf(xmlContent);

            // Create an Attachment record
            Attachment attach = new Attachment(
                Name = fileName,
                Body = excelBlob,
                ContentType = 'application/vnd.ms-excel',
                ParentId = accountId
            );

            // Insert the attachment
            insert attach;
            System.debug('Excel File uploaded successfully: ' + attach.Id);
        } catch (Exception e) {
            System.debug('Error uploading Excel: ' + e.getMessage());
        }
    }

    private static String generateExcelXML(List<Map<String, String>> failedTransactions) {
        String header = '<?xml version="1.0"?>' +
            '<?mso-application progid="Excel.Sheet"?>' +
            '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" ' +
            'xmlns:o="urn:schemas-microsoft-com:office:office" ' +
            'xmlns:x="urn:schemas-microsoft-com:office:excel" ' +
            'xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" ' +
            'xmlns:html="http://www.w3.org/TR/REC-html40">' +
            '<Worksheet ss:Name="Failed Transactions">' +
            '<Table>';

        // Add column headers
        String headers = '<Row>' +
            '<Cell><Data ss:Type="String">Request Body</Data></Cell>' +
            '<Cell><Data ss:Type="String">Response Body</Data></Cell>' +
            '</Row>';

        // Add transaction data
        String rows = '';
        for (Map<String, String> trans : failedTransactions) {
            String requestBody = trans.containsKey('Request Body') ? trans.get('Request Body') : '';
            String responseBody = trans.containsKey('Response Body') ? trans.get('Response Body') : '';

            rows += '<Row>' +
                '<Cell><Data ss:Type="String">' + escapeXML(requestBody) + '</Data></Cell>' +
                '<Cell><Data ss:Type="String">' + escapeXML(responseBody) + '</Data></Cell>' +
                '</Row>';
        }

        // Closing tags
        String footer = '</Table></Worksheet></Workbook>';

        return header + headers + rows + footer;
    }

    private static String escapeXML(String input) {
        if (String.isEmpty(input)) return '';
        return input.replace('&', '&amp;')
                    .replace('<', '&lt;')
                    .replace('>', '&gt;')
                    .replace('"', '&quot;')
                    .replace('"', '&apos;');
    }
}