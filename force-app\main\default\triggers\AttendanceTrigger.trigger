trigger AttendanceTrigger on Attendance__c (before insert,After Insert,After Update,before delete , after delete){
    
    AttendanceTriggerHandler attHandler = new AttendanceTriggerHandler();
    if(trigger.isInsert && trigger.isBefore)
    {
        attHandler.beforeInsert(trigger.New);
    }
    if(trigger.isInsert && trigger.isAfter)
    {
       // attHandler.afterInsert(trigger.New);
        attHandler.updateCountOnAccount(trigger.New);
        attHandler.createPointTransaction(trigger.New);
         attHandler.handleEventActivity(trigger.New);
    }
     if(trigger.isupdate && trigger.isAfter)
    {
        attHandler.updateCountOnAccount(trigger.New);
        attHandler.handleEventActivity(trigger.New);
    }    
    if(trigger.isDelete && trigger.isAfter)
    {
        attHandler.updateCountOnAccount(trigger.old);
    }
}