global class UserActivityTimelineScheduale implements Schedulable {
    global void execute(SchedulableContext sc) {
  
        Integer batchSize = Integer.valueOf(System.Label.Batch_Size_For_User_TimeLine_API);
        System.debug('Batch Size >>>>>> ' + batchSize);

        UserActivityTimelineBatchForAccount Accountbatch = new UserActivityTimelineBatchForAccount();
        Database.executeBatch(Accountbatch,batchSize);
       System.debug('Finished UserActivityTimelineBatchForAccount execution>>>>>>');
        
        UserActivityTimelineBatchForLead Leadbatch = new UserActivityTimelineBatchForLead();
        Database.executeBatch(Leadbatch, batchSize);
      System.debug('Finished UserActivityTimelineBatchForLead execution>>>>>>' );
        
          DeleteUserActivityRecord deletionHandler = new DeleteUserActivityRecord();
          deletionHandler.deleteRecords();
         System.debug('Deleted UserActivity Record execution>>>>>>'  );
    }
}