global class OutboundMessageAPIController {
    public static set<String> leadSource = new Set<String>{'Offline Events','WealthTrust','External Masterclass Session','Whatsapp','Email','Facebook','Linkedin','Website','Masterclass','Google Form','Others','Saturday Calls','LinkedIn Lead','Member Referral','ET Newspaper','Webinar leads','IPV Database','Status Match 1','Status Match 4','Status Match','Feedback Form','Linkedin - Sales Navigator','Partnerships','CXO Leads','Partnerships - Vinners','Investor Harvesting','Sweetbox DB','Real Estate Project','IPV Portfolio Founder','External Database','MF Database','Events','YouTube','SonyLIVweb','Twitter','Ozonetel','App - Startup Referral','Instagram','Apollo Tool','External Sessions','Online Events','Instagram DM','FB Comment','FB DM','Webinar','FGD','Google Display Ads','Sales QL'};
        @future(callout=true)
        public static void sendWhatsappAPILead(set<Id> leadIdSet)
    {
       /* List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('OutboundMessageAPIController API_Setting__c>>>'+settingList);
        if(settingList==null)
            return;
        
        List<Lead__c> leadList = [select id,name,Full_Primary_Contact__c,Relationship_Owner__r.Name,Personal_Email__c,Date_of_receiving_lead__c from Lead__c where id in :leadIdSet ];
        String fromAddress = ''+settingList[0].Whatsapp_from_address__c;//'917338180839';
        String toAddress = '';
        String sId = ''+settingList[0].Whatsapp_sId__c;//'HXAP1694460718IN';
        String apikey = ''+settingList[0].Whatsapp_api_key__c;//'A0da16b6b946124f5b3e6ef6cb7b2e630';
        String enddURL = ''+settingList[0].Whatsapp_API_end_URL__c;//'https://e2ewebservice20190528111726.azurewebsites.net/api/KORAIWhatsappNotification'; New URL -https://orailap.azurewebsites.net
        String templateName = ''+settingList[0].Whatsapp_API_Lead_Template_Name__c;//'https://e2ewebservice20190528111726.azurewebsites.net/api/KORAIWhatsappNotification';
        //  String mediaa = 'https://www.learningcontainer.com/wp-content/uploads/2019/09/sample-pdf-file.pdf';
        HttpRequest request = new HttpRequest();
        request.setEndpoint(enddURL);
        request.setHeader('Content-Type','application/json');
        request.setMethod('POST');
        request.setTimeout(120000);
        
        for(Lead__c ld : leadList)
        {
            toAddress = ld.Full_Primary_Contact__c;
            
            String jsonData = '{'+
                '"from": "'+fromAddress+'",'+
                '"to": "'+toAddress+'",'+
                '"type":"", '+
                '"template_name":"'+templateName+'",';
            if(templateName == 'unapproved_back_out_pdf') 
            { jsonData = jsonData + '"media":"https://s3.ap-south-1.amazonaws.com/file-upload-public/prod/34782/SEND_DOCUMENT_ATTACHMENT/88742_IPV_Membership_Document.pdf-BpxmP.pdf",';
            }
            
            jsonData = jsonData + '"params":"\\"'+ld.name+'\\",\\"'+ld.Relationship_Owner__r.Name+'\\",\\"'+ld.Personal_Email__c+'\\",\\"'+ld.Date_of_receiving_lead__c+'\\",\\"'+ld.Relationship_Owner__r.Name+'\\"",'+
                '"Sid":"'+sId+'",'+
                '"apikey":"'+apikey+'"'+
                '}';
            
            
            system.debug('request jsonData>>>'+jsonData);
            request.setBody(jsonData);
            
            Http http1 = new Http();
            system.debug('request body>>>'+request.getBody());
            HTTPResponse res1 = http1.send(request);
            system.debug('res1 >>>'+res1 );
        }
        */
    }
    
    @InvocableMethod (label='Send Inv Msg')
    public static void sendWhatsappAPIInvPB(List<Id> invIDs) 
    {
        Map<Id,List<String>> invIdTemplateMap1 = new Map<Id,List<String>>();
        //Added by Karan removed field from below query Startup_Round__r.Date_of_sending_out_call_for_money__c to send Bot message.
        List<Investment__c > invList = [select id,Type__c,Investment_Date__c,Startup_Round__r.Startup_Name__c,Account__r.Bot_Communication_Country_Code__c,Account__r.Bot_Communication_Number__c,Account__r.name,Startup_Name__c,Account__r.Full_Primary_Contact__c from Investment__c where id in :invIDs];
        
        for(Investment__c inv : invList)
        {
            if (!invIdTemplateMap1.containsKey(inv.Id)) {
                invIdTemplateMap1.put(inv.Id, new List<String>());   
            }
            invIdTemplateMap1.get(inv.Id).add('fund_transfered_confirmation');
        }
        // sendWhatsappAPIInv(invIdTemplateMap1);
        String jsonData = JSON.serialize(invIdTemplateMap1);
        sendWhatsappAPIInv(jsonData);
    }
    
    @future(callout=true)
    public static void sendWhatsappAPIInv(String jsonMap)
    {       
            // Deserialize JSON back to Map<Id, List<String>>
            Map<Id, List<String>> invIdTemplateMap = (Map<Id, List<String>>) JSON.deserialize(jsonMap, Map<Id, List<String>>.class);
    
            List<API_Setting__c> settingList = API_Setting__c.getall().values();
            system.debug('OutboundMessageAPIController API_Setting__c>>>'+settingList);
            if(settingList==null)
                return;
            
            List<Investment__c > invList = [select id,Account__r.Relationship_Manager__r.Name,Account__r.Relationship_Manager__r.FirstName,Account__r.Relationship_Manager__r.LastName,Account__r.Relationship_Manager__r.Contact_No__c,Relationship_Manager__c,Type__c,IPV_Fees_Cheque__c,Investment_Date__c,Startup_Round__r.Date_of_Investor_Call__c,Startup_Round__r.Startup_Name__c,Account__r.Bot_Communication_Country_Code__c,Account__r.Bot_Communication_Number__c,Account__r.name,Startup_Name__c,Account__r.Full_Primary_Contact__c,Investment_Amount__c,Investment_Fee_Received__c , Startup_round__r.IR_Owner_Email__c from Investment__c where id in :invIdTemplateMap.Keyset()];
            String fromAddress = ''+settingList[0].Whatsapp_from_address__c;
            String toAddress = '';
            String sId = ''+settingList[0].Whatsapp_sId__c;
            String apikey = ''+settingList[0].Whatsapp_api_key__c;
            String enddURL = ''+settingList[0].Whatsapp_API_end_URL__c;
            String media = 'https://fpu.branding-element.com/prod/50537/SEND_DOCUMENT_ATTACHMENT/44062_How_To_Earn_Points.pdf-WLjML.pdf';
            HttpRequest request = new HttpRequest();
            request.setEndpoint(enddURL);
            request.setHeader('Content-Type','application/json');
            request.setHeader('api-key', apikey);
            request.setMethod('POST');
            request.setTimeout(120000);
            
            for(Investment__c inv : invList )
            {
                system.debug('Processing Investment: ' + inv.Id);
                toAddress = inv.Account__r.Full_Primary_Contact__c;
                if(inv.Account__r.Bot_Communication_Country_Code__c!=null && inv.Account__r.Bot_Communication_Number__c!=null && inv.Account__r.Bot_Communication_Number__c!='')
                    toAddress = inv.Account__r.Bot_Communication_Country_Code__c + inv.Account__r.Bot_Communication_Number__c;
                
                if(invIdTemplateMap.containsKey(inv.Id)) {
                    for(String templateName : invIdTemplateMap.get(inv.Id)) {
                        String rmName = inv.Account__r.Relationship_Manager__r.FirstName+ ' '+ inv.Account__r.Relationship_Manager__r.LastName;
                        if(rmName!=null && rmName.contains('null'))
                            rmName = rmName.remove('null');
                        
                        String jsonData = '{'+
                            '"from": "'+fromAddress+'",'+
                            '"to": "'+toAddress+'",'+
                            '"type":"mediatemplate", '+
                            '"channel":"whatsapp", '+
                            '"template_name":"'+templateName+'",';
                //'"params":"\\"'+inv.Account__r.name+'\\",\\"'+inv.Startup_Name__c +'\\",\\"'+inv.Account__r.name+'\\"",'+
                if(templateName == 'waitlist_to_commitment_confirmation_auto')
                    jsonData = jsonData + '"params":"\\"'+inv.Account__r.name+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\",\\"'+rmName+'\\",\\" '+inv.Account__r.Relationship_Manager__r.Contact_No__c+'\\"",';
                
                else if(templateName == 'commitment_released_confirmation_auto_bot')
                    jsonData = jsonData + '"params":"\\"'+inv.Account__r.name+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\",\\"'+rmName+'\\",\\" '+inv.Account__r.Relationship_Manager__r.Contact_No__c+'\\"",';
                
                // Added by ankush for FnF T1&T2 and unapproved BOT Message. 16.3.23.
                
                else if(templateName =='fnf_t1_investment_amount_received_confirmation')
                    //JsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+'Rs'+'\\",\\"'+Inv.Investment_Amount__c+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\"",';
                    JsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+Inv.Investment_Amount__c+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\"",';

                else if(templateName =='fnf_t2_investment_amount_received_confirmation')
                    //JsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+'Rs'+'\\",\\"'+Inv.Investment_Amount__c+'\\",\\"'+'Rs'+'\\",\\"'+Inv.Investment_Fee_Received__c+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\"",';
                    JsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+Inv.Investment_Amount__c+'\\",\\"'+Inv.Investment_Fee_Received__c+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\"",';

                else if(templateName =='confirmation_for_waitlist_members_confirmed_by_the_lead_member')
                    //JsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\",\\"'+'(link expires in 72 hours)'+'\\",\\"'+'100'+'\\",\\"'+rmName+'\\",\\"'+Inv.Account__r.Relationship_Manager__r.Contact_NO__c+'\\"",';
                    JsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\"",';

                else if(templateName =='unapproved_back_out_pdf'){
                    jsonData = jsonData + '"media_url":"'+media+'",'+'"params":"\\"'+inv.Account__r.name+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\"",';
                }
                else if(templateName =='platform_investment_amount_received_confirmation')
                    //jsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\",\\"'+'Rs'+'\\",\\"'+Inv.Investment_Amount__c+'\\"",';
                    jsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\",\\"'+Inv.Investment_Amount__c+'\\"",';

                else if(templateName =='aif_investment_amount_received_confirmation')
                    //jsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+'Rs'+'\\",\\"'+Inv.Investment_Amount__c+'\\",\\"'+'Rs'+'\\",\\"'+Inv.Investment_Fee_Received__c+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\"",';
                    jsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+Inv.Investment_Amount__c+'\\",\\"'+Inv.Investment_Fee_Received__c+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\"",';

                else if(templateName =='ipv_investment_feereceived_confirmation')
                    jsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\",\\"'+Inv.Investment_Fee_Received__c+'\\"",';
                else if(templateName == 'nri_communication') 
                    jsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\",\\"'+Inv.Startup_Round__r.IR_Owner_Email__c+'\\"",';
                
                else
                    jsonData = jsonData +'"params":"\\"'+inv.Account__r.name+'\\",\\"'+inv.Startup_Round__r.Startup_Name__c+'\\"",';//Account__r.Relationship_Manager__r.Name
                
                jsonData = jsonData + '"Sid":"'+sId+'",'+
                    '"apikey":"'+apikey+'"'+
                    '}';
                
                    system.debug('request jsonData>>>'+jsonData);
                    request.setBody(jsonData);
                    
                    try {
                        Http http1 = new Http();
                        HttpResponse res1 = http1.send(request);
                        system.debug('API Response: ' + res1.getBody());
                    } catch (Exception e) {
                        system.debug('Error sending API request: ' + e.getMessage());
                    }
                }
            }
        }
    }
    
    
    public static void sendWhatsappAPIForNonRefLead(set<Id> leadIdSet)
    {
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('OutboundMessageAPIController called...'+settingList);
        if(settingList==null)
            return;
        
        List<Lead__c> leadList = [select id,name,Primary_Contact__c,Primary_Country_Code__c,Referred_By__c,Referred_By__r.Name,Bot_Journey_Stage__c,Relationship_Owner__r.Name,Personal_Email__c,Lead_Source__c,Date_of_receiving_lead__c,Membership_Status__c,BOT_Journey_Switch__c from Lead__c where id in :leadIdSet ];
        
        List<Lead__c> leadsToUpdate = new List<Lead__c>(); // Collect leads that need to be updated
        
        
        // Commit the DML transaction for updates
      
        String enddURL = 'https://waba.360dialog.io/v1/messages';  //''+settingList[0].Whatsapp_API_end_URL__c;//'https://e2ewebservice20190528111726.azurewebsites.net/api/KORAIWhatsappNotification'; New URL -https://orailap.azurewebsites.net
        String templateName ='onboard';        
        for(Lead__c ld : leadList)
        {   
            if(ld.Referred_By__c != null){
                templateName = 'referred';
            }
            if(ld.Bot_Journey_Stage__c == 'First Message Initiated'){
                templateName = 'kickstart';
            }
            if(ld.Bot_Journey_Stage__c == 'Second Message Initiated'){
                templateName = 'startup_investing_opportunities';
            }   
            
            system.debug('ld.BOT_Journey_Switch__c ...'+ld.BOT_Journey_Switch__c);
            system.debug('ld.Membership_Status__c ...'+ld.Membership_Status__c);
            system.debug('ld.Lead_Source__c ...'+ld.Lead_Source__c);
            
            if(ld.BOT_Journey_Switch__c){                
                        if((ld.Membership_Status__c == 'New Lead' || ld.Membership_Status__c == 'No Response - Call 1' ||
                    ld.Membership_Status__c == 'No Response - Call 2' ||ld.Membership_Status__c == 'No Response - Call 3' ||
                    ld.Membership_Status__c == 'No Response - Call 1') && Leadsource.contains(ld.Lead_Source__c)){
                        String JSONBody = '{'+'"template": {'+'"components": ['+'{'+
                            '"type": "body",'+'"parameters": ['+'{'+'"type": "text",'+'"text": "'+ld.Name+'"'+'},';
                        if(ld.Referred_By__c != null){
                            JSONBody += '{'+
                                '"type": "text",'+
                                '"text": "'+ld.Referred_By__r.Name+'"'+
                                '},';
                        }
                        JSONBody += '{'+
                            '"type": "text",'+
                            '"text": "https://play.google.com/store/apps/details?id=com.ipv.memberapp"'+
                            '},'+'{'+'"type": "text",'+'"text": "https://apps.apple.com/in/app/inflection-point-ventures/id1518536213"'+
                            '}'+']'+'},'+'{'+'"type": "button",'+'"index": 0,'+'"parameters": ['+'{'+'"type": "payload",'+
                            '"payload": "flow_8A9F0EDB66594098820F63026AAC2382"'+'}'+'],'+'"sub_type": "quick_reply"'+'},'+'{'+'"type": "button",'+
                            '"index": 1,'+'"parameters": ['+'{'+'"type": "payload",'+'"payload": "flow_545395A72B6C4ADEA54EA848A9325F75"'+
                            '}'+'],'+'"sub_type": "quick_reply"'+'},'+'{'+'"type": "button",'+'"index": 2,'+
                            '"parameters": ['+'{'+'"type": "payload",'+'"payload": "flow_676D58DE608C4563B68DA2881A3EE4A8"'+
                            '}'+'],'+'"sub_type": "quick_reply"'+'}'+'],'+'"namespace": "1f7ae8e5_ec1e_47d8_b7a6_3d16c14cd346",'+
                            '"name": "'+templateName+'",'+'"language": {'+'"code": "en",'+'"policy": "deterministic"'+
                            '}'+'},'+'"to": "'+ld.Primary_Country_Code__c+ld.Primary_Contact__c+'",'+'"type": "template"'+'}';
                             calloutForNonRefLead(ld.id,JSONBody);                 
            }                  
            }            
        }       
        //ID jobID = System.enqueueJob(new LeadOraiQueueableController(leadsToUpdate));
        //System.debug('jobID'+jobID);
    }
    
    @future(callout=true)
    public static void calloutForNonRefLead(Id leadid,string jsonBody)
    {
        Lead__c ld = [select id,name,Primary_Contact__c,Primary_Country_Code__c,Referred_By__c,Referred_By__r.Name,Bot_Journey_Stage__c,Relationship_Owner__r.Name,Personal_Email__c,Lead_Source__c,Date_of_receiving_lead__c,Membership_Status__c,BOT_Journey_Switch__c from Lead__c where id = :leadId ];
        HttpRequest request = new HttpRequest();
                        request.setEndpoint('https://waba.360dialog.io/v1/messages');
                        request.setHeader('D360-API-KEY', 'hxg0eTHaVLpQKODiNkeL7CxOAK');
                        request.setHeader('Content-Type','application/json');
                        request.setMethod('POST');
                        request.setTimeout(120000);
                        system.debug('request jsonData>>>'+jsonBody);
                        request.setBody(jsonBody);
                        
                        Http http1 = new Http();
                        system.debug('request body>>>'+request.getBody());
                        HTTPResponse res1 = http1.send(request);
                        system.debug('res1 >>>'+res1 );
                        system.debug('ld.Bot_Journey_Stage__c >>>'+ld.Bot_Journey_Stage__c );
                        system.debug('res1.getStatus() >>>'+res1.getStatus() );
                        if(res1.getStatus() == 'CREATED' && res1.getStatusCode() == 201){
                            if(ld.Bot_Journey_Stage__c == '' || ld.Bot_Journey_Stage__c == null){
                                Datetime scheduledTime = Datetime.now().addHours(24);
                                String cronExpression = '' + scheduledTime.second() + ' ' + scheduledTime.minute() + ' ' + scheduledTime.hour() + ' ' + scheduledTime.day() + ' ' + scheduledTime.month() + ' ? ' + scheduledTime.year();
                                LeadWhatsappScheduler.scheduleJob('nextMessage hours for :'+ld.Id,cronExpression,new set<id>{ld.Id});
                                scheduledTime = Datetime.now().addHours(72);
                                cronExpression = '' + scheduledTime.second() + ' ' + scheduledTime.minute() + ' ' + scheduledTime.hour() + ' ' + scheduledTime.day() + ' ' + scheduledTime.month() + ' ? ' + scheduledTime.year();
                                LeadWhatsappScheduler.scheduleJob('Execute msg After 72 hours for :'+ld.Id,cronExpression,new set<id>{ld.Id});  
                                ld.Bot_Journey_Stage__c = 'First Message Initiated';
                               // leadsToUpdate.add(ld);
                            } 
                            else if(ld.Bot_Journey_Stage__c == 'First Message Initiated'){
                                ld.Bot_Journey_Stage__c = 'Second Message Initiated';
                            }
                            //leadsToUpdate.add(ld);
                            update ld;
                        }
    }
}