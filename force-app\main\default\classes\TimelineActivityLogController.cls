public with sharing class TimelineActivityLogController {

    @AuraEnabled(cacheable=true)
    public static List<ActivityWrapper> getActivities(Id accountId, List<String> activityTypes, Date startDate, Date endDate) {
        List<ActivityWrapper> activities = new List<ActivityWrapper>();

        // Validate the accountId
        if (accountId == null) {
            return activities; // Return an empty list if no accountId is provided
        }

        // Set default dates if startDate or endDate are null
        if (startDate == null) {
            startDate = Date.today().addYears(-5); // Default to 1 year ago
            system.debug('Default Start Date: ' + startDate);
        }
        if (endDate == null) {
            endDate = Date.today().addYears(5); // Default to today
            system.debug('Default End Date: ' + endDate);
        }

        // Convert Date to DateTime to ensure proper query
        DateTime startDateTime = DateTime.newInstance(startDate.year(), startDate.month(), startDate.day(), 0, 0, 0);
        DateTime endDateTime = DateTime.newInstance(endDate.year(), endDate.month(), endDate.day(), 23, 59, 59);

        // Query activities for Account
        List<User_Activity__c> accountActivities = new List<User_Activity__c>();
        if (activityTypes != null && !activityTypes.isEmpty()) {
            accountActivities = [SELECT Activity_Type__c, Activity_Detail_RICH__c, Time_Stamp__c 
                                 FROM User_Activity__c 
                                 WHERE Related_Account__c = :accountId 
                                 AND Activity_Type__c IN :activityTypes
                                 AND Time_Stamp__c >= :startDateTime
                                 AND Time_Stamp__c <= :endDateTime
                                 ORDER BY Time_Stamp__c DESC];
        } else {
            accountActivities = [SELECT Activity_Type__c, Activity_Detail_RICH__c, Time_Stamp__c 
                                 FROM User_Activity__c 
                                 WHERE Related_Account__c = :accountId 
                                 AND Time_Stamp__c >= :startDateTime
                                 AND Time_Stamp__c <= :endDateTime
                                 ORDER BY Time_Stamp__c DESC];
        }

        // Add account activities to the wrapper list
        for (User_Activity__c act : accountActivities) {
            activities.add(new ActivityWrapper(act.Activity_Type__c, act.Activity_Detail_RICH__c, act.Time_Stamp__c));
        }

        // Query activities for Lead
        List<User_Activity__c> leadActivities = new List<User_Activity__c>();
        if (activityTypes != null && !activityTypes.isEmpty()) {
            leadActivities = [SELECT Activity_Type__c, Activity_Detail_RICH__c, Time_Stamp__c
                              FROM User_Activity__c 
                              WHERE Related_Lead__c = :accountId 
                              AND Activity_Type__c IN :activityTypes
                              AND Time_Stamp__c >= :startDateTime
                              AND Time_Stamp__c <= :endDateTime
                              ORDER BY Time_Stamp__c DESC];
        } else {
            leadActivities = [SELECT Activity_Type__c, Activity_Detail_RICH__c, Time_Stamp__c 
                              FROM User_Activity__c 
                              WHERE Related_Lead__c = :accountId 
                              AND Time_Stamp__c >= :startDateTime
                              AND Time_Stamp__c <= :endDateTime
                              ORDER BY Time_Stamp__c DESC];
        }

        // Add lead activities to the wrapper list
        for (User_Activity__c act : leadActivities) {
            activities.add(new ActivityWrapper(act.Activity_Type__c, act.Activity_Detail_RICH__c, act.Time_Stamp__c));
        }

        return activities;
    }

    public class ActivityWrapper {
        @AuraEnabled public String activityType;
        @AuraEnabled public String activityDetail;
        @AuraEnabled public DateTime activityTimestamp;

        public ActivityWrapper(String type, String detail, DateTime timestamp) {
            this.activityType = type;
            this.activityDetail = detail;
            this.activityTimestamp = timestamp;
        }
    }
}