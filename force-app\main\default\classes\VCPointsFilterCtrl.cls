global class VCPointsFilterCtrl
{
    Public sObject sObjStd{get;set;} 
    Public static String sObjectType;
    
    Public List<StartupVConnectWrapper> StartupVConnectWrapperFullList{get;set;}
    Public List<StartupVConnectWrapper> StartupVConnectWrapperList{get;set;}
    Map<Integer,List<StartupVConnectWrapper>> startupVConnectWrapPageMap = new Map<Integer,List<StartupVConnectWrapper>>();   
    
    Public List<Venture_Connect__c> ventureCList{get;set;}
    private integer currentPageNo = 1;  //keeps track of the offset
    public integer maxRecordSize=10; //sets the page size or number of rows
    public String queryString = '';
    global static String sortDir;
    Public String sortDirvf {get;set;}

    
    Public VCPointsFilterCtrl(ApexPages.StandardController controller)
    {
        
        sortDirvf = 'desc';
        ventureCList = new List<Venture_Connect__c>();
        StartupVConnectWrapperList = new List<StartupVConnectWrapper>();
        StartupVConnectWrapperFullList = new List<StartupVConnectWrapper>();
        sObjectType = controller.getRecord().getSObjectType().getDescribe().getName();
        
        Map<Integer,String> intStrMap = new Map<Integer,String>();
        intStrMap.put(20,'j');
        intStrMap.put(25,'i');
        intStrMap.put(30,'h');
        intStrMap.put(45,'g');
        intStrMap.put(50,'f');
        intStrMap.put(55,'e');
        intStrMap.put(70,'d');
        intStrMap.put(75,'c');
        intStrMap.put(80,'b');
        intStrMap.put(100,'a');
        
        if(sObjectType=='Venture_Connect__c') 
        { 
            List<Startup__c> startupTempList = new List<Startup__c>();
            sObjStd = [select id,name,Investment_Stage__c,Sector_Focus__c,Geographical_Focus__c,Not_preferred_Sector__c,Not_preferred_sub_sector__c,Industry__c,Sub_sector__c,Series__c,Investment_Size_From__c,Investment_Size_To__c,VC_Connect__c from Venture_Connect__c where id=:controller.getRecord().Id limit 1];
            queryString = getVCSOQLStr(sObjStd);
            Venture_Connect__c vcObj;
            vcObj = (Venture_Connect__c)sObjStd;
            
            system.debug('## queryString :'+queryString);
            
            if(String.isNotBlank(queryString))
            {
                //queryString += ' limit ' + maxRecordSize + ' offset ' + counter;
                queryString += ' limit 1000';
                startupTempList = Database.query(queryString);
            }
            system.debug('##startupTempList>>>>>'+startupTempList);
            system.debug('Total Startup :- ' + startupTempList.size());
            
            for(Startup__c strup : startupTempList)
            {
                system.debug(' From StartupTempList ' + strup.Public_Name__c );
                if(startupTempList.size()==3)
                {
                 //   break;
                }
                //Match all 6 condition
                  if(strup.Portfolio_map_Sector__c!=null && vcObj.Not_preferred_Sector__c!=null && isMultiPicklistMatch(''+	strup.Portfolio_map_Sector__c,vcObj.Not_preferred_Sector__c)
                  	  || strup.Industry__c!=null && vcObj.Not_preferred_sub_sector__c!=null && isMultiPicklistMatch(''+strup.Industry__c,vcObj.Not_preferred_sub_sector__c)
                      || strup.Portfolio_map_Sector__c!=null && vcObj.Industry__c!=null && isMultiPicklistMatch(''+strup.Portfolio_map_Sector__c,vcObj.Industry__c)
                      || strup.Industry__c!=null && vcObj.Sub_sector__c!=null && isMultiPicklistMatch(''+strup.Industry__c,vcObj.Sub_sector__c)
                      || strup.Round__c!=null && vcObj.Series__c!=null && isMultiPicklistMatch(''+strup.Round__c,vcObj.Series__c)
                      || (vcObj.Investment_Size_From__c !=null && strup.Fundraise__c!=null
                      && vcObj.Investment_Size_From__c <= strup.Fundraise__c
                      && vcObj.Investment_Size_From__c !=null && strup.Fundraise__c!=null
                      && vcObj.Investment_Size_To__c >= strup.Fundraise__c)
                   )
                {
                    system.debug('current startup ' + strup.Public_Name__c + 'pass basic condition');
                    Integer finalCount = 0;
                     if(strup.VC_Connect__c == true)
                    {
                        system.debug('current startup ' + strup.Public_Name__c + 'enters matchinng process');
                    	if((vcObj.Not_Preferred_Sector__c != null && strup.Portfolio_map_Sector__c != null && isMultiPicklistMatch(''+vcObj.Not_Preferred_Sector__c , strup.Portfolio_map_Sector__c))
                       	|| (vcObj.Not_Preferred_Sub_Sector__c != null && strup.Industry__c != null && isMultiPicklistMatch(''+vcObj.Not_Preferred_Sub_Sector__c , strup.Industry__c)))
                    	{
                        
                        	system.debug('<<<Not_Preferred_Sector__c>>>>');
                        	finalCount = 0;
                        	system.debug('finalCount >>>>'+finalCount);
                        	//FinalScoreMapForVC.put(strup.Id , finalCount);
                    	}
                    	else
                    	{
                            if((vcObj.Industry__c!=null && strup.Portfolio_map_Sector__c!=null && isMultiPicklistMatch(''+vcObj.Industry__c,strup.Portfolio_map_Sector__c))
                              || (vcObj.Industry__c!=null && checkForSectorAgnostic(''+vcObj.Industry__c)))
                        	{
                            	system.debug('<<<PosrtFolio_Map_Sector__C>>>>');    
                            	finalCount+= 25;
                        	}
                            if((vcObj.Sub_Sector__c!=null && strup.Industry__c!=null && isMultiPicklistMatch(''+vcObj.Sub_Sector__c,strup.Industry__c))
                              || (vcObj.Sub_Sector__c!=null && checkForSectorAgnostic(''+vcObj.Sub_Sector__c)))
                        	{
                            	system.debug('<<<Sub Sector To Industry>>>>');
                                finalCount+= 30;
                        	}
                        	if(vcObj.Series__c!=null && strup.Round__c!=null && isMultiPicklistMatch(''+vcObj.Series__c,strup.Round__c))
                        	{
                            	system.debug('<<<Series__c>>>>');    
                            	finalCount+= 25;
                        	}
                        	if(vcObj.Investment_Size_To__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_To__c >= strup.Fundraise__c
                            	&& vcObj.Investment_Size_From__c!=null && strup.Fundraise__c!=null && vcObj.Investment_Size_From__c <= strup.Fundraise__c
                        	)
                        	{
                            	system.debug('<<<Investment_Size_To__c>>>>');
                            	finalCount+= 20;
                        	}
                        	
                        	system.debug('strup Id>>>>'+strup.Id);    
                        	system.debug('vcObj >>>>'+vcObj);    
                        	system.debug('strup >>>>'+strup);    
                        	system.debug('finalCount >>>>'+finalCount);    
                        	StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(strup,finalCount,intStrMap.get(finalCount)));
                    	}
                	}
                }
               
            }
            
            system.debug('##StartupVConnectWrapperFullList>>>>>'+StartupVConnectWrapperFullList);
            system.debug('##StartupVConnectWrapperFullList.size()>>>>>'+StartupVConnectWrapperFullList.size());
            
            sortDir = 'desc';
            StartupVConnectWrapperFullList.sort();
            system.debug('## AFTER OR StartupVConnectWrapperFullList>>>>>'+StartupVConnectWrapperFullList);
            system.debug('## AFTER OR StartupVConnectWrapperFullList.size()>>>>>'+StartupVConnectWrapperFullList.size());
            
            startupVConnectWrapPageMap = getStartupRecordPageMap(StartupVConnectWrapperFullList);
            StartupVConnectWrapperList = startupVConnectWrapPageMap.get(1);
            
        }
        else
        {
            sObjStd = [select id,Industry__c,Investment_Stage__c,Sector_Focus__c,Fundraise__c,Round__c,Total_Investment_In_INR__c,Portfolio_map_Sector__c,VC_Connect__c from Startup__c WHERE id=:controller.getRecord().Id limit 1];
            queryString = getStartUpSOQLStr(sObjStd);
            ventureCList = new List<Venture_Connect__c>();
            List<Venture_Connect__c> ventureCTempList = new List<Venture_Connect__c>();
            Startup__c strNewObj;
            strNewObj = (Startup__c)sObjStd;
            
            if(String.isNotBlank(queryString))
            {
                system.debug('## queryString111 :'+queryString);
                //queryString += ' limit ' + maxRecordSize + ' offset ' + counter;
                queryString += ' limit 1000';
                ventureCTempList = Database.query(queryString);
            }
            system.debug('ventureCTempList::'+ventureCTempList);
            
            /*****************************************************************/
            
            
            system.debug('## queryString :'+queryString);
                           
            for(Venture_Connect__c vcNewObj : ventureCTempList)
            {
                if(ventureCList.size()==3)
                {
                   // break;
                }
                //Match all 6 condition
                if(strNewObj.Portfolio_map_Sector__c!=null && vcNewObj.Not_preferred_Sector__c!=null && isMultiPicklistMatch(''+strNewObj.Portfolio_map_Sector__c,vcNewObj.Not_preferred_Sector__c)
                  	  ||strNewObj.Industry__c!=null && vcNewObj.Not_preferred_sub_sector__c!=null && isMultiPicklistMatch(''+strNewObj.Industry__c,vcNewObj.Not_preferred_sub_sector__c)
                      || strNewObj.Portfolio_map_Sector__c!=null && vcNewObj.Industry__c!=null && isMultiPicklistMatch(''+strNewObj.Portfolio_map_Sector__c,vcNewObj.Industry__c)
                      || strNewObj.Industry__c!=null && vcNewObj.Sub_sector__c!=null && isMultiPicklistMatch(''+strNewObj.Industry__c,vcNewObj.Sub_sector__c)
                      || strNewObj.Round__c!=null && vcNewObj.Series__c!=null && isMultiPicklistMatch(''+strNewObj.Round__c,vcNewObj.Series__c)
                      || (vcNewObj.Investment_Size_From__c !=null && strNewObj.Fundraise__c!=null
                      && vcNewObj.Investment_Size_From__c <= strNewObj.Fundraise__c
                      && vcNewObj.Investment_Size_From__c !=null && strNewObj.Fundraise__c!=null
                      && vcNewObj.Investment_Size_To__c >= strNewObj.Fundraise__c)
                   )
                {
                  Integer finalCount = 0;
                     if(vcNewObj.VC_Connect__c == true)
                    {
                    	if((vcNewObj.Not_Preferred_Sector__c != null && strNewObj.Portfolio_map_Sector__c != null && isMultiPicklistMatch(''+vcNewObj.Not_Preferred_Sector__c , strNewObj.Portfolio_map_Sector__c))
                       	|| (vcNewObj.Not_Preferred_Sub_Sector__c != null && strNewObj.Industry__c != null && isMultiPicklistMatch(''+vcNewObj.Not_Preferred_Sub_Sector__c , strNewObj.Industry__c)))
                    	{
                        
                        	system.debug('<<<Not_Preferred_Sector__c>>>>');
                        	finalCount = 0;
                        	system.debug('finalCount >>>>'+finalCount);
                        	//FinalScoreMapForVC.put(strup.Id , finalCount);    
                    	}
                    	else
                    	{
                            if((vcNewObj.Industry__c!=null && strNewObj.Portfolio_map_Sector__c!=null && isMultiPicklistMatch(''+vcNewObj.Industry__c,strNewObj.Portfolio_map_Sector__c))
                              || (vcNewObj.Industry__c!=null && checkForSectorAgnostic(''+vcNewObj.Industry__c)))
                        	{
                            	system.debug('<<<PosrtFolio_Map_Sector__C>>>>');    
                            	finalCount+= 25;
                        	}
                            if((vcNewObj.Sub_Sector__c!=null && strNewObj.Industry__c!=null && isMultiPicklistMatch(''+vcNewObj.Sub_Sector__c,strNewObj.Industry__c))
                              || (vcNewObj.Sub_Sector__c!=null && checkForSectorAgnostic(''+vcNewObj.Sub_Sector__c)))
                        	{
                            	system.debug('<<<Sub Sector To Industry>>>>');
                                finalCount+= 30;
                        	}
                        	if(vcNewObj.Series__c!=null && strNewObj.Round__c!=null && isMultiPicklistMatch(''+vcNewObj.Series__c,strNewObj.Round__c))
                        	{
                            	system.debug('<<<Investment_Stage__c>>>>');    
                            	finalCount+= 25;
                        	}
                        	if(vcNewObj.Investment_Size_To__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_To__c >= strNewObj.Fundraise__c
                            	&& vcNewObj.Investment_Size_From__c!=null && strNewObj.Fundraise__c!=null && vcNewObj.Investment_Size_From__c <= strNewObj.Fundraise__c
                        	)
                        	{
                            	system.debug('<<<Investment_Size_To__c>>>>');
                            	finalCount+= 20;
                        	}
                        	
                        	system.debug('strup Id>>>>'+strNewObj.Id);    
                        	system.debug('vcObj >>>>'+vcNewObj);    
                        	system.debug('strup >>>>'+strNewObj);    
                        	system.debug('finalCount >>>>'+finalCount);
                        	StartupVConnectWrapperFullList.add(new StartupVConnectWrapper(vcNewObj,finalCount,intStrMap.get(finalCount)));
                    	}
                	}
                    }                 
            }
            
            system.debug('##StartupVConnectWrapperFullList>>>>>'+StartupVConnectWrapperFullList);
            system.debug('##StartupVConnectWrapperFullList.size()>>>>>'+StartupVConnectWrapperFullList.size());
           
            sortDir = 'desc';
            StartupVConnectWrapperFullList.sort();
            system.debug('## vc StartupVConnectWrapperFullList>>>>>'+StartupVConnectWrapperFullList);
            system.debug('## vc StartupVConnectWrapperFullList.size()>>>>>'+StartupVConnectWrapperFullList.size());
            
            startupVConnectWrapPageMap = getStartupRecordPageMap(StartupVConnectWrapperFullList);
            StartupVConnectWrapperList = startupVConnectWrapPageMap.get(1);
                        
            /*****************************************************************/            
        }
        
        system.debug('sObjStd>>>'+sObjStd);
        system.debug('queryString>>>'+queryString);
    }
    
    global Class StartupVConnectWrapper implements Comparable 
    {
        global Startup__c startup{get;set;}
        global Venture_Connect__c vConnect{get;set;}
        global Integer noOfCriteriaMatch{get;set;}
        global String matchingStringToSort{get;set;}
         
        global StartupVConnectWrapper(sObject st,Integer noOfMatch,String match)
        {
            if(sObjectType=='Venture_Connect__c') 
            { 
                startup = (Startup__c)st;
            }
            else
            {
                vConnect = (Venture_Connect__c)st;
            }
            noOfCriteriaMatch = noOfMatch;
            matchingStringToSort = match;
        }
    
        global Integer compareTo(Object ObjToCompare) {
            //system.debug('sortDir >>>>'+sortDir );         
            if(sortDir =='asc')
            {
                //system.debug('sort direction>>>111');
                return Integer.valueOf(noOfCriteriaMatch - ((StartupVConnectWrapper)objToCompare).noOfCriteriaMatch);
            }
            else
            {
                //system.debug('sort direction>>>222');
                return matchingStringToSort.CompareTo(((StartupVConnectWrapper)ObjToCompare).matchingStringToSort);        
            } 
        }
    }
    
    public void toggleSort() {
        system.debug('sortDir000>>>'+sortDir );
        system.debug('sortDirvf >>>'+sortDirvf );
        system.debug('StartupVConnectWrapperList000>>>'+StartupVConnectWrapperFullList);
       
        sortDirvf = sortDirvf.equals('asc') ? 'desc' : 'asc';
        sortDir = sortDirvf;
        
        StartupVConnectWrapperFullList.sort();
        currentPageNo = 1;
        system.debug('StartupVConnectWrapperList111>>>'+StartupVConnectWrapperFullList);
        startupVConnectWrapPageMap = getStartupRecordPageMap(StartupVConnectWrapperFullList);
        system.debug('startupVConnectWrapPageMap6666>>'+startupVConnectWrapPageMap);
        
        StartupVConnectWrapperList = startupVConnectWrapPageMap.get(currentPageNo);
                
        system.debug('sortDir>>>'+sortDir );
    }
    
    public Map<Integer,List<StartupVConnectWrapper>> getStartupRecordPageMap(List<StartupVConnectWrapper> stWrapList)
    {
        system.debug('stWrapList>>>'+stWrapList);
            Integer mapKeyCount = 1;
            startupVConnectWrapPageMap = new Map<Integer,List<StartupVConnectWrapper>>();   
            for(StartupVConnectWrapper sp : stWrapList)
            {
                if(!startupVConnectWrapPageMap.containsKey(mapKeyCount))
                {
                    startupVConnectWrapPageMap.put(mapKeyCount,new List<StartupVConnectWrapper>());
                }
                
                if(startupVConnectWrapPageMap.get(mapKeyCount).size()<maxRecordSize)
                {
                    startupVConnectWrapPageMap.get(mapKeyCount).add(sp);
                }
                else
                {
                    mapKeyCount++;
                    startupVConnectWrapPageMap.put(mapKeyCount,new List<StartupVConnectWrapper>()); 
                    startupVConnectWrapPageMap.get(mapKeyCount).add(sp);                   
                }
            }    
            system.debug('StartupVConnectWrapperFullList>>>>>>>'+stWrapList.size());
            system.debug('startupVConnectWrapPageMap>>>>>>>'+startupVConnectWrapPageMap.size());
            system.debug('mapKeyCount >>>>>>>'+mapKeyCount );
            //system.debug('Last page >>>>>>>'+startupVConnectWrapPageMap.get(mapKeyCount).size());
            
            if(startupVConnectWrapPageMap.containsKey(mapKeyCount) && startupVConnectWrapPageMap.get(mapKeyCount).size()==0)
            {
                startupVConnectWrapPageMap.remove(mapKeyCount);
                mapKeyCount --;
            }    
            
            system.debug('StartupVConnectWrapperFullList>>>>>>>'+stWrapList.size());
            system.debug('startupVConnectWrapPageMap>>>>>>>'+startupVConnectWrapPageMap.size());
            system.debug('mapKeyCount >>>>>>>'+mapKeyCount );
            //system.debug('Last page >>>>>>>'+startupVConnectWrapPageMap.get(mapKeyCount).size());
        return startupVConnectWrapPageMap;            
    }
    
    Static String getStartUpSOQLStr(sObject obj)
    {
        //Added by Karan queryString updated field Investment_Size_To__c added to queryString
        String queryString = 'select id,name,Type__c,Series__c,Investment_Stage__c,Industry__c,Sector_Focus__c,Geographical_Focus__c,Investment_Size_From__c,Investment_Size_To__c,Not_preferred_Sector__c,Not_preferred_sub_sector__c,Sub_sector__c,VC_Connect__c from Venture_Connect__c WHERE ';
        String condition = '';    
        Startup__c vcObj;
        
        vcObj = (Startup__c)obj;
        system.debug('vcObj getSOQLStr>>>'+vcObj);
         
        if(vcObj.Industry__c !=null)
            condition = condition+' Industry__c in '+getMultiPicklistCondition(vcObj.Industry__c)+' OR';
        if(vcObj.Sector_Focus__c!=null)
            condition = condition+' Sector_Focus__c in '+getMultiPicklistCondition(vcObj.Sector_Focus__c)+' OR';
        if(vcObj.Fundraise__c!=null)
            condition = condition+' Investment_Size_From__c <= '+vcObj.Fundraise__c+' OR';
        if(vcObj.Fundraise__c!=null)
            condition = condition+' Investment_Size_To__c>= '+vcObj.Fundraise__c+' OR';
        if(vcObj.Round__c !=null)
            condition = condition+' Series__c in '+getMultiPicklistCondition(vcObj.Round__c )+' OR';
        condition = condition.removeEnd(' OR');
        
        system.debug('condition>>>'+condition);
         
        if(String.isNotBlank(condition))
            return queryString + condition +' order by LastModifiedDate desc';
        else
            return '';
    }
    
    Static String getVCSOQLStr(sObject obj)
    {
        String queryString = 'select id,Name,Public_Name__c,Sector_Focus__c,Investment_Stage__c,Geographical_Focus__c,Industry__c,Round__c,Fundraise__c,Total_Investment_In_INR__c,Portfolio_map_Sector__c,VC_Connect__c from Startup__c WHERE ';
        String condition = '';    
        Venture_Connect__c vcObj;
        
        vcObj = (Venture_Connect__c)obj;
        system.debug('vcObj getSOQLStr>>>'+vcObj);
            
        if(vcObj.Industry__c!=null)
            condition = condition+' Portfolio_map_Sector__c in '+getMultiPicklistCondition(vcObj.Industry__c)+' OR';
        if(vcObj.Investment_Size_From__c!=null)
            condition = condition+' Fundraise__c >= '+vcObj.Investment_Size_From__c+' OR';
        if(vcObj.Investment_Size_To__c!=null)
            condition = condition+' Fundraise__c <= '+vcObj.Investment_Size_To__c+' OR';
        if(vcObj.Series__c!=null)
            condition = condition+' Round__c in '+getMultiPicklistCondition(vcObj.Series__c)+' OR';
        if(vcObj.Sub_sector__c !=null)
            condition = condition+' Industry__c in '+getMultiPicklistCondition(vcObj.Sub_sector__c )+' OR';
               
        condition = condition.removeEnd(' OR');
        
        system.debug('condition>>>'+condition);
         
        if(String.isNotBlank(condition))
            return queryString + condition +' order by LastModifiedDate desc';
        else
            return '';
    }
    
    static String getMultiPicklistCondition(String mPicklistval)
    {
        List<String> mPicklistvalList = mPicklistval.split(';');
        system.debug('mPicklistvalList>>>'+mPicklistvalList);
        
        String mpicklistStr = '';
        for(String includeValue :mPicklistvalList)
            mpicklistStr += '\''+includeValue+'\',';
        
        system.debug('mpicklistStr>>>'+mpicklistStr);
        
        mpicklistStr = '('+ mpicklistStr.removeEnd(',') +')';
        
        return mpicklistStr;
    }
    
    public static Boolean isMultiPicklistMatch(String srcStr,String desStr)
    {
        List<String> srcList = srcStr.split(';');
        List<String> desList = desStr.split(';');
        Boolean retFlag = false;
       
        for(String s : srcList)
        {
            //system.debug('Source MultiPicklist val>>>'+s);
            if(desList.contains(s))
            {
                retFlag = true;
                break;
            }
        }
        return retFlag;
    }
    public static Boolean checkForSectorAgnostic(String selectedValue)
    {
        List<String> selectedValueList = selectedValue.split(';');
        return selectedValue.contains('Sector Agnostic');
    }
   
    public PageReference Beginning() { //user clicked beginning
        currentPageNo = 1;
        StartupVConnectWrapperList = startupVConnectWrapPageMap.get(currentPageNo);
        return null;
    }
   
    public PageReference Previous() { //user clicked previous button
        currentPageNo--;
        StartupVConnectWrapperList = startupVConnectWrapPageMap.get(currentPageNo);
        return null;
    }
    
    public PageReference Next() { //user clicked next button
        currentPageNo++;
        StartupVConnectWrapperList = startupVConnectWrapPageMap.get(currentPageNo);
        return null;
    }
    
    public PageReference End() { //user clicked end
        currentPageNo = startupVConnectWrapPageMap.size();
        StartupVConnectWrapperList = startupVConnectWrapPageMap.get(currentPageNo);
        return null;
    }
    public Boolean getDisablePrevious() { 
        //this will disable the previous and beginning buttons
        if (currentPageNo>1) return false; else return true;
    }
    public Boolean getDisableNext() { //this will disable the next and end buttons
        if (currentPageNo < startupVConnectWrapPageMap.size()) return false; else return true;
    }
    public Integer getPageNumber() {
        return currentPageNo ;
    }
    
    public Integer getTotalPages() {
         return startupVConnectWrapPageMap.size();
    }
}