Global class LeadRestAPIController {
Private Static String CLASS_Name ='LeadRestAPIController';
	Global static String AccessToken;

@Future(Callout=True)
	Public Static Void SendLeadDetails (Set<id> leadId, Boolean IsInsert){
	System.debug('SentLeadDetails' +leadId+'----'+IsInsert);
	
	try{
		Map<String,string> CredMap = New Map<String,String>();
		String jsonData;
		String endURLSetting;
		  credMAp.put('password','hello');	
          credMAp.put('mobile','8866104284');
          credMAp.put('country_code','+91');
		
		List<API_Setting__c> SettingList = API_Setting__c.getall().values();
		System.debug('LeadRestAPIController API_Setting__c' + SettingList);
		If(SettingList != NULL && SettingList.Size() > 0 && !String.IsEmpty(SettingList[0].End_URL__c)){
			endURLSetting ='' +SettingList[0].End_URL__c;
		} 
		If(SettingList != NULL && SettingList.Size() > 0 && !String.IsEmpty(SettingList[0].Mobile_Username__c)){
			CredMap.put('mobile',SettingList[0].Mobile_Username__c);
		}
		If(SettingList != NULL && SettingList.Size() > 0 && !String.IsEmpty(SettingList[0].Mobile_Password__c)){
			CredMap.put('password',SettingList[0].Mobile_Password__c);
		}
		System.debug('CredMap>>>>' + CredMap);
		System.debug('EndUrlSetting>>>' +endURLSetting);
		
		List<Lead__C> LeadRecords =[select id,Name,
                                    //RecordTypeId,
                                    Referred_By__c,
									Primary_Contact__c, 
									Primary_Country_Code__c,
                                    Lead_Source__c,
									Referred_By__r.Name, 
									Referred_By__r.Primary_Contact__c, 
									Referred_By__r.Primary_Country_Code__c
									from Lead__c WHERE Id IN:leadId];
									
		If(!LeadRecords.isEmpty()){
			JSONGenerator jsonGen = JSON.createGenerator(true);
                jsonGen.writeStartObject(); 
                jsonGen.writeFieldName('Leadlist');
                jsonGen.writeStartArray();
				
				For(Lead__c ld : LeadRecords)
				{
					jsonGen.writeStartObject(); 
					if(ld.Referred_By__c != Null && ld.Lead_Source__c !=''){
						jsonGen.writeStringField('contact',ld.Primary_Contact__c);
						jsonGen.writeNumberField('contactCC',ld.Primary_Country_Code__c);
						jsonGen.writeStringField('reffredByContact',ld.Referred_By__r.Primary_Contact__c);
						jsonGen.writeNumberField('reffredByCC',ld.Referred_By__r.Primary_Country_Code__c);
						jsonGen.writeStringField('referral_name',ld.Name);
						
					}
					jsonGen.writeEndObject();
				}
				jsonGen.writeEndArray();
                jsonGen.writeEndObject();
                jsonData = jsonGen.getAsString();
                System.debug('Json Data - ' + jsonData);
		}
			HttpRequest req = new HttpRequest();
			String endURL = endURLSetting +'/userAuthentication/login-admin';
			System.debug('endURL1111---'+endURL);
			String bodyy =  JSON.serialize(CredMap);
            system.debug('---'+bodyy);
            req.setEndpoint(endURL);
            req.setHeader('Content-Type','application/json');
            req.setMethod('POST');
            req.setTimeout(120000);
            req.setBody(bodyy);
            Http http = new Http();
            HTTPResponse res = http.send(req);
			System.debug(res.getStatusCode());
            System.debug('res---'+res.toString());
            System.debug('STATUS:'+res.getStatus());
			
			if(res.getStatusCode() == 200){
                system.debug('Data sent'+res.getBody());
                String jsonstr = JSON.serialize(res.getBody());
                JSONParser parser = JSON.createParser(jsonStr);
                Map<String, Object> results = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                String tkn = JSON.serialize(results.get('data'));
                Map<String, Object> token = (Map<String, Object>) JSON.deserializeUntyped(tkn);
                accessToken = String.valueOF(token.get('token'));
                system.debug('accessToken  '+token.get('token'));
                system.debug('accessToken in add  '+accessToken);
                
                if(isInsert)
                    endURL = endURLSetting+'/userReferralRoutes/addUserReferralCRM';
               // else
               //    endURL = endURLSetting+'/userReferralRoutes/addUserReferralCRM';

                HttpRequest request = new HttpRequest();
                request.setEndpoint(endURL);
                request.setHeader('Authorization','Bearer ' + accessToken);
                request.setHeader('Content-Type','application/json');
      
                if(!isInsert)
                    request.setMethod('PUT');
                else
                    request.setMethod('POST');

                request.setTimeout(120000);
                request.setBody(jsonData);
                System.debug('JsonData>>>>' +jsonData);
                Http http1 = new Http();
                HTTPResponse res1 = http1.send(request);
                system.debug('****');
                System.debug(res1.getStatusCode());
                System.debug('res--add-'+res1.getBody());
                System.debug('STATUS:'+res1.getStatus());
                   
            }
	}
		 catch(Exception ex){
            system.debug('Error '+ex);
            Error_Log__c log =  new Error_Log__c();
            log.Class_Name__c = CLASS_NAME;
            log.method__c = 'SendLeadDetails';
            log.Error_Message__c = ex.getMessage();
            insert log;
        }
	}
}