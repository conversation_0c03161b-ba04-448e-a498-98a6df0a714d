/************************************************************************
    Test Class for AccountTrigger and AccountTriggerHandler.
************************************************************************/

@isTest(SeeAllData=false)
private class AccountTriggerTestClass{
    
    @testSetup static void setup() {
        
        B2BcustomerAPITest.setup();
        
        List<Account> accList = new List<Account>();
        account acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Platinum';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        acc.App_City__c = 'BANDA';
        acc.Personal_Email__c = '<EMAIL>';
        acc.Bot_Communication_Country_Code__c = null;
        acc.Bot_Communication_Number__c = '';
        accList.add(acc);
        
        acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Platinum';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        acc.Title__c = 'Ms';
        accList.add(acc);
        
        acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Complimentary';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        acc.Title__c = 'Ms';
        acc.Date_of_Addition__c = date.today().addDays(-4);
        accList.add(acc);
        
       /* acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Paid IPV Fee';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        acc.Date_of_1st_Payment__c=Date.today().adddays(-1);
        acc.Date_of_Payment__c =Date.today().adddays(-8);
        acc.Title__c = 'Ms';
        accList.add(acc); */
        
        
        acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Platinum';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        acc.Title__c = 'Ms1';
        acc.Facebook_Campaign_Name__c = 'AAFM';
        accList.add(acc);
        
        
        
        insert accList;       
        
        Lead__c ldObj = TestFactory.createLead();
        ldObj.Preferred_Email__c = accList[0].Personal_Email__c;
        ldObj.Relationship_Owner__c= UserInfo.getUserId();
        ldobj.Date_of_receiving_lead__c = date.today();
        insert ldObj;
    }
    
    static testMethod void test1()
    {
        test.startTest();
            account parAcc = [select id,Primary_Contact__c from account limit 1];
        	account acc = new account();
            Profile p = [SELECT Id FROM Profile WHERE Name='Standard User']; 
            User u = new User(Alias = 'standt', Email='<EMAIL>', 
            EmailEncodingKey='UTF-8', LastName='Testing', LanguageLocaleKey='en_US', 
            LocaleSidKey='en_US', ProfileId = p.Id, 
            TimeZoneSidKey='America/Los_Angeles', UserName='<EMAIL>',Contact_No__c='1234567');
        
        	List<Account> accList = new List<Account>();
        
            System.runAs(u) {
                // The following code runs as user 'u' 
                System.debug('Current User: ' + UserInfo.getUserName());
                System.debug('Current Profile: ' + UserInfo.getProfileId()); 
                acc = TestFactory.createAccount();
                acc.Refer_By_Contact_No__c= parAcc.Primary_Contact__c;
                acc.Relationship_Manager_Contact__c = u.Contact_No__c;
                acc.Membership_Status__c = 'Platinum';
                insert acc;
                				
                Account referralAcc1 = TestFactory.createAccount();
                referralAcc1.Name='AC';
                referralAcc1.Primary_Contact__c = '**********';
                insert referralAcc1;
                
                Account referralAcc2 = TestFactory.createAccount();
                referralAcc2.Name='AC';
                referralAcc2.Primary_Contact__c = '**********';
                insert referralAcc2;
            }
          list <user> usr=[select id from user limit 10];
            acc.Membership_Status__c = 'On Trial';
        
            update acc;
           
            acc.Membership_Status__c ='On Trial';
            acc.Relationship_Manager__c=usr[1].Id;
            acc.Membership_Slab__c = 'Bronze';
            acc.Account_Premier_Status__c='Interested in Premier';
            acc.Premier_Premier_Deboarding_Date__c =Date.today();
            update acc;
        
             acc.Membership_Status__c ='Paid IPV Fee';
             acc.Account_Premier_Status__c='Onboarded on Premier';
           acc.Relationship_Manager__c=usr[2].Id;
            acc.Date_of_Payment__c=Date.today();
             acc.Date_of_1st_Payment__c=date.Today();
            acc.Payment_Type__c ='New Payment';
            acc.AIM__c = 'Interested in AIM+';
            acc.AIM_Onboarding_Date__c=DAte.today();
             update acc;
        
             acc.Membership_Status__c ='Complimentary';
             acc.Account_Premier_Status__c='Interested in Premier Select';
            acc.Date_of_Payment__c=Date.today();
             acc.Date_of_1st_Payment__c=date.Today();
            acc.Payment_Type__c ='Renewal';
             update acc;
        
            
                  
            acc.Personal_Email__c = '<EMAIL>';
            acc.Primary_Contact__c = '**********';
            acc.Refer_By_Contact_No__c = '**********';
        	acc.Facebook_Campaign_Name__c = 'AAFM';
            acc.Membership_Status__c = 'No Response 1';
        	update acc;
        
        
        test.stopTest();
    }
}