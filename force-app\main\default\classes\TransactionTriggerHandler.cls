public class TransactionTriggerHandler {
	public static void bulkAfter(List<Transaction__c> transList)
    {
        decimal totalTrans = 0;
        set<ID> caID = new set<ID>();
        for(Transaction__c tr: transList){
            system.debug('tr ::'+tr);
            if(tr.contribution_agreement__c != null)
            caID.add(tr.contribution_agreement__c);
        }
        if(caID != null){
            List<Contribution_Agreement__c> lstCA = [SELECT Id,Total_Transfer__c,(SELECT Id,Amount__c from Transactions__r)
                                                 FROM Contribution_Agreement__c WHERE Id IN :CAID];
            system.debug('lstCA ::'+lstCA);
            for(Contribution_Agreement__c ca : lstCA){
                for(Transaction__c trans : ca.Transactions__r){
                    if(trans.amount__c != null)
                    totalTrans += trans.amount__c;
                }
                ca.Total_Transfer__c = totalTrans;
            }
            update lstCA;
        }
    }
    
    public static void afterInsert(List<Transaction__c> transList)
    {
        Set<Id> lealdIdSet = new Set<Id>();
        List<Account> accList = new List<Account>();
        List<Lead__c> ldList = new List<Lead__c>();
        Map<Id, Transaction__c> transAccMap = new Map<Id,Transaction__c>();
        Map<Id, Transaction__c> transLeadMap = new Map<Id,Transaction__c>();
        Map<String , Id> referralCodeMap = new Map<String , Id>();
        Set<String> coupenCodeSet = new Set<String>();
        List<Account> referredByAcc = new List<Account>();
        List<Lead__c> updatedLeadList = new List<Lead__c>();
        
        Set<Id> accountIdsForRenewalTransaction = new Set<Id>();
        Map<Id, Account> accountMap = new Map<Id, Account>();
        List<Account> accountsToUpdate = new List<Account>();      
        List<Transaction__c> emailToSendForRenewalMembership = new List<Transaction__c>();

        for (Transaction__c trans : transList) {
            accountIdsForRenewalTransaction.add(trans.Account_Name__c);
        }

        List<Account> accountList = [SELECT Id , Name , Date_of_Payment__c , Membership_Validity__c FROM Account WHERE Id IN :accountIdsForRenewalTransaction];

        for (Account account : accountList) {
            accountMap.put(account.Id , account);
        }

        for (Transaction__c trans : transList) {

            if(trans.Transaction_Type__c == 'Renewal Membership' && trans.Account_Name__c != null) {
                
                Integer years = yearsOfPayment(trans.Total_Year_of_Payment__c);
            
                Account account = accountMap.get(trans.Account_Name__c);
                Boolean isDateOfPast = account.Membership_Validity__c != null && account.Membership_Validity__c < Date.Today() ? true : false;
                account.Date_of_Payment__c = trans.CreatedDate.Date();
            
                if(isDateOfPast) {
                    account.Membership_Validity__c = trans.CreatedDate.Date().addYears(years);
                }
                else{
                    account.Membership_Validity__c = account.Membership_Validity__c.addYears(years);
                }

                accountsToUpdate.add(account);
                emailToSendForRenewalMembership.add(trans);
                
                // Added By bharat for razorpay phase 1
            }else if(trans.Transaction_Type__c == 'New Membership Payment'){
                
                if(trans.Account_Name__c != null){
                    transAccMap.put(trans.Account_Name__c ,  trans);
                }else if(trans.Leads_Name__c != null){
                    transLeadMap.Put(trans.Leads_Name__c , trans);
                }
                
                if(trans.Coupon_Code_Used__c != null){
                    coupenCodeSet.add(trans.Coupon_Code_Used__c);
                }
            }
        }
        
        // Added By bharat for razorpay phase 1
        System.debug('transLeadMap>>>>>>>>>' + transLeadMap);
        System.debug('coupenCodeSet>>>>>>>>>' + coupenCodeSet);
        If(coupenCodeSet != null )
        {
            referredByAcc = [SELECT Id , Primary_Contact__c , Primary_Country_Code__c , Unique_Referral_Code__c FROM Account WHERE Unique_Referral_Code__C IN : coupenCodeSet LIMIT 1 ];
            System.debug('referredByAcc>>>>>>>>>' + referredByAcc);
        }
        if(transAccMap != null){
            accList = [Select Id, Name, Primary_Contact__c, Primary_Country_Code__c,
                                     Membership_Validity__c, Membership_Status__c , Date_of_Payment__c ,
                                     Payment_Type__c , couponCode__c , Amount_Paid__c , ParentId
                                     FROM Account WHERE Id IN : transAccMap.keySet() ];
            System.Debug('accList>>>>>>>>>' + accList);
        }
        for(Account acc : accList)
        {
            if(acc.Membership_Status__c == 'On Trial'){
                acc.Membership_Status__c = 'Paid IPV Fee';
            }else If(acc.Membership_Status__c == 'On Trial Community'){
                acc.Membership_Status__c = 'Paid Community';
            }
            
            if(acc.Membership_Validity__c != null ){
                acc.Membership_Validity__c = acc.Membership_Validity__c.addDays(365);
            }else{
                acc.Membership_Validity__c = Date.Today().addDays(365);
            }
            acc.Date_of_Payment__c = transAccMap.get(acc.Id).Transaction_Date__c.date();
            acc.Payment_Type__c = 'New Payment';
            acc.Amount_Paid__c = transAccMap.get(acc.Id).Amount__c;
            if(transAccMap.get(acc.Id).Coupon_Code_Used__c != null){
                acc.couponCode__c = transAccMap.get(acc.Id).Coupon_Code_Used__c;
            }
            if(acc.ParentId == NULL && referredByAcc!= null && referredByAcc.size()>0  ){
                acc.ParentId = referredByAcc[0].Id;
            }
        }
        
        if(transLeadMap != null ){
             ldList = [Select Id, Name, Primary_Contact__c, Referred_By__c , Primary_Country_Code__c FROM Lead__c WHERE Id IN : transLeadMap.keySet() ];
        }
        System.debug('ldList>>>>>>>>>' + ldList);
        
        if(ldList != null){
            for(Lead__c ld : ldList){
                if(ld.Referred_By__c == NULL && referredByAcc != null && referredByAcc.size()> 0){
                    ld.Referred_By__c = referredByAcc[0].Id;
                }
                updatedLeadList.add(ld);
            }
        }
        System.debug('updatedLeadList>>>>>>>>>' + updatedLeadList);
        if(accList != null){
            Update accList;
        }
        if(updatedLeadList != null ){
            Update updatedLeadList;
        }
        
        // added by sahil for razorpay phase 2
        if(accountsToUpdate.size() > 0)
        {
            update accountsToUpdate;
        }

        if(emailToSendForRenewalMembership.size() > 0)
        {
            renewalPaymentEmailNotification(emailToSendForRenewalMembership);
        }
        
    }
    

    public static void renewalPaymentEmailNotification(List<Transaction__c> transactionList)
    {
        Set<Id> accountIds = new Set<Id>();
        Set<Id> relationshipManagerIds = new Set<Id>();
        Map<Id , Account> accountMap = new Map<Id , Account>();
        Map<Id , User> userMap = new Map<Id , User>();        

        for (Transaction__c trans : transactionList) {
            accountIds.add(trans.Account_Name__c);
        }

        List<Account> transactionAccounts = [SELECT Id , Name  , Actual_Email__c , Relationship_Manager__c , Membership_Validity__c FROM Account WHERE Id IN :accountIds]; 

        for(Account acc : transactionAccounts) {
            relationshipManagerIds.add(acc.Relationship_Manager__c);
            accountMap.put(acc.Id , acc);
        }

        List<User> userList = [SELECT Id , Name , Contact_No__c FROM User WHERE Id IN :relationshipManagerIds];

        for(User us : userList) {
            userMap.put(us.Id , us);
        }

        List<Messaging.SingleEmailMessage> emailList = new List<Messaging.SingleEmailMessage>();

        for(Transaction__c trans : transactionList)
        {
            if(trans.Transaction_Type__c == 'Renewal Membership' && trans.Account_Name__c != null)
            {
                System.debug('Inside baby >>>> ');
                System.debug('>>>>' + accountMap.get(trans.Account_Name__c).Name);
                System.debug('>>>>' + String.valueOf(accountMap.get(trans.Account_Name__c).Actual_Email__c));
                String toAddress = String.valueOf(accountMap.get(trans.Account_Name__c).Actual_Email__c);
                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                email.setToAddresses(new String[]{toAddress});
                email.setSubject('Your IPV Membership Renewal is Successful!');
                String htmlBody = '<html><body>Hi <b>' + accountMap.get(trans.Account_Name__c).Name + '</b><br><br>';
                htmlBody += 'We are pleased to inform you that your IPV Membership has been successfully renewed. Thank you for your continued trust and support. <br><br>';
                htmlBody += '<b>Membership Renewal Details : </b> ';
                htmlBody += '<ul><li><b>Membership Plan : </b>'+ trans.Total_Year_of_Payment__c +'</li> ';
                htmlBody += '<li><b>Amount Paid (incl. GST) : </b>'+ trans.Amount__c +'</li></ul><br>';
                htmlBody += 'Your membership is now valid until <b>' + accountMap.get(trans.Account_Name__c).Membership_Validity__c.format() + ' </b><br><br>';
                htmlBody += 'If you have any questions or need further assitance, please feel free to call your RM at <b>' + userMap.get(accountMap.get(trans.Account_Name__c).Relationship_Manager__c).Contact_No__c + '</b><br><br>';
                htmlBody += 'Thank you once again for being a valued member of IPV. We look forward to continuing to serve you.<br><br>';
                htmlBody += '<b>Best Regards,</b><br>';
                htmlBody += '<b>Inflection Point Ventures</b><br><br><br>';
                htmlBody += '<p>This is an automated email. Please do not reply to this email. If you need to get in touch with us, use the contact details provided above.</p>';

                htmlBody += '</body></html>';
                email.setHtmlBody(htmlBody);
                emailList.add(email);
            }
        }

        if (!emailList.isEmpty()) {
            Messaging.sendEmail(emailList);
        }
    }

    public static Integer yearsOfPayment(String totalYearsOfPayment)
    {
        Integer years;

        if(totalYearsOfPayment == '1 Year')
        {
            years = 1;
        }
        else if(totalYearsOfPayment == '2 Years')
        {
            years = 2;
        }
        else if(totalYearsOfPayment == '3 Years')
        {
            years = 3;
        }
        else if(totalYearsOfPayment == '4 Years')
        {
            years = 4;
        }
        else if(totalYearsOfPayment == '5 Years')
        {
            years = 5;
        }

        return years;
    }
    
    /*
    public static void beforeInsert(List<Transaction__c> transList)
    {
        Set<Id> lealdIdSet = new Set<Id>();
        List<Account> accList = new List<Account>();
        List<Lead__c> ldList = new List<Lead__c>();
        Map<Id, Transaction__c> transAccMap = new Map<Id,Transaction__c>();
        Map<Id, Transaction__c> transLeadMap = new Map<Id,Transaction__c>();
        Map<String , Id> referralCodeMap = new Map<String , Id>();
        Set<String> coupenCodeSet = new Set<String>();
        List<Account> referredByAcc = new List<Account>();
        List<Lead__c> updatedLeadList = new List<Lead__c>();
        
        for(Transaction__c tr : transList){
            if(tr.Transaction_Type__c == 'New Membership Payment'){
                if(tr.Account_Name__c != null){
                    transAccMap.put(tr.Account_Name__c ,  tr);
                }else if(tr.Lead_Name__c != null){
                    transLeadMap.Put(tr.Lead_Name__c , tr);
                }
                
                if(tr.Coupon_Code_Used__c != null){
                    coupenCodeSet.add(tr.Coupon_Code_Used__c);
                }
            }
        }
        System.debug('transLeadMap>>>>>>>>>' + transLeadMap);
        System.debug('coupenCodeSet>>>>>>>>>' + coupenCodeSet);
        If(coupenCodeSet != null ){
            referredByAcc = [SELECT Id , Primary_Contact__c , Primary_Country_Code__c , Unique_Referral_Code__c FROM Account WHERE Unique_Referral_Code__C IN : coupenCodeSet LIMIT 1 ];
            System.debug('referredByAcc>>>>>>>>>' + referredByAcc);
        }
        if(transAccMap != null){
            accList = [Select Id, Name, Primary_Contact__c, Primary_Country_Code__c,
                                     Membership_Validity__c, Membership_Status__c , Date_of_Payment__c ,
                                     Payment_Type__c , couponCode__c , Amount_Paid__c , ParentId
                                     FROM Account WHERE Id IN : transAccMap.keySet() ];
            System.Debug('accList>>>>>>>>>' + accList);
        }
        
        for(Account acc : accList)
        {
            if(acc.Membership_Status__c == 'On Trial'){
                acc.Membership_Status__c = 'Paid IPV Fee';
            }else If(acc.Membership_Status__c == 'On Trial Community'){
                acc.Membership_Status__c = 'Paid Community';
            }
            
            if(acc.Membership_Validity__c != null ){
                acc.Membership_Validity__c = acc.Membership_Validity__c.addDays(365);
            }else{
                acc.Membership_Validity__c = Date.Today().addDays(365);
            }
            acc.Date_of_Payment__c = transAccMap.get(acc.Id).Transaction_Date__c.date();
            acc.Payment_Type__c = 'New Payment';
            acc.Amount_Paid__c = transAccMap.get(acc.Id).Amount__c;
            if(transAccMap.get(acc.Id).Coupon_Code_Used__c != null){
                acc.couponCode__c = transAccMap.get(acc.Id).Coupon_Code_Used__c;
            }
            if(acc.ParentId == NULL ){
                acc.ParentId = referredByAcc[0].Id;
            }
        }
        
        if(transLeadMap != null ){
             ldList = [Select Id, Name, Primary_Contact__c, Referred_By__c , Primary_Country_Code__c FROM Lead__c WHERE Id IN : transLeadMap.keySet() ];
        }
        System.debug('ldList>>>>>>>>>' + ldList);
        
        if(ldList != null){
            for(Lead__c ld : ldList){
                if(ld.Referred_By__c == NULL && referredByAcc != null){
                    ld.Referred_By__c = referredByAcc[0].Id;
                }
                updatedLeadList.add(ld);
            }
        }
        System.debug('updatedLeadList>>>>>>>>>' + updatedLeadList);
        if(accList != null){
            Update accList;
        }
        if(updatedLeadList != null ){
            Update updatedLeadList;
        }
    }*/
}