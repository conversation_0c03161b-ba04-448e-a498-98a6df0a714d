@RestResource(urlMapping='/AccountSyncAPI/*')
global with sharing class AccountSyncAPI{
    
    @HttpPost
    global Static ResponseWrapper getAccounts()
    {
        ResponseWrapper wResponse = new ResponseWrapper();
        
        try
        {
            RestRequest req = RestContext.request;
            RestResponse res = Restcontext.response;
            string jsonReqString=req.requestBody.tostring();
            Set<Integer> countryCodeSet = new Set<Integer>();
            Set<String> primaryNumberSet = new Set<String>();
            Map<String,Integer> primaryNumberMap = new Map<String,Integer>();
            Set<String> leadIdFromAppSet = new Set<String>();
            //Added by <PERSON><PERSON><PERSON> to send only IPV Records In Response
            String SobjectType;
            id AccRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
            //Added by <PERSON><PERSON> to send only IPV lead Records In Response
            id LeadRecordTypeId = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
            
            String query='Select ';
            List<sObject> accList = new List<sObject>();
            List<List<sObject>> tempList = new List<List<sObject>>();
            Map<String,List<sObject>> resDataMap = new Map<String,List<sObject>>();
            //Map<String,List<AccountWrapper>> resDataMap = new Map<String,List<Accountwrapper>>();
            RequestWrapper wRequest = (RequestWrapper) JSON.deserialize(jsonReqString,RequestWrapper.class);
            
            system.debug('wRequest>>>>>>>'+wRequest);
            system.debug('accList>>>>>>>'+accList);
            
			//added by bharat for sending a referral discount percentage om 09-05-2024 
            List<sObject> MembershipDetailsObj = [SELECT Referral_Dicount_Percentage__c, Amount_Without_GST__c FROM Standard_Memberships_Amount__mdt];
            System.debug('referralObject>>>>>>>>>>>>>>' + MembershipDetailsObj[0]);
			
            if(wRequest.objName == Null || wRequest.objName=='')
            {
                return createResponse(False,'Please enter valid object name.i.e.,Account,Lead__c.',null);
            }
            
            for(ObjPrimaryContactWrapper acc : wRequest.primaryContactList)
            {
                If(acc.countryCode==null || acc.countryCode=='' || !acc.countryCode.isNumeric())
                    acc.countryCode = '91';
                
                if(String.IsNotBlank(acc.primaryNumber))
                {
                    primaryNumberSet.add(acc.primaryNumber);
                }
                if(String.IsNotBlank(acc.countryCode) && acc.countryCode.isNumeric())
                {
                    countryCodeSet.add(Integer.valueof(acc.countryCode.Trim()));
                }
                
                if(String.IsNotBlank(acc.primaryNumber) && String.IsNotBlank(acc.countryCode) && acc.countryCode.isNumeric())
                {
                    primaryNumberMap.put(Integer.valueof(acc.countryCode.Trim())+'-'+acc.primaryNumber,Integer.valueof(acc.countryCode.Trim()));
                }
                
                if(String.IsNotBlank(acc.leadIdFromApp))
                    leadIdFromAppSet.add(acc.leadIdFromApp);
            }
            
            system.debug('primaryNumberMap>>>>'+primaryNumberMap);
            system.debug('primaryNumberSet>>>>'+primaryNumberSet);
            system.debug('countryCodeSet>>>>'+countryCodeSet);
            
            If(wRequest.columns!=null && wRequest.columns.size()>0)
            {
                system.debug('wRequest.columns>>>>>'+wRequest.columns);
                //Set<String> accFieldSet = Schema.SObjectType.Account.fields.getMap().keySet();
                Set<String> accFieldSet = Schema.getGlobalDescribe().get(wRequest.objName).getDescribe().fields.getMap().Keyset();
                System.debug('accFieldSet >>>'+accFieldSet );
                
                for(String clm : wRequest.columns)
                {
                    System.debug('clm>>>'+clm);
                    
                    if(clm.containsIgnoreCase('__r.'))
                    {
                        String pField = clm.replace('__r.', '__c.');
                        System.debug('hjhj>>>'+pField);
                        pField = pField.substring(0,pField.indexOf('.'));
                        System.debug('hjhj1>>>'+pField);
                        System.debug('clm>>>'+clm);
                        
                        if(accFieldSet.contains(pField.toLowerCase()))
                        {
                            query = query +clm+',';
                        }
                    }
                    else if(accFieldSet.contains(clm.toLowerCase()))
                    {
                        query = query +clm+',';
                    }
                }
                
                System.debug('query>>>'+query );
                
            }
            else
            {
                return createResponse(False,'Please send field API Names for object:'+wRequest.objName,null);
            }
            
            If(query.contains(','))
            {
                if(!query.contains('Full_Primary_Contact__c'))
                {
                    query = query +'Full_Primary_Contact__c' +','+'RecordTypeId' + ','; //AIF_Contributor__c
                    
                }                 
                query = query.removeEnd(',');
                System.debug('Query111>>>>'+query);
                //query = query +' From Account where Primary_Contact__c in :primaryNumberSet AND Primary_Country_Code__c in :countryCodeSet';
                //Updated by Karan on 27/12/2021 to search account by primarynumber without country Code.
                
                query = query +' From '+wRequest.objName+' where (Primary_Contact__c in :primaryNumberSet AND Primary_Country_Code__c in :countryCodeSet) OR (Full_Primary_Contact__c in :primaryNumberSet)';
                
                if(leadIdFromAppSet.size()>0)
                {
                    query = query +' OR Lead_External_ID_App__c in :leadIdFromAppSet';
                }
            }
            else
            {
                return createResponse(False,'No Fields found for object:'+wRequest.objName,null);
            }
            
            if(primaryNumberMap.size()>0 || leadIdFromAppSet.size()>0)
            {
                
                //TODO: Searh for primary and secondary contact with above sets. 31/01/21
                system.debug('query113>>>>'+query);
                accList = Database.query(query);//[select id,Primary_Contact__c,Primary_Country_Code__c from account where Primary_Contact__c in :primaryNumberSet AND Primary_Country_Code__c in :countryCodeSet]; 
                List<sObject> accResList = new List<sObject>();
                //List<AccountWrapper> accResList = new List<AccountWrapper>();
                List<lead__c> leadList = Database.query('SELECT id,Name,Primary_Country_Code__c,Primary_Contact__c,Membership_Status__c,Lead_External_ID_App__c,Relationship_Owner__r.name,Relationship_Owner__r.Contact_NO__c,Relationship_Owner__r.Email,RecordTypeId FROM Lead__C WHERE (Primary_Contact__c in :primaryNumberSet AND Primary_Country_Code__c in :countryCodeSet) OR Lead_External_ID_App__c In:leadIdFromAppSet');
                
                system.debug('accList>>>>'+accList);
                for(sObject acc : accList)
                {
                    //Added by Ankush to send only IPV Records In Response
                    if(acc.get('RecordTypeId') == AccRecordTypeId)
                    {
                        SobjectType = getSObjectTypeNameFromId(Acc.id);                           
                        system.debug('SobjectName>>>'+SobjectType);
                        if(leadIdFromAppSet.Contains(''+acc.get('Lead_External_ID_App__c'))
                           || primaryNumberMap.containsKey(acc.get('Primary_Country_Code__c')+'-'+acc.get('Primary_Contact__c'))
                           || primaryNumberSet.contains(''+acc.get('Full_Primary_Contact__c')))
                        {
                            accResList.add(acc);
                        }
                    }
                 }
                
                //Added by Karan to send lead RMs details only for IPV Records In Response 1.7.23
                for(sObject lead : leadList)
                {
                    if(lead.get('RecordTypeId') == LeadRecordTypeId)
                    {   
                        SobjectType = getSObjectTypeNameFromId(lead.id);
                        system.debug('SobjectName>>>'+SobjectType);
                        if(primaryNumberMap.containsKey(lead.get('Primary_Country_Code__c')+'-'+lead.get('Primary_Contact__c'))
                          || leadIdFromAppSet.Contains(''+lead.get('Lead_External_ID_App__c')))
                        {
                           //AccountWrapper wrapper = new AccountWrapper(lead, sObjectType);
            			  // accResList.add(wrapper);
                           accResList.add(lead);
                    	}
                	}
                }
                if(accResList.size()> 0 )
                {
                    System.debug('accResList>>>>>>>' + accResList);
                    //tempList.add(accResList);
                    resDataMap.put(wRequest.objName,accResList);
                                       
                    //added by bharat for sending a referral discount percentage om 09-05-2024
                    if(MembershipDetailsObj.size()>0){
                        resDataMap.put('Referral_Details' , MembershipDetailsObj);
                    }
                    //String objectType = wRequest.objName;
                    //Type sObjectType = Type.forName(objectType);
                    //SObject objInstance = (SObject)sObjectType.newInstance();
                    //resDataMap.put('user_account_type' , new List<sObject>{objInstance});
                    System.debug('resDataMap>?>>>>>>>' + resDataMap);
                    List<sObject> sObjectList =New List<sObject>(resDataMap.get('Account'));
                    if(sObjectList!=null && sObjectList.size()>0){
                    for (sObject record : sObjectList) {
                        System.debug('Record: >>>>>>>>>>>>>>>>>>' + record);
                    }}
                    return createResponse(true,'Records found for object(s):'+wRequest.objName,resDataMap);
                }
                else
                {
                    return createResponse(False,'Records not found for object:'+wRequest.objName,null);
                }
                
            }
            else
            {
                return createResponse(False,'Please send valid Lead Id from App or primary contact number and country code.',null);
            }
            
        }
        catch(exception e)
        {
            return createResponse(False,'Exception:'+e.getMessage(),null);
        }
    }
    
    public static String getSObjectTypeNameFromId(Id recordId) {
        
        Schema.SObjectType sObjectType = recordId.getSObjectType();
        
        String sObjectTypeName = sObjectType.getDescribe().getName();
        
        return sObjectTypeName;
    }
    
    public static ResponseWrapper createResponse(Boolean resFlag,String msg, Map<String,List<sObject>> dataMap)      
    {
        ResponseWrapper res = new ResponseWrapper();
        res.isSuccess = resFlag;
        res.message = msg;
        
        if(dataMap!=null && dataMap.size()>0)
        {
            res.data= dataMap;
        }
        return res;
    }
    
    global class ObjPrimaryContactWrapper{
        global string countryCode;
        global string primaryNumber;       
        global string leadIdFromApp;       
    }
     
    global class RequestWrapper{
        global List<string> columns;
        global List<ObjPrimaryContactWrapper> primaryContactList;   
        global String objName;	
    }
}