/*************************************************************************
 Test class of letsGrowtrigger and letsgrowHandler 
 created date : 28/12/2023
 Createdby : Ankush pawar
***************************************************************************/ 
@IsTest(SeeAlldata =false)
public class LetsgrowTriggerHandlerTestclass {

    @testsetup static void setup(){
        List<Account> accList = new List<Account>();
        account acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Platinum';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        accList.add(acc);
        
        acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Platinum';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        acc.Title__c = 'Mr';
        accList.add(acc);
        
        insert acclist;
        
        List<Startup__c> startuplist = new list<Startup__c>();
        startup__c st = testfactory.createStartUp();
        startuplist.add(st);
        
        st = testfactory.createStartUp();
        st.Legal_Name__c ='Lets Grow startup';
        startuplist.add(st);
        
        insert startuplist;
    }
    
    Static testmethod void letsgrow(){
       test.startTest(); 
       list<Account> Acidlist = [select id,LetsGrow_Lead_Investor_Mentor__c,LetsGrow_Investor_Mentor__c from account];
       Startup__c stid =[Select id from startup__c limit 1];
       LetsGrow__c lg = new LetsGrow__c ();
       lg.Start_Date__c = date.newinstance(2023,12,12);
       lg.Member_Name__c = Acidlist[0].id;
       lg.Startup_Name__c = stid.id;
       lg.LetsGrow_Membership_Type__c ='LetsGrow Investor Mentor';
       lg.Cooling_off_period__c = 5;
       insert lg; 
       
       Account Acc = [Select id,LetsGrow_Lead_Investor_Mentor__c,LetsGrow_Investor_Mentor__c from account where id =:lg.Member_Name__c]; 
       acc.LetsGrow_Investor_Mentor__c = true;
       update acc; 
        
       // Update scenario 
       lg.LetsGrow_Membership_Type__c ='LetsGrow Lead Investor Mentor';
       lg.Member_Name__c = Acidlist[1].id; 
       update lg; 
        
       //delete scenario
       delete lg; 
       test.stopTest();
    }
}