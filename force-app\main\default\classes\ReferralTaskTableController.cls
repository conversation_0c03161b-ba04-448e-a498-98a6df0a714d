public with sharing class ReferralTaskTableController {
   @AuraEnabled(cacheable=true)
    public static List<Task>ReferralTaskTable(Id RecordId) {
        
       
        List<Task> listName =[select id,Referral_Trigger_Type__c,CreatedDate,Referral_Trigger_Status__c,Call_Date__c,Outcome__c  from task where Referral_Trigger_Status__c != null
                              ];

           return listName;

    }
}