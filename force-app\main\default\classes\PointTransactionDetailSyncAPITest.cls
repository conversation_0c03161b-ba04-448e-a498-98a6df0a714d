@isTest
public class PointTransactionDetailSyncAPITest {

    @isTest
    static void testValidInput() {
        // Setup test data
        Account testAccount1= TestFactory.createAccount();
        testAccount1.Name ='Test Account 1';
        testAccount1.Primary_Contact__c = '**********';
        testAccount1.Primary_Country_Code__c= 91;
        testAccount1.Membership_Slab__c = 'Gold';
        insert testAccount1;
        
        Account testAccount2= TestFactory.createAccount();
        testAccount2.Membership_Slab__c = 'Silver';
        testAccount2.Name ='Test Account 2';
        insert testAccount2;
        
        Account testAccount3= TestFactory.createAccount();
        testAccount3.Membership_Slab__c = 'Bronze';
        testAccount3.Name ='Test Account 3';
        insert testAccount3;

        List<Points_Transaction__c> pointList = new List<Points_Transaction__c>();
        Points_Transaction__c testTransaction1 = new Points_Transaction__c(
            Credit_To__c = testAccount1.Id,
            Debit_From__c = testAccount2.Id,
            Point_Type__c = 'Startup Voting',
            Date_of_Startup_Voting__c = Date.Today(),
            Startup_Count__c = 10,
            Points_Alloted__c = 100
        );
        pointList.add(testTransaction1);
        
        Points_Transaction__c testTransaction2 = new Points_Transaction__c(
            Credit_To__c = testAccount1.Id,
            Debit_From__c = testAccount2.Id,
            Point_Type__c = 'Investor Call',
            Event_Date__c = Date.Today(),
            Startup_Count__c = 10,
            Points_Alloted__c = 100
        );
        pointList.add(testTransaction2);

        Points_Transaction__c testTransaction3 = new Points_Transaction__c(
            Credit_To__c = testAccount1.Id,
            Debit_From__c = testAccount3.Id,
            Point_Type__c = 'Refer a Startup',
            Date_of_Receiving_Lead__c = Date.Today(),
            Startup_Count__c = 10,
            Points_Alloted__c = 100
        );
        pointList.add(testTransaction3);

        Points_Transaction__c testTransaction4 = new Points_Transaction__c(
            Credit_To__c = testAccount3.Id,
            Debit_From__c = testAccount2.Id,
            Point_Type__c = 'LetsGrow SIP Call',
            Date_of_SIP_Call__c = Date.Today(),
            Startup_Count__c = 10,
            Points_Alloted__c = 100
        );
        pointList.add(testTransaction4);
        
        Points_Transaction__c testTransaction5 = new Points_Transaction__c(
            Credit_To__c = testAccount1.Id,
            Debit_From__c = testAccount2.Id,
            Point_Type__c = 'LetsGrow Business Connected',
            Date_of_SIP_Call__c = Date.Today(),
            Startup_Count__c = 10,
            Points_Alloted__c = 100
        );
        pointList.add(testTransaction5);

        insert pointList;
            
        // Create request body
        PointTransactionDetailSyncAPI.RequestWrapper requestBody = new PointTransactionDetailSyncAPI.RequestWrapper();
        PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper contact = new PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper();
        contact.primaryNumber = '**********';
        contact.countryCode = '91';
        requestBody.primaryContactList = new List<PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper>{contact};
        requestBody.lastSyncTime = null;
        
        // Serialize request and set it to RestContext
        RestContext.request = new RestRequest();
        RestContext.response = new RestResponse();
        RestContext.request.requestBody = Blob.valueOf(JSON.serialize(requestBody));

        // Call the method
        Test.startTest();
        PointTransactionDetailSyncAPI.getPointTransactionDetails();
        Test.stopTest();

        // Validate response
        RestResponse response = RestContext.response;
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response.responseBody.toString());
    }

    @isTest
    static void testEmptyPrimaryNumber() {
        // Create request body with empty primaryNumber
        PointTransactionDetailSyncAPI.RequestWrapper requestBody = new PointTransactionDetailSyncAPI.RequestWrapper();
        PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper contact = new PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper();
        contact.primaryNumber = '';  // Empty primary number
        contact.countryCode = '91';
        requestBody.primaryContactList = new List<PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper>{contact};

        RestContext.request = new RestRequest();
        RestContext.response = new RestResponse();
        RestContext.request.requestBody = Blob.valueOf(JSON.serialize(requestBody));

        // Call the method
        Test.startTest();
        PointTransactionDetailSyncAPI.getPointTransactionDetails();
        Test.stopTest();

        // Validate response
        RestResponse response = RestContext.response;
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response.responseBody.toString());
    }

    @isTest
    static void testInvalidCountryCode() {
        // Create request body with invalid country code
        PointTransactionDetailSyncAPI.RequestWrapper requestBody = new PointTransactionDetailSyncAPI.RequestWrapper();
        PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper contact = new PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper();
        contact.primaryNumber = '**********';
        contact.countryCode = '909';  // Non-numeric country code
        requestBody.primaryContactList = new List<PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper>{contact};

        RestContext.request = new RestRequest();
        RestContext.response = new RestResponse();
        RestContext.request.requestBody = Blob.valueOf(JSON.serialize(requestBody));

        // Call the method
        Test.startTest();
        PointTransactionDetailSyncAPI.getPointTransactionDetails();
        Test.stopTest();

        // Validate response
        RestResponse response = RestContext.response;
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response.responseBody.toString());
    }

    @isTest
    static void testNoRecordsFound() {
        // Create request body with no matching records
        PointTransactionDetailSyncAPI.RequestWrapper requestBody = new PointTransactionDetailSyncAPI.RequestWrapper();
        PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper contact = new PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper();
        contact.primaryNumber = '**********'; // Non-existing primary number
        contact.countryCode = '91';
        requestBody.primaryContactList = new List<PointTransactionDetailSyncAPI.ObjPrimaryContactWrapper>{contact};

        RestContext.request = new RestRequest();
        RestContext.response = new RestResponse();
        RestContext.request.requestBody = Blob.valueOf(JSON.serialize(requestBody));

        // Call the method
        Test.startTest();
        PointTransactionDetailSyncAPI.getPointTransactionDetails();
        Test.stopTest();

        // Validate response
        RestResponse response = RestContext.response;
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response.responseBody.toString());
    }

    @isTest
    static void testExceptionHandling() {
        // Create a request body with invalid JSON to simulate an exception
        RestContext.request = new RestRequest();
        RestContext.response = new RestResponse();
        RestContext.request.requestBody = Blob.valueOf('Invalid JSON');

        // Call the method
        Test.startTest();
        PointTransactionDetailSyncAPI.getPointTransactionDetails();
        Test.stopTest();

        // Validate response
        RestResponse response = RestContext.response;
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response.responseBody.toString());
    }
}