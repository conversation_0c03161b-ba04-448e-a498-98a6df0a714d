global class ExternalAPICalloutQueueable implements Queueable, Database.AllowsCallouts {
    List<List<String>> sfidChunks;
    Integer currentIndex;

    // Constructor to accept chunks and the current index
    global ExternalAPICalloutQueueable(List<List<String>> sfidChunks, Integer currentIndex) {
        this.sfidChunks = sfidChunks;
        this.currentIndex = currentIndex;
    }

    global void execute(QueueableContext context) {
        if (currentIndex < sfidChunks.size()) {
            List<String> currentChunk = sfidChunks[currentIndex];

            // Perform the API callout
            doCallout(currentChunk);

            // Enqueue the next job if there are more chunks to process
            if (currentIndex + 1 < sfidChunks.size()) {
                System.enqueueJob(new ExternalAPICalloutQueueable(sfidChunks, currentIndex + 1));
            }
        }
    }

    public void doCallout(List<String> sfids) {
        String accessToken = RestLoginController.loginExternalSystem();
        Http http = new Http();
        HttpRequest request = new HttpRequest();
        request.setEndpoint('https://stg.dashboard.ipventures.in/report/getActivitySummary');
        request.setMethod('POST');
        request.setHeader('Content-Type', 'application/json');
        request.setHeader('Authorization', 'Bearer ' + accessToken);

        Map<String, Object> requestBody = new Map<String, Object>();
        requestBody.put('sfids', sfids);
        requestBody.put('startDate', String.valueOf(Date.Today().format()));
        requestBody.put('endDate', String.valueOf(Date.Today().addDays(-1).format()));

        request.setBody(JSON.serialize(requestBody));

        Map<Integer, String> activityDetailMap = new Map<Integer, String>{
            0 => 'Startup Tab',
            1 => 'Agenda Tab',
            2 => 'Sign in',
            3 => 'Refer a Member',
            4 => 'Discussion Add',
            5 => 'Discussion Add Comment',
            6 => 'Commitment Form Link',
            7 => 'Contact Your RM',
            8 => 'FAQs',
            9 => 'Feedback Form Link',
            10 => 'Leaderboard',
            11 => 'Favourite Startup',
            12 => 'Portfolio Tab',
            13 => 'Refer a Startup',
            14 => 'Raise a Query',
            15 => 'Startup Call Recording'
        };
        
        Id recordId;
        String sourceObjectName = '';

        try {
           HttpResponse res = http.send(request);
String responseBody = res.getBody();
System.debug('Response received from external API: Status Code: ' + res.getStatusCode() + ', Body: ' + responseBody);

if (res.getStatusCode() == 200) {
    // Parse the response
    Map<String, Object> apiResponse = (Map<String, Object>) JSON.deserializeUntyped(responseBody);

    // Check if the 'data' key exists in the response
    if (apiResponse.containsKey('data')) {
        Map<String, Object> dataMap = (Map<String, Object>) apiResponse.get('data');

        // Get the 'summaries' list from the 'data' map
        List<Object> summaries = (List<Object>) dataMap.get('summaries');

        // Process the summaries and create User_Activity__c records
        List<User_Activity__c> accountActivities = new List<User_Activity__c>();
        List<User_Activity__c> leadActivities = new List<User_Activity__c>();

        for (Object activityObj : summaries) {
            Map<String, Object> activityData = (Map<String, Object>) activityObj;
            String salesforce_user_account_id = (String) activityData.get('salesforce_user_account_id');
            String activitySummary = (String) activityData.get('activity_summary');
            Integer activityDetail = (Integer) activityData.get('activity_type');

            if (salesforce_user_account_id != null) {
                // Determine whether the record is an Account or Lead
                SObject record = [SELECT Id FROM Account WHERE Id = :salesforce_user_account_id LIMIT 1];

                if (record != null) {
                    User_Activity__c activity = new User_Activity__c();
                    
                    if (record.getSObjectType().getDescribe().getName() == 'Account') { 
                        activity.Related_Account__c = salesforce_user_account_id;
                    } else {
                        activity.Related_Lead__c = salesforce_user_account_id;
                    }
                    
                    activity.Activity_Detail_RICH__c = activityDetailMap.get(activityDetail) + ' >>> ' + activitySummary;  // Combine the details
                    activity.Activity_Type__c = activityDetailMap.get(activityDetail);
                    activity.Time_Stamp__c = Date.today();

                    if (record.getSObjectType().getDescribe().getName() == 'Account') {
                        accountActivities.add(activity);
                      
                    } else {
                        leadActivities.add(activity);
                        System.debug('Creating User_Activity__c record for Lead ID: ' + salesforce_user_account_id);
                    }
                      System.debug('Creating User_Activity__c record for Account ID: ' + accountActivities );
                }
            } else {
                System.debug('ID not found in the original recordIds: ' + salesforce_user_account_id);
            }
        }

        // Insert Account User_Activity__c records
        if (!accountActivities.isEmpty()) {
            insert accountActivities;
            System.debug('Inserted ' + accountActivities.size() + ' User_Activity__c records for Accounts.');
        } else {
            System.debug('No Account activities to insert.');
        }

        // Insert Lead User_Activity__c records
        if (!leadActivities.isEmpty()) {
            insert leadActivities;
            System.debug('Inserted ' + leadActivities.size() + ' User_Activity__c records for Leads.');
        } else {
            System.debug('No Lead activities to insert.');
        }
    } else {
        System.debug('Error: "data" key not found in the API response.');
    }
} else {
    // Handle the error response
    System.debug('Error response from external API: ' + res.getBody());
}


        } catch (Exception e) {
            System.debug('Error during callout: ' + e.getMessage());
        }
    }

    global class ResponseWrapper {
        public List<ActivitySummary> summaries;
    }
        
    global class ActivitySummary {
        public Integer activity_type;
        public String activity_summary;
        public String salesforce_user_account_id;
    }
}