public class InvestmentSyncBatch implements Database.Batchable<SObject> {

    public Database.QueryLocator start(Database.BatchableContext context) {
        return Database.getQueryLocator([SELECT Id FROM Investment__c LIMIT 10]);
    }

    public void execute(Database.BatchableContext context, List<Investment__c> scope) {
        List<Id> idBatch = new List<Id>();
        Set<Id> processedIds = new Set<Id>(); // Processed IDs
        Integer batchSize = 10;

        for (Investment__c inv : scope) {
            Id invId = (Id) inv.get('Id');
            if (!processedIds.contains(invId)) // Skip already processed IDs
            { 
                idBatch.add(invId);
                processedIds.add(invId);
            }

            if (idBatch.size() == batchSize) {
                sendBatch(idBatch);
                idBatch.clear();
            }
        }

        // Send any remaining IDs
        if (!idBatch.isEmpty()) {
            sendBatch(idBatch);
        }
    }

    public void finish(Database.BatchableContext context) {
        // post-processing logic here
    }

    private void sendBatch(List<Id> ids) {
        System.debug('Passedidstosync>>>' +ids);
        InvestmentRestAPIController.sendInvestmentDetails(new Set<Id>(ids), true);
    }
}