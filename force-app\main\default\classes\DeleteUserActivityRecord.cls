public class DeleteUserActivityRecord {
    
     //Added By <PERSON> On 28th september 2024
     //delete  Records of User Actyvity which Created Date is More Than One Year 

    public void deleteRecords() {
        Date oneYearAgo = System.today().addYears(-1);
        List<User_Activity__c> oldRecords = [SELECT Id FROM User_Activity__c WHERE CreatedDate < :oneYearAgo];
        if (!oldRecords.isEmpty()) {
            delete oldRecords;
        }
    }
}