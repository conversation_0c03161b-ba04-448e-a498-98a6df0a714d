public class StartupPipelineEmailHandler {
    
    public static void sendOnboardEmail(Startup_Pipeline__c sp) {
        
        if (sp.SP_Founder_Email__c != null)
        {
            Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
            mail.setToAddresses(new String[] { sp.SP_Founder_Email__c });
            mail.setCcAddresses(new String[] { '<EMAIL>' });
            mail.setSubject('Congratulations Your Startup has been Shortlisted | Inflection Point Ventures');
            
            String emailBody = '<p>Hi ' + sp.SP_Founder_Name__c + ',</p>' + 
                               '<p>Hope you\'re doing well!</p>' + 
                               '<p>Thank you for your patience. Our selection panel has evaluated your startup and the founders of IPV have expressed their interest in meeting you to get more information about the startup and founders. Our team will reach out to you to discuss the next steps we follow at IPV from hereon and to confirm your availability for the meeting.</p>' + 
                               '<p>Kindly fill in the details required in the <strong>attached self-evaluation form,</strong> which requires to be filled and sent back to us. It is a mandatory requirement and will help us understand about your business. Also, <strong>please note that all the fields marked asterisk (*) are compulsory to fill,</strong> basic information like funds sought and pre-money valuation is mandatory no matter what stage the startup is, and the startup won’t move to the next stage if the form is incomplete and send it to us before the meeting so that we can process ahead.</p>' + 
                               '<p>The next steps are as follows:</p>' +
                               '<table border="1" cellpadding="10">' + 
                               '  <tr>' +
                               '    <th>Sr.no</th>' +
                               '    <th>Process</th>' +
                               '    <th>Timeline</th>' +
                               '  </tr>' +
                               '  <tr>' +
                               '    <td>1.</td>' +
                               '    <td>Meeting with co-founder of IPV</td>' +
                               '    <td>3-4 days</td>' +
                               '  </tr>' +
                               '  <tr>' +
                               '    <td>2.</td>' +
                               '    <td>Basis the comments of Co-founder, we’ll share the Terms & Conditions with you</td>' +
                               '    <td>1-2 days</td>' +
                               '  </tr>' +
                               '  <tr>' +
                               '    <td>3.</td>' +
                               '    <td>Scheduling your webinar with our network of investors</td>' +
                               '    <td>2-3 weeks</td>' +
                               '  </tr>' +
                               '</table>' +
                               '<p>Looking forward to connecting with you!</p>';
            
            mail.setHtmlBody(emailBody);
            
            Blob excelBlob = [SELECT Body FROM StaticResource WHERE Name = 'Evaluation_Form'].Body;
            String excelFileName = 'evaluation_form.xlsx';
            Messaging.EmailFileAttachment attachment = new Messaging.EmailFileAttachment();
            attachment.setFileName(excelFileName);
            attachment.setBody(excelBlob);
            
            mail.setFileAttachments(new Messaging.EmailFileAttachment[] { attachment });
            Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
        }
    }

    public static void sendRejectionEmail (Startup_Pipeline__c sp){
        
        if (sp.SP_Founder_Email__c != null) {
            
            Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
            
            mail.setToAddresses(new String[] { sp.SP_Founder_Email__c });
            mail.setCcAddresses(new String[] { '<EMAIL>' });
            mail.setSubject('Your Startup has not been Shortlisted | Inflection Point Ventures');
            String emailBody =	'<p>Hi ' + sp.SP_Founder_Name__c + ',</p>' +
                				'<p>Hope you are doing well.</p>' + 
                   				'<p>Thank you for sharing your business presentation with us. Our selection panel has reviewed your idea in great detail.</p>' + 
                   				'<p>After reviewing your business idea and the relevant aspects, the selection panel feels that at the current state of business we will not be able to consider it for funding. Hence, we will not be able to move forward with the proposal currently. However, we encourage you to stay in touch with us and keep us updated with how your start-up progresses over the next few months.</p>' + 
                   				'<p>We wish you all the best.</p>';
            
            mail.setHtmlBody(emailBody);
            Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
        }
    }
}