/*
    This APEX class is Stricly for CODIOT internal use only. please do not modify OR use this class without prior approval from <PERSON><PERSON><PERSON>
*/

global class InvestmentBatchSync implements Database.Batchable<sObject>, Database.AllowsCallouts, Database.Stateful {
    
    public static String ipvAccount = Label.IPV_Account_Id;

    public Database.QueryLocator start(Database.BatchableContext bc) {
        return Database.getQueryLocator('SELECT Id FROM Investment__c WHERE Account__c != :ipvAccount AND Investor__c != null'); 
    }

    public void execute(Database.BatchableContext bc, List<Investment__c> investmentList) {
        List<Id> investmentIds = new List<Id>();
        for (Investment__c inv : investmentList) {
            if(!investmentIds.contains(inv.Id))
            {
                investmentIds.add(inv.Id);
            }
        }

        System.debug('The Size of the Investment Ids is: ' + investmentIds.size());

        if (!investmentIds.isEmpty()) {
            InvestmentSyncJob queueSync = new InvestmentSyncJob(investmentIds);
            System.enqueueJob(queueSync);
        }
    }

    public void finish(Database.BatchableContext bc) {
        System.debug('Batch Processing Completed.');
    }
}