public class FileUploadController {

    public void uploadFile(String fileName, Blob fileData) {
        system.debug('test');

        HttpRequest req = KYCController.loginToDigio();

        req.setEndpoint('https://api.digio.in/v3/client/kyc/analyze/file/idcard'); // Replace with your desired endpoint URL

        req.setMethod('POST');

        system.debug('req');

        String boundary = '----------------------------' + String.valueOf(System.currentTimeMillis());

        String header = '--' + boundary + '\r\nContent-Disposition: form-data; name="front_part"'+ '"\r\nContent-Type: application/octet-stream\r\n\r\n';

        String footer = '\r\n--' + boundary + '--\r\n';

        

        String body = header + EncodingUtil.base64Encode(fileData) + footer;

        system.debug(body);

        req.setHeader('Content-Type', 'multipart/form-data; boundary=' + boundary);


        req.setHeader('Content-Length', String.valueOf(body.length()));

        

        req.setBody(body);

        

        Http http = new Http();

        HttpResponse res = http.send(req);

        

        if (res.getStatusCode() == 200) {

            // File uploaded successfully

        } else {

            // Handle error response

        }

    }

}