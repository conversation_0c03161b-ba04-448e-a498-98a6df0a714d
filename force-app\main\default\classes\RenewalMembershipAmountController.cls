public with sharing class RenewalMembershipAmountController {
    
    @AuraEnabled(cacheable=true)
    public static Map<String, Object> renewalMembershipAmount(Id recordId) {
        Map<String, Integer> renewalAmount = new Map<String, Integer>();
        
        // Query custom metadata
        List<Amount_Payable_For_Renewal__mdt> renewalCustomMetaData = [
            SELECT Id, Bronze_Slab_Discount__c, Silver_Slab_Discount__c, Gold_Slab_Discount__c, CXO_Genie_Membership_Discount__c, Price_Per_Point__c, GST__c, One_Year_excl_GST__c, Two_Years_excl_GST__c, Three_Years_excl_GST__c, Four_Years_excl_GST__c, Five_Years_excl_GST__c 
            FROM Amount_Payable_For_Renewal__mdt LIMIT 1
        ];

        if (!renewalCustomMetaData.isEmpty()) {
            Amount_Payable_For_Renewal__mdt metaDataRecord = renewalCustomMetaData[0];
            
            // Describe and loop over fields to populate renewalAmount map
            Schema.DescribeSObjectResult describeResult = metaDataRecord.getSObjectType().getDescribe();
            for (Schema.SObjectField field : describeResult.fields.getMap().values()) {
                String fieldName = field.getDescribe().getName();
                if (fieldName.endsWith('__c')) {
                    Object fieldValue = metaDataRecord.get(field);
                    if (fieldValue != null && fieldValue instanceof Decimal) {
                        renewalAmount.put(fieldName, ((Decimal) fieldValue).intValue());
                    }
                }
            }
        }

        // Query for the Account record
        Account acc = [SELECT Id, Membership_Slab__c, Membership_Status__c, Total_Point__c, Primary_Contact__c, Primary_Country_Code__c, Additional_Discount__c  
                       FROM Account 
                       WHERE Id = :recordId 
                       AND Membership_Status__c NOT IN ('On Trial', 'On Trial Community', 'IPV Team') 
                       AND RecordType.Name = 'IPV'];
        
        // Initialize variables
        String primaryContact = acc.Primary_Contact__c;
        Decimal countryCode = acc.Primary_Country_Code__c;

        // Query for CXO Account with proper null checks
        List<Account> cxoAccountList = [SELECT Id, Current_Membership_fee_upto__c, Current_Membership_Status__c 
                                        FROM Account 
                                        WHERE RecordType.Name = 'CXO Genie' 
                                        AND Primary_Country_Code__c = :countryCode 
                                        AND Primary_Contact__c = :primaryContact 
                                        LIMIT 1];

        String currentMembershipStatus;
        Date currentMembershipFeeUpto;
        Boolean isCXOGenieMembership = false;

        // Check if the cxoAccountList is not empty
        if (!cxoAccountList.isEmpty()) {
            Account cxoAccount = cxoAccountList[0];
            currentMembershipStatus = cxoAccount.Current_Membership_Status__c;
            currentMembershipFeeUpto = cxoAccount.Current_Membership_fee_upto__c;
            
            // Validate CXO membership
            isCXOGenieMembership = (Date.Today() <= currentMembershipFeeUpto && 
                (currentMembershipStatus == 'Membership Extended' || 
                 currentMembershipStatus == 'Existing Paid Member' || 
                 currentMembershipStatus == '50% Paid by IPV' || 
                 currentMembershipStatus == 'First Time Paid' || 
                 currentMembershipStatus == 'Add on - Paid' || 
                 currentMembershipStatus == 'Renewals- Follow up by RM' || 
                 currentMembershipStatus == 'Renewals- follow up by Finance'));
        }

        // Calculate slab discounts
        Decimal goldSlabDiscount = Decimal.valueOf(renewalAmount.get('Gold_Slab_Discount__c'));
        Decimal silverSlabDiscount = Decimal.valueOf(renewalAmount.get('Silver_Slab_Discount__c'));
        Decimal bronzeSlabDiscount = Decimal.valueOf(renewalAmount.get('Bronze_Slab_Discount__c'));
        Decimal cxoGenieDiscount = Decimal.valueOf(renewalAmount.get('CXO_Genie_Membership_Discount__c'));
        Decimal pricePerPoint = Decimal.valueOf(renewalAmount.get('Price_Per_Point__c'));
        Decimal gstPrice = Decimal.valueOf(renewalAmount.get('GST__c'));
        Decimal oneYearMembershipExclGST = Decimal.valueOf(renewalAmount.get('One_Year_excl_GST__c'));
        Decimal twoYearsMembershipExclGST = Decimal.valueOf(renewalAmount.get('Two_Years_excl_GST__c'));
        Decimal threeYearsMembershipExclGST = Decimal.valueOf(renewalAmount.get('Three_Years_excl_GST__c'));
        Decimal fourYearsMembershipExclGST = Decimal.valueOf(renewalAmount.get('Four_Years_excl_GST__c'));
        Decimal fiveYearsMembershipExclGST = Decimal.valueOf(renewalAmount.get('Five_Years_excl_GST__c'));
        Decimal totalPointsOfAccount;
        // Integer totalPointsOfAccount = acc.Total_Point__c != null ? Integer.valueOf(acc.Total_Point__c) : 0;
        Decimal totalAmountFromPoints = 0;
        Decimal additionalDiscount = acc.Additional_Discount__c != null ? acc.Additional_Discount__c : Decimal.valueOf(0);
        String membershipSlab = acc.Membership_Slab__c != null ? acc.Membership_Slab__c : null;
        Boolean isBelowGoldSlab = (membershipSlab == 'Silver' || membershipSlab == 'Bronze' || membershipSlab == null);
        if(acc.Total_Point__c != null)
        {
            totalPointsOfAccount = acc.Total_Point__c;
        }
        else
        {
            totalPointsOfAccount = 0;
        }
        
        
        // System.debug('');
        // System.debug('Membership Slab >>>> ' + membershipSlab);
        // System.debug('The Additional Dicount By RM Is >>>> ' + additionalDiscount);
        // System.debug('');

        // System.debug('Price Per Point >>>> ' + pricePerPoint);
        // System.debug('Total Points of Account >>>> ' + totalPointsOfAccount);
        // System.debug('Total Amount From Points >>>> ' + totalAmountFromPoints);

        if(totalPointsOfAccount != null)
        {
            totalAmountFromPoints = totalPointsOfAccount * pricePerPoint;
        }
        else
        {
            totalAmountFromPoints = 0;
        }
        // System.debug('');
        // System.debug('Total Points >>>> ' + totalAmountFromPoints);
        // System.debug('Total Amount From Points >>>> ' + totalAmountFromPoints);
        // System.debug('');
        
        Decimal oneYearGSTAmount = ((oneYearMembershipExclGST * gstPrice) / 100).round();
        Decimal twoYearsGSTAmount = ((twoYearsMembershipExclGST * gstPrice) / 100).round();
        Decimal threeYearsGSTAmount = ((threeYearsMembershipExclGST * gstPrice) / 100).round();
        Decimal fourYearsGSTAmount = ((fourYearsMembershipExclGST * gstPrice) / 100).round();
        Decimal fiveYearsGSTAmount = ((fiveYearsMembershipExclGST * gstPrice) / 100).round();


        Decimal oneYearMembershipIncGST = (oneYearMembershipExclGST + oneYearGSTAmount).round();
        Decimal twoYearsMembershipIncGST = (twoYearsMembershipExclGST + twoYearsGSTAmount).round(); 
        Decimal threeYearsMembershipIncGST = (threeYearsMembershipExclGST + threeYearsGSTAmount).round(); 
        Decimal fourYearsMembershipIncGST = (fourYearsMembershipExclGST + fourYearsGSTAmount).round();
        Decimal fiveYearsMembershipIncGST = (fiveYearsMembershipExclGST + fiveYearsGSTAmount).round();
        
        Decimal oneYearDiscountAmountAsPerSlab , twoYearsDiscountAmountAsPerSlab , threeYearsDiscountAmountAsPerSlab , fourYearsDiscountAmountAsPerSlab , fiveYearsDiscountAmountAsPerSlab;
        
        
        if(isBelowGoldSlab && isCXOGenieMembership)
        {
            membershipSlab = 'CXO Genie Member Discount';
        }

        if(membershipSlab == 'Gold')
        {
            //System.debug('Amount Calculation As Per ' + membershipSlab + ' Slab' );
            oneYearDiscountAmountAsPerSlab = ((oneYearMembershipExclGST * goldSlabDiscount) / 100 ).round();
            twoYearsDiscountAmountAsPerSlab = ((twoYearsMembershipExclGST * goldSlabDiscount) / 100 ).round();
            threeYearsDiscountAmountAsPerSlab = ((threeYearsMembershipExclGST * goldSlabDiscount) / 100 ).round();
            fourYearsDiscountAmountAsPerSlab = ((fourYearsMembershipExclGST * goldSlabDiscount) / 100 ).round();
            fiveYearsDiscountAmountAsPerSlab = ((fiveYearsMembershipExclGST * goldSlabDiscount) / 100 ).round();
            
            // System.debug('');
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 1 Year >>> ' + oneYearDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 2 Year >>> ' + twoYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 3 Year >>> ' + threeYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 4 Year >>> ' + fourYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 5 Year >>> ' + fiveYearsDiscountAmountAsPerSlab);
            // System.debug('');
        }
        else if(membershipSlab == 'Silver')
        {
            // System.debug('Amount Calculation As Per ' + membershipSlab + ' Slab' );
            oneYearDiscountAmountAsPerSlab = ((oneYearMembershipExclGST * silverSlabDiscount) / 100 ).round();
            twoYearsDiscountAmountAsPerSlab = ((twoYearsMembershipExclGST * silverSlabDiscount) / 100 ).round();
            threeYearsDiscountAmountAsPerSlab = ((threeYearsMembershipExclGST * silverSlabDiscount) / 100 ).round();
            fourYearsDiscountAmountAsPerSlab = ((fourYearsMembershipExclGST * silverSlabDiscount) / 100 ).round();
            fiveYearsDiscountAmountAsPerSlab = ((fiveYearsMembershipExclGST * silverSlabDiscount) / 100 ).round();
            
            // System.debug('');
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 1 Year >>> ' + oneYearDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 2 Year >>> ' + twoYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 3 Year >>> ' + threeYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 4 Year >>> ' + fourYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 5 Year >>> ' + fiveYearsDiscountAmountAsPerSlab);
            // System.debug('');
        }
        else if(membershipSlab == 'Bronze')
        {
            //System.debug('Amount Calculation As Per ' + membershipSlab + ' Slab' );
            oneYearDiscountAmountAsPerSlab = ((oneYearMembershipExclGST * bronzeSlabDiscount) / 100 ).round();
            twoYearsDiscountAmountAsPerSlab = ((twoYearsMembershipExclGST * bronzeSlabDiscount) / 100 ).round();
            threeYearsDiscountAmountAsPerSlab = ((threeYearsMembershipExclGST * bronzeSlabDiscount) / 100 ).round();
            fourYearsDiscountAmountAsPerSlab = ((fourYearsMembershipExclGST * bronzeSlabDiscount) / 100 ).round();
            fiveYearsDiscountAmountAsPerSlab = ((fiveYearsMembershipExclGST * bronzeSlabDiscount) / 100 ).round();
            
            // System.debug('');
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 1 Year >>> ' + oneYearDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 2 Year >>> ' + twoYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 3 Year >>> ' + threeYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 4 Year >>> ' + fourYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 5 Year >>> ' + fiveYearsDiscountAmountAsPerSlab);
            // System.debug('');
        }
        else if (membershipSlab == 'CXO Genie Member Discount')
        {
            //System.debug('Amount Calculation As Per ' + membershipSlab + ' Slab' );
            oneYearDiscountAmountAsPerSlab = ((oneYearMembershipExclGST * cxoGenieDiscount) / 100 ).round();
            twoYearsDiscountAmountAsPerSlab = ((twoYearsMembershipExclGST * cxoGenieDiscount) / 100 ).round();
            threeYearsDiscountAmountAsPerSlab = ((threeYearsMembershipExclGST * cxoGenieDiscount) / 100 ).round();
            fourYearsDiscountAmountAsPerSlab = ((fourYearsMembershipExclGST * cxoGenieDiscount) / 100 ).round();
            fiveYearsDiscountAmountAsPerSlab = ((fiveYearsMembershipExclGST * cxoGenieDiscount) / 100 ).round();
            
            // System.debug('');
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 1 Year >>> ' + oneYearDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 2 Year >>> ' + twoYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 3 Year >>> ' + threeYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 4 Year >>> ' + fourYearsDiscountAmountAsPerSlab);
            // System.debug('Discount Amount As Per ' + membershipSlab + ' Slab For 5 Year >>> ' + fiveYearsDiscountAmountAsPerSlab);
            // System.debug('');
        }
        else
        {
            oneYearDiscountAmountAsPerSlab = 0;
            twoYearsDiscountAmountAsPerSlab = 0;
            threeYearsDiscountAmountAsPerSlab = 0;
            fourYearsDiscountAmountAsPerSlab = 0;
            fiveYearsDiscountAmountAsPerSlab = 0;
        }
        String slabAndDiscount;
        if(membershipSlab == 'Gold')
        {
            slabAndDiscount = membershipSlab + ' (' + goldSlabDiscount + '%)';
        }
        else if(membershipSlab == 'Silver')
        {
            slabAndDiscount = membershipSlab + ' (' + silverSlabDiscount + '%)';
        }
        else if(membershipSlab == 'Bronze')
        {
            slabAndDiscount = membershipSlab + ' (' + bronzeSlabDiscount +'%)';
        }
        else if(membershipSlab == 'CXO Genie Member Discount')
        {
            slabAndDiscount = membershipSlab +' (' + cxoGenieDiscount + '%)';
        }

        Decimal oneYearAdditionalDiscount , twoYearsAdditionalDiscount , threeYearsAdditionalDiscount , fourYearsAdditionalDiscount , fiveYearsAdditionalDiscount;

        if(additionalDiscount != null)
        {
            oneYearAdditionalDiscount = ((((oneYearMembershipExclGST - oneYearDiscountAmountAsPerSlab) * additionalDiscount) / 100 )).round();
            twoYearsAdditionalDiscount = ((((twoYearsMembershipExclGST - twoYearsDiscountAmountAsPerSlab) * additionalDiscount) / 100)).round();
            threeYearsAdditionalDiscount = ((((threeYearsMembershipExclGST - threeYearsDiscountAmountAsPerSlab) * additionalDiscount) / 100)).round();
            fourYearsAdditionalDiscount = ((((fourYearsMembershipExclGST - fourYearsDiscountAmountAsPerSlab) * additionalDiscount) / 100)).round();
            fiveYearsAdditionalDiscount = ((((fiveYearsMembershipExclGST - fiveYearsDiscountAmountAsPerSlab) * additionalDiscount) / 100)).round();
        }
        else
        {
            oneYearAdditionalDiscount = 0;
            twoYearsAdditionalDiscount = 0;
            threeYearsAdditionalDiscount = 0;
            fourYearsAdditionalDiscount = 0;
            fiveYearsAdditionalDiscount = 0;   
        }
        
        // System.debug('');
        // System.debug('One Year Discount As Per ' + additionalDiscount + '% Discount >> ' + oneYearAdditionalDiscount);
        // System.debug('Two Years Discount As Per ' + additionalDiscount + '% Discount >> ' + twoYearsAdditionalDiscount);
        // System.debug('Three Years Discount As Per ' + additionalDiscount + '% Discount >> ' + threeYearsAdditionalDiscount);
        // System.debug('Four Years Discount As Per ' + additionalDiscount + '% Discount >> ' + fourYearsAdditionalDiscount);
        // System.debug('Five Years Discount As Per ' + additionalDiscount + '% Discount >> ' + fiveYearsAdditionalDiscount);
        // System.debug('');

        Decimal amountToPayForOneYearBeforePoints = (oneYearMembershipExclGST - oneYearDiscountAmountAsPerSlab - oneYearAdditionalDiscount).round();
        Decimal amountToPayForTwoYearBeforePoints = (twoYearsMembershipExclGST - twoYearsDiscountAmountAsPerSlab - twoYearsAdditionalDiscount).round();
        Decimal amountToPayForThreeYearBeforePoints = (threeYearsMembershipExclGST - threeYearsDiscountAmountAsPerSlab - threeYearsAdditionalDiscount).round();
        Decimal amountToPayForFourYearBeforePoints = (fourYearsMembershipExclGST - fourYearsDiscountAmountAsPerSlab - fourYearsAdditionalDiscount).round();
        Decimal amountToPayForFiveYearBeforePoints = (fiveYearsMembershipExclGST - fiveYearsDiscountAmountAsPerSlab - fiveYearsAdditionalDiscount).round();

        // System.debug('');
        // System.debug(oneYearMembershipExclGST + ' - ' + oneYearDiscountAmountAsPerSlab + ' - ' + oneYearAdditionalDiscount + ' = ' + amountToPayForOneYearBeforePoints);
        // System.debug(twoYearsMembershipExclGST + ' - ' + twoYearsDiscountAmountAsPerSlab + ' - ' + twoYearsAdditionalDiscount + ' = ' + amountToPayForTwoYearBeforePoints);
        // System.debug(threeYearsMembershipExclGST + ' - ' + threeYearsDiscountAmountAsPerSlab + ' - ' + threeYearsAdditionalDiscount + ' = ' + amountToPayForThreeYearBeforePoints);
        // System.debug(fourYearsMembershipExclGST + ' - ' + fourYearsDiscountAmountAsPerSlab + ' - ' + fourYearsAdditionalDiscount + ' = ' + amountToPayForFourYearBeforePoints);
        // System.debug(fiveYearsMembershipExclGST + ' - ' + fiveYearsDiscountAmountAsPerSlab + ' - ' + fiveYearsAdditionalDiscount + ' = ' + amountToPayForFiveYearBeforePoints);

        
        List<Decimal> membershipPrices = new List<Decimal>{
            amountToPayForOneYearBeforePoints, // 1st Year
            amountToPayForTwoYearBeforePoints, // 2nd Year
            amountToPayForThreeYearBeforePoints, // 3rd Year
            amountToPayForFourYearBeforePoints, // 4th Year
            amountToPayForFiveYearBeforePoints// 5th Year
        };

        // Call the method to get the results
        Map<Integer, Map<String, Decimal>> results = calculateFinalMembershipAmounts(totalPointsOfAccount, membershipPrices , pricePerPoint);

        // Variables to store each year's final membership amount and remaining points
        Decimal finalAmountYear1, finalAmountYear2, finalAmountYear3, finalAmountYear4, finalAmountYear5;
        Decimal remainingPointsYear1, remainingPointsYear2, remainingPointsYear3, remainingPointsYear4, remainingPointsYear5;
        
        // Extract and store the results for each year
        finalAmountYear1 = (results.get(1).get('finalMembershipAmount')).round();
        remainingPointsYear1 = results.get(1).get('remainingPoints').round();
        
        finalAmountYear2 = (results.get(2).get('finalMembershipAmount')).round();
        remainingPointsYear2 = results.get(2).get('remainingPoints').round();
        
        finalAmountYear3 = (results.get(3).get('finalMembershipAmount')).round();
        remainingPointsYear3 = results.get(3).get('remainingPoints').round();
        
        finalAmountYear4 = (results.get(4).get('finalMembershipAmount')).round();
        remainingPointsYear4 = results.get(4).get('remainingPoints').round();
        
        finalAmountYear5 = (results.get(5).get('finalMembershipAmount')).round();
        remainingPointsYear5 = results.get(5).get('remainingPoints').round();
        
        // Debugging each variable to verify the output
        // System.debug('Year 1 - Final Amount: ' + finalAmountYear1 + ', Remaining Points: ' + remainingPointsYear1);
        // System.debug('Year 2 - Final Amount: ' + finalAmountYear2 + ', Remaining Points: ' + remainingPointsYear2);
        // System.debug('Year 3 - Final Amount: ' + finalAmountYear3 + ', Remaining Points: ' + remainingPointsYear3);
        // System.debug('Year 4 - Final Amount: ' + finalAmountYear4 + ', Remaining Points: ' + remainingPointsYear4);
        // System.debug('Year 5 - Final Amount: ' + finalAmountYear5 + ', Remaining Points: ' + remainingPointsYear5);
        
        Decimal remainingPointBalanceYear1, remainingPointBalanceYear2, remainingPointBalanceYear3, remainingPointBalanceYear4, remainingPointBalanceYear5;
        
        // Calculate the remaining point balance for each year  
        remainingPointBalanceYear1 = (remainingPointsYear1 * pricePerPoint).round();
        remainingPointBalanceYear2 = (remainingPointsYear2 * pricePerPoint).round();
        remainingPointBalanceYear3 = (remainingPointsYear3 * pricePerPoint).round();
        remainingPointBalanceYear4 = (remainingPointsYear4 * pricePerPoint).round();
        remainingPointBalanceYear5 = (remainingPointsYear5 * pricePerPoint).round();
        
        // System.debug('');
        // System.debug('Year 1 - Remaining Point Balance: ' + remainingPointBalanceYear1);
        // System.debug('Year 2 - Remaining Point Balance: ' + remainingPointBalanceYear2);
        // System.debug('Year 3 - Remaining Point Balance: ' + remainingPointBalanceYear3);
        // System.debug('Year 4 - Remaining Point Balance: ' + remainingPointBalanceYear4);
        // System.debug('Year 5 - Remaining Point Balance: ' + remainingPointBalanceYear5);
        // System.debug('');
        
        Decimal finalAmountGSTYear1, finalAmountGSTYear2, finalAmountGSTYear3, finalAmountGSTYear4, finalAmountGSTYear5;
        
        // Calculate the GST for Final Amount each year
        finalAmountGSTYear1 = ((finalAmountYear1 * gstPrice) / 100).round();
        finalAmountGSTYear2 = ((finalAmountYear2 * gstPrice) / 100).round();
        finalAmountGSTYear3 = ((finalAmountYear3 * gstPrice) / 100).round();
        finalAmountGSTYear4 = ((finalAmountYear4 * gstPrice) / 100).round();
        finalAmountGSTYear5 = ((finalAmountYear5 * gstPrice) / 100).round();

        // System.debug('');
        // System.debug('GST for Final Amount Year 1: ' + finalAmountGSTYear1);
        // System.debug('GST for Final Amount Year 2: ' + finalAmountGSTYear2);
        // System.debug('GST for Final Amount Year 3: ' + finalAmountGSTYear3);
        // System.debug('GST for Final Amount Year 4: ' + finalAmountGSTYear4);
        // System.debug('GST for Final Amount Year 5: ' + finalAmountGSTYear5);
        // System.debug('');
        
        Decimal finalAmountIncGSTYear1, finalAmountIncGSTYear2, finalAmountIncGSTYear3, finalAmountIncGSTYear4, finalAmountIncGSTYear5;

        // Calculate the final amount including GST for each year
        finalAmountIncGSTYear1 = (finalAmountYear1 + finalAmountGSTYear1).round();
        finalAmountIncGSTYear2 = (finalAmountYear2 + finalAmountGSTYear2).round();
        finalAmountIncGSTYear3 = (finalAmountYear3 + finalAmountGSTYear3).round();
        finalAmountIncGSTYear4 = (finalAmountYear4 + finalAmountGSTYear4).round();
        finalAmountIncGSTYear5 = (finalAmountYear5 + finalAmountGSTYear5).round();

        // System.debug('');
        // System.debug('Final Amount with GST for Year 1: ' + finalAmountIncGSTYear1);
        // System.debug('Final Amount with GST for Year 2: ' + finalAmountIncGSTYear2);
        // System.debug('Final Amount with GST for Year 3: ' + finalAmountIncGSTYear3);
        // System.debug('Final Amount with GST for Year 4: ' + finalAmountIncGSTYear4);
        // System.debug('Final Amount with GST for Year 5: ' + finalAmountIncGSTYear5);
        // System.debug('');

        Decimal savingPercentageYear1 , savingPercentageYear2 , savingPercentageYear3 , savingPercentageYear4 , savingPercentageYear5;
        savingPercentageYear1 = (100 - ((finalAmountIncGSTYear1 / oneYearMembershipIncGST) * 100));  
        savingPercentageYear2 = (100 - ((finalAmountIncGSTYear2 / twoYearsMembershipIncGST) * 100));  
        savingPercentageYear3 = (100 - ((finalAmountIncGSTYear3 / threeYearsMembershipIncGST) * 100));  
        savingPercentageYear4 = (100 - ((finalAmountIncGSTYear4 / fourYearsMembershipIncGST) * 100));  
        savingPercentageYear5 = (100 - ((finalAmountIncGSTYear5 / fiveYearsMembershipIncGST) * 100));  

        savingPercentageYear1 = Math.max(savingPercentageYear1.setScale(2) , 0.00).round();
        savingPercentageYear2 = Math.max(savingPercentageYear2.setScale(2) , 0.00).round();
        savingPercentageYear3 = Math.max(savingPercentageYear3.setScale(2) , 0.00).round();
        savingPercentageYear4 = Math.max(savingPercentageYear4.setScale(2) , 0.00).round();
        savingPercentageYear5 = Math.max(savingPercentageYear5.setScale(2) , 0.00).round();

        // System.debug('');
        // System.debug('Saving Percentage for Year 1: ' + savingPercentageYear1);
        // System.debug('Saving Percentage for Year 2: ' + savingPercentageYear2);
        // System.debug('Saving Percentage for Year 3: ' + savingPercentageYear3);
        // System.debug('Saving Percentage for Year 4: ' + savingPercentageYear4);
        // System.debug('Saving Percentage for Year 5: ' + savingPercentageYear5);
        // System.debug('');

        String savingPercentage1 , savingPercentage2 , savingPercentage3 , savingPercentage4 , savingPercentage5;
        savingPercentage1 = savingPercentageYear1.round() + '%';
        savingPercentage2 = savingPercentageYear2.round() + '%';
        savingPercentage3 = savingPercentageYear3.round() + '%';
        savingPercentage4 = savingPercentageYear4.round() + '%';
        savingPercentage5 = savingPercentageYear5.round() + '%';


        Decimal netSavingAmountYear1 , netSavingAmountYear2 , netSavingAmountYear3 , netSavingAmountYear4 , netSavingAmountYear5;
        if(finalAmountIncGSTYear1 == 0)
        {
            netSavingAmountYear1 = oneYearMembershipIncGST;
        }
        else
        {
            netSavingAmountYear1 = oneYearMembershipIncGST - finalAmountIncGSTYear1;
        }

        if(finalAmountIncGSTYear2 == 0)
        {
            netSavingAmountYear2 = twoYearsMembershipIncGST;
        }
        else
        {
            netSavingAmountYear2 = twoYearsMembershipIncGST - finalAmountIncGSTYear2;
        }

        if(finalAmountIncGSTYear3 == 0)
        {
            netSavingAmountYear3 = threeYearsMembershipIncGST;
        }
        else
        {
            netSavingAmountYear3 = threeYearsMembershipIncGST - finalAmountIncGSTYear3;
        }

        if(finalAmountIncGSTYear4 == 0)
        {
            netSavingAmountYear4 = fourYearsMembershipIncGST;
        }
        else
        {
            netSavingAmountYear4 = fourYearsMembershipIncGST - finalAmountIncGSTYear4;
        }

        if(finalAmountIncGSTYear5 == 0)
        {
            netSavingAmountYear5 = fiveYearsMembershipIncGST;
        }
        else
        {
            netSavingAmountYear5 = fiveYearsMembershipIncGST - finalAmountIncGSTYear5;
        }
        
        Map<String, Object> renewalMembershipAmountData = new Map<String, Object>();
        
        // 1 Year.
        renewalMembershipAmountData.put('Year 1', new Map<String, Object>{
            'ipvStandardAmountExclGST' => oneYearMembershipExclGST.format(),
            'ipvStandardAmountIncGST' => oneYearMembershipIncGST.format(),
            'membershipSlab' => slabAndDiscount,
            'membershipSlabDiscountAmount' => oneYearDiscountAmountAsPerSlab.format(),
            'additionalDiscountPercent' => additionalDiscount,
            'additionalDiscountAmount' => oneYearAdditionalDiscount.format(),
            'pointsRedeemed' => (totalPointsOfAccount - remainingPointsYear1).format(),
            'pointsRedemptionAmount' => (totalAmountFromPoints - remainingPointBalanceYear1).format(),
            'totalDiscount' => (oneYearMembershipExclGST - finalAmountYear1).format(),
            'netFeesExclGST' => finalAmountYear1.format(),
            'gstAmount' => finalAmountGSTYear1.format(),
            'amountPayableInclGST' => finalAmountIncGSTYear1.format(),
            'netSavingsAmount' => netSavingAmountYear1.format(),
            'netSavingsPercent' => savingPercentage1,
            'gstPercent' => gstPrice
        });
        
        // 2 Year
        renewalMembershipAmountData.put('Year 2', new Map<String, Object>{
            'ipvStandardAmountExclGST' => twoYearsMembershipExclGST.format(),
            'ipvStandardAmountIncGST' => twoYearsMembershipIncGST.format(),
            'membershipSlab' => slabAndDiscount,
            'membershipSlabDiscountAmount' => twoYearsDiscountAmountAsPerSlab.format(),
            'additionalDiscountPercent' => additionalDiscount,
            'additionalDiscountAmount' => twoYearsAdditionalDiscount.format(),
            'pointsRedeemed' => (totalPointsOfAccount - remainingPointsYear2).format(),
            'pointsRedemptionAmount' => (totalAmountFromPoints - remainingPointBalanceYear2).format(),
            'totalDiscount' => (twoYearsMembershipExclGST - finalAmountYear2).format(),
            'netFeesExclGST' => finalAmountYear2.format(),
            'gstAmount' => finalAmountGSTYear2.format(),
            'amountPayableInclGST' => finalAmountIncGSTYear2.format(),
            'netSavingsAmount' => netSavingAmountYear2.format(),
            'netSavingsPercent' => savingPercentage2
        });

        // 3 Year
        renewalMembershipAmountData.put('Year 3', new Map<String, Object>{
            'ipvStandardAmountExclGST' => threeYearsMembershipExclGST.format(),
            'ipvStandardAmountIncGST' => threeYearsMembershipIncGST.format(),
            'membershipSlab' => slabAndDiscount,
            'membershipSlabDiscountAmount' => threeYearsDiscountAmountAsPerSlab.format(),
            'additionalDiscountPercent' => additionalDiscount,
            'additionalDiscountAmount' => threeYearsAdditionalDiscount.format(),
            'pointsRedeemed' => (totalPointsOfAccount - remainingPointsYear3).format(),
            'pointsRedemptionAmount' => (totalAmountFromPoints - remainingPointBalanceYear3).format(),
            'totalDiscount' => (threeYearsMembershipExclGST - finalAmountYear3).format(),
            'netFeesExclGST' => finalAmountYear3.format(),
            'gstAmount' => finalAmountGSTYear3.format(),
            'amountPayableInclGST' => finalAmountIncGSTYear3.format(),
            'netSavingsAmount' => netSavingAmountYear3.format(),
            'netSavingsPercent' => savingPercentage3
        });

        // 4 Year
        renewalMembershipAmountData.put('Year 4', new Map<String, Object>{
            'ipvStandardAmountExclGST' => fourYearsMembershipExclGST.format(),
            'ipvStandardAmountIncGST' => fourYearsMembershipIncGST.format(),
            'membershipSlab' => slabAndDiscount,
            'membershipSlabDiscountAmount' => fourYearsDiscountAmountAsPerSlab.format(),
            'additionalDiscountPercent' =>additionalDiscount,
            'additionalDiscountAmount' => fourYearsAdditionalDiscount.format(),
            'pointsRedeemed' => (totalPointsOfAccount - remainingPointsYear4).format(),
            'pointsRedemptionAmount' => (totalAmountFromPoints - remainingPointBalanceYear4).format(),
            'totalDiscount' => (fourYearsMembershipExclGST - finalAmountYear4).format(),
            'netFeesExclGST' => finalAmountYear4.format(),
            'gstAmount' => finalAmountGSTYear4.format(),
            'amountPayableInclGST' => finalAmountIncGSTYear4.format(),
            'netSavingsAmount' => netSavingAmountYear4.format(),
            'netSavingsPercent' => savingPercentage4
        });

        // 5 Year
        renewalMembershipAmountData.put('Year 5', new Map<String, Object>{
            'ipvStandardAmountExclGST' => fiveYearsMembershipExclGST.format(),
            'ipvStandardAmountIncGST' => fiveYearsMembershipIncGST.format(),
            'membershipSlab' => slabAndDiscount,
            'membershipSlabDiscountAmount' => fiveYearsDiscountAmountAsPerSlab.format(),
            'additionalDiscountPercent' => additionalDiscount,
            'additionalDiscountAmount' => fiveYearsAdditionalDiscount.format(),
            'pointsRedeemed' => (totalPointsOfAccount - remainingPointsYear5).format(),
            'pointsRedemptionAmount' => (totalAmountFromPoints - remainingPointBalanceYear5).format(),
            'totalDiscount' => (fiveYearsMembershipExclGST - finalAmountYear5).format(),
            'netFeesExclGST' => finalAmountYear5.format(),
            'gstAmount' => finalAmountGSTYear5.format(),
            'amountPayableInclGST' => finalAmountIncGSTYear5.format(),
            'netSavingsAmount' => netSavingAmountYear5.format(),
            'netSavingsPercent' => savingPercentage5
        });

        //System.debug('Return Statement : ' + renewalMembershipAmountData);
    
        return renewalMembershipAmountData;
    }


    public static Map<Integer, Map<String, Decimal>> calculateFinalMembershipAmounts(Decimal points, List<Decimal> membershipPrices , Decimal pricePerPoint) {
        
        // Initialize a map to store the results for each year
        Map<Integer, Map<String, Decimal>> results = new Map<Integer, Map<String, Decimal>>();
        
        // Convert points to the corresponding monetary value
        Decimal pointsValue = points * pricePerPoint; 
        
        // Loop through each year’s membership price
        for (Integer i = 0; i < membershipPrices.size(); i++) {
            Decimal membershipPrice = membershipPrices[i];
            Decimal finalMembershipAmount;
            Decimal remainingPoints;
            
            // Check if the points can cover the entire membership price
            if (pointsValue >= membershipPrice) {
                finalMembershipAmount = 0.00;
                remainingPoints = (pointsValue - membershipPrice) / 10; // Remaining points after reducing the membership price
            } else {
                finalMembershipAmount = membershipPrice - pointsValue;
                remainingPoints = 0.00;
            }
            
            // Store the result for this year
            Map<String, Decimal> yearResult = new Map<String, Decimal>();
            yearResult.put('finalMembershipAmount', finalMembershipAmount);
            yearResult.put('remainingPoints', remainingPoints);
            
            results.put(i + 1, yearResult); // Store in the map with the year as the key (1-based index)
        }
        
        return results;
    }
}