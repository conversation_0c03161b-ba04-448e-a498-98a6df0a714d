public with sharing class MatchingCapitalDevelopmentController {
	
    @AuraEnabled(cacheable=true)
    public static List<Capital_Development__c> getMatchingCapitalDevelopments(Id startupId) {
	
        Startup__c startup = [SELECT Id , Industry_Sector__c, Fundraise_Amount__c FROM Startup__c WHERE Id = :startupId LIMIT 1];
        
        List<String> startupIndustries = new List<String>();
        Decimal fundRaiseAmount;
                
        if (!String.isBlank(startup.Industry_Sector__c)) {
            startupIndustries = startup.Industry_Sector__c.split(';');
            System.debug('Startup Industries >>>>> '+ startupIndustries);
        }
        else {
            System.debug('The Size of List >>>> '+ startupIndustries.size());
        }
        
        fundRaiseAmount = startup.Fundraise_Amount__c;
        System.debug('FundRaise Amount >>>>> '+ fundRaiseAmount);
            
        String dynamicQuery = null;
		
        // Fundraise Amount & Industry Both Exist On Startup Object
        if (fundRaiseAmount != null && !startupIndustries.isEmpty()) {
            dynamicQuery = 'SELECT Id, Name, Minimum_Transaction_Size__c, Maximum_Transaction_Size__c, Industry_Sector__c FROM Capital_Development__c ';
            dynamicQuery += ' WHERE (Minimum_Transaction_Size__c != null AND Maximum_Transaction_Size__c != null AND Industry_Sector__c != null AND Minimum_Transaction_Size__c <= :fundRaiseAmount AND Maximum_Transaction_Size__c >= :fundRaiseAmount AND Industry_Sector__c INCLUDES ( ' ;
            for (String industry : startupIndustries) {
                dynamicQuery += '\'' + String.escapeSingleQuotes(industry) + '\',';
            }
            dynamicQuery = dynamicQuery.removeEnd(',') + ')';
            
            dynamicQuery += ' ) OR (Minimum_Transaction_Size__c = null AND Maximum_Transaction_Size__c != null AND Industry_Sector__c != null AND Maximum_Transaction_Size__c >= :fundRaiseAmount AND Industry_Sector__c INCLUDES ( ';
            for (String industry : startupIndustries) {
                dynamicQuery += '\'' + String.escapeSingleQuotes(industry) + '\',';
            }
            dynamicQuery = dynamicQuery.removeEnd(',') + ')';
            
            dynamicQuery += ' )	OR ( Minimum_Transaction_Size__c != null AND Maximum_Transaction_Size__c = null AND Industry_Sector__c != null AND Minimum_Transaction_Size__c <= :fundRaiseAmount AND Industry_Sector__c INCLUDES ( ';
            for (String industry : startupIndustries) {
                dynamicQuery += '\'' + String.escapeSingleQuotes(industry) + '\',';
            }
            dynamicQuery = dynamicQuery.removeEnd(',') + ')';
            
            dynamicQuery += ' ) OR ( Minimum_Transaction_Size__c != null AND Maximum_Transaction_Size__c != null  AND Industry_Sector__c = null AND Minimum_Transaction_Size__c <= :fundRaiseAmount AND Maximum_Transaction_Size__c >= :fundRaiseAmount ) OR ( Minimum_Transaction_Size__c != null AND Maximum_Transaction_Size__c = null AND Industry_Sector__c = null AND Minimum_Transaction_Size__c <= :fundRaiseAmount ) OR ( Minimum_Transaction_Size__c = null AND Maximum_Transaction_Size__c != null AND Industry_Sector__c = null AND Maximum_Transaction_Size__c >= :fundRaiseAmount) OR (Minimum_Transaction_Size__c = null AND Maximum_Transaction_Size__c = null AND Industry_Sector__c != null AND Industry_Sector__c INCLUDES ( ';
            
            for (String industry : startupIndustries) {
                dynamicQuery += '\'' + String.escapeSingleQuotes(industry) + '\',';
            }
            dynamicQuery = dynamicQuery.removeEnd(',') + ')';
            
            dynamicQuery += ' ) ';
        } 
        // Only Industry Exist On Startup Object
        else if (fundRaiseAmount == null && !startupIndustries.isEmpty()) {
            dynamicQuery = 'SELECT Id, Name, Minimum_Transaction_Size__c, Maximum_Transaction_Size__c, Industry_Sector__c FROM Capital_Development__c ';
            dynamicQuery += ' WHERE Industry_Sector__c != null AND Industry_Sector__c INCLUDES ( ';
            for (String industry : startupIndustries) {
                dynamicQuery += '\'' + String.escapeSingleQuotes(industry) + '\',';
            }                
            dynamicQuery = dynamicQuery.removeEnd(',') + ')';
            
        } 
        // Only Fundraise Amount Exist On Startup Object
        else if (fundRaiseAmount != null && startupIndustries.isEmpty()) {
            dynamicQuery = 'SELECT Id, Name, Minimum_Transaction_Size__c, Maximum_Transaction_Size__c, Industry_Sector__c FROM Capital_Development__c ';
            dynamicQuery += 'Where ( Minimum_Transaction_Size__c != null AND Maximum_Transaction_Size__c != null AND Minimum_Transaction_Size__c <= :fundRaiseAmount AND Maximum_Transaction_Size__c >= :fundRaiseAmount ) OR (Minimum_Transaction_Size__c = null AND Maximum_Transaction_Size__c != null AND Maximum_Transaction_Size__c >= :fundRaiseAmount ) OR ( Minimum_Transaction_Size__c != null AND Maximum_Transaction_Size__c = null AND Minimum_Transaction_Size__c <= :fundRaiseAmount ) ';
        }
        
        List<Capital_Development__c> capitalDevelopment = new List<Capital_Development__c>();
        
        if(dynamicQuery != null)
        {
			capitalDevelopment = Database.query(dynamicQuery);
        }

        System.debug('Capital Development >>>>> '+ capitalDevelopment);
        System.debug('The Size of Capital Development is >>>>> '+ capitalDevelopment.size());
        
        return capitalDevelopment;       
    }
}