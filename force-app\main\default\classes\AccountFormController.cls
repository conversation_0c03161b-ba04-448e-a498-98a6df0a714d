public with sharing class AccountFormController {
    public Account account { get; set; }

    // Constructor
    public AccountFormController(ApexPages.StandardController controller) {
        this.account = (Account)controller.getRecord();
    }

    // Method to save account information
    public PageReference saveAccount() {
        try {
            upsert account; // Save or update the account record
            ApexPages.addMessage(new ApexPages.Message(ApexPages.Severity.CONFIRM, 'Information saved successfully.'));
        } catch (Exception e) {
            ApexPages.addMessage(new ApexPages.Message(ApexPages.Severity.ERROR, 'Error saving information: ' + e.getMessage()));
        }
        return null; // Stay on the same page after submission
    }
}