trigger LetsGrowTrigger on LetsGrow__c (Before Insert,Before Update,After Insert,After Update,after delete) {
    
    LetsgrowTriggerHandler lghandler = new LetsgrowTriggerHandler();
    
    if(Trigger.IsBefore && Trigger.IsInsert){ 
       // LetsgrowTriggerHandler lghandler = new LetsgrowTriggerHandler();
        lghandler.beforeinsert(Trigger.new);
    	}
     if(Trigger.Isafter && Trigger.IsInsert){ 
       // LetsgrowTriggerHandler lghandler = new LetsgrowTriggerHandler(); afterDelete
        lghandler.afterinsert(Trigger.new);
        lghandler.handleLetsGrowActivities(Trigger.new,new Map<Id, LetsGrow__c>());
    	}
    if(Trigger.isafter && trigger.Isupdate){ 
         lghandler.afterupdate(trigger.New,trigger.OldMap);
         lghandler.handleLetsGrowActivities(Trigger.new,TRigger.oldmap);
    }
    
    if(Trigger.isafter && trigger.Isdelete){ 
         lghandler.afterDelete(trigger.Old);
    }
        
}