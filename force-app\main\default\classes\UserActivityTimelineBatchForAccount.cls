global class UserActivityTimelineBatchForAccount implements Database.Batchable<SObject>, Database.AllowsCallouts {

    global Database.QueryLocator start(Database.BatchableContext bc) {
       
        String query = 'SELECT Id, App_signup_date__c FROM Account Where App_signup_date__c != null ';
         System.debug('query: '+ query);
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext bc, List<SObject> scope) {
        list<Id> recordIds = new list<Id>();
        String endDate = system.now().format('dd-MM-yyyy'); 
        String startDate = system.now().addDays(-1).format('dd-MM-yyyy');

        for (SObject obj : scope) {
            Account acc = (Account)obj;
            recordIds.add(acc.Id);
            }
        System.debug('Account ID: ' + recordIds);
       
        if (!recordIds.isEmpty()) {
            UserActivityRestService.callExternalApi(recordIds, startDate, endDate);
             System.debug('Account records found  App_signup_date__c');
        } else {
            System.debug('No Account records found with non-null App_signup_date__c');
        }
    }

    global void finish(Database.BatchableContext bc) {
     
        System.debug('Finished processing Accounts, now starting Lead batch.');
    }
}