<apex:page standardController="Lead__c" recordSetVar="accs" extensions="CreateActivityCtrl" lightningStylesheets="true">
    <apex:form >

        <apex:pageBlock >
            <apex:pageMessages id="showmsg"></apex:pageMessages>
            <div style="text-align: center">
                <apex:commandButton value="Save" action="{!saveTask}" />
                <apex:commandButton value="Cancel" action="{!cancelBtn}" immediate="true" html-formnovalidate="formnovalidate"/>
            </div>
            
            <apex:pageBlockSection title="Create Activity" columns="1" collapsible="false">
                <apex:inputField value="{!taskObj.Subject__c}" required="true"/>
                <apex:inputField value="{!taskObj.ActivityDate }"  />
                <apex:inputField value="{!taskObj.Description }" required="true" />
            </apex:pageBlockSection>
            
            <div style="text-align: center">
                <apex:commandButton value="Save" action="{!saveTask}" />
                <apex:commandButton value="Cancel" action="{!cancelBtn}" immediate="true" html-formnovalidate="formnovalidate"/>
            </div>
            
        </apex:pageBlock>
    </apex:form>

</apex:page>