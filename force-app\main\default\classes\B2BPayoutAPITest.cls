@isTest(SeeAllData=false)
public class B2BPayoutAPITest {
	
    @testSetup
    public static void setup() {
        
        B2BcustomerAPITest.setup();
		
        Id ipvRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        List<Account> acc = [SELECT Id from Account where Name = 'B2B Customer' LIMIT 1];
        List<Startup_Round__c> round = [SELECT Id from Startup_Round__c where Name = 'test startup round' LIMIT 1];
        List<Investment__c> inv = [SELECT Id from Investment__c where Investor_s_PAN__c = '**********' LIMIT 1];
        List<Account> accList = [SELECT Id from Account where Name = 'Parent Account' LIMIT 1];
        
        Payout_Transaction__c transacion = new Payout_Transaction__c(
            Transaction_Date__c = Date.today(),
            B2B_Member_Name__c = acc[0].Id,
            Startup_Round_Name__c = round[0].Id,
            Payout_Type__c = 'Transaction Fee',
            Amount_for_payout_calculation__c = 60000,
            Syndicate_Payout_Share__c = 7,
            Payout_Status__c = 'Paid',
            Payout_Date__c = Date.today(),
            IPVs_Share__c = 10,
            Investment__c = inv[0].Id,
            B2B_Tenant__c = accList[0].Id
        );
        insert transacion;
        
       
        Account specificAccount = new Account(
            RecordTypeId = ipvRecordTypeId,
            Name = 'IPV Tenant',
            Personal_Email__c = '<EMAIL>',
            Primary_Country_Code__c = 91,
            Primary_Contact__c = '**********',
            Preferred_Email__c = 'Personal',
            Lead_Source__c = 'ORAI',
            Relationship_Manager__c = UserInfo.getUserId(),
            Title__c = 'Mr',
            Date_of_Addition__c = Date.Today(),
            Primary_Group_Name__c = 'IPV 1'
        );
        insert specificAccount;

    }
    
     @IsTest
    static void testSendPayoutTransactions_Success() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestBody = Blob.valueOf('{"pageSize":10, "pageNo":1, "orglist":["' + [SELECT Id FROM Account LIMIT 1][0].Id + '"], "startDate":"2023-07-01", "endDate":"2023-07-31"}');
        req.httpMethod = 'POST';
        RestContext.request = req;
        RestContext.response = res;

        B2BPayoutAPI.ReqWrapper response = B2BPayoutAPI.sendPayoutTransactions();
    }
    

    @IsTest
    static void testSendPayoutTransactions_InvalidDate() {
        // Set up the request with invalid date
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestBody = Blob.valueOf('{"pageSize":10, "pageNo":1, "orglist":["' + [SELECT Id FROM Account LIMIT 1][0].Id + '"], "startDate":"invalid-date", "endDate":"2023-07-31"}');
        req.httpMethod = 'POST';
        RestContext.request = req;
        RestContext.response = res;

        B2BPayoutAPI.ReqWrapper response = B2BPayoutAPI.sendPayoutTransactions();
    }

    @IsTest
    static void testSendPayoutTransactions_InternalServerError() {
        // Set up the request with valid parameters
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestBody = Blob.valueOf('{"pageSize":10, "pageNo":1, "orglist":["invalid-id"], "startDate":"2023-07-01", "endDate":"2023-07-31"}');
        req.httpMethod = 'POST';
        RestContext.request = req;
        RestContext.response = res;

        B2BPayoutAPI.ReqWrapper response = B2BPayoutAPI.sendPayoutTransactions();
    }


    
    @isTest
    static void queryTransactionsTest(){
        
        Set<Id> accIdSet = new Set<Id>();
        accIdSet.add([SELECT Id FROM Account WHERE Name = 'Parent Account' LIMIT 1].Id);
        Test.startTest();
        List<B2BPayoutAPI.PayoutTransactionWrapper> results = B2BPayoutAPI.queryTransactions(accIdSet, 10, 1, Date.today().addDays(-7), Date.today().addDays(1));
        Test.stopTest();   
    }  
}