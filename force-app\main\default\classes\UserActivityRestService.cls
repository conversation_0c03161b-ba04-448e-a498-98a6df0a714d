global class UserActivityRestService {
    
    public static void callExternalApi(list<Id> recordIds, String startDate, String endDate) {
        String endURLSetting;
        
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('API_Setting__c>>>'+settingList);
        if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].End_URL__c)) {
            endURLSetting = '' + settingList[0].End_URL__c;
        }
        system.debug('--endURLSetting--' + endURLSetting);
        
        String enddURL = endURLSetting + '/report/getActivitySummaryV1';
        system.debug('endURL --->' + enddURL);
        
       // String accessToken = RestLoginController.loginExternalSystem();
        String    accessToken = restLoginController.loginExternalSystem();
        Http http = new Http();
        HttpRequest request = new HttpRequest();
        request.setEndpoint(enddURL);
        request.setMethod('POST');
        request.setHeader('Content-Type', 'application/json');
        request.setHeader('Authorization', 'Bearer ' + accessToken);
        
        Map<String, Object> requestBody = new Map<String, Object>();
        requestBody.put('sfids', recordIds);
        requestBody.put('startDate', String.valueOf(Date.Today().addDays(-1).format()));
        requestBody.put('endDate', String.valueOf(Date.Today().format()));
        
        request.setBody(JSON.serialize(requestBody));
        System.debug('Request Body sent to external API is given below : ');
        System.debug(JSON.serialize(requestBody));
        System.debug(' Request Body : ' + request);
        
        Id recordId;
        String sourceObjectName = '';
        Map<String, String> activityDetailMap = new Map<String, String>{
            'Startup' => 'Startup Tab',
                'Agenda' => 'Agenda Tab',
                'Sign IN' => 'Sign in',
                'Referral' => 'Refer a Member',
                'Discussion' => 'Discussion Add',
                'Comment' => 'Discussion Add Comment',
                'Commitment Request' => 'Commitment Form Link',
                'Contact your RM' => 'Contact Your RM',
                'FAQs' => 'FAQs',
                'Feedback' => 'Feedback Form Link',
                'Leaderboard' => 'Leaderboard',
                'Marking Startup as Favourite' => 'Favourite Startup',
                'Portfolio' => 'Portfolio Tab',
                'Refer a Startup' => 'Refer a Startup',
                'Query Raised' => 'Raise a Query',
                'Startup Call' => 'Startup Call Recording',
                'App User Duration' => 'App User Duration',
                'Portfolio Excel Export' => 'Portfolio Excel Export',
                'Startup Share' => 'Startup - Share',
                'Startup Download' => 'Startup - download'
                };
                    try {
                         List<User_Activity__c> accountActivities = new List<User_Activity__c>();
                         List<User_Activity__c> leadActivities = new List<User_Activity__c>();
                        HttpResponse res = http.send(request);
                        String responseBody = res.getBody();
                        System.debug('Response received from external API: Status Code: ' + res.getStatusCode() + ', Body: ' + responseBody);
                        
                        if (res.getStatusCode() == 200) {
                            Map<String, Object> apiResponse = (Map<String, Object>) JSON.deserializeUntyped(responseBody);
                            
                            if (apiResponse.containsKey('data')) {
                                Map<String, Object> dataMap = (Map<String, Object>) apiResponse.get('data');
                                List<Object> summaries = (List<Object>) dataMap.get('summaries');
                               
                                
                                Map<String, Integer> activityCounts = new Map<String, Integer>();
                                
                                for (Object activityObj : summaries) {
                                    Map<String, Object> activityData = (Map<String, Object>) activityObj;
                                    String salesforceUserId = (String) activityData.get('salesforce_user_account_id');
                                    
                                    if (salesforceUserId != null) {
                                        for (String key : activityData.keySet()) {
                                            if (key == 'salesforce_user_account_id') {
                                                continue;
                                            }
                                            
                                            Map<String, Object> activityDetails = (Map<String, Object>) activityData.get(key);
                                            Integer count = (Integer) activityDetails.get('count');
                                            Integer tabTaps = (Integer) activityDetails.get('tab_taps');
                                            Integer signIns = (Integer) activityDetails.get('sign_ins');
                                            Integer totalLogoutDuration = (Integer) activityDetails.get('total_logout_duration');
                                            if (count != null) {
                                                String activityName = activityDetailMap.get(key) != null ? activityDetailMap.get(key) : key;
                                                activityCounts.put(activityName, activityCounts.get(activityName) != null ? activityCounts.get(activityName) + count : count);
                                                if (key == 'Sign IN') {
                                                    if (signIns != null) {
                                                        activityCounts.put('Sign in', signIns);
                                                    }
                                                    if (totalLogoutDuration != null) {
                                                        activityCounts.put('App User Duration',totalLogoutDuration);
                                                    }
                                                } if (key == 'Portfolio') {
                                                    if (tabTaps != null && count != null) {
                                                        Integer value = count - tabTaps;
                                                        if (value > 0) {
                                                            activityCounts.put('Portfolio Excel Export', value);
                                                        }
                                                    }  if (tabTaps != null) {
                                                        activityCounts.put('Portfolio Tab', tabTaps);
                                                    }
                                                }if (key == 'Startup') {
                                                    if (tabTaps != null && count != null) {
                                                        Integer value = count - tabTaps;
                                                        if (value > 0) {
                                                            activityCounts.put('Startup Detail', value);
                                                        }
                                                    }  if (tabTaps != null) {
                                                        activityCounts.put('Startup Tab', tabTaps);
                                                    }
                                                } if (key == 'Agenda') {
                                                    if (tabTaps != null && count != null) {
                                                        Integer value = count - tabTaps;
                                                        if (value > 0) {
                                                            activityCounts.put('Agenda Event Details', value);
                                                        }
                                                    } if (tabTaps != null) {
                                                        activityCounts.put('Agenda Tab', tabTaps);
                                                    }
                                                }if (key == 'Referral' && count != null) {
                                                    activityCounts.put('Refer a Member',  count);
                                                }if (key == 'Discussion' && count != null) {
                                                    activityCounts.put('Discussion Add',  count);
                                                }if (key == 'Comment' && count != null) {
                                                    activityCounts.put('Discussion Add Comment',  count);
                                                }if (key == 'Commitment Request' && count != null) {
                                                    activityCounts.put('Commitment Form Link',  count);
                                                    if (key == 'Contact your RM' && count != null) {
                                                        if (!activityCounts.containsKey('Contact your RM')) {
                                                            activityCounts.put('Contact your RM', count);
                                                        }
                                                    }
                                                    
                                                }if (key == 'FAQs' && count != null) {
                                                    activityCounts.put('FAQs',  count);
                                                }if (key == 'Feedback' && count != null) {
                                                    activityCounts.put('Feedback Form Link',  count);
                                                }if (key == 'Leaderboard' && count != null) {
                                                    activityCounts.put('Leaderboard',  count);
                                                }if (key == 'Marking Startup as Favourite' && count != null) {
                                                    activityCounts.put('Favourite Startup',  count);
                                                }if (key == 'Refer a Startup' && count != null) {
                                                    activityCounts.put('Refer a Startup',  count);
                                                }if (key == 'Query Raised' && count != null) {
                                                    activityCounts.put('Raise a Query',  count);
                                                }if (key == 'Startup Call' && count != null) {
                                                    activityCounts.put('Startup Call Recording',  count);
                                                }if (key == 'Startup Download' && count != null) {
                                                    activityCounts.put('Startup - download',  count);
                                                }
                                                if (key == 'Startup Share' && count != null) {
                                                    activityCounts.put('Startup - Share',  count);
                                                }
                                            }
                                        }
                                        
                                        for (String activityType : activityCounts.keySet()) {
                                            Integer value = activityCounts.get(activityType);
                                            if (value != null && value > 0) {
                                                User_Activity__c activity = new User_Activity__c();
                                                activity.Activity_Detail_RICH__c = activityType + ' >>> ' + value;
                                                activity.Time_Stamp__c = Date.Today().addDays(-1);
                                                activity.Activity_Type__c = activityType;
                                                try {
                                                    Id idOfRecord = salesforceUserId;
                                                    String objectName = idOfRecord.getSObjectType().getDescribe().getName();
                                                    
                                                    if (objectName == 'Account') { 
                                                        activity.Related_Account__c = salesforceUserId;
                                                        accountActivities.add(activity);
                                                    } else if (objectName == 'Lead__c') 
                                                    {
                                                        activity.Related_Lead__c = salesforceUserId;
                                                        leadActivities.add(activity);
                                                    } else {
                                                        System.debug('Unhandled object type: ' + objectName);
                                                    }
                                                } catch (Exception e) {
                                                    System.debug('Error finding record for ID: ' + salesforceUserId + ' - ' + e.getMessage());
                                                }
                                            }
                                        }
                                        activityCounts.clear();
                                    } else {
                                        System.debug('Invalid or missing data for salesforce_user_account_id: ' + salesforceUserId);
                                    }
                                }
                                if (!accountActivities.isEmpty()) {
                                    insert accountActivities;
                                    System.debug('Account Activities created: ' +accountActivities);
                                }
                                if (!leadActivities.isEmpty()) {
                                    insert leadActivities;
                                    System.debug('Account Activities created: ' +leadActivities);
                                }
                                System.debug('Account Activities created: ' + accountActivities.size());
                                System.debug('Lead Activities created: ' + leadActivities.size());
                            }
                        }
                    } catch (Exception e) {
                        System.debug('Error processing response: ' + e.getMessage());
                    }
    }
}