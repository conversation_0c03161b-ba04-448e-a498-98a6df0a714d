/************************************************************************
    Test Class for ConvertLeadCtrl.
************************************************************************/

@isTest(SeeAllData=false)
private class ConvertLeadCtrlTestClass{////////
    
    @testSetup static void setup() {
        account acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Platinum';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        insert acc;  
        id IPVrecordTypeId = Schema.SObjectType.lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead__c leadObj = new Lead__c();
        leadObj = TestFactory.createLead(); 
        leadObj.RecordTypeId = IPVrecordTypeId;
        leadObj.Relationship_Owner__c = UserInfo.getUserId();
        leadObj.Relationship_Manager__c = UserInfo.getUserId();
        leadObj.Membership_Status__c = 'On Trial';
        leadObj.utm_campaign__c = 'test Camp';
        leadObj.utm_medium__c = 'Test medium';
        leadObj.Type_of_Lead__c = 'Hot Lead';
        leadObj.Lead_Source__c = 'SBI Landing Page';
        leadObj.couponCode__c = 'ABCD1234';
        leadObj.Personal_Email__c = '<EMAIL>';
        leadObj.Are_you_a_first_time_investor__c ='';
        leadObj.Company__c='test';
        leadObj.Gain_from_Joining__c = 'I want to Generate Wealth through curated startup investments';
        leadObj.Date_of_Assignment_to_FiveS_RM__c = date.today();
        leadobj.Date_of_Assignment_to_IPV_RM__c = date.today();
        leadObj.utm_source__c ='Test';        
        leadObj.RecordTypeId = IPVrecordTypeId;
        leadObj.Date_of_Addition__c = date.today();
        leadObj.Primary_Group_Name__c = 'IPV 1';
        leadObj.City__c = 'agra';
        leadobj.App_Age__c=23;
        leadobj.App_Address__c ='testaddress';
        leadobj.App_Designation__c ='CTO';
        leadobj.App_City__c ='Mumbai';
        leadobj.Company__c ='Codiot';
        leadobj.App_Email__c ='<EMAIL>';
        leadobj.App_Postal_Code__c = 1234;
        leadobj.App_PAN_Number__c ='**********';
        leadobj.Preferred_Sectors_to_Invest__c ='D2C';
        leadObj.Date_of_receiving_lead__c = date.today();
        leadObj.Referred_By__c = acc.Id;
        leadObj.Are_you_a_first_time_investor__c = 'Yes';
        leadObj.Referral_Taken_By__c = acc.Id;
        leadObj.App_Status__c = 'Yes';
        leadObj.App_Country__c = 'India';
        leadObj.App_Company__c = 'CODIOT';
        leadObj.App_Household_Income__c = 100;
        leadObj.Bot_Journey_Stage__c = 'First Message Initiated';
        leadObj.Bot_Input__c = 'xyz';
        leadObj.Preferred_Sub_Sectors_to_Invest__c = 'abxc';
        leadObj.App_Full_Name__c = 'ABC';
        leadObj.App_Industry__c = 'XYZ';
        leadObj.App_Expertise__c = 'ABCXYZ';
        leadObj.App_LinkedIn_ID__c = 'www.login.salesforce.com';
        leadObj.LinkedIn_Question_1__c = 'Question 1';
        leadObj.LinkedIn_Question_2__c = 'Question 2';
        leadObj.LinkedIn_Question_3__c = 'Question 3';
        leadObj.LinkedIn_Answer_1__c = 'Answer 1';
        leadObj.LinkedIn_Answer_2__c = 'Answer 2';
        leadObj.LinkedIn_Answer_3__c = 'Answer 3';
        leadObj.Lead_From_External_Platform__c = true;
         
        insert leadObj;
        
        id CXOrecordTypeId = Schema.SObjectType.lead__c.getRecordTypeInfosByName().get('CXO Genie').getRecordTypeId();
        lead__c leadObj1 = new Lead__c();
        leadObj1.RecordTypeId = CXOrecordTypeId;
        leadObj1.Lead_Source__c = 'IMA';
        leadObj1.Name = 'Test CXO Lead1';
        leadObj1.Title__c = 'Mr';
        leadObj1.Preferred_Email__c = 'Personal';
        leadObj1.Personal_Email__c = '<EMAIL>';
        leadObj1.Referral_Source__c = 'Internal';
        leadObj1.Primary_Contact__c = '**********';
        leadObj1.Primary_Country_Code__c = 91;
        leadObj1.Relationship_Owner__c = UserInfo.getUserId();
        leadObj1.Relationship_Manager__c = UserInfo.getUserId(); 
        insert leadObj1;
        
        Startup__c st = TestFactory.createStartUp();
        insert st; 
        
        Startup_Round__c strObj = TestFactory.createStartUpRound( acc.Id,acc.Id, st.Id);
        insert strObj;
        system.debug('Roundss>>' +strObj);
        
        Events__c evObj = TestFactory.createEvents(''+strobj.Name);
        evObj.Startup_Round__c =strobj.id;
        insert evObj;
        system.debug('eventss>>' +evObj);
        
        Attendance__c att =new attendance__C();
        att.Events__c = evObj.id;
        att.Lead__c = leadObj.id;   
        att.Primary_Contact__c = leadObj.Primary_Contact__c;
        att.Primary_Country_Code__c = leadObj.Primary_Country_Code__c;
        att.Personal_Email__c = leadObj.Personal_Email__c;
        att.Lead_Source__c = leadObj.Lead_Source__c;
        Att.Name = 'Test';
        
        Insert att;
        
        User_Activity__c usr = new User_Activity__c();
        usr.Activity_Type__c = 'Agenda Tab';
        usr.Activity_Detail_RICH__c = 'Agenda Tab >>>>> 10';
        usr.Time_Stamp__c= System.now();
        usr.Related_Lead__c =leadObj.id;
        insert usr;
        
        id sqlDetailIPVrecordTypeId = Schema.SObjectType.SQL_Details__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        SQL_Details__c sqlObj = New SQL_Details__c();
        sqlObj.RecordTypeId = sqlDetailIPVrecordTypeId;
        sqlObj.Designation__c = 'Test Designation';
        sqlObj.Years_of_Experience__c = '1-2 years';
        sqlObj.Prior_Experience_in_Angel_Investing__c = 'Yes';
        sqlObj.Currently_Invested_in__c = 'Equity';
        sqlObj.Company__c = 'Test Company';
        sqlObj.Lead_Name__c = leadObj.Id;
        sqlObj.Customer_s_Expectation_from_IPV__c = 'Testing sql detail';
        sqlObj.Preferred_Date__c = Date.Today();
        sqlObj.Preferred_Time__c = System.now().Time();
        sqlObj.Preferred_Call_Type__c = 'Zoom';
        sqlObj.L1_BDA__c = TestFactory.createAccount().Id;
        sqlObj.L2_BDA__c = TestFactory.createAccount().Id;
        insert sqlObj;
        
        Transaction__c transObj = New Transaction__c();
        transObj.Transaction_Type__c = 'New Membership Payment';
        transObj.Leads_Name__c = leadObj.Id;
        transObj.Transaction_Date__c = System.now();
        Insert transObj;
        
        Task tt = new task();
        tt.Subject = 'Call';
        tt.WhatId = acc.id;
        tt.OwnerId = userInfo.getUserId();
        insert tt;
        
    }
    
    static testMethod void testAccountToLead()
    {
        test.startTest();
        Account parAcc = [select id,Primary_Contact__c,Personal_Email__c from account limit 1];
        PageReference pageRef = Page.ConvertLeadVfPage;
        test.setCurrentPageReference(pageRef);
        pageRef.getParameters().put('id',parAcc.Id);
        
        ConvertLeadCtrl ctrl = new ConvertLeadCtrl();
        ctrl.convert();
        ctrl.cancelAction();
        test.stopTest();
    }
    
    static testMethod void testLeadToAccount()
    {
        test.startTest();
        
        lead__c parAcc = [select id,Primary_Contact__c, recordTypeid,Personal_Email__c from lead__c limit 1];
        List<lead__c> accList = new List<lead__c>();
        accList.add(parAcc);
        update accList;
        PageReference pageRef = Page.ConvertLeadVfPage;
        test.setCurrentPageReference(pageRef);
        pageRef.getParameters().put('id',parAcc.Id);
        ConvertLeadCtrl ctrl = new ConvertLeadCtrl();
        List<Task> taskList = new List<Task>();
        taskList = [select id,WhatId from Task where WhatId in:accList];
        system.debug('taskList>>>>'+taskList);
        
        if(taskList!=null && taskList.size()>0)
        {
            for(Task tObj : taskList)
            {
                tObj.WhatId = ''+accList[0];
            }
            update taskList;
        }
        ctrl.convert();
        ctrl.cancelAction();
        test.stopTest();
    }
    
    static testMethod void testMassLeadToAccount()
    {
        test.startTest();
        lead__c parAcc = [select id,Primary_Contact__c, recordTypeid,Personal_Email__c,Is_Converted_From_Lead__c from lead__c limit 1];
        Lead__c cxoLead = [select id,Primary_Contact__c, recordTypeid FROM Lead__c WHERE Primary_Contact__c = '**********'];
        List<Lead__c> leadList = new List<Lead__c>();
        leadList.add(parAcc);
        leadList.add(cxoLead);
        update leadList;
        List<Attendance__c> AttenList = new list<Attendance__c>();
        AttenList = [SELECT Id, Account__c,Lead__c,Personal_Email__c  FROM Attendance__c WHERE Lead__c IN :leadList];
        Map<Id, Attendance__c> updatedAttenMap = new Map<Id, Attendance__c>();
        for (Attendance__c atten : attenList) {
            Id leadId = atten.Lead__c;
            if(atten.Personal_Email__c != null){
                update AttenList;
            }
        }
        System.debug('Atten list'+ AttenList);
        
        ApexPages.StandardSetController stdSetController = new ApexPages.StandardSetController(leadList);
        stdSetController.setSelected(leadList);
        ConvertLeadsToAccountsCtrl ctrl = new ConvertLeadsToAccountsCtrl(stdSetController);
        
        ctrl.convert();
        ctrl.cancelBtn();
        test.stopTest();
    }
   
}