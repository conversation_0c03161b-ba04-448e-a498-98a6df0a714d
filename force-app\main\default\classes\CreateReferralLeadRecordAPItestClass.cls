@isTest(SeeAllData=false)
public class CreateReferralLeadRecordAPItestClass 
{
	@isTest
    static void CreateReferralLeadRecordAPITest(){
		Account Acc = TestFactory.createAccount();
      //acc.RecordTypeId = '0120l000001PyTUAA0';
        acc.Primary_Contact__c ='**********';
        acc.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('CXO Genie').getRecordTypeId();
        Insert acc;
        
        //Lead__c ld = TestFactory.createLead();
        //ld.RecordTypeId = '0120l000001Q3Z6AAK';
        //ld.Primary_Contact__c = '**********';
        //insert ld;
        
        CreateReferralLeadRecordAPI.requestWrapper reqWrap = new CreateReferralLeadRecordAPI.requestWrapper();
        CreateReferralLeadRecordAPI.leadRequestWrapper ldWrap = new CreateReferralLeadRecordAPI.leadRequestWrapper();
        ldWrap.referral_name = 'referral1';
        ldwrap.referral_mobile = '**********';
        ldwrap.referred_by_contact = '**********';
        ldwrap.referral_mobile_countryCode = '91';
        ldwrap.referred_by_countrycode = '91';
        ldwrap.created_date = date.newInstance(2022, 11, 15);
        ldWrap.referral_app_id = '123321';
        ldwrap.forum = 'CFO Genie';
        ldwrap.referred_by_name = 'referral test';
        ldwrap.cxo_member_id = 'ASDFG1234';
        string referredByName = ldwrap.referred_by_name ;
        
        CreateReferralLeadRecordAPI.leadRequestWrapper ldWrap2 = new CreateReferralLeadRecordAPI.leadRequestWrapper();
        ldWrap2.referral_name = 'referral2';
        ldWrap2.forum = 'CHRO Genie';
        ldWrap2.referral_mobile = '9090909011';
        ldWrap2.referred_by_contact = '**********';
        ldWrap2.referral_mobile_countryCode = '91';
        ldWrap2.referred_by_countrycode = '91';
        
        CreateReferralLeadRecordAPI.leadRequestWrapper ldWrap3 = new CreateReferralLeadRecordAPI.leadRequestWrapper();
        ldWrap3.referral_name = 'referral3';
        ldWrap3.forum = 'CEO Genie';
        ldWrap3.referral_mobile = '9090909022';
        ldWrap3.referred_by_contact = '**********';
        ldWrap3.referral_mobile_countryCode = '91';
        
        
        CreateReferralLeadRecordAPI.leadRequestWrapper ldWrap4 = new CreateReferralLeadRecordAPI.leadRequestWrapper();
        ldWrap4.referral_name = 'referral4';
        ldWrap4.forum = 'CSCO Genie';
        ldWrap4.referral_mobile = '9090909033';
        ldWrap4.referred_by_contact = '**********';
        ldWrap4.referred_by_countrycode = '91';
        
        List<CreateReferralLeadRecordAPI.leadRequestWrapper> leadList1 = new List<CreateReferralLeadRecordAPI.leadRequestWrapper>();
        leadList1.add(ldWrap);
        leadList1.add(ldWrap2);
        leadList1.add(ldWrap3);
        leadList1.add(ldWrap4);
        
        reqWrap.leadList = leadList1;
        String myJSON = JSON.serialize(reqWrap);
        myJSON = JSON.serialize(reqWrap);
		
		RestRequest request = new RestRequest();
        request.requestUri = URL.getSalesforceBaseUrl().toExternalForm()+'/services/apexrest/CreateReferralLeadRecordAPI';
        request.httpMethod = 'POST';
        request.requestBody = Blob.valueof(myJSON);
        
        RestContext.request = request;
        CreateReferralLeadRecordAPI.createLead();
    }
}