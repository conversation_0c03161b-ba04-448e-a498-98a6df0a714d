<apex:page extensions="CSVFileRead" standardController="Data_Entry__c" lightningStylesheets="true">
    <apex:form >
        <apex:pagemessages />
        <apex:pageBlock >
            <apex:pageBlockSection >
                Please provide the file in following format:
                <table border="1">
                    <tr>
                        <th>CA no</th>
                        <th>Account No</th>
                        <th>IFSC</th>
                        <th>Bank Holder Name</th>
                        <th>Bank Name</th>
                    </tr>
                    <tr>
                        <th>CA0001</th>
                        <th>***************</th>
                        <th>IFSC000000</th>
                        <th>Test</th>
                        <th>ABC Bank</th>
                    </tr>
                    
                </table>
            </apex:pageBlockSection>
            <apex:pageBlockSection columns="4">
                <apex:inputFile value="{!csvFileBody}"  filename="{!csvAsString}"/>
                <apex:commandButton value="Submit" action="{!importBankData}"/>
            </apex:pageBlockSection>
            
                <apex:pageBlockSection columns="2" title="Add details for single Record">
                    Bank Account Number : <apex:inputText value="{!acNumber}"/>
                    Account Holder Name : <apex:inputText value="{!acName}"/>
                    Bank Name : <apex:inputText value="{!bankName}"/>
                    IFSC : <apex:inputText value="{!IFSCNo}"/>
                    CA Number : <apex:inputText value="{!contriNo}"/>
                    <apex:commandButton value="Submit" action="{!importSingleData}"/>
                </apex:pageBlockSection>
            
            
        </apex:pageBlock>
    </apex:form>
</apex:page>