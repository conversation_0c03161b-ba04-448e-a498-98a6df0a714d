@isTest
public class EmailNotificationTest{ 
@isTest
    public static void membershipEmailNotificationTest()
    {
        Profile userProfile = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        
        User newUser = new User(
            FirstName = 'John',
            LastName = 'Doe',
            Username = 'johndoe' + DateTime.now().getTime() + '@example.com',
            Email = 'mailto:<EMAIL>',
            Alias = 'jdoe',
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            ProfileId = userProfile.Id,
            UserRoleId = null,
            CompanyName = 'Your Company',
            Division = 'Your Division',
            Department = 'Your Department',
            Title = 'Your Title',
            Street = '1234 Main St',
            City = 'San Francisco',
            State = 'CA',
            PostalCode = '94105',
            Country = 'USA'
        );
        
        List<Account> accountListToInsert = new List<Account>();
        
        Account account1 = TestFactory.createAccount();
        account1.Membership_Status__c = 'Complimentary';
        account1.Relationship_Manager__c = newUser.Id;
        account1.Membership_Slab__c = 'Bronze';
        account1.Date_of_Slab_Updation__c = Date.Today();
        account1.Membership_Slab_Validity_Upto__c = null;
        account1.Date_of_Addition__c = Date.Today();
        account1.Date_of_Payment__c = Date.Today();
        accountListToInsert.add(account1);
        
        Account account2 = TestFactory.createAccount();
        account2.Membership_Status__c = 'Complimentary';
        account2.Membership_Slab__c = 'Silver';
        account2.Relationship_Manager__c = newUser.Id;
        account2.Date_of_Slab_Updation__c = Date.Today();
        account2.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        account2.Date_of_Addition__c = Date.Today();
        account2.Date_of_Payment__c = Date.Today();
        accountListToInsert.add(account2);
        
        Account account3 = TestFactory.createAccount();
        account3.Membership_Status__c = 'Complimentary';
        account3.Relationship_Manager__c = newUser.Id;
        account3.Membership_Slab__c = 'Gold';
        account3.Date_of_Slab_Updation__c = Date.Today();
        account3.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        account3.Date_of_Addition__c = Date.Today();
        account3.Date_of_Payment__c = Date.Today();
        accountListToInsert.add(account3);
                
        insert accountListToInsert;
        
        List<Account> accountsToUpdate = new List<Account>();
        
        //  Changing Membership Slab To Send The Emails
        account1.Membership_Slab__c = 'Silver';
        account1.Date_of_Slab_Updation__c = Date.Today();
        account1.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        accountsToUpdate.add(account1);
        
        account2.Membership_Slab__c = 'Gold';
        account2.Date_of_Slab_Updation__c = Date.Today();
        account2.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        accountsToUpdate.add(account2);
        
        account3.Membership_Slab__c = 'Silver';
        account3.Date_of_Slab_Updation__c = Date.Today();
        account3.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
        accountsToUpdate.add(account3);
        
        update accountsToUpdate;
     
        
        Test.startTest();
        MembershipSlabEmailNotification emailNotification = new MembershipSlabEmailNotification();
        emailNotification.execute(null);
        Test.stopTest();
    
    }
}