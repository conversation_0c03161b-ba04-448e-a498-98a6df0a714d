public class VCNetworkRouteToMarketTriggerHandler {
    
    public static void afterInsertDelete(List<VC_Network_Route_to_Market__c> vcNetworkList) {
        
        Set<Id> transactionId = new Set<Id>();
        Set<Id> capitalDevlopmentId = new Set<Id>();
        Set<String> uniqueVentureConnect = new Set<String>();
        List<Transaction_Advisory_History__c> transactionList = new List<Transaction_Advisory_History__c>();
        List<Capital_Development__c> capitalListToUpdate = new List<Capital_Development__c>();
        List<Transaction_Advisory_History__c> transactionToUpdate = new List<Transaction_Advisory_History__c>();
        List<String> ventureConnectNamesForTransaction = new List<String>();
        String ventureString , ventureNamesForTrans;
        
		
        for (VC_Network_Route_to_Market__c vcNetworkRoute : vcNetworkList) {
            if (vcNetworkRoute.Transaction_Advisory_History__c != null) {
                transactionId.add(vcNetworkRoute.Transaction_Advisory_History__c);
            } 
        }
        
        Transaction_Advisory_History__c currentTransaction = [SELECT Id , Capital_Development__c FROM Transaction_Advisory_History__c WHERE Id =: transactionId LIMIT 1];
            
         for(Transaction_Advisory_History__c trans : [SELECT Id , Name , Capital_Development__c FROM Transaction_Advisory_History__c 
                                                     WHERE Capital_Development__c =: currentTransaction.Capital_Development__c])
        {
            if(!transactionList.contains(trans))
            {
                transactionList.add(trans);
            }
            
            if(!capitalDevlopmentId.contains(currentTransaction.Capital_Development__c))
            {
                capitalDevlopmentId.add(currentTransaction.Capital_Development__c);
            }
            
            System.debug('Name of All The Transaction Advisory History >>> ' + trans.Name);
        }	
        
        System.debug('The List of Capital Development IDs >>> ' + capitalDevlopmentId);
        System.debug('The List of Transaction Advisory History >>> ' + transactionList);
        
        
        for(Transaction_Advisory_History__c trans: transactionList)
        {
            for(VC_Network_Route_to_Market__c vcNetworkRoute : [SELECT Id , Transaction_Advisory_History__c , Venture_Connect__r.Name FROM VC_Network_Route_to_Market__c WHERE Transaction_Advisory_History__c =: trans.Id])
            {
                if(!uniqueVentureConnect.contains(vcNetworkRoute.Venture_Connect__r.Name))
                {
                    uniqueVentureConnect.add(vcNetworkRoute.Venture_Connect__r.Name);
                }
                
                if(!ventureConnectNamesForTransaction.contains(vcNetworkRoute.Venture_Connect__r.Name))
                {
                    ventureConnectNamesForTransaction.add(vcNetworkRoute.Venture_Connect__r.Name);
                }
          	}
            
            System.debug('The List of Unique Venture Connect For a Particular Transaction Is : Transaction >>>> ' + trans.Name + ' & The Related Venture Connects Are >>>> ' + ventureConnectNamesForTransaction);
        	ventureNamesForTrans = String.join(new List<String>(ventureConnectNamesForTransaction), ' , ');
            trans.VC_Network_Route_to_market__c = ventureNamesForTrans;
            
            if(!transactionToUpdate.contains(trans))
            {
                transactionToUpdate.add(trans);
            }
            
            ventureConnectNamesForTransaction.clear();
            
            System.debug('The List of Unique Venture Connect For Paricular Transaction Advisoy History After The CLEAR Method >>>>> ' + ventureConnectNamesForTransaction);
        }
               

        System.debug('The Name of Unique Venture Connects Are >>>> ' + uniqueVentureConnect);
        
        ventureString = String.join(new List<String>(uniqueVentureConnect), ' , ');
        
        System.debug('The String Is >>>> ' + ventureString);
        
        for(Capital_Development__c capitalDev : [SELECT Id , Name , VC_Network_Route_to_market__c FROM Capital_Development__c WHERE Id =: capitalDevlopmentId])
        {
            capitalDev.VC_Network_Route_to_market__c = ventureString;
            
            if(!capitalListToUpdate.contains(capitalDev))
            {
                capitalListToUpdate.add(capitalDev);
            }
        }
        
        
        if (!capitalListToUpdate.isEmpty()) {
            
            update capitalListToUpdate;
        }
        
        if (!transactionToUpdate.isEmpty()) {
            
            update transactionToUpdate;
        }
    }
}