/***************************************
Test class for lead Trigger
***************************************/
@isTest(SeeAllData=false)
public class LeadTriggerTestClass {
    
    @testSetup static void setup()
     {
         List<Account> accountList = new List<Account>();
         Account acc1 = TestFactory.createAccount();
         acc1.Primary_Contact__c = '**********';
         acc1.Membership_Status__c = 'Exited by own';
         acc1.Date_of_Rotation_Exited__c = Date.Today();
         acc1.Exit_Fee__c = 1;
         acc1.Reason_For_Exit__c = 'A';
         accountList.add(acc1);
         
         Account acc2 = TestFactory.createAccount();
         acc2.Primary_Contact__c = '**********';
		 acc2.Membership_Status__c = 'Exited by own';
         acc2.Date_of_Rotation_Exited__c = Date.Today();
         acc2.Exit_Fee__c = 1;
         acc2.Reason_For_Exit__c = 'B';
         accountList.add(acc2);
         
         insert accountList;
         
         List<Lead__c> leadcList = new List<Lead__c>();
		 Lead__c ldc1 = TestFactory.createLead();
         ldc1.Primary_Contact__c = '**********';
         leadcList.add(ldc1);
         
         Lead__c ldc2 = TestFactory.createLead();
         ldc2.Primary_Contact__c = '**********';
         ldc2.Lead_Source__c = 'App - Startup Referral';
         ldc2.Membership_Status__c = 'Startup looking for fund';
         ldc2.Referred_By__c = acc1.Id;
         leadcList.add(ldc2);
         
         insert leadcList;
         
		 List<Lead> leadList = new List<Lead>();
         Lead lds1 = testfactory.CreatStandardlead();
         lds1.Facebook_Campaign_Name__c ='ABCD';
         lds1.Phone = '0**********';
         leadList.add(lds1);
         
         Lead lds2 = testfactory.CreatStandardlead();
         lds2.LeadSource = 'Facebook';
         lds2.Facebook_Campaign_Name__c ='ABCD';
         lds2.Phone = '**********';
         leadList.add(lds2);
         
         Lead lds3 = testfactory.CreatStandardlead();
         lds3.LeadSource = 'Facebook';
         lds3.Facebook_Campaign_Name__c ='ABCD';
		 lds3.LinkedIn_Question_1__c = 'Question One';
         lds3.LinkedIn_Answer_1__c = 'Answer One';         
         lds3.LinkedIn_Question_2__c = 'Question Two';
         lds3.LinkedIn_Answer_2__c = 'Answer Two';
         lds3.LinkedIn_Question_3__c = 'Question Three';
         lds3.LinkedIn_Answer_3__c = 'Answer Three';
         lds3.Phone = '9192939498';
         leadList.add(lds3);

         Lead lds4 = testfactory.CreatStandardlead();
         lds4.LeadSource = 'LinkedIn';
         lds4.Phone = '91939498';
         lds4.Country_Name__c = 'Guyana';
         leadList.add(lds4);
         
         insert leadList;
         
     }
    public TestMethod static void TestLeadTrigger()
    {
        Test.startTest();
        id recordTypeId = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead__c ld = new lead__c ();
        ld.RecordTypeId = recordTypeId;
        ld.Name ='testTom';
        ld.Designation__c='CFO';
        ld.Lead_Source__c = 'Website';
        ld.Personal_Email__c = '<EMAIL>';
        ld.Company__c ='abc';
        // ld.Are_you_a_first_time_investor__c ='';
        ld.Primary_Contact__c = '8141282841';
        ld.Facebook_Campaign_Name__c ='ABCD';
        // Commented By Sahil On 30.11.2023
        //ld.City__c ='test';
        ld.I_would_like_IPV_to_block_my_calendar_on__c =true;
        ld.Sales_Manager__c = userInfo.getUSerId();
        ld.Relationship_Owner__c = userinfo.getUserId();
        ld.Date_of_receiving_lead__c = date.today();
        insert ld;
        
        
        test.stopTest();
    }
}