global class ExternalAPICalloutBatch implements Database.Batchable<SObject>, Database.AllowsCallouts {
    List<List<String>> sfidChunks;
    Integer chunkLimit = 10; // Limit number of chunks processed in one batch execution
    
    global ExternalAPICalloutBatch(List<List<String>> sfidChunks) {
        this.sfidChunks = sfidChunks;
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {
        return Database.getQueryLocator([SELECT Id FROM Account LIMIT 1]); // Dummy query
    }

    global void execute(Database.BatchableContext bc, List<SObject> scope) {
        Integer processed = 0;
        
        for (Integer i = 0; i < sfidChunks.size(); i++) {
            if (processed == chunkLimit) {
                break; // Stop if limit reached
            }

            List<String> currentChunk = sfidChunks[i];
            doCallout(currentChunk);
            processed++;
        }

        for (Integer i = 0; i < chunkLimit; i++) {
            sfidChunks.remove(i);
        }
        // Remove processed chunks
    }

    global void finish(Database.BatchableContext bc) {
        
    }

    // Method to perform the HTTP callout
    public void doCallout(List<String> sfids) {
        String accessToken = RestLoginController.loginExternalSystem(); // Make sure you have the right access token retrieval logic
        Http http = new Http();
        HttpRequest request = new HttpRequest();
        request.setEndpoint('https://stg.dashboard.ipventures.in/report/getActivitySummary');
        request.setMethod('POST');
        request.setHeader('Content-Type', 'application/json');
        request.setHeader('Authorization', 'Bearer ' + accessToken);
        
        // Prepare the request body
        Map<String, Object> requestBody = new Map<String, Object>();
        requestBody.put('sfids', sfids);
        requestBody.put('startDate', String.valueOf(Date.Today().format()));
        requestBody.put('endDate', String.valueOf(Date.Today().addDays(-1).format()));
        
        // Convert the request body to JSON
        request.setBody(JSON.serialize(requestBody));

        Map<Integer, String> activityDetailMap = new Map<Integer, String>{
            0 => 'Startup',
            1 => 'Agenda',
            2 => 'Sign in',
            3 => 'Referral',
            4 => 'Discussion',
            5 => 'Comment',
            6 => 'Commitment Request',
            7 => 'Contact your RM',
            8 => 'FAQs',
            9 => 'Feedback',
            10 => 'Leaderboard',
            11 => 'Marking Startup as Favourite',
            12 => 'Portfolio',
            13 => 'Refer a Startup',
            14 => 'Query Raised',
            15 => 'Startup Call'
        };
        
        Id recordId;
        String sourceObjectName = '';

        try {
            HttpResponse response = http.send(request);
            if (response.getStatusCode() == 200) {
                System.debug('Response: >>>> ' + response.getBody());

                List<User_Activity__c> accountActivities = new List<User_Activity__c>();
                List<User_Activity__c> leadActivities = new List<User_Activity__c>();
                List<Map<String, Object>> apiResponse = (List<Map<String, Object>>) JSON.deserializeUntyped(response.getBody());

                for (Map<String, Object> activityData : apiResponse) {
                    String salesforce_user_account_id = (String) activityData.get('sfid');
                    String activityDetail = (String) activityData.get('activityDetail');
                    String activitySummary = (String) activityData.get('activity_summary');
                    // Additional fields based on the response structure can be added here

                    if (salesforce_user_account_id != null) {
                        recordId = salesforce_user_account_id;
                        sourceObjectName = recordId.getSObjectType().getDescribe().getName();
                            
                        if (sourceObjectName == 'Account') { 
                            User_Activity__c activity = new User_Activity__c();
                            activity.Related_Account__c = salesforce_user_account_id;
                            activity.Activity_Detail_RICH__c = activityDetail + ' >>> ' + activitySummary;  // Combine the details
                             activity.Activity_Type__c =activityDetail;
                            activity.Time_Stamp__c =date.today();
                            accountActivities.add(activity);
                            System.debug('Creating User_Activity__c record for Account ID: ' + salesforce_user_account_id);
                            
                        } else if (sourceObjectName == 'Lead__c') { 
                            User_Activity__c activity = new User_Activity__c();
                             activity.Related_Lead__c = salesforce_user_account_id;
                        activity.Activity_Detail_RICH__c = activityDetail + ' >>> ' + activitySummary;  // Combine the details
                        activity.Activity_Type__c =activityDetail;
                        activity.Time_Stamp__c =date.today();
                            System.debug('Creating User_Activity__c record for Lead ID: ' + salesforce_user_account_id);
                        }
                    } else {
                        System.debug('ID not found in the original recordIds: ' + salesforce_user_account_id);
                    }
                }

                // Insert Account User_Activity__c records
                if (!accountActivities.isEmpty()) {
                    insert accountActivities;
                    System.debug('Inserted ' + accountActivities.size() + ' User_Activity__c records for Accounts.');
                } else {
                    System.debug('No Account activities to insert.');
                }

                // Insert Lead User_Activity__c records
                if (!leadActivities.isEmpty()) {
                    insert leadActivities;
                    System.debug('Inserted ' + leadActivities.size() + ' User_Activity__c records for Leads.');
                } else {
                    System.debug('No Lead activities to insert.');
                }

            } else {
                System.debug('Failed callout with status: ' + response.getStatus() + ' and body: ' + response.getBody());
            }
        } catch (Exception e) {
            System.debug('Error in HTTP request: >>>> ' + e.getMessage());
        }
    }
}