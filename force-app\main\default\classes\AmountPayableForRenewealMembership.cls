public with sharing class AmountPayableForRenewealMembership {
    
    public void amountPayableForReneweal()
    {
        Map<String, Integer> renewalAmount = new Map<String, Integer>();

        List<Amount_Payable_For_Renewal__mdt> renewalCustomMetaData = [SELECT Id , Bronze_Slab_Discount__c , Silver_Slab_Discount__c , Gold_Slab_Discount__c , CXO_Genie_Membership_Discount__c , Price_Per_Point__c , GST__c , One_Year_excl_GST__c , Two_Years_excl_GST__c , Three_Years_excl_GST__c , Four_Years_excl_GST__c , Five_Years_excl_GST__c FROM Amount_Payable_For_Renewal__mdt WITH SECURITY_ENFORCED LIMIT 1];

        System.debug('');
        System.debug('Data of Custom Meta Data >>>>> ' + renewalCustomMetaData);
        System.debug('');

        if(!renewalCustomMetaData.isEmpty())
        {
            Amount_Payable_For_Renewal__mdt metaDataRecord = renewalCustomMetaData[0];
            
            // Get the describe result for the metadata type
            Schema.DescribeSObjectResult describeResult = metadataRecord.getSObjectType().getDescribe();
            
            for (Schema.SObjectField field : describeResult.fields.getMap().values()) {
                // Get the field name
                String fieldName = field.getDescribe().getName();
                
                if (fieldName.endsWith('__c')) {
                    // Get the field value from the record
                    Object fieldValue = metadataRecord.get(field);
                    
                    // Check if the field value is not null and is an instance of Decimal
                    if (fieldValue != null && fieldValue instanceof Decimal) {
                        // Convert the Decimal to Integer
                        Integer intValue = ((Decimal) fieldValue).intValue();
                        
                        // Add the field name and integer value to the map
                        renewalAmount.put(fieldName, intValue);
                    }
                }
            }
        }
    }
}