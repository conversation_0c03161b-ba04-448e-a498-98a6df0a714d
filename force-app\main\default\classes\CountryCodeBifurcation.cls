public class CountryCodeBifurcation {
    
    // public static void countryCodeCheckUp(String phoneNumber)
    // {
        // Map to store country codes and corresponding country names

    // }

    public static Map<String, String> countryCodes = new Map<String, String>{
        '+93' => 'Afghanistan',
        '+355' => 'Albania',
        '+213' => 'Algeria',
        '+376' => 'Andorra',
        '+244' => 'Angola',
        '******' => 'Antigua and Barbuda',
        '+54' => 'Argentina',
        '+374' => 'Armenia',
        '+61' => 'Australia',
        '+43' => 'Austria',
        '+994' => 'Azerbaijan',
        '******' => 'Bahamas',
        '+973' => 'Bahrain',
        '+880' => 'Bangladesh',
        '******' => 'Barbados',
        '+375' => 'Belarus',
        '+32' => 'Belgium',
        '+501' => 'Belize',
        '+229' => 'Benin',
        '+975' => 'Bhutan',
        '+591' => 'Bolivia',
        '+387' => 'Bosnia and Herzegovina',
        '+267' => 'Botswana',
        '+55' => 'Brazil',
        '+673' => 'Brunei',
        '+359' => 'Bulgaria',
        '+226' => 'Burkina Faso',
        '+257' => 'Burundi',
        '+238' => 'Cabo Verde',
        '+855' => 'Cambodia',
        '+237' => 'Cameroon',
        '+1' => 'Canada',
        '+236' => 'Central African Republic',
        '+235' => 'Chad',
        '+56' => 'Chile',
        '+86' => 'China',
        '+57' => 'Colombia',
        '+269' => 'Comoros',
        '+242' => 'Congo (Congo-Brazzaville)',
        '+243' => 'Congo (Democratic Republic)',
        '+506' => 'Costa Rica',
        '+385' => 'Croatia',
        '+53' => 'Cuba',
        '+357' => 'Cyprus',
        '+420' => 'Czech Republic',
        '+45' => 'Denmark',
        '+253' => 'Djibouti',
        '+1-767' => 'Dominica',
        '+1-809' => 'Dominican Republic',
        '+593' => 'Ecuador',
        '+20' => 'Egypt',
        '+503' => 'El Salvador',
        '+240' => 'Equatorial Guinea',
        '+291' => 'Eritrea',
        '+372' => 'Estonia',
        '+268' => 'Eswatini',
        '+251' => 'Ethiopia',
        '+679' => 'Fiji',
        '+358' => 'Finland',
        '+33' => 'France',
        '+241' => 'Gabon',
        '+220' => 'Gambia',
        '+995' => 'Georgia',
        '+49' => 'Germany',
        '+233' => 'Ghana',
        '+30' => 'Greece',
        '+1-473' => 'Grenada',
        '+502' => 'Guatemala',
        '+224' => 'Guinea',
        '+245' => 'Guinea-Bissau',
        '+592' => 'Guyana',
        '+509' => 'Haiti',
        '+504' => 'Honduras',
        '+36' => 'Hungary',
        '+354' => 'Iceland',
        '+91' => 'India',
        '+62' => 'Indonesia',
        '+98' => 'Iran',
        '+964' => 'Iraq',
        '+353' => 'Ireland',
        '+972' => 'Israel',
        '+39' => 'Italy',
        '+1-876' => 'Jamaica',
        '+81' => 'Japan',
        '+962' => 'Jordan',
        '+7' => 'Kazakhstan',
        '+254' => 'Kenya',
        '+686' => 'Kiribati',
        '+965' => 'Kuwait',
        '+996' => 'Kyrgyzstan',
        '+856' => 'Laos',
        '+371' => 'Latvia',
        '+961' => 'Lebanon',
        '+266' => 'Lesotho',
        '+231' => 'Liberia',
        '+218' => 'Libya',
        '+423' => 'Liechtenstein',
        '+370' => 'Lithuania',
        '+352' => 'Luxembourg',
        '+261' => 'Madagascar',
        '+265' => 'Malawi',
        '+60' => 'Malaysia',
        '+960' => 'Maldives',
        '+223' => 'Mali',
        '+356' => 'Malta',
        '+692' => 'Marshall Islands',
        '+222' => 'Mauritania',
        '+230' => 'Mauritius',
        '+52' => 'Mexico',
        '+691' => 'Micronesia',
        '+373' => 'Moldova',
        '+377' => 'Monaco',
        '+976' => 'Mongolia',
        '+382' => 'Montenegro',
        '+212' => 'Morocco',
        '+258' => 'Mozambique',
        '+95' => 'Myanmar',
        '+264' => 'Namibia',
        '+674' => 'Nauru',
        '+977' => 'Nepal',
        '+31' => 'Netherlands',
        '+64' => 'New Zealand',
        '+505' => 'Nicaragua',
        '+227' => 'Niger',
        '+234' => 'Nigeria',
        '+389' => 'North Macedonia',
        '+47' => 'Norway',
        '+968' => 'Oman',
        '+92' => 'Pakistan',
        '+680' => 'Palau',
        '+507' => 'Panama',
        '+675' => 'Papua New Guinea',
        '+595' => 'Paraguay',
        '+51' => 'Peru',
        '+63' => 'Philippines',
        '+48' => 'Poland',
        '+351' => 'Portugal',
        '+974' => 'Qatar',
        '+40' => 'Romania',
        '+7' => 'Russia',
        '+250' => 'Rwanda',
        '+1-869' => 'Saint Kitts and Nevis',
        '+1-758' => 'Saint Lucia',
        '+1-784' => 'Saint Vincent and the Grenadines',
        '+685' => 'Samoa',
        '+378' => 'San Marino',
        '+239' => 'Sao Tome and Principe',
        '+966' => 'Saudi Arabia',
        '+221' => 'Senegal',
        '+381' => 'Serbia',
        '+248' => 'Seychelles',
        '+232' => 'Sierra Leone',
        '+65' => 'Singapore',
        '+421' => 'Slovakia',
        '+386' => 'Slovenia',
        '+677' => 'Solomon Islands',
        '+252' => 'Somalia',
        '+27' => 'South Africa',
        '+82' => 'South Korea',
        '+211' => 'South Sudan',
        '+34' => 'Spain',
        '+94' => 'Sri Lanka',
        '+249' => 'Sudan',
        '+597' => 'Suriname',
        '+46' => 'Sweden',
        '+41' => 'Switzerland',
        '+963' => 'Syria',
        '+886' => 'Taiwan',
        '+992' => 'Tajikistan',
        '+255' => 'Tanzania',
        '+66' => 'Thailand',
        '+670' => 'Timor-Leste',
        '+228' => 'Togo',
        '+676' => 'Tonga',
        '+1-868' => 'Trinidad and Tobago',
        '+216' => 'Tunisia',
        '+90' => 'Turkey',
        '+993' => 'Turkmenistan',
        '+688' => 'Tuvalu',
        '+256' => 'Uganda',
        '+380' => 'Ukraine',
        '+971' => 'United Arab Emirates',
        '+44' => 'United Kingdom',
        '+1' => 'United States',
        '+598' => 'Uruguay',
        '+998' => 'Uzbekistan',
        '+678' => 'Vanuatu',
        '+379' => 'Vatican City',
        '+58' => 'Venezuela',
        '+84' => 'Vietnam',
        '+967' => 'Yemen',
        '+260' => 'Zambia',
        '+263' => 'Zimbabwe'
    };
    
    // Method to identify if the mobile number has a country code
    public static String identifyCountryCode(String mobileNumber) {

        List<String> countryName = countryCodes.values();

        System.debug('The Length of Country Set is >>>> ' + countryName.size());

        // Remove spaces, hyphens, or other formatting characters
        mobileNumber = mobileNumber.replaceAll('[^0-9+]', '');

        // Check if the number starts with '+'
        if (mobileNumber.startsWith('+')) {

            // Remove the '+' symbol for processing
            mobileNumber = mobileNumber.substring(1);

            // Loop through the possible country code lengths (1 to 3 digits)
            for (Integer i = 1; i <= 3; i++) {

                if (mobileNumber.length() >= i) {

                    String potentialCode = mobileNumber.substring(0, i);

                    // Check if the extracted code is a valid country code
                    if (countryCodes.containsKey(potentialCode)) {
                        return 'Country Code: ' + potentialCode + ' (' + countryCodes.get(potentialCode) + ')';
                    }
                }
            }
            // If no valid country code is found
            return 'Invalid country code or not found.';

        } else {
            // If the number does not start with '+'
            return 'No country code detected. Please add a country code.';
        }
    }

    public static void exampleUsage() {

        String number1 = '+************'; // India
        String number2 = '+************'; // United Kingdom
        String number3 = '9876543210';    // No country code

        System.debug(identifyCountryCode(number1)); // Should return "Country Code: 91 (India)"
        System.debug(identifyCountryCode(number2)); // Should return "Country Code: 44 (United Kingdom)"
        System.debug(identifyCountryCode(number3)); // Should return "No country code detected. Please add a country code."

    }
}