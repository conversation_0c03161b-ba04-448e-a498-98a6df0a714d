global class CAInvestmentMappingBatch implements Database.batchable<sObject>,Database.Stateful {
    set<ID> CAId = new set<Id>();
 	global Database.QueryLocator start(Database.BatchableContext bc) {
       String query = 'SELECT ID, Contribution_Agreement__c,Type__c,Startup_Round__r.Round_Type__c,Investor__r.Contribution_Agreement__c From Investment__c'+
           +' where (Type__c = \'Invested\' OR Type__c = \'Committed\' OR Type__c = \'Exit\')'
           +' AND Investor_Type__c = \'Via AIF\' AND'
           +' Contribution_Agreement__c = null AND Investor__r.Contribution_Agreement__c != null'
           +' AND ID != \'a0B0l00000FhV7REAV\'';
        return Database.getQueryLocator(query);
    }
    global void execute(Database.BatchableContext bc, List<Investment__c> scope){
        system.debug('Records ::'+scope);
        for(Investment__c inv : scope){
            if( (inv.Startup_Round__r.Round_Type__c == 'Raise' && (inv.Type__c == 'Invested' || inv.Type__c == 'Committed'))
              	|| (inv.Startup_Round__r.Round_Type__c == 'Internal Transfers' && inv.Type__c == 'Invested')
                || (inv.Startup_Round__r.Round_Type__c == 'Exit' && inv.Type__c == 'Exit')
              ){
                inv.Contribution_Agreement__c = inv.Investor__r.Contribution_Agreement__c;
            	CAId.add(inv.Investor__r.Contribution_Agreement__c);
            }            
        }
        try{
            update scope;
        }
        catch(exception ex){
            system.debug('Error ::'+ex.getMessage());
            throw ex;            
        } 
        
    }    
    global void finish(Database.BatchableContext bc){
        system.debug('CAId ::'+CAId);
		//System.enqueueJob(new CAinvestmentMappingQueueable(CAId));
        //String cronExp = '0 0 6 * * ?'; // Schedule to run at 6 am every day
        //System.schedule('CA Investment Mapping Batch :', cronExp, this);
    } 
    //public void execute(SchedulableContext context) {
       // CAInvestmentMappingBatch batch = new CAInvestmentMappingBatch();
       // Database.executeBatch(batch);
   // }
}