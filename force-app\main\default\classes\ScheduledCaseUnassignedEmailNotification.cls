global class ScheduledCaseUnassignedEmailNotification implements Schedulable {
    
    global void execute(SchedulableContext sc) {
        sendEmailNotifications();
    }
    
    public void sendEmailNotifications()
    {
        List<Case> casesToSendEmailList = [SELECT Id, OwnerId, Subject, Description, Owner.Email, Responsibility_to_Solve__r.email FROM Case WHERE CreatedDate = THIS_YEAR AND Complaint_Type__c = null AND Relevant_Team__c =null];
        List<Messaging.SingleEmailMessage> emailsList = new List<Messaging.SingleEmailMessage>();
        List<String> groupUserEmailsList = new List<String>();
        Id customerSuccessGrpID;
        Map<String, Case_escalation_setting__mdt> customMetadataMap = Case_escalation_setting__mdt.getAll();
        EmailUtility eCls = new EmailUtility();
        
        for (Case_escalation_setting__mdt record : customMetadataMap.values()) {
            System.debug('Developer Name: ' + record.DeveloperName);
            System.debug('Customer_Success_Team_Group_ID__c: ' + record.Customer_Success_Team_Group_ID__c);
            if(record.Customer_Success_Team_Group_ID__c!=null)
                customerSuccessGrpID = record.Customer_Success_Team_Group_ID__c;
        }
        
        if(customerSuccessGrpID!=null)
            groupUserEmailsList = getUserEmailFromGroup(customerSuccessGrpID);
            
        System.debug('groupUserEmailsList>>>>' + groupUserEmailsList);
        Id templateId = EmailUtility.getTemplateId('CASEUNASSIGNMENT');
        Id conId = ecls.getDummyContactId();
        
        for (Case c : casesToSendEmailList) {
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            List<String> toAddrList = new List<String>();
            //toAddrList.add(c.Owner.Email);  
            if (c.Responsibility_to_Solve__r.email != null && c.Responsibility_to_Solve__r.email != '') {
                toAddrList.add(c.Responsibility_to_Solve__r.email);  
            }
            
            if(groupUserEmailsList!=null && groupUserEmailsList.size()>0)
                toAddrList.addAll(groupUserEmailsList);
            
            System.debug('ccase>>>>'+c);
            System.debug('toAddrList>>>>'+toAddrList);
            //toAddrList.add('<EMAIL>');
            if(toAddrList!=null && toAddrList.size()>0)
            {
                email = eCls.createMailTemplateObj(c.Id,templateId,toAddrList,null,null,null,conId);
                emailsList.add(email);
            }
        }
        
        if (!emailsList.isEmpty()) {
            eCls.sendEmailBulk(emailsList);
        }
    }
    Public List<String> getUserEmailFromGroup(String grpId)
    {        
        List<GroupMember> groupMembers = [SELECT UserOrGroupId FROM GroupMember WHERE GroupId = :grpId];
        Set<Id> userIds = new Set<Id>();
        
        for (GroupMember member : groupMembers) {
            userIds.add(member.UserOrGroupId);
        }
        
        List<User> usersInGroup = [SELECT Id, Email FROM User WHERE Id IN :userIds];
        
        List<String> emailAddresses = new List<String>();

        for (User user : usersInGroup) {
            emailAddresses.add(user.Email);
        }
        
        return emailAddresses;
    }
    /*
    ScheduledCaseUnassignedEmailNotification emailNotification = new ScheduledCaseUnassignedEmailNotification ();
String cronExp = '0 0 0,4,8,12,16,20 * * ?'; // Runs every 4 hours
System.schedule('Scheduled Unassigned Case Email Notification Every4Hours', cronExp, emailNotification);

*/
}