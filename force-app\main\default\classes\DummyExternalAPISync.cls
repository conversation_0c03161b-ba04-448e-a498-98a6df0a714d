public with sharing class DummyExternalAPISync {
    
    public static void callExternalApi(List<Id> recordIds, String startDate, String endDate) {
        
        String endURLSetting;
        
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        
        System.debug('API_Setting__c>>>'+settingList);
        
        if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].End_URL__c))
        {
            endURLSetting = ''+settingList[0].End_URL__c;
        }
        
        System.debug('--endURLSetting--'+endURLSetting);
        
        String enddURL = endURLSetting;
        enddURL += '/report/getActivitySummary';
        
        System.debug('endURL --->'+enddURL);
        
        String accessToken;
        accessToken = restLoginController.loginExternalSystem();

        HttpRequest req = new HttpRequest();
        req.setEndpoint(endURLSetting);
        req.setMethod('POST');
        req.setHeader('Authorization', 'Bearer ' + accessToken);
        req.setHeader('Content-Type', 'application/json');

        JSONGenerator jsonGen = JSON.createGenerator(true);
        jsonGen.writeStartObject();
        jsonGen.writeFieldName('sfids');
        jsonGen.writeStartArray();
        
        for (Id recordId : recordIds) {
            jsonGen.writeString(recordId);
        }
        
        jsonGen.writeEndArray();
        jsonGen.writeStringField('startDate', startDate);
        jsonGen.writeStringField('endDate', endDate);
        jsonGen.writeEndObject();

        req.setBody(jsonGen.getAsString());

        Http http = new Http();
        HttpResponse res = http.send(req);
        String responseBody = res.getBody();
        
        System.debug('Response Object >>>>> ' + res);
        System.debug('Response Body >>>> ' + res.getBody());
        
        System.debug('Response received from external API: Status Code: ' + res.getStatusCode() + ', Body: ' + responseBody);
        
    }
}