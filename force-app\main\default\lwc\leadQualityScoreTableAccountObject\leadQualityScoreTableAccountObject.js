import { LightningElement, api, wire } from 'lwc';
import getLeadQualityScoreAccountObject from '@salesforce/apex/LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject';


const columns = [
    { label: 'Field Name', fieldName: 'fieldName', type: 'text' },
    { label: 'Quality Score', fieldName: 'qualityScore', type: 'number' }
];


export default class LeadQualityScoreTableAccountObject extends LightningElement {

    @api recordId;
    data = [];
    columns = columns;
    error;

    @wire(getLeadQualityScoreAccountObject, { accountId: '$recordId' })
    wiredLeadQuality({ error, data }) {
        if (data) {
            // Transform the map data into an array for lightning-datatable
            this.data = Object.keys(data).map((fieldName) => ({
                fieldName,
                qualityScore: data[fieldName]
            }));
        } else if (error) {
            this.error = error;
            console.error('Error fetching lead quality score', error);
        }
    }
}