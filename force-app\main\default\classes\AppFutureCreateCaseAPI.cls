public class AppFutureCreateCaseAPI {
    
    @future (callout=true)
    public static void sendCaseInfo(Set<Id> caseIdSet) {
        try{
            HttpRequest request = new HttpRequest();
            HttpResponse response = new HttpResponse();
            Http http = new Http();
            String endURLSetting;
            Map<String,String> credMAp = new Map<String,String>();
            
            credMAp.put('password','hello');
            credMAp.put('mobile','8866104284');
            credMAp.put('country_code','+91');
            
            List<API_Setting__c> settingList = API_Setting__c.getall().values();
            system.debug('StartupRestAPIController API_Setting__c>>>'+settingList);
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].End_URL__c))
            {
                endURLSetting = ''+settingList[0].End_URL__c;
            }
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Username__c))
            {
                credMAp.put('mobile',''+settingList[0].Mobile_Username__c);
            }
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Password__c))
            {
                credMAp.put('password',''+settingList[0].Mobile_Password__c);
            }
            
            system.debug('--credMAp--'+credMAp);
            
             
            List<Case> cList = [Select Id,CaseNumber,Date_Issue_Raised__c,Issue_raised_By__c,Issue_raised_By__r.Primary_Contact__c,Issue_raised_By__r.Primary_Country_Code__c,Complaint_Updates__c,Description , Status, Origin, Turnaround_Time__c,Date_Issue_resolved__c from Case where Id IN:caseIdSet] ; 
            JSONGenerator gen = JSON.createGenerator(true);    
            gen.writeStartObject(); 
            gen.writeFieldName('feedbackList');
            gen.writeStartArray();
            
            for(Case c : cList)
            {       
                gen.writeStartObject();
                    gen.writeStringField('description',c.Description);
                    gen.writeNumberField('salesforce_case_number', Integer.valueOf(c.CaseNumber));
                    
                    gen.writeNumberField('feedback_category_id',6);
                    if(c.Turnaround_Time__c!=null)
                        gen.writeNumberField('tat',c.Turnaround_Time__c);
                    if(c.Date_Issue_resolved__c!=null)
                        gen.writeDateField('issue_closed_date',c.Date_Issue_resolved__c);
                        
                    gen.writeNumberField('status',getStatusEnum(''+c.Status));
                    gen.writeStringField('salesforce_case_id',c.id);
                    
                    if(c.Issue_raised_By__c!=null && c.Issue_raised_By__r.Primary_Contact__c!=null)
                        gen.writeStringField('primary_contact',c.Issue_raised_By__r.Primary_Contact__c);
                        
                    if(c.Issue_raised_By__c!=null && c.Issue_raised_By__r.Primary_Country_Code__c!=null)
                        gen.writeStringField('primary_contact_country','+'+c.Issue_raised_By__r.Primary_Country_Code__c);
                    
                    if(!String.isEmpty(c.Complaint_Updates__c))
                        gen.writeStringField('complaint_update',c.Complaint_Updates__c);
                    
                    if(c.Date_Issue_Raised__c!=null)
                        gen.writeDateField('issue_raised_date',c.Date_Issue_Raised__c);
                gen.writeEndObject();    
            }
            gen.writeEndArray();
            gen.writeEndObject();
            String jsonData = gen.getAsString();
            System.debug('jsonMaterials'+jsonData);
            
            HttpRequest req = new HttpRequest();
            String enddURL = endURLSetting+'/userAuthentication/login-admin';
            
            String bodyy =  JSON.serialize(credMAp);
            system.debug('---'+bodyy);
            req.setEndpoint(enddURL);
            req.setHeader('Content-Type','application/json');
            req.setMethod('POST');
            req.setTimeout(120000);
            req.setBody(bodyy);
            Http http1 = new Http();
            HTTPResponse res = http1.send(req);
            system.debug('****');
            System.debug(res.getStatusCode());
            System.debug('res---'+res.toString());
            System.debug('STATUS:'+res.getStatus());
            //String accToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************.gOc9O5Y2Z-CkDsMWl1uRTCe5hRIveZzRkJHSdiVXMwY';
            String accessToken;

            if(res.getStatusCode() == 200){
                system.debug('Data sent'+res.getBody());
                String jsonstr = JSON.serialize(res.getBody());
                JSONParser parser = JSON.createParser(jsonStr);
                Map<String, Object> results = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                String tkn = JSON.serialize(results.get('data'));
                Map<String, Object> token = (Map<String, Object>) JSON.deserializeUntyped(tkn);
                accessToken = String.valueOF(token.get('token'));
                system.debug('accessToken  '+token.get('token'));
                system.debug('accessToken in add  '+accessToken);
                
                enddURL = endURLSetting+'/feedback/addBulkfeedback';
                
                system.debug('enddURL>>>>>'+enddURL);
                
                request.setEndpoint(enddURL);
                request.setHeader('Authorization','Bearer ' + accessToken);
                request.setHeader('Content-Type','application/json');
                request.setMethod('POST');

                request.setTimeout(120000);
                request.setBody(jsonData);
                Http httpSend = new Http();
                HTTPResponse resPost = httpSend.send(request);
                system.debug('****');
                System.debug(resPost.getStatusCode());
                System.debug('res--add-'+resPost.getBody());
                System.debug('STATUS:'+resPost.getStatus());
            }
        }
        catch(System.CalloutException e){
            System.debug('Error-' + e.getMessage());   
        }
    }
    
    public Static Integer getStatusEnum(String status)
    {
        Integer ret= 0;
        
        if(status=='')
            ret = 0;
        else if(status=='WIP')
            ret = 1;
        else if(status=='Closed' || status=='Closed - SOP updated')
            ret = 2;
        
        return ret;    
    }
/* Call it from developer console to testre
    Set<Id> caseIdSet = new Set<Id>();
    caseIdSet.add('5000l000007CFyC');
    AppFutureCreateCaseAPI.sendCaseInfo(caseIdSet);
*/    
    
}