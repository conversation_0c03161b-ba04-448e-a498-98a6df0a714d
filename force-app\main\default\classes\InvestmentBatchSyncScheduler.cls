global class InvestmentBatchSyncScheduler implements Schedulable {
    global void execute(SchedulableContext SC) {
        try {
            Integer batchSize = 50;
            InvestmentBatchSync batchJob = new InvestmentBatchSync();
            Id batchId = Database.executeBatch(batchJob, batchSize); 
        } catch (Exception e) {
            System.debug('Error in scheduling batch: ' + e.getMessage());
        }
    }
}