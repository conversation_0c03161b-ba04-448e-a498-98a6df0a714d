public class Last12MonthInvestmentedAmount {
    
    // Constants for thresholds
    private static final Integer BRONZE_POINTS_THRESHOLD = 500;
    private static final Decimal BRONZE_INVESTMENT_THRESHOLD = 1000000;
    private static final Decimal SILVER_INVESTMENT_THRESHOLD = 3000000;
    
    public static boolean last12MonthInvestment() {
        Date lastYear = Date.Today().addYears(-1);
        Id IPVrecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        System.debug('Last 12 Months From Today Is >>>> ' + lastYear );

        List<Investment__c> investmentList = [SELECT Id, Account__c, Investment_Amount__c FROM Investment__c WHERE LastModifiedDate >= :lastYear AND Type__c = 'Invested' AND Investment_Amount__c != null AND RecordTypeId = :IPVrecordTypeId];
        
        Map<Id, Decimal> accountInvestmentTotalMap = new Map<Id, Decimal>();
        
        for (Investment__c inv : investmentList) {
            if (inv.Account__c != null) {
                Decimal investmentAmount = accountInvestmentTotalMap.containsKey(inv.Account__c) ? accountInvestmentTotalMap.get(inv.Account__c) : 0;
                investmentAmount += inv.Investment_Amount__c;
                accountInvestmentTotalMap.put(inv.Account__c, investmentAmount);
            }
        }
        
        System.debug('Number of Unique Accounts with Investments Modified in Last 12 Months >>>> ' + accountInvestmentTotalMap.size());

        List<Account> accountsToUpdate = new List<Account>();
        for (Account acc : [SELECT Id, Total_Amount_Invested_in_Last_12_Months__c FROM Account WHERE Id IN :accountInvestmentTotalMap.keySet()]) {
            Decimal totalInvestment = accountInvestmentTotalMap.get(acc.Id);
                       
            if (acc.Total_Amount_Invested_in_Last_12_Months__c != totalInvestment) {
                acc.Total_Amount_Invested_in_Last_12_Months__c = totalInvestment;
                accountsToUpdate.add(acc);
            }
        }        
        
        if (!accountsToUpdate.isEmpty()) {
            update accountsToUpdate;
            System.debug('Total Amount Invested in Last 12 Months updated on ' + accountsToUpdate.size() + ' Account records.');
        }
        
        System.debug('Calculated >>> ');
        return true;
    }

    
    public static void membershipCalculation() {
        Id IPVrecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        List<Account> accountsToUpdate = new List<Account>();
        
        for (Account acc : [SELECT Id, Membership_Slab__c, Total_Points__c, Total_Amount_Invested_in_Last_12_Months__c, 
                                    Date_of_Slab_Updation__c, Membership_Slab_Validity_Upto__c
                            FROM Account
                            WHERE (Total_Points__c != null OR Total_Amount_Invested_in_Last_12_Months__c != null)
                                AND Membership_Status__c NOT IN ('On Trial', 'On Trial Community', 'Rotated by IPV', 
                                                                'Exited by Own', 'Exited Member Reactivated', 'Add On', 
                                                                'Old Yet to Pay', 'SIC', 'Physis Contributor', 'Physis Prospect')
                                AND RecordTypeId = :IPVrecordTypeId]) {
            
            // Initialize variables
            Decimal totalPoints = acc.Total_Points__c != null ? acc.Total_Points__c : 0;
            Decimal investmentAmount = acc.Total_Amount_Invested_in_Last_12_Months__c != null ? acc.Total_Amount_Invested_in_Last_12_Months__c : 0;
            Date slabUpdationDate = acc.Date_of_Slab_Updation__c;
            Date slabValidityDate = acc.Membership_Slab_Validity_Upto__c;
            
            // Calculate membership slab
            String newMembershipSlab = calculateMembershipSlab(totalPoints, investmentAmount);
            
            // Update account if necessary
            if (newMembershipSlab != acc.Membership_Slab__c || slabUpdationDate == null) {
                acc.Membership_Slab__c = newMembershipSlab;
                acc.Date_of_Slab_Updation__c = Date.today();
                acc.Membership_Slab_Validity_Upto__c = Date.today().addDays(365);
                accountsToUpdate.add(acc);
            }
        }
        
        // Update accounts
        if (!accountsToUpdate.isEmpty()) {
            update accountsToUpdate;
            System.debug('Updated Membership Accounts >>>>> ' + accountsToUpdate.size());
        }
    }
    
    // Method to calculate membership slab based on points and investment amount
    private static String calculateMembershipSlab(Decimal totalPoints, Decimal investmentAmount) {
        if (investmentAmount >= SILVER_INVESTMENT_THRESHOLD || totalPoints >= 1000) {
            return 'Gold';
        } else if ((investmentAmount >= BRONZE_INVESTMENT_THRESHOLD && investmentAmount < SILVER_INVESTMENT_THRESHOLD) ||
                   (totalPoints >= BRONZE_POINTS_THRESHOLD && totalPoints < 1000)) {
            return 'Silver';
        } else {
            return 'Bronze';
        }
    }
    
    
     public static void MembershipSlabEmailNotification()
     {
        List<Messaging.SingleEmailMessage> emailList = new List<Messaging.SingleEmailMessage>();
        String htmlContent = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html><head><title>Email template</title><link href=\'https://fonts.googleapis.com/css?family=Roboto:400,700\' rel=\'stylesheet\' type=\'text/css\'><style type="text/css">@media screen and (max-width: 600px){.mTableBox{min-height: 80px;}.mobileFlex{display: flex;flex-direction: column;padding-left: 40px;}}@font-face{font-family:\'CAVIARDREAMS\';font-style:normal;font-weight:400;src:url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/CAVIARDREAMS.TTF);}@font-face{font-family:\'CELIAS\';font-style:normal;font-weight:400;src:url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/CELIAS.TTF);}@font-face{font-family:\'CELIAS\';font-style:normal;font-weight:600;src:url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/CELIAS-BOLD.TTF);}body{font-family:\'CELIAS\';}</style></head><body style="text-align: center; margin: 0; padding-top: 10px; padding-bottom: 10px; padding-left: 0; padding-right: 0; -webkit-text-size-adjust: 100%;background-color: #f2f4f6; color: #000000" align="center"><div style="display:none; white-space:nowrap; font:15px courier; color:#ffffff; line-height:0; width:600px !important; min-width:600px !important; max-width:600px !important;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</div><table border="0" cellpadding="0" cellspacing="0" align="center" style="border:1px solid #dedede;text-align: center; vertical-align: middle; width: 600px; max-width: 600px;background-color: #ffffff;border-collapse: collapse;" width="600"><tbody><tr><td colspan="2" style="vertical-align: middle;"><img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/IPV+Logo.png" alt="Logo" style="max-width: 150px;position: relative;bottom: -25px;"></td></tr><tr style="background: #D3AA50;"><td colspan="2"><table border="0" cellpadding="0" cellspacing="0" style="height: 25px;border-collapse: collapse;"><tr><td width="200px"></td><td style="border-radius: 0 0 13px 13px;background: #fff;width: 200px;"></td><td width="200px"></td></tr></table></td></tr><tr><td colspan="2" style="background: #D3AA50;height: 15px;"></td></tr><tr><td colspan="2" style="background: #D3AA50;vertical-align: middle;"><p style="color: #fff;font-size: 30px;text-align: center;margin: 0;letter-spacing: 1px;">Welcome to IPV\'s <b>"Gold Club"</b></p></td></tr><tr><td colspan="2" style="background: #D3AA50;height: 20px;"></td></tr><tr align="center" style="background: #D3AA50;"><td colspan="2"><table border="0" cellpadding="0" cellspacing="0" width="80%" style="border-collapse:collapse;background-image: url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/bg.png);"><tr><td style="height: 20px;"></td></tr><tr><td align="center"><img style="display: block;" src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Icon.png" alt="icon" width="60px"><img style="display: block;" src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Congratulations.png" alt="congratulations" width="300px"><p style="margin: 0;height: 20px;"></p><p style="margin: 0;color:#D3AA50;line-height: 22px;letter-spacing: 1px;">on becoming a <b >Gold Member</b> with</p><p style="margin: 0;color: #D3AA50;line-height: 22px;letter-spacing: 1px;">Inflection Point Ventures</p></td></tr><tr><td style="height: 20px;"></td></tr></table></td></tr><tr><td colspan="2" style="height: 20px;background: #D3AA50;"></td></tr><tr align="center"><td  colspan="2"><table border="0" cellpadding="0" cellspacing="0" width="80%" style="border-collapse:collapse;"><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;"><b>Dear Member,</b></p></td></tr><tr><td colspan="3" style="height: 10px;"></td></tr><tr><td colspan="3"><p style="margin: 0;padding: 10px;background: #D3AA50;color: #fff;border-radius: 15px;font-weight: 500;letter-spacing: 1px">Heartiest congratulations on becoming esteemed "Gold member" with IPV! Your unwavering support and active engagement have woven an irreplaceable thread in our vibrant community\'s tapestry.</p></td></tr><tr><td colspan="3" style="height: 15px;"></td></tr><tr><td colspan="3"><p style="margin: 0;letter-spacing: 1px">As a Gold member, you are now part of an exclusive circle, and we are thrilled to offer you exceptional privileges as a token of our appreciation.</p></td></tr><tr><td colspan="3" style="height: 15px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 600;font-size: 17px;">IPV Privileges</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 600;font-size: 17px;">Gold Member</p></td></tr><tr><td colspan="3" style="height: 5px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Discount on membership fee on renewal</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">50% Discount</p></td></tr><tr><td colspan="3" style="height: 5px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Renewal Bonus (Points)</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">100</p></td></tr><tr><td colspan="3" style="height: 5px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Eligible to become a Selection Panel Member*</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Yes</p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;">We are thrilled to showcase your achievements and dedication to <b>growing India\'s startup ecosystem on our various platforms.</b></p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;">However, if you prefer not to have your name announced publicly, please let us know and we will respect your privacy.</p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;">Thank you for being an invaluable part of our journey, and here\'s to many more milestones ahead!</p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr></table></td></tr><tr><td width="70%" style="background: #BC964A;color: #fff;"><table border="0" cellpadding="0" cellspacing="0" style="border-collapse:collapse;"><tr style="height: 20px"><td></td></tr><tr class="mobileFlex"><td width="50px"></td><td width="150px"><img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Ankur+Mittal.png" height="150px" width="150px" alt="Ankur Mittal" /></td><td><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-size: 14px;padding-bottom: 6px">Warm regards,</p><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-weight: 600;font-size: 26px;padding-bottom: 3px">Ankur Mittal</p><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-size: 14px;padding-bottom: 3px">(Co-Founder)</p><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-size: 14px;">Inflection Point Ventures</p><p style="margin: 0;height: 10px;"></p></td><td width="30px;"></td></tr></table></td><td width="150px;"></td></tr><tr><td colspan="2" style="height: 30px;"></td></tr></tbody></table></body></html>';
		Date currentDay = Date.Today();
        Date nextYear = Date.Today().addDays(365);
    	for(Account account : [SELECT Id  , Official_Email__c , Relationship_Manager__c FROM Account WHERE Membership_Slab__c = 'Gold' AND Date_of_Slab_Updation__c =: currentDay AND Membership_Slab_Validity_Upto__c =: nextYear])
        {
            
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            if(account.Official_Email__c != null)
            {
		        email.setToAddresses(new String[]{account.Official_Email__c});
	            email.setSubject('Welcome to IPV\'s GOLD Member Club');
    	        email.setHtmlBody(htmlContent);
            }
              
            if(account.Relationship_Manager__c != null)
            {
                User usr = [SELECT Id , Name , Email FROM User WHERE Id =: account.Relationship_Manager__c];
                email.setCCAddresses(new String[]{usr.Email});                    
            }
            emailList.add(email);
        }
        
        if (!emailList.isEmpty()) 
        {
            Messaging.sendEmail(emailList);
	        System.debug('Mail Sent >>> ');
        }
    }
    
    /*public static void MembershipSlabEmailNotification()
    {
        List<Messaging.SingleEmailMessage> emailList = new List<Messaging.SingleEmailMessage>();
        String htmlContent = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html><head><title>Email template</title><link href=\'https://fonts.googleapis.com/css?family=Roboto:400,700\' rel=\'stylesheet\' type=\'text/css\'><style type="text/css">@media screen and (max-width: 600px){.mTableBox{min-height: 80px;}.mobileFlex{display: flex;flex-direction: column;padding-left: 40px;}}@font-face {font-family: \'CAVIARDREAMS\';font-style: normal;font-weight: 400;src: url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/CAVIARDREAMS.TTF);}@font-face {font-family: \'CELIAS\';font-style: normal;font-weight: 400;src: url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/CELIAS.TTF);}@font-face {font-family: \'CELIAS\';font-style: normal;font-weight: 600;src: url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/CELIAS-BOLD.TTF);}body {font-family:\'CELIAS\';}</style></head><body style="text-align: center; margin: 0; padding-top: 10px; padding-bottom: 10px; padding-left: 0; padding-right: 0; -webkit-text-size-adjust: 100%;background-color: #f2f4f6; color: #000000" align="center"><div style="display:none; white-space:nowrap; font:15px courier; color:#ffffff; line-height:0; width:600px !important; min-width:600px !important; max-width:600px !important;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</div><table border="0" cellpadding="0" cellspacing="0" align="center" style="border:1px solid #dedede;text-align: center; vertical-align: middle; width: 600px; max-width: 600px;background-color: #ffffff;border-collapse: collapse;" width="600"><tbody><tr><td colspan="2" style="vertical-align: middle;"><img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/LOGO-04.svg" alt="Logo" style="max-width: 150px;position: relative;bottom: -25px;"></td></tr><tr style="background: #D3AA50;"><td colspan="2"><table border="0" cellpadding="0" cellspacing="0" style="height: 25px;border-collapse: collapse;"><tr><td width="200px"></td><td style="border-radius: 0 0 13px 13px;background: #fff;width: 200px;"></td><td width="200px"></td></tr></table></td></tr><tr><td colspan="2" style="background: #D3AA50;height: 15px;"></td></tr><tr><td colspan="2" style="background: #D3AA50;vertical-align: middle;"><p style="color: #fff;font-size: 30px;text-align: center;margin: 0;letter-spacing: 1px;">Welcome to IPV\'s <b>"Gold Club"</b></p></td></tr><tr><td colspan="2" style="background: #D3AA50;height: 20px;"></td></tr><tr align="center" style="background: #D3AA50;"><td colspan="2"><table border="0" cellpadding="0" cellspacing="0" width="80%" style="border-collapse:collapse;background-image: url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/bg.png);"><tr><td style="height: 20px;"></td></tr><tr><td align="center"><img style="display: block;" src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Icon-03.svg" alt="icon" width="60px"><img style="display: block;" src="Congratulations-Vector.svg" alt="congratulations" width="300px"><p style="margin: 0;height: 20px;"></p><p style="margin: 0;color:#D3AA50;line-height: 22px;letter-spacing: 1px;">on becoming a <b >Gold Member</b> with</p><p style="margin: 0;color: #D3AA50;line-height: 22px;letter-spacing: 1px;">Inflection Point Ventures</p></td></tr><tr><td style="height: 20px;"></td></tr></table></td></tr><tr><td colspan="2" style="height: 20px;background: #D3AA50;"></td></tr><tr align="center"><td  colspan="2"><table border="0" cellpadding="0" cellspacing="0" width="80%" style="border-collapse:collapse;"><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;"><b>Dear Member,</b></p></td></tr><tr><td colspan="3" style="height: 10px;"></td></tr><tr><td colspan="3"><p style="margin: 0;padding: 10px;background: #D3AA50;color: #fff;border-radius: 15px;font-weight: 500;letter-spacing: 1px">Heartiest congratulations on becoming esteemed "Gold member" with IPV! Your unwavering support and active engagement have woven an irreplaceable thread in our vibrant community\'s tapestry.</p></td></tr><tr><td colspan="3" style="height: 15px;"></td></tr><tr><td colspan="3"><p style="margin: 0;letter-spacing: 1px">As a Gold member, you are now part of an exclusive circle, and we are thrilled to offer you exceptional privileges as a token of our appreciation.</p></td></tr><tr><td colspan="3" style="height: 15px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 600;font-size: 17px;">IPV Privileges</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 600;font-size: 17px;">Gold Member</p></td></tr><tr><td colspan="3" style="height: 5px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Discount on membership fee on renewal</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">50% Discount</p></td></tr><tr><td colspan="3" style="height: 5px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Renewal Bonus (Points)</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">100</p></td></tr><tr><td colspan="3" style="height: 5px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Eligible to become a Selection Panel Member*</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Yes</p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;">We are thrilled to showcase your achievements and dedication to <b>growing India\'s startup ecosystem on our various platforms.</b></p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;">However, if you prefer not to have your name announced publicly, please let us know and we will respect your privacy.</p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;">Thank you for being an invaluable part of our journey, and here\'s to many more milestones ahead!</p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr></table></td></tr><tr><td width="70%" style="background: #BC964A;color: #fff;"><table border="0" cellpadding="0" cellspacing="0" style="border-collapse:collapse;"><tr style="height: 20px"><td></td></tr><tr class="mobileFlex"><td width="50px"></td><td width="150px"><img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/ANKUR.svg" alt="Ankur Mittal" /></td><td><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-size: 14px;padding-bottom: 6px">Warm regards,</p><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-weight: 600;font-size: 26px;padding-bottom: 3px">Ankur Mittal</p><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-size: 14px;padding-bottom: 3px">(Co-Founder)</p><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-size: 14px;">Inflection Point Ventures</p><p style="margin: 0;height: 10px;"></p></td><td width="30px;"></td></tr></table></td><td width="150px;"></td></tr><tr><td colspan="2" style="height: 30px;"></td></tr></tbody></table></body></html>';
		Date currentDay = Date.Today();
        Date nextYear = Date.Today().addDays(365);
    	for(Account account : [SELECT Id  , Official_Email__c , Relationship_Manager__c FROM Account WHERE Membership_Slab__c = 'Gold' AND Date_of_Slab_Updation__c =: currentDay AND Membership_Slab_Validity_Upto__c =: nextYear])
        {
            
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            if(account.Official_Email__c != null)
            {
		        email.setToAddresses(new String[]{account.Official_Email__c});
	            email.setSubject('Welcome to IPV\'s GOLD Member Club');
    	        email.setHtmlBody(htmlContent);
            }
              
            if(account.Relationship_Manager__c != null)
            {
                User usr = [SELECT Id , Name , Email FROM User WHERE Id =: account.Relationship_Manager__c];
                email.setCCAddresses(new String[]{usr.Email});                    
            }
            emailList.add(email);
        }
        
        if (!emailList.isEmpty()) 
        {
            Messaging.sendEmail(emailList);
        }
    }*/
}