@RestResource(urlMapping='/transactionUtility/*')
global with sharing class TransactionsOptAPI{

    @HttpPost
    global Static void transaction()
    {
         
        RestRequest req = RestContext.request;
        RestResponse res = Restcontext.response;
        string jsonReqString=req.requestBody.tostring();
        System.debug('jsonReqString>>>>>'+jsonReqString);
        Map<String, Object> reqMap = (Map<String, Object>) JSON.deserializeUntyped(jsonReqString);
        Map<String, String> errorMap = new  Map<String, String>();
        string retJSONstr;
        System.debug('m >>>>>'+reqMap);
        try
        {
             
            if(reqMap!=null && reqMap.size()>0)
            {
                List<Contact> conList = new List<Contact>();
                conList = [Select id from contact where Virtual_A_C_Investor__c =: reqMap.get('VirtualAccountNumber').toString()];
                System.debug('conList >>>>>'+conList );
                
                Transaction__c trnsObj = new Transaction__c();
                trnsObj.Amount__c = decimal.valueOf(''+reqMap.get('TransactionAmount'));
                
                //trnsObj.Bank_Transaction_Id__c = ''+reqMap.get('TransactionAmount');
                trnsObj.Mode_of_Payment__c = ''+reqMap.get('Mode');
                trnsObj.Remarks__c = ''+reqMap.get('SendertoReceiverInformation');
                trnsObj.Remitters_Bank_A_C__c = decimal.valueOf(''+reqMap.get('RemitterAccountNumber'));
                trnsObj.Remitters_IFSC__c = ''+reqMap.get('RemitterIFSC_Code');
                trnsObj.Remitters_Name__c = ''+reqMap.get('RemitterName');
                trnsObj.Transaction_Date__c = Datetime.valueOf(''+reqMap.get('Date'));
                trnsObj.Transaction_Type__c = 'Amount transferred to FPC';
                trnsObj.UTR_Number__c = ''+reqMap.get('UTRnumber');
                trnsObj.Virtual_Account_Number__c = ''+reqMap.get('VirtualAccountNumber');
                
                if(reqMap.containsKey('clientCode'))
                    trnsObj.Client_code__c = ''+reqMap.get('clientCode');
                if(reqMap.containsKey('senderName')){
                    trnsObj.Sender_name__c = ''+reqMap.get('senderName');
                    
                    System.debug('trnsObj.Remitters_Name__c>>>>>>>>>>'+trnsObj.Remitters_Name__c);
                    System.debug('trnsObj.Sender_name__c>>>>>>>>>>'+trnsObj.Sender_name__c);
                    System.debug('reqMap.get(RemitterName)>>>>>>>>>>'+reqMap.get('RemitterName'));
                    
                    //if(reqMap.get('RemitterName') =='' || reqMap.get('RemitterName') == null)
                    trnsObj.Remitters_Name__c = trnsObj.Sender_name__c;
                   
                    System.debug('123456 trnsObj.Remitters_Name__c>>>>>>>>>>'+trnsObj.Remitters_Name__c);

                }
                if(conList.size()>0)
                {
                    trnsObj.Investor__c = conList[0].Id;
                }
                
                upsert trnsObj UTR_Number__c;
                System.debug('trnsObj>>>>>'+trnsObj);
                
                reqMap.put('Status','Accept');
                reqMap.put('RejectReason','Null');
                reqMap.put('Refundcode','Null');    
                
                retJSONstr = JSON.serialize(reqMap);
                RestContext.response.addHeader('Content-Type', 'application/json');
                RestContext.response.responseBody = Blob.valueOf(retJSONstr );
                
                //return retJSONstr;
            }
            else
            {
                //return 'Please provide the valid request';
                errorMap.put('Error','Please provide the valid request');
                retJSONstr = JSON.serialize(errorMap);

                RestContext.response.addHeader('Content-Type', 'application/json');
                RestContext.response.responseBody = Blob.valueOf(retJSONstr );
            }
        }
        catch(Exception e)
        {
            //return 'Please resolve the error: '+e.getMessage();
            errorMap.put('Error','Please resolve the error: '+e.getMessage());
            retJSONstr = JSON.serialize(errorMap);

            RestContext.response.addHeader('Content-Type', 'application/json');
            RestContext.response.responseBody = Blob.valueOf(retJSONstr );
        }
    }  
}