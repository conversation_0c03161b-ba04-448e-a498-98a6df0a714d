@isTest
private class ReferralTreeVfPageControllerTest {
    
    @isTest
    public static void testAccountTree() {
        List<Account> accountList1 = new List<Account>(); 
        List<Account> accountList2 = new List<Account>(); 
        List<Lead__c> LeadList1 = new List<Lead__c>(); 

        Account mainParentAccount = TestFactory.createAccount();
        insert mainParentAccount;
        
        for (Integer i = 0; i < 5; i++) {
            Account childAccount = TestFactory.createAccount();
            childAccount.ParentId = mainParentAccount.Id;
            accountList1.add(childAccount);
        }
        insert accountList1;
        
        for (Account parentAccount : accountList1) {
            Account childAccount = TestFactory.createAccount();
            childAccount.ParentId = parentAccount.Id;
            accountList2.add(childAccount);
        }
        insert accountList2;
        
        for (Integer i = 0; i < 4; i++) {
            Lead__c childLead = TestFactory.createLead();
            childLead.Referred_By__c = mainParentAccount.Id;
            LeadList1.add(childLead);
        }
        insert LeadList1;

        Test.startTest();
        Test.setCurrentPage(Page.ReferralTreeVfPage); 
        ApexPages.currentPage().getParameters().put('id', mainParentAccount.Id);

        ReferralTreeVfPageController accountTree = new ReferralTreeVfPageController();
        accountTree.initializeHierarchy();
       
        Test.stopTest();
    }
}