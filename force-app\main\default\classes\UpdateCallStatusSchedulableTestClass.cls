@isTest
private class UpdateCallStatusSchedulableTestClass {

    @isTest
    static void testSchedulable() {
        Account testAccount = TestFactory.createAccount();
        testAccount.Date_of_Payment__c = Date.today().addDays(-20);
        testAccount.Name = 'Test Account';
        insert testAccount;
		
        List<task> TList = new list<task>();
        Task testTask = new Task(Subject = 'Call', WhatId = testAccount.Id, Type = 'L1');
        insert testTask;
        Task testTask3 = new Task(Subject = 'Call', WhatId = testAccount.Id, Type = 'L3');
        insert testTask3;
        Task testTask4 = new Task(Subject = 'Call', WhatId = testAccount.Id, Type = 'L4');
        insert testTask4;
        Task testTask5 = new Task(Subject = 'Call', WhatId = testAccount.Id, Type = 'L5');
        insert testTask5;
        Task testTask6 = new Task(Subject = 'Call', WhatId = testAccount.Id, Type = 'L6');
        insert testTask6;
        
        Task testTask7 = new Task(Subject = 'Call', WhatId = testAccount.Id, Type = 'L2');
        insert testTask7;

        Test.startTest();
        // Schedule the job
        String cronExp = '0 ' + (System.now().minute() + 1) + ' ' + System.now().hour() + ' ' + System.now().day() + ' ' + System.now().month() + ' ? ' + System.now().year();
        String jobId = System.schedule('Test UpdateCallStatusSchedulable', cronExp, new UpdateCallStatusSchedulable());
      //String jobId = System.schedule('Test UpdateCallStatusSchedulable', '0 0 0 15 3 ? 2023', new UpdateCallStatusSchedulable());

        CronTrigger ct = [SELECT Id FROM CronTrigger WHERE Id = :jobId];
        System.assertNotEquals(null, ct);

        Test.stopTest();
        Account updatedAccount = [SELECT Id, L1_Call_status__c, L1_Call_delayed_days__c FROM Account WHERE Id = :testAccount.Id];
        System.assertEquals('Overdue', updatedAccount.L1_Call_status__c);
        // Add more assertions for other fields and conditions as needed
    }
}