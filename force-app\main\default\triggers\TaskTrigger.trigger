trigger TaskTrigger on Task (after insert, after update , before insert , before update) {
    
    if(trigger.isafter && (trigger.isinsert || trigger.isupdate)){
        
        Id parentObjId = Trigger.New[0].WhatId;
        
        System.debug('Object Name >>> ' + parentObjId.getSObjectType());
        System.debug('Task parentObjId>>>>>>'+ parentObjId);
        
        if(''+parentObjId.getSObjectType() == 'Account'){
            TaskTriggerHandler.updateCallStatus(Trigger.newMap.keySet());
            
            System.debug('Account >>>>> ');
        }
    }
    
    if(Trigger.isBefore && (Trigger.isinsert || Trigger.isupdate))
    {
        TaskTriggerHandler.beforeInsertUpdate(Trigger.new);
    }
}