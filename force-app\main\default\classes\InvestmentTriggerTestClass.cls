/************************************************************************
    Test Class for InvestmentTrigger and InvestmentTriggerHandler and InvestmentRestAPIController
************************************************************************/
@isTest(SeeAllData=false)
private class InvestmentTriggerTestClass{
    
    private class RestMock implements HttpCalloutMock {
        
        public HTTPResponse respond(HTTPRequest req) {
            String fullJson = 'your Json Response';
            Map<String,Object> credMAp1 = new Map<String,Object>();
            credMAp1.put('token','token111');
            
            Map<String,Object> credMAp = new Map<String,Object>();
            credMAp.put('data',credMAp1);
            credMAp.put('mobile','8866104284');
            credMAp.put('country_code','+91');
            fullJson = JSON.serialize(credMAp);
            
            HTTPResponse res = new HTTPResponse();
            res.setHeader('Content-Type', 'text/json');
            res.setBody(fullJson);
            res.setStatusCode(200);
            return res;
        }
    }
    
    @testSetup static void setup() {
       
        API_Setting__c setting = new API_Setting__c();
        setting.Name = 'Test Setting';
        setting.Enable_Investment_API_Call__c = false;
        setting.Enable_Investor_API_Call__c = true;
        setting.Enable_Startup_API_Call__c = false;
        setting.Enable_Whatsapp_API_Call__c = true;
        setting.End_URL__c = 'test.com';
        insert setting;

        EmailTemplate emailTemp = [select id,name from EmailTemplate order by LastModifiedDate desc limit 1];
        EmailTemplate emailTempAssign = [select id,name from EmailTemplate where DeveloperName = 'New_Case_Assigned' limit 1];
            List<Email_Notification_Setting__c> emailSettingList = new List<Email_Notification_Setting__c>();
            Email_Notification_Setting__c emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Back_out_approved';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Back_out_Unapproved';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Commitment_Released_Confirmation';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Round_closed_deal_dropped';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'IPV_Fees_cheque';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'IPV_Membership_Fee_Commitment_for';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Renewal_IPV_Membership_Fee';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Waitlist_members_getting_Confirmed';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Residential_status_Committed';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Fnf_T1_Confirmation_members';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Fnf_T2_Confirmation_members';
            emailSettingList.add(emailSetting);
                        
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Status_Waitlist_Over_Subscription';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Status_Waitlist_Unapproved_Backout';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Reason_for_Waitlist_Pending_Documents';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Reason_for_Waitlist_IPV_Fee_Pending';
            emailSettingList.add(emailSetting);
            
            
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Inv_amount_received_confirmation';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'AIF_Investment_Amount_received';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Fnf_T2_Investment_Amount_Received';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Fnf_T1_Investment_amount_Received';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'CASEASSIGNMENT';
            emailSettingList.add(emailSetting); 
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'CASERESOLVED';
            emailSettingList.add(emailSetting); 
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'CASEDUEDATEREVISED';
            emailSettingList.add(emailSetting); 
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'CASEUNASSIGNMENT';
            emailSettingList.add(emailSetting); 
            
            insert emailSettingList;
            
        Startup__c st = TestFactory.createStartUp();
        insert st;     
        System.debug('Startup insert....12' + st);
        
          id recordTypeIdIPV = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
            List<Account> accList = new List<Account>();
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            for(Account accAux: accList)
            {
                accAux.RecordTypeId = recordTypeIdIPV;
                accAux.ShippingStreet       = '';
                accAux.ShippingState        = '';
                accAux.ShippingPostalCode   = '';
                accAux.ShippingCountry      = '';
                accAux.ShippingCity         = '';
                accAux.Description          = '';
                accAux.BillingStreet        = '';
                accAux.BillingState         = '';
                accAux.BillingPostalCode    = '';
                accAux.BillingCountry       = '';
                accAux.BillingCity          = '';
                accAux.Designation__c ='Test';
                accAux.Company__c = 'Codiot';
                accAux.Are_you_a_first_time_investor_c__c ='';
                accAux.Preferred_Email__c = 'Personal';
                accAux.Personal_Email__c = '<EMAIL>';
            }
            insert accList;
            System.debug('invTrigger Acct insert....13' + accList);    
        
            List<Contact> conList = new List<Contact>();
            integer i = 1;
            for(Account acc : accList)
            {
                Contact cont = new Contact();
                cont.FirstName='Test';
                cont.LastName='Test';
                cont.Email = i+'<EMAIL>';
                cont.Accountid= acc.id;
                cont.Investor_s_PAN__c = 'ABCDE'+i+'173D';
                cont.Investment_in_Own_Name_Family_Member__c = 'Own Name';
                cont.Send_Auto_Com_Email__c = true;
                conList.add(cont);
                i++;
            }
            insert conList;
            System.debug('invTrigger Cont insert....14' + conList);
            Startup__c stObj = [select id from Startup__c limit 1];
            
            Startup_Round__c strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,stObj.Id);
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 12, 9);
            strObj.Send_Auto_Comms_Email__c = true; 
          strObj.Startup_Converted__c = TRUE;
        strObj.Round_Type__c = 'Raise';
            insert strObj;  

        Fund_Module__c fnc = new Fund_Module__c();
        fnc.Name = 'TEST';
        insert fnc;
        
        Contribution_Agreement__c conAgree = new Contribution_Agreement__c();
        conAgree.Investor1__c = conList[0].Id;
        conAgree.Fund_Onboarded_on__c = fnc.id;
        insert conAgree;
            System.debug('invTrigger startupRound insert....15' + strObj);
    }
    
  
    static testMethod void invTrigger()
    {
           
        test.startTest();
            user ur = TestFactory.CreateUser();
            ur.ProfileId = Userinfo.getProfileId();
            insert ur;
            Startup_Round__c strObj = [select id,name from Startup_Round__c limit 1];
            List<Account> accList = [Select id,Personal_Email__c,Preferred_Email__c,Membership_status__c,Membership_Validity__c,Email_Id_cc__c,Relationship_Manager__c,Primary_Contact__c from account];
            Contribution_Agreement__c conAgree = [SELECT Id FROM Contribution_Agreement__c LIMIT 1];
            List<Contact> conList = [select id from contact];
            List<Fund_Module__c> fnc = [select id from Fund_Module__c limit 1];

            List<Investment__c> invList = new list<Investment__c >();       
            Investment__c inv1 = TestFactory.createInvestment(accList[0].Id); 
            inv1.Investor_s_PAN__c = '**********'; 
            inv1.Startup_Round_Name__c = strObj.Name;
            inv1.Investor_Type__c ='Via Platform';
            //Added for lead tracker.
            inv1.Investment_Amount__c = 10;
            inv1.Number_Of_Shares__c =25;
            inv1.Investment_Amount_Due__c = 20;
            inv1.Date_of_FnF_CFM__c = date.newInstance(2022, 07, 23);
            inv1.Call_For_Money_Sent__c = true;
            inv1.Issue_Type__c ='Primary';
            inv1.Intransfer_balance_share__c = false;
            inv1.IPVFnF_Shadow_Balance_share__c = true;
            inv1.Fund_Type__c = fnc[0].id;
            
            //inv.Startup_Round__c = strObj.Id;
            invList.add(inv1);
            
            Investment__c inv2 = TestFactory.createInvestment(accList[0].Id); 
            inv2.Investor_s_PAN__c = '**********';
            inv2.Startup_Round_Name__c = strObj.Name;
            inv2.Investment_in_Own_Name_Family_Member__c = 'Own Name';
            inv2.Is_Investor_Exists__c= true;
            inv2.Contribution_Agreement__c = conAgree.Id;
            inv2.SAF_soft_copy__c = false;
            inv2.Transfer_Deed_hard_copy__c = false;
            inv2.Transfer_Deed_soft_copy__c = false;
            inv2.Fund_Type__c = fnc[0].id;
            invList.add(inv2);
            
            //Joints Scenario
            Investment__c inv3 = TestFactory.createInvestment(accList[0].Id); 
            inv3.Investor_s_PAN__c = '**********'; 
            inv3.Startup_Round_Name__c = strObj.Name;
        
            inv3.Investment_in_Own_Name_Family_Member__c = 'Joint A/c - Outside';
            inv3.Primary_Holder_Contact__c= accList[0].Primary_Contact__c;
            inv3.Is_Primary__c=true;
            inv3.Type__c = 'Invested';
            inv3.Issue_Type__c = 'Primary - AIF';
            inv3.SAF_soft_copy__c = false;
            inv3.Transfer_Deed_hard_copy__c = false;
            inv3.Transfer_Deed_soft_copy__c = false;
            invList.add(inv3);
            
            Investment__c inv4 = TestFactory.createInvestment(accList[1].Id); 
            inv4.Investor_s_PAN__c = '**********'; 
            inv4.Startup_Round_Name__c = strObj.Name;
            inv4.Investment_in_Own_Name_Family_Member__c = 'Joint A/c - Outside';
            inv4.Primary_Holder_Contact__c= accList[0].Primary_Contact__c;
            inv4.Is_Primary__c=false;
            inv4.Type__c = 'Invested';
        	inv4.Number_Of_Shares__c = 100;
            inv4.Contribution_Agreement__c = conAgree.Id;
            inv4.Issue_Type__c = 'Primary';
            inv4.SAF_soft_copy__c = false;
            inv4.Transfer_Deed_hard_copy__c = false;
            inv4.Transfer_Deed_soft_copy__c = false;
            invList.add(inv4);
            
            Investment__c inv5 = TestFactory.createInvestment(accList[2].Id); 
            inv5.Investor_s_PAN__c = '**********'; 
            inv5.Startup_Round_Name__c = strObj.Name;
            inv5.Investment_in_Own_Name_Family_Member__c = 'Joint A/c - Outside';
            inv5.Primary_Holder_Contact__c= accList[0].Primary_Contact__c;
            inv5.Is_Primary__c=false;
        	inv5.Number_Of_Shares__c = 10;
            inv5.Investment_Amount__c = 550000;
            inv5.Type__c = 'Invested';
            inv5.SAF_soft_copy__c = false;
            inv5.Transfer_Deed_hard_copy__c = false;
            inv5.Transfer_Deed_soft_copy__c = false;
            inv5.Parent_Investment__c = null;
        	inv5.IRR_Value__c = 0;
        
            //inv.Startup_Round__c = strObj.Id;
            invList.add(inv5);
        
       		Investment__c inv6 = TestFactory.createInvestment(accList[0].Id); 
            inv6.Investor_s_PAN__c = '**********'; 
            inv6.Startup_Round_Name__c = strObj.Name;
            inv6.Investor_Type__c ='Via Platform';
            //Added for lead tracker.
            inv6.Investment_Amount__c = 10;
            inv6.Number_Of_Shares__c = 25;
            inv6.Investment_Amount_Due__c = 20;
            inv6.Date_of_FnF_CFM__c = date.newInstance(2022, 07, 23);
            inv6.Call_For_Money_Sent__c = true;
        	inv6.Parent_Investment__c = inv1.Id;
        	inv6.Type__c = 'Exit';
            inv6.Issue_Type__c ='Primary';
            inv6.Intransfer_balance_share__c = false;
            inv6.IPVFnF_Shadow_Balance_share__c = true;
            invList.add(inv6);
            
        	Investment__c inv7 = TestFactory.createInvestment(accList[0].Id); 
            inv7.Investor_s_PAN__c = '**********'; 
            inv7.Startup_Round_Name__c = strObj.Name;
            inv7.Investor_Type__c ='Via Platform';
            //Added for lead tracker.
            inv7.Investment_Amount__c = 10;
            inv7.Number_Of_Shares__c = 10;
            inv7.Investment_Amount_Due__c = 20;
            inv7.Date_of_FnF_CFM__c = date.newInstance(2022, 07, 23);
            inv7.Call_For_Money_Sent__c = true;
        	inv7.Parent_Investment__c = inv5.Id;
        	inv7.Type__c = 'Exit';
            inv7.Issue_Type__c ='Primary';
            inv7.Intransfer_balance_share__c = false;
            inv7.IPVFnF_Shadow_Balance_share__c = true;
        	inv7.IRR_Value__c = -10;
            inv7.Parent_Investment__c = inv1.Id;
            inv7.Family_Balance_Share__c = true;
            inv7.Number_Of_Shares__c = 10;
            invList.add(inv7);
            
            
            Investment__c inv8 = TestFactory.createInvestment(accList[2].Id); 
            inv8.Investor_s_PAN__c = '**********'; 
            inv8.Startup_Round_Name__c = strObj.Name;
            inv8.Investment_in_Own_Name_Family_Member__c = 'Joint A/c - Outside';
            inv8.Primary_Holder_Contact__c= accList[0].Primary_Contact__c;
            inv8.Is_Primary__c=false;
        	inv8.Number_Of_Shares__c = 10;
            inv8.Investment_Amount__c = 550000;
            inv8.Type__c = 'Invested';
            inv8.SAF_soft_copy__c = false;
            inv8.Transfer_Deed_hard_copy__c = false;
            inv8.Transfer_Deed_soft_copy__c = false;
            inv8.Parent_Investment__c = null;
            inv8.Issue_Type__c = 'Primary';
        	inv8.IRR_Value__c = 0;
            invList.add(inv8);
            //insert invList;

            Investment__c inv9 = TestFactory.createInvestment(accList[2].Id); 
             
            inv9 = TestFactory.createInvestment(accList[2].Id); 
            inv9.Investor_s_PAN__c = '**********'; 
            inv9.Startup_Round_Name__c = strObj.Name;
            inv9.Investment_in_Own_Name_Family_Member__c = 'LLP';
            inv9.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
            inv9.Is_Primary__c=true;
            inv9.Investor_Type__c = 'Via AIF';
            inv9.Type__c= 'Invested';
            inv9.Issue_Type__c = 'Primary';
            inv9.SAF_soft_copy__c = false;
            inv9.Number_Of_Shares__c = 25;
            inv9.Investor__c = conList[0].ID; //System.Label.Investor_IPV_Advisors_Pvt_Ltd;
            inv9.Carry_fee_received__c = 123;
            inv9.Startup_Converted__c = true;
            inv9.Date_of_Gross_Gain_Loss_starts__c = Date.newInstance(2023, 04, 21);
            inv9.Investment_Fee_Received__c = 0908;
            inv9.Date_of_transaction__c = Date.TODAY();
            inv9.Residential_Status__c ='Resident';
            inv9.Share_Certificates_Scan_Sent__c = 'yes';
            inv9.Share_Certificates_Delivered_Hard_Copy__c ='Exited';
            inv9.Share_Certificates_Tracking_ID__c = 'yes';
            inv9.Is_Primary__c=true;
        	invList.add(inv9);
        
            
        	Investment__c inv10 = TestFactory.createInvestment(accList[2].Id);      
            inv10 = TestFactory.createInvestment(accList[2].Id); 
            inv10.Investor_s_PAN__c = '**********'; 
            inv10.Startup_Round_Name__c = strObj.Name;
            inv10.Investment_in_Own_Name_Family_Member__c = 'LLP';
            inv10.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
            inv10.Is_Primary__c=true;
            inv10.Investor_Type__c = 'Via AIF';
            inv10.Type__c= 'Invested';
            inv10.Issue_Type__c = 'Primary';
            inv10.SAF_soft_copy__c = false;
            inv10.Number_Of_Shares__c = 25;
            inv10.Investor__c = conList[0].ID; //System.Label.Investor_IPV_Advisors_Pvt_Ltd;
            inv10.Carry_fee_received__c = 123;
            inv10.Startup_Converted__c = true;
            inv10.Date_of_Gross_Gain_Loss_starts__c = Date.newInstance(2023, 04, 21);
            inv10.Investment_Fee_Received__c = 0908;
            inv10.Date_of_transaction__c = Date.TODAY();
            inv10.Residential_Status__c ='Resident';
            inv10.Share_Certificates_Scan_Sent__c = 'yes';
            inv10.Share_Certificates_Delivered_Hard_Copy__c ='Exited';
            inv10.Share_Certificates_Tracking_ID__c = 'yes';
        inv10.Is_Primary__c=true;
        	invList.add(inv10);
            
        
            Investment__c inv11 = TestFactory.createInvestment(accList[2].Id);  
            inv11 = TestFactory.createInvestment(accList[2].Id); 
            inv11.Investor_s_PAN__c = '**********'; 
            inv11.Startup_Round_Name__c = strObj.Name;
            inv11.Investment_in_Own_Name_Family_Member__c = 'LLP';
            inv11.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
            inv11.Is_Primary__c=true;
            inv11.Investor_Type__c = 'Via AIF';
            inv11.Type__c= 'Invested';
            inv11.Issue_Type__c = 'Primary';
            inv11.SAF_soft_copy__c = false;
            inv11.Number_Of_Shares__c = 25;
            inv11.Investor__c = conList[0].ID; //System.Label.Investor_IPV_Advisors_Pvt_Ltd;
            inv11.Carry_fee_received__c = 123;
            inv11.Startup_Converted__c = true;
            inv11.Date_of_Gross_Gain_Loss_starts__c = Date.newInstance(2023, 04, 21);
            inv11.Investment_Fee_Received__c = 0908;
            inv11.Date_of_transaction__c = Date.TODAY();
            inv11.Residential_Status__c ='Resident';
            inv11.Share_Certificates_Scan_Sent__c = 'yes';
            inv11.Share_Certificates_Delivered_Hard_Copy__c ='Exited';
            inv11.Share_Certificates_Tracking_ID__c = 'yes';
        inv11.Is_Primary__c=true;
			invList.add(inv11);
        
        	insert invList;
        
          
            
        	inv9.IPV_Fees_Cheque__c = 'yes';
            inv9.Issue_Type__c = 'Primary - AIF';
            inv9.Type__c= 'Invested';
            //inv.Type__c = 'Back out approved';
            update inv9;
            inv10.Issue_Type__c = 'IPV FnF - Shadow';
            inv10.Type__c = 'Back out approved';
            update inv10;   
            inv11.Issue_Type__c = 'IPV FnF - Shadow';
        	inv11.Type__c = 'Back out unapproved';
            update inv11;   

            System.debug('invTrigger Investment insert....16' + invList);
        
            //Added by karan Update scenario 
            accList[0].Membership_status__c = 'On Trial';
            accList[0].Membership_Validity__c = Date.newInstance(2023, 04, 21);
            update accList;
            accList[1].Membership_status__c = 'IPV Team';
            accList[1].Membership_Validity__c = Date.newInstance(2023, 04, 21);
            update accList;
            System.debug('invTrigger Acct Update....17' + accList);
        
            delete inv5;
            
            delete inv4;
            /*
            //Update scenario
            inv1.Funds_Cheque__c = true;
            inv1.Type__c= 'invested';
            inv1.SAF_soft_copy__c = true;
            inv1.Transfer_Deed_hard_copy__c = true;
            inv1.Transfer_Deed_soft_copy__c = true;
            inv1.Investor_Type__c = 'Via AIF';
            inv1.Call_For_Money_Sent__c = false;
            inv1.Issue_Type__c ='Friends & Family T1';
            update inv1;
            System.debug('invTrigger Investment Update....18' + inv1);
        
            inv2.Sent_Email_Communications__c = inv2.Type__c;
            update inv2;
            System.debug('invTrigger Investment Update....19' + inv2);
            inv3.Is_Primary__c = true;
            update inv3;
            System.debug('invTrigger Investment Update....20' + inv3);
         */   
        
        test.stopTest();
    }
    
    static testMethod void investmentBatchSync()
    {
            Test.startTest();
            user ur = TestFactory.CreateUser();
            ur.ProfileId = Userinfo.getProfileId();
            insert ur;
            Startup_Round__c strObj = [select id,name from Startup_Round__c limit 1];
            List<Account> accList = [Select id,Personal_Email__c,Preferred_Email__c,Membership_status__c,Membership_Validity__c,Email_Id_cc__c,Relationship_Manager__c,Primary_Contact__c from account];
            //Contribution_Agreement__c conAgree = [SELECT Id FROM Contribution_Agreement__c LIMIT 1];
        
            List<Investment__c> invList = new list<Investment__c >();       
            Investment__c inv1 = TestFactory.createInvestment(accList[0].Id); 
            inv1.Investor_s_PAN__c = '**********'; 
            inv1.Startup_Round_Name__c = strObj.Name;
            inv1.Investor_Type__c ='Via Platform';
            //Added for lead tracker.
            inv1.Investment_Amount__c = 10;
            inv1.Number_Of_Shares__c =25;
            inv1.Type__c = 'Invested';
            inv1.Investment_Amount_Due__c = 20;
            inv1.Date_of_FnF_CFM__c = date.newInstance(2022, 07, 23);
            inv1.Call_For_Money_Sent__c = true;
            inv1.Issue_Type__c ='Primary';
            inv1.Intransfer_balance_share__c = false;
            inv1.IPVFnF_Shadow_Balance_share__c = true;
            inv1.Carry_fee_received__c = 10;
            inv1.Startup_Converted__c = true;
            inv1.IRR_Value__c = 10;
            inv1.SAF_soft_copy__c = true;
            
            //inv.Startup_Round__c = strObj.Id;
            invList.add(inv1);

            insert invList;

            InvestmentBatchSync batchJob = new InvestmentBatchSync();
            Id batchId = Database.executeBatch(batchJob , 50);
            Test.stopTest();
    }
    
    static testMethod void invPointsTest()
    {
            test.startTest();
            user ur = TestFactory.CreateUser();
            ur.ProfileId = Userinfo.getProfileId();
            insert ur;
            Startup_Round__c strObj = [select id,name , Startup__c from Startup_Round__c limit 1];
            List<Account> accList = [Select id,Personal_Email__c,Preferred_Email__c,Membership_status__c,Membership_Validity__c,Email_Id_cc__c,Relationship_Manager__c,Primary_Contact__c from account];
            Contribution_Agreement__c conAgree = [SELECT Id FROM Contribution_Agreement__c LIMIT 1];
        
            List<Investment__c> invList = new list<Investment__c >();       
            Investment__c inv1 = TestFactory.createInvestment(accList[0].Id); 
            inv1.Investor_s_PAN__c = '**********'; 
            inv1.Startup_Round_Name__c = strObj.Name;
            inv1.Investment_Amount__c = 10;
            inv1.Number_Of_Shares__c =25;
            inv1.Investment_Amount_Due__c = 20;
            inv1.Date_of_FnF_CFM__c = date.newInstance(2022, 07, 23);
            inv1.Call_For_Money_Sent__c = true;
            inv1.Issue_Type__c ='Primary';
            inv1.Type__c = 'Back out unapproved';
            inv1.Intransfer_balance_share__c = false;
            inv1.IPVFnF_Shadow_Balance_share__c = true;
            inv1.Startup_Round__c = strObj.Id;
            invList.add(inv1);
            
            insert invList;

            System.debug('invTrigger Investment insert....16' + invList);
        
            //Added by karan Update scenario 
            accList[0].Membership_status__c = 'On Trial';
            accList[0].Membership_Validity__c = Date.newInstance(2023, 04, 21);
            update accList;
            accList[1].Membership_status__c = 'IPV Team';
            accList[1].Membership_Validity__c = Date.newInstance(2023, 04, 21);
            update accList;
            System.debug('invTrigger Acct Update....17' + accList);
            test.stopTest();
            
    }
    
    //InvestmentRestAPIController
    static testMethod void invAPITest()
    {
        Test.setMock(HttpCalloutMock.class, new RestMock());
        test.startTest();
        
            user ur = TestFactory.CreateUser();
            ur.ProfileId = Userinfo.getProfileId();
            insert ur;
            Startup_Round__c strObj = [select id,name from Startup_Round__c limit 1];
        //     strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 05, 02);
        //     strObj.Date_of_sending_out_call_for_money__c = Date.newInstance(2022,08,05);
        //     strObj.Send_Auto_Comms_Email__c = true;
        //   strObj.Issue_Price__c = 14028.00;
        //   strObj.Issue_Price_FnF__c = 1428.00;
        //   strObj.Round_Type__c = 'Internal Transfers';
        //   strObj.Old_Issue_Price__c = 148.00;
        //   strObj.Old_Type_of_Shares__c = 'CCD';
        //   strObj.Old_Issue_Price__c = 123;
        //   strObj.Currency__c ='USD';
        //   update strObj;   
            
            List<Account> accList = [Select id,Personal_Email__c,Preferred_Email__c,Membership_status__c,Membership_Validity__c,Email_Id_cc__c,Relationship_Manager__c,Primary_Contact__c from account];
            List<Contact> conList = [select id from contact];
            Boolean callWhatsappAPI = false;
            List<Investment__c> invList = new list<Investment__c >();       
            Investment__c inv = TestFactory.createInvestment(accList[2].Id); 
             
            inv = TestFactory.createInvestment(accList[2].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_in_Own_Name_Family_Member__c = 'LLP';
            inv.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
            inv.Is_Primary__c=true;
            inv.Investor_Type__c = 'Via AIF';
            inv.Type__c= 'Invested';
            inv.Issue_Type__c = 'Primary';
            inv.SAF_soft_copy__c = false;
          inv.Number_Of_Shares__c = 25;
            inv.Investor__c = conList[0].ID; //System.Label.Investor_IPV_Advisors_Pvt_Ltd;
            inv.Carry_fee_received__c = 123;
          inv.Startup_Converted__c = true;
          inv.Date_of_Gross_Gain_Loss_starts__c = Date.newInstance(2023, 04, 21);
          inv.Investment_Fee_Received__c = 0908;
          inv.Date_of_transaction__c = Date.TODAY();
          inv.Residential_Status__c ='Resident';
          inv.Share_Certificates_Scan_Sent__c = 'yes';
          inv.Share_Certificates_Delivered_Hard_Copy__c ='Exited';
          inv.Share_Certificates_Tracking_ID__c = 'yes';
          invList.add(inv);
        
          Investment__c inv2 = TestFactory.createInvestment(accList[2].Id); 
             
            inv2 = TestFactory.createInvestment(accList[2].Id); 
            inv2.Investor_s_PAN__c = '**********'; 
            inv2.Startup_Round_Name__c = strObj.Name;
            inv2.Investment_in_Own_Name_Family_Member__c = 'LLP';
            inv2.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
            inv2.Is_Primary__c=true;
            inv2.Investor_Type__c = 'Via AIF';
            inv2.Type__c= 'Exit';
            inv2.Issue_Type__c = 'Primary';
            inv2.SAF_soft_copy__c = false;
          inv2.Number_Of_Shares__c = 25;
            inv2.Investor__c = conList[0].ID; //System.Label.Investor_IPV_Advisors_Pvt_Ltd;
            inv2.Carry_fee_received__c = 123;
          inv2.Startup_Converted__c = true;
          inv2.Date_of_Gross_Gain_Loss_starts__c = Date.newInstance(2023, 04, 21);
          inv2.Investment_Fee_Received__c = 0908;
          inv2.Date_of_transaction__c = Date.TODAY();
          inv2.Residential_Status__c ='Resident';
          inv2.Share_Certificates_Scan_Sent__c = 'yes';
          inv2.Share_Certificates_Delivered_Hard_Copy__c ='Exited';
          inv2.Share_Certificates_Tracking_ID__c = 'yes';
          inv2.Date_of_Allotment__c = Date.TODAY();
          inv2.Final_Commitment_Amount__c = 0987;
          inv2.Exit_Type__c = null;
          inv2.Exit_Date__c= Date.TODAY();
          inv2.Old_Date_of_Gross_Gain_Loss_starts__c = Date.TODAY();
          inv2.Old_Date_of_Allotment__c =Date.Today() - 21;
          inv2.Old_Number_of_Shares__c = 123;
          inv2.Old_Number_of_Units_Held__c = 12;
          inv2.Old_Investment_Amount_Remitted__c = 998877;
          inv2.IRR_Value__c = 20.25;
          inv2.Funds_Cheque__c = TRUE;
          inv2.SAF_soft_copy__c = TRUE;
          inv2.PAS_4_Investor_Bankdetails_soft_copy__c = TRUE;
          inv2.Investment_Amount__c= 5643;
          inv2.Investment_AIF_Class__c = 'Class D';
          inv2.Exit_Fee_received__c = 123;
          inv2.Exit_amount_transferred__c = 123;
          
          invList.add(inv2);
        
          Investment__c inv3 = TestFactory.createInvestment(accList[2].Id); 
             
          inv3 = TestFactory.createInvestment(accList[2].Id); 
          inv3.Investor_s_PAN__c = '**********'; 
          inv3.Startup_Round_Name__c = strObj.Name;
          inv3.Investment_in_Own_Name_Family_Member__c = 'LLP';
          inv3.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
          inv3.Is_Primary__c=true;
          inv3.Investor_Type__c = 'Via AIF';
          inv3.Type__c= 'Invested';
          inv3.Issue_Type__c = 'Primary';
          inv3.SAF_soft_copy__c = false;
          inv3.Number_Of_Shares__c = 25;
          inv3.Investor__c = conList[0].ID; //System.Label.Investor_IPV_Advisors_Pvt_Ltd;
          inv3.Carry_fee_received__c = 123;
          inv3.Startup_Converted__c = False;
          inv3.Date_of_Gross_Gain_Loss_starts__c = Date.newInstance(2023, 04, 21);
          inv3.Investment_Fee_Received__c = 0908;
          inv3.Date_of_transaction__c = Date.TODAY();
          inv3.Residential_Status__c ='Resident';
          inv3.Share_Certificates_Scan_Sent__c = 'yes';
          inv3.Share_Certificates_Delivered_Hard_Copy__c ='Exited';
          inv3.Share_Certificates_Tracking_ID__c = 'yes';
          inv3.Date_of_Allotment__c = Date.TODAY();
          inv3.Final_Commitment_Amount__c = 0987;
          inv3.Exit_Type__c = null;
          inv3.Exit_Date__c= Date.TODAY();
          inv3.Old_Date_of_Gross_Gain_Loss_starts__c = Date.TODAY();
          inv3.Old_Date_of_Allotment__c =Date.Today() - 21;
          inv3.Old_Number_of_Shares__c = 123;
          inv3.Old_Number_of_Units_Held__c = 12;
          inv3.Old_Investment_Amount_Remitted__c = 998877;
          inv3.IRR_Value__c = 20.25;
          inv3.Funds_Cheque__c = TRUE;
          inv3.SAF_soft_copy__c = TRUE;
          inv3.PAS_4_Investor_Bankdetails_soft_copy__c = TRUE;
          inv3.Investment_Amount__c= 5643;
          inv3.Investment_AIF_Class__c = 'Class D';
          inv3.Exit_Fee_received__c = 123;
          inv3.Exit_amount_transferred__c = 123;
          
          invList.add(inv3);
          
            insert invList;
            System.debug('invAPITest Investment insert....26' + invList);
          
          // Call future method for Insert
        Set<Id> investmentIds = new Set<Id>();
        for (Investment__c investment : invList) {
            investmentIds.add(investment.Id);
          }
        InvestmentRestAPIController.sendInvestmentDetails(investmentIds, true);
            //Added by karan Update scenario
            accList[2].Membership_status__c = 'On Trial Community';
            accList[2].Membership_Validity__c = Date.newInstance(2023, 04, 21);
            //accList[2].Relationship_Manager__r.Email = '<EMAIL>';
            update accList;
            
            System.debug('invTrigger Acct Update....27' + accList);
            accList[1].Membership_status__c = 'On Trial';
            accList[1].Membership_Validity__c = Date.newInstance(2023, 04, 21);
            update accList;
            System.debug('invAPITest Acct update....28' + accList);
            
            callWhatsappAPI = true;
            List<Investment__c> InvesList1 = new List<Investment__c>();
            List<Investment__c> InvesList2 = new List<Investment__c>();
            List<Investment__c> InvesList = [SELECT Id, Name,Issue_Type__c, IPV_Fees_Cheque__c,Reason_for_waitlist__c,Type__c, Membership_status__c FROM Investment__c WHERE Id IN: invList];
       
            System.debug('invAPITest Investment update....30' + InvesList2);
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();

            inv.IPV_Fees_Cheque__c = 'yes';
            inv.Issue_Type__c = 'IPV HQ - Shadow';
          inv.Type__c= 'Committed';
            //inv.Type__c = 'Back out approved';
            update inv;         
            System.debug('invAPITest investment update....31' + inv); 
           // Call future method for Update
        investmentIds.clear(); // Reset the set
           for (Investment__c investment : invList) {
            investmentIds.add(investment.Id);
             }
        InvestmentRestAPIController.sendInvestmentDetails(investmentIds, false);
        
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
      
          inv.IPV_Fees_Cheque__c = 'yes';
            inv.Issue_Type__c = 'IPV HQ - Shadow';
          inv.Date_of_Payment__c = Date.newInstance(2023, 04, 21);
            inv.Type__c= 'Committed';
            update inv;
             // Call future method for Update
        investmentIds.clear(); // Reset the set
           for (Investment__c investment : invList) {
            investmentIds.add(investment.Id);
             }
        InvestmentRestAPIController.sendInvestmentDetails(investmentIds, false);
            System.debug('invAPITest Investment update....31' + InvesList2);
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            invList[0].Type__c= 'Round Closed - Commitment Released';
            update invList;
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            invList[0].Type__c= 'Invested';
            update invList;
        	
        	InvestmentBatchSync batchJob = new InvestmentBatchSync();
			Id batchId = Database.executeBatch(batchJob, 50);
            
        	test.stopTest();
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            invList[0].Type__c= 'Committed';
            update invList;
          
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            invList[0].Type__c= 'Waitlist';
            update invList;
  
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
          invList[0].Type__c= 'Round closed - deal dropped';
            update invList;
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            invList[0].Type__c= 'Waitlist';
            invList[0].Reason_for_waitlist__c= 'IPV Fee Pending';
            
            update invList;
            
            System.debug('invAPITest Investment update....32' + InvesList2);
        
        
        delete invList;

    }
  
    
    static testMethod void invAPITestEmail()
    {
        Test.setMock(HttpCalloutMock.class, new RestMock());
        
        
            user ur = TestFactory.CreateUser();
            ur.ProfileId = Userinfo.getProfileId();
            insert ur;
            Startup_Round__c strObj = [select id,name from Startup_Round__c limit 1];
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 05, 02);
            strObj.Date_of_sending_out_call_for_money__c = Date.newInstance(2022,08,05);
            strObj.Send_Auto_Comms_Email__c = true;
            update strObj;   
            
            List<Account> accList = [Select id,Personal_Email__c,Preferred_Email__c,Membership_status__c,Membership_Validity__c,Email_Id_cc__c,Relationship_Manager__c,Primary_Contact__c from account];
            List<Contact> conList = [select id from contact];
            Boolean callWhatsappAPI = false;
            List<Investment__c> invList = new list<Investment__c >();       
            Investment__c inv = TestFactory.createInvestment(accList[2].Id); 
             
            inv = TestFactory.createInvestment(accList[2].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_in_Own_Name_Family_Member__c = 'LLP';
            inv.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
            inv.Is_Primary__c=true;
            inv.Investor_Type__c = 'Via AIF';
            inv.Type__c= 'Waitlist';
            inv.Issue_Type__c = 'Primary';
            inv.SAF_soft_copy__c = false;
            inv.Investor__c = conList[0].ID; //System.Label.Investor_IPV_Advisors_Pvt_Ltd;
            
            invList.add(inv);
            insert invList;
             
            
            callWhatsappAPI = true;
                         
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();

            accList[2].Membership_status__c = 'Paid IPV Fee';
            accList[2].Membership_Validity__c = Date.newInstance(2023, 11, 21);
            accList[2].Date_of_Payment__c = Date.newInstance(2023, 11, 10);
            
            update accList;
            
            test.startTest();            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            inv.Type__c= 'Committed';
            inv.Residential_Status__c= 'NRI';
            inv.Issue_Type__c = 'Primary';
            update inv;

            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            inv.Type__c= 'Waitlist';
            inv.Reason_for_waitlist__c = 'Over Subscription';
            update inv;
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            inv.Type__c= 'Committed';
            inv.Issue_Type__c = 'Friends & Family T1';
            update inv;
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            inv.Type__c= 'Invested';
            inv.Issue_Type__c = 'Friends & Family T1';
            update inv;
            
            test.stopTest();
            
        delete invList;
    }
    
    static testMethod void invAPITestEmail1()
    {
        Test.setMock(HttpCalloutMock.class, new RestMock());
        test.startTest();
        
            user ur = TestFactory.CreateUser();
            ur.ProfileId = Userinfo.getProfileId();
            insert ur;
            Startup_Round__c strObj = [select id,name from Startup_Round__c limit 1];
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 05, 02);
            strObj.Date_of_sending_out_call_for_money__c = Date.newInstance(2022,08,05);
            strObj.Send_Auto_Comms_Email__c = true;
            update strObj;   
            
            List<Account> accList = [Select id,Personal_Email__c,Preferred_Email__c,Membership_status__c,Membership_Validity__c,Email_Id_cc__c,Relationship_Manager__c,Primary_Contact__c from account];
            List<Contact> conList = [select id from contact];
            Boolean callWhatsappAPI = false;
            List<Investment__c> invList = new list<Investment__c >();       
            Investment__c inv = TestFactory.createInvestment(accList[2].Id); 
             
            inv = TestFactory.createInvestment(accList[2].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_in_Own_Name_Family_Member__c = 'LLP';
            inv.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
            inv.Is_Primary__c=true;
            inv.Investor_Type__c = 'Via AIF';
            inv.Type__c= 'Waitlist';
            inv.Issue_Type__c = 'Primary';
            inv.SAF_soft_copy__c = false;
            inv.Investor__c = conList[0].ID; //System.Label.Investor_IPV_Advisors_Pvt_Ltd;
            
            invList.add(inv);
            insert invList;
             
            
            callWhatsappAPI = true;
                         
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();

            accList[2].Membership_status__c = 'Paid IPV Fee';
            accList[2].Membership_Validity__c = Date.newInstance(2023, 11, 21);
            accList[2].Date_of_Payment__c = Date.newInstance(2023, 11, 10);
            
            update accList;
             
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            inv.Type__c= 'Waitlist';
            inv.Reason_for_waitlist__c = 'Unapproved Back Outs';
            update inv;
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            inv.Type__c= 'Committed';
            inv.Issue_Type__c = 'Friends & Family T2';
            update inv;
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            inv.Type__c= 'Invested';
            inv.Issue_Type__c = 'Friends & Family T2';
            inv.IPV_Fees_Cheque__c = 'Yes';
            update inv;
            
            RecursiveHandler.invIdsEmailNotification = new Set<Id>();
            inv.Type__c= 'Waitlist';
            inv.Reason_for_waitlist__c = 'Pending Documents';
            update inv;
            test.stopTest();
            
        delete invList;
    }
    
    static testMethod void invEXITTest()
    {
        Test.setMock(HttpCalloutMock.class, new RestMock());
        test.startTest();
      
            List<Account> accList = [Select id,Personal_Email__c,Preferred_Email__c,Membership_status__c,Membership_Validity__c,Email_Id_cc__c,Relationship_Manager__c,Primary_Contact__c from account];
            List<Contact> conList = [select id from contact];
              
            Startup__c stObj = [select id from Startup__c limit 1];
                        
            
            Startup_Round__c strObj = [select id,name,Date_of_sending_out_call_for_money_AIF__c from Startup_Round__c limit 1];
            strObj.Date_of_Investor_Call__c = Date.newInstance(2020, 05, 02);
            strObj.Date_of_sending_out_call_for_money_AIF__c = Date.newInstance(2020, 05, 02);
            strObj.Send_Auto_Comms_Email__c = true;
            strObj.Date_Of_Exit__c = Date.newInstance(2022, 05, 02);
            strObj.Date_of_sending_out_call_for_money__c = Date.newInstance(2022,08,05);

            update strObj;   
            System.debug('strObj>>>>>12>>'+strObj.Date_of_sending_out_call_for_money_AIF__c);
            System.debug('strObj>>>>>12>>'+strObj);
            
            List<Investment__c> invList = new list<Investment__c >();       
            Investment__c inv = TestFactory.createInvestment(accList[2].Id); 
             
                        
            inv = TestFactory.createInvestment(accList[2].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_in_Own_Name_Family_Member__c = 'LLP';
            inv.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
            inv.Is_Primary__c=false;
        	inv.Number_Of_Shares__c = 100;
            inv.Investor_Type__c = 'Via AIF';
            inv.Type__c= 'Invested';
            inv.Issue_Type__c = 'Friends & Family T1 - AIF';
            inv.Investor_Type__c= 'Via AIF';
            inv.Exit_Date__c = Date.newInstance(2022, 05, 02);
            inv.Startup_Round__c = strObj.id;
            insert inv;
        
            Investment__c inv1 = TestFactory.createInvestment(accList[2].Id); 
            inv1.Investor_s_PAN__c = '**********'; 
            inv1.Startup_Round_Name__c = strObj.Name;
            inv1.Investment_in_Own_Name_Family_Member__c = 'LLP';
            inv1.Primary_Holder_Contact__c= accList[2].Primary_Contact__c;
            inv1.Is_Primary__c=false;
            inv1.Investor_Type__c = 'Via AIF';
            inv1.Type__c= 'Exit';
            inv1.Parent_Investment__c = inv.Id;
        	inv1.Number_Of_Shares__c = 10;
            inv1.Issue_Type__c = 'Friends & Family T1 - AIF';
            inv1.Investor_Type__c= 'Via AIF';
        	inv1.IRR_Value__c = 10;
            inv1.Exit_Date__c = Date.newInstance(2022, 05, 02);
            inv1.Startup_Round__c = strObj.id;
            invList.add(inv1);
            
            insert invList;
            
            accList[2].Preferred_Email__c = 'Personal';
            accList[2].Personal_Email__c = '<EMAIL>';
            update accList[2];
            
        test.stopTest();
        Set<Id> invIdSet = new Set<Id>();
        invIdSet.add(inv1.Id);
        
        
    }
    
    static testMethod void emailBulkTest()
    {
        Test.setMock(HttpCalloutMock.class, new RestMock());
        test.startTest();
        
        Account acc = [select id from account limit 1];
        User systemAdminUser = [SELECT Id, ProfileId, Profile.Name , Name,Email FROM User WHERE Profile.Name = 'System Administrator' and isactive = true LIMIT 1];

        Case cs1 = new Case();
        cs1.Issue_raised_By__c = acc.Id;
        cs1.Priority = 'High';
        cs1.Status ='WIP';
        cs1.Date_Issue_Raised__c = date.today();
        cs1.Description = 'test Case1';
        cs1.Relevant_Team__c = 'Tech Related';
        cs1.Date_of_issue_assigned_Internal__c = date.today()-2 ;
        cs1.Responsibility_to_solve_Internal__c = systemAdminUser.Id;
        cs1.Due_date_for_closure__c = date.today()+1;
        cs1.Complaint_Type__c = 'Information/ Guidance';
        insert cs1;
        
        List<Case> cNewList = new List<Case>();
        cNewList.add(cs1);
         
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
        EmailUtility eCls = new EmailUtility();

        Id templateId = EmailUtility.getTemplateId('CASEASSIGNMENT');
        Id conId = [select id, Email from Contact limit 1].Id;

        List<String> ccAddrList = new List<String>();
        List<String> toAddrList = new List<String>();
        
        ccAddrList.add('<EMAIL>');
        toAddrList.add('<EMAIL>');
        
        system.debug('cs1.Id>>>>>>>>>'+cs1.Id);
        system.debug('templateId>>>>>>>>>'+templateId);
        system.debug('toAddrList>>>>>>>>>'+toAddrList);
        system.debug('ccAddrList>>>>>>>>>'+ccAddrList);
        system.debug('ccAddrList>>>>>>>>>'+ccAddrList);
        email = eCls.createMailTemplateObj(cs1.Id,templateId,toAddrList,ccAddrList,null,null,conId );
        emails.add(email );
        //system.debug('emails>>>>>>>>>'+emails);
        eCls.sendEmailBulk(emails);
        
    }
     
}