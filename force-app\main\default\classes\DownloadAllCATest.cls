@isTest
public class DownloadAllCATest {
    public static testmethod void testSendMail(){
        Contact con = new Contact();
        con.FirstName='Test';
        con.LastName='Test';
        con.Investor_s_PAN__c = '**********';
        con.AIF_Contributor__c = true;
        //contList.add(Cont);
        insert con; 
        Contribution_Agreement__c ca = new Contribution_Agreement__c();
        CA.investor1__c =  con.id;
        ca.Virtual_Account_Number__c = '********';
        insert ca;
        Contribution_Agreement__c ca1 = new Contribution_Agreement__c();
        CA1.investor1__c =  con.id;
        insert ca1;      
        Contribution_Agreement__c caObj = [select id,name from Contribution_Agreement__c limit 1];
        DownloadAllCA controller = new DownloadAllCA();
        controller.csvFileBody = Blob.valueOf('CA,fromdate,Todate\r'+caObj.Name+',01/01/2021,01/01/2023\r'+caObj.Name+'\r');
        controller.downloadAll();            
    }
}