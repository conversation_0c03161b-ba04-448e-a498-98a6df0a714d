@isTest
public class CADetailsOnTableTest {
    
    @isTest
    public static void caDetailsOnAccountObject(){

        Account account1 = new Account();
        account1.Name = 'testing1';
        account1.ShippingStreet       = '1 Main St.';
        account1.ShippingState        = 'VA';
        account1.ShippingPostalCode   = '12345';
        account1.ShippingCountry      = 'USA';
        account1.ShippingCity         = 'Anytown';
        account1.Description          = 'This is a test account';
        account1.BillingStreet        = '1 Main St.';
        account1.BillingState         = 'VA';
        account1.BillingPostalCode    = '12345';
        account1.BillingCountry       = 'USA';
        account1.BillingCity          = 'Anytown';
        account1.AnnualRevenue        = 10000;
        account1.ParentId = null;
        account1.Official_Email__c = '<EMAIL>';
        account1.Title__c= 'Mr';
        account1.Date_of_Addition__c= date.newInstance(2020, 9, 15);
        account1.Membership_Validity__c= date.newInstance(2020, 9, 15);
        account1.Primary_Country_Code__c= 91;
        account1.Lead_Source__c= 'Others';
        account1.Preferred_Email__c= 'Personal';
        account1.Personal_Email__c = '<EMAIL>';
        account1.Secondary_Contact__c ='*********';
        account1.Official_Email__c ='<EMAIL>';
      	account1.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        Integer len = 10;
        String str = string.valueof(Math.abs(Crypto.getRandomLong()));
        String randomNumber =  str.substring(0, len);
        account1.Primary_Contact__c= ''+randomNumber ;
        account1.Primary_Group_Name__c = 'Trial 10';
        account1.Membership_Status__c = 'Platinum';
        account1.Relationship_Manager__c = UserInfo.getUserId();
        account1.Are_They_Part_of_Other_Platform__c = 'Yes';
        account1.App_signup_date__c = Date.Today();
        account1.Referred_leads__c = 0;
        account1.Total_Number_of_Founder_Calls__c = 4;
        account1.Total_Number_of_Investor_Calls__c = 5;
        account1.Total_Numbe_of_Offline_Online_event__c = 3;
        account1.College_Tier__c = 'Tier 1';
        account1.Designation_Band__c = 'Band 1';
        account1.Bot_Input__c = 'Interacted -Chat with RM';
        account1.lead_Source__c = 'Referral'; 
        insert account1;

        Startup__c startup = new Startup__c();
        startup.Public_Name__c = 'Demo Startup';
        startup.Legal_Name__c = 'Demo Testing';
        insert startup;
        
        Startup_Round__c startupRound = new Startup_Round__c();
        startupRound.Lead_Analyst__c = account1.Id;
        startupRound.Lead_Member__c = account1.Id;
        startupRound.Startup__c = startup.Id;
        startupRound.Date_of_Investor_Call__c = Date.Today();
        startupRound.Date_Of_Founders_Call__c = date.newInstance(2020, 9, 15);
        startupRound.Pre_Money_Valuation__c = 10;
        startupRound.Doi_Percent_Fee__c = 11;
        startupRound.Doi_Percent_Equity__c = 12;
        
        insert startupRound;

        Contact con = new Contact();
        con.FirstName = 'Test';
        con.LastName = 'Test';
        con.AccountId = account1.Id;
        con.Investor_s_PAN__c = '**********';
        con.Document_Type__c = 'PAN';
        con.AIF_Contributor__c = true;
        con.Residential_Status__c = 'NRI';
        insert con;

        Contact con2 = new Contact();
        con2.FirstName = 'Test';
        con2.LastName = 'Test';
        con2.AccountId = account1.Id;
        con2.Investor_s_PAN__c = '**********';
        con2.Document_Type__c = 'PAN';
        con2.AIF_Contributor__c = true;
        con2.Residential_Status__c = 'NRI';
        insert con2;

        Investment__c inv1 = new Investment__c();
        inv1.Account__c = account1.Id;
        inv1.Type__c = 'Invested';
        inv1.Investor_Name__c = 'test inv name1';
        inv1.Investor__c = System.Label.Investor_IPV_Advisors_Pvt_Ltd;
        inv1.Investment_Amount__c = 1000;
        inv1.Investor_Type__c = 'Via LLP';
        inv1.Investor_s_PAN__c = '**********';
        inv1.Startup_Round__c = startupRound.Id;
        inv1.Currency__c = 'INR';
        insert inv1;

        Fund_Module__c fnc = new Fund_Module__c();
        fnc.Name = 'test fund module';
        insert fnc;


        Contribution_Agreement__c conAgree = new Contribution_Agreement__c();
        conAgree.Investor1__c = con.Id;
        conAgree.Fund_Onboarded_on__c = fnc.Id;
        conAgree.RecordTypeId = Schema.SObjectType.Contribution_Agreement__c.getRecordTypeInfosByName().get('Joint').getRecordTypeId();
        conAgree.Investor2__c = con2.Id;
        insert conAgree;

        Id accountId = account1.Id;
        Id investorId = con.Id;

        Test.startTest();
        List<CADetailsOnTable.ContributionAgreementWrapper> accountResult = CADetailsOnTable.getContributionAgreementsForAccountObject(accountId);
        List<CADetailsOnTable.ContributionAgreementWrapper> investorResult = CADetailsOnTable.getContributionAgreementsForInvestorObject(investorId);
        Test.stopTest();
    
    }
}