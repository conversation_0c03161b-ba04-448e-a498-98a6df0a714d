public class importDataFromCSVController {

    public String selectedObj { get; set;}
    public Blob csvFileBody{get;set;}
    public string csvAsString{get;set;}
    public String[] csvRowValues{get;set;}
    public List<account> acclist{get;set;}
    public map<String,list<Data_Import_Mapping__c>> dataImportMap;
    public string TextConst = 'STRING';
    public string NumberConst = 'DOUBLE';
    public string DateConst = 'DATE';
    public string DateTimeConst = 'DATETIME';
    public string CheckboxConst = 'BOOLEAN';  
    public string lookupConst = 'LOOKUP';  
    public List<SelectOption> ObjOptions{get;set;}
    Map<String,string> lookupMap = new Map<String,string>();

    public importDataFromCSVController(){
        csvRowValues = new String[]{};
        acclist = New List<Account>(); 
        ObjOptions = new List<SelectOption>();
        
        dataImportMap = new map<String,list<Data_Import_Mapping__c>>();
        ObjOptions.add(new SelectOption('','-- None --'));
        
        for(Data_Import_Mapping__c dimp : [select id,name,Related_Object_API_Name__c,Related_Object_Unique_Field_API_Name__c,CSV_Column_Header__c,Target_sObject_API_Name__c,Target_Field_Data_Type__c from Data_Import_Mapping__c limit 10000])
        {
            String objName = dimp.Target_sObject_API_Name__c;
            objName = objName.trim().toUpperCase();
            
            if(!dataImportMap.containsKey(objName))
            {
                dataImportMap.put(objName, new List<Data_Import_Mapping__c>());
            }
            dataImportMap.get(objName).add(dimp);
        }
        
        system.debug('dataImportMap>>>'+dataImportMap);
        
        for(String dim : dataImportMap.keyset())
        {
            ObjOptions.add(new SelectOption(''+dim,''+dim));
        }
 
 
    }
    
    public void importCSVFile(){
        //try{
            
            if(selectedObj!='' && selectedObj!=null && csvFileBody!=null)
            {
                createAccount(csvFileBody,selectedObj);
                Import_Wizard_History__c imh = new Import_Wizard_History__c(Uploaded_For_Object__c=''+selectedObj);
                insert imh;
                
                Attachment att=new Attachment();
                att.Body = csvFileBody;
                att.Name = csvAsString;
                att.parentId = imh.id;
                insert att; 
                
                ApexPages.Message errorMessage = new ApexPages.Message(ApexPages.severity.CONFIRM,'You records for '+selectedObj+' has been created..');
                ApexPages.addMessage(errorMessage);             
            }
            else
            {
                ApexPages.Message errorMessage = new ApexPages.Message(ApexPages.severity.ERROR,'Please Select The Object And Choose The File...');
                ApexPages.addMessage(errorMessage);
            }
      /* }
        catch (Exception e)
        {
            system.debug('Error Message>>>'+e.getMessage());
            ApexPages.Message errorMessage = new ApexPages.Message(ApexPages.severity.ERROR,'An error has occured while importin data Please make sure input csv file is correct');
            ApexPages.addMessage(errorMessage);
        }
        */ 
    }
    
    public void createAccount(Blob csvFileBody,String selObjName){
        Map<Integer,String> fieldNumberMap = new Map<Integer,String>();
        Map<String,list<string> > phoneMap = new Map<String,list<string>>();
        List<String> lstFieldNames = new list<String>();
        Integer fieldNumber;
        String fieldValue;
        //Blob csvFileBody;
        //map<String,list<Data_Import_Mapping__c>> dataImportMap = new map<String,list<Data_Import_Mapping__c>>();
        map<String,Data_Import_Mapping__c> dataImportFieldMap = new map<String,Data_Import_Mapping__c>();
        map<String,set<String>> lookupFieldsDataMap = new map<String,set<String>>();

        String csvFullData;

        //Applied below logic to avoid "BLOB is not a valid UTF-8 string" || csvFullData = ''+csvFileBody.toString();
        //csvFullData = ''+csvFileBody.toString();
        csvFullData = blobToString( csvFileBody,'ISO-8859-1');
        system.debug('csvFullData>>>>'+csvFullData);
                        
        String[] csvRowValues = csvFullData.split('\n');
        string[] headerNames = csvRowValues[0].split(',');
        system.debug('csvRowValues>>>>'+csvRowValues);
        system.debug('headerNames>>>>'+headerNames);
        system.debug('headerNames>>>>'+headerNames.size());
/*
        for(Data_Import_Mapping__c dimp : [select id,name,CSV_Column_Header__c,Target_sObject_API_Name__c,Target_Field_Data_Type__c from Data_Import_Mapping__c limit 10000])
        {
            String objName = dimp.Target_sObject_API_Name__c;
            objName = objName.trim().toUpperCase();
            
            if(!dataImportMap.containsKey(objName))
            {
                dataImportMap.put(objName, new List<Data_Import_Mapping__c>());
            }
            dataImportMap.get(objName).add(dimp);
        }
*/                
        for(Data_Import_Mapping__c dimp1 : dataImportMap.get(selObjName))
        {
            String clmStr = dimp1.CSV_Column_Header__c;
            clmStr = clmStr.trim().removeEnd(' ').removeStart(' ').toUpperCase();
            dataImportFieldMap.put(clmStr,dimp1);
            
            if(dimp1.Target_Field_Data_Type__c =='LOOKUP')
            {
                lookupFieldsDataMap.put(clmStr,new set<String>());
            }
        }

        System.debug(LoggingLevel.INFO, 'headerNames>>>>'+headerNames);
        System.debug(LoggingLevel.INFO, 'dataImportFieldMap>>>>'+dataImportFieldMap);

        for(Integer i = 0; i < headerNames.size(); i++)
        {
            String hName = headerNames[i];
            System.debug('hName1>>>>'+hName);
            hName = hName.trim().removeEnd(' ').removeStart(' ').toUpperCase();
            System.debug('hName2>>>>'+hName);
            fieldNumberMap.put(i,hName);
        }
        
        System.debug(LoggingLevel.INFO, 'fieldNumberMap>>>>'+fieldNumberMap);
        List<sObject> listAccount = new List<sObject>();
                
        for (Integer i = 1; i < csvRowValues.size(); i++)
        {
            system.debug('csvRowValues>>>>'+csvRowValues[i]);
                                
            string[] csvRecordData = csvRowValues[i].split(',');
            system.debug('csvRecordData>>>>'+csvRecordData);
            for(integer k =0; k <csvRecordData.size(); k++)
            { 
                if(String.IsNotBlank(''+csvRecordData[k].trim().removeEnd(' ').removeStart(' ')) && lookupFieldsDataMap!=null && lookupFieldsDataMap.containsKey(fieldNumberMap.get(k)))
                {
                    lookupFieldsDataMap.get(fieldNumberMap.get(k)).add(''+csvRecordData[k].trim().removeEnd(' ').removeStart(' '));
                    system.debug('lookupFieldsDataMap00>>>>'+lookupFieldsDataMap);

                }    
            }
        }
        
        System.debug(LoggingLevel.INFO, 'lookupFieldsDataMap>>>>'+lookupFieldsDataMap);
        set<String> phoneSet = new set<String>();
        String ObjKey = '';
        for(String key : lookupFieldsDataMap.keyset())
        {
            if(lookupFieldsDataMap.get(key)!=null && lookupFieldsDataMap.get(key).size()>0)
            {
                phoneSet.addAll(lookupFieldsDataMap.get(key));
                ObjKey = key;
            }
        }
        
        if(String.isNotBlank(ObjKey))
        {
            String soqlStr = 'select id,'+dataImportFieldMap.get(ObjKey).Related_Object_Unique_Field_API_Name__c+' from '+dataImportFieldMap.get(ObjKey).Related_Object_API_Name__c+' Where '+dataImportFieldMap.get(ObjKey).Related_Object_Unique_Field_API_Name__c+' in : phoneSet' ;
            system.debug('soqlStr>>>>'+soqlStr );
            List<sObject> listObject = Database.query(soqlStr);
            system.debug('listObject>>>>'+listObject);
            
            for(sObject sObj : listObject)
                lookupMap.put(''+sobj.get(dataImportFieldMap.get(ObjKey).Related_Object_Unique_Field_API_Name__c),''+sobj.get('ID'));
            
            
            system.debug('lookupMap>>>>'+lookupMap);
        }
        
        system.debug('csvRowValues000>>>>'+csvRowValues);
        system.debug('csvRowValues000size()>>>>'+csvRowValues.size());
        for (Integer i = 1; i < csvRowValues.size(); i++)
        {
            system.debug('csvRowValues>>>>'+csvRowValues[i]);
                                
            string[] csvRecordData = csvRowValues[i].split(',');
            system.debug('csvRecordData>>>>'+csvRecordData);
              
            //Account acc = new Account();
            sObject acc = createObject(selObjName);
            for(integer k =0; k <csvRecordData.size(); k++)
            {    
                System.debug(LoggingLevel.INFO, 'k>>>>'+k);
                System.debug(LoggingLevel.INFO, 'fieldNumberMap.get>>>>'+fieldNumberMap.get(k));
                system.debug('dataImportFieldMap>>>>'+dataImportFieldMap);
                System.debug(LoggingLevel.INFO, 'dataImportFieldMap values>>>>'+dataImportFieldMap.get(fieldNumberMap.get(k)));
                system.debug('csvRecordData[k]>>>>'+csvRecordData[k]);
                
                if(dataImportFieldMap!=null && dataImportFieldMap.get(fieldNumberMap.get(k))!=null)
                {
                    //acc.put(''+dataImportFieldMap.get(fieldNumberMap.get(k)).Name, ''+csvRecordData[k]);
                    acc = setFieldVal(acc, ''+csvRecordData[k],dataImportFieldMap.get(fieldNumberMap.get(k)));
                    
                }
                system.debug('acc>>>>'+acc);                
            }  
            if(acc!=null)
            {
                //acc.put('Title__c','qwe');
                listAccount.add(acc);
            }
        }  
        system.debug('listAccount>>>>'+listAccount);
        insert listAccount;        
    }
    
    public sObject setFieldVal(SObject sobj, string fieldVal,Data_Import_Mapping__c dataMap){  
        
        string dataType = dataMap.Target_Field_Data_Type__c;
        string fieldAPIName = dataMap.Name;
        fieldVal = fieldVal.trim().removeEnd(' ').removeStart(' ');
        system.debug('fieldAPIName>>'+fieldAPIName + '<<fieldVal>>'+fieldVal+'<<dataType>>'+dataType);
        system.debug('sobj>>'+sobj);
        
        dataType = dataType.toUpperCase();
        system.debug('Updated dataType>>'+dataType);
        
        if(String.isNotBlank(fieldVal))
        {   
        
            if(dataType == lookupConst){
                system.debug('fieldVal>>'+fieldVal);
                if(String.isNotBlank(fieldVal))
                {
                    system.debug('lookupMap>>'+lookupMap);
                    system.debug('lookupMap.get(fieldVal)>>'+lookupMap.get(fieldVal));
    
                    if(lookupMap.get(fieldVal)!=null)                
                        sobj.put(fieldAPIName ,lookupMap.get(fieldVal));
                }
                
            }
            else if(dataType == TextConst || string.isBlank(dataType)){
                sobj.put(fieldAPIName,fieldVal);
            }else if(dataType == NumberConst){
                sobj.put(fieldAPIName,decimal.valueOf(fieldVal));
            }else if(dataType == DateConst){
                
                
                //date parsedDate = date.parse(fieldVal);
                //sobj.put(fieldAPIName,parsedDate);
                if(string.isNotBlank(fieldVal))
                    sobj.put(fieldAPIName,Date.valueOf(fieldVal));
                
            }else if(dataType == DateTimeConst){
                sobj.put(fieldAPIName,DateTime.valueOf(fieldVal));
            }else if(dataType == CheckboxConst){
                sobj.put(fieldAPIName,boolean.valueOf(fieldVal));
            }
            else{
                sobj.put(fieldAPIName,fieldVal);
            }
        }
        return sobj;
    }
    
    public static sObject createObject(string typeName){        
        Schema.SObjectType targetType = Schema.getGlobalDescribe().get(typeName);        
        return targetType.newSObject();        
    }
    
    public static String blobToString(Blob input, String inCharset){
        String hex = EncodingUtil.convertToHex(input);
        System.assertEquals(0, hex.length() & 1);
        final Integer bytesCount = hex.length() >> 1;
        String[] bytes = new String[bytesCount];
        for(Integer i = 0; i < bytesCount; ++i)
            bytes[i] =  hex.mid(i << 1, 2);
        return EncodingUtil.urlDecode('%' + String.join(bytes, '%'), inCharset);
    }         

}