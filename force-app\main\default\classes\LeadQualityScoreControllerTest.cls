@isTest
public class LeadQualityScoreControllerTest {
	
    
    @isTest
    public static void testLeadQualityScore()
    {
        List<Lead__c> lead = listOfLeads();
        
        LeadQualityScoreController.getLeadQualityScore(lead[0].Id);
        LeadQualityScoreController.getLeadQualityScore(lead[1].Id);
		LeadQualityScoreController.getLeadQualityScore(lead[2].Id);
        LeadQualityScoreController.getLeadQualityScore(lead[3].Id);
        LeadQualityScoreController.getLeadQualityScore(lead[4].Id);
        LeadQualityScoreController.getLeadQualityScore(lead[5].Id);
        LeadQualityScoreController.getLeadQualityScore(lead[6].Id);
        LeadQualityScoreController.getLeadQualityScore(lead[7].Id);
        LeadQualityScoreController.getLeadQualityScore(lead[8].Id);
        LeadQualityScoreController.getLeadQualityScore(lead[9].Id);
        LeadQualityScoreController.getLeadQualityScore(lead[10].Id);
    }
    
    public static List<Lead__c> listOfLeads()
    {
     	   
        List<Lead__c> leadList = new List<Lead__c>();
        
        Lead__c lead1 = new Lead__c();
        Lead__c lead2 = new Lead__c();
        Lead__c lead3 = new Lead__c();
        Lead__c lead4 = new Lead__c();
        Lead__c lead5 = new Lead__c();
        Lead__c lead6 = new Lead__c();
        Lead__c lead7 = new Lead__c();
        Lead__c lead8 = new Lead__c();
        Lead__c lead9 = new Lead__c();
        Lead__c lead10 = new Lead__c();
        Lead__c lead11 = new Lead__c();
        
        
        lead1.Name = 'Test1';
        lead1.Lead_Source__c= 'Others';
        lead1.Title__c= 'Mr';
        lead1.Preferred_Email__c= '<EMAIL>';
        lead1.Primary_Country_Code__c= 91;
        Integer len = 10;
        String str = string.valueof(Math.abs(Crypto.getRandomLong()));
        String randomNumber =  str.substring(0, len);
        Id LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead1.Primary_Contact__c= ''+randomNumber ;
        lead1.RecordTypeId =LeadRecordTypeIdIPV;
        lead1.Relationship_Owner__c = UserInfo.getUserId();
        lead1.Are_They_Part_of_Other_Platform__c = 'Yes';
        lead1.App_signup_date__c = Date.Today();
        lead1.Total_Referred_Account_Leads__c = 3;
        lead1.Total_Number_of_Founder_Calls__c = 4;
        lead1.Total_Number_of_Investor_Calls__c = 5;
        lead1.Total_Number_of_Offline_Online_event__c = 3;
        lead1.College_Tier__c = 'Tier 1';
        lead1.Designation_Band__c = 'Band 1';
        lead1.Bot_Input__c = 'Interacted -Chat with RM';
        lead1.lead_Source__c = 'Referral';
        insert lead1;

        
        lead2.Name = 'Test2';
        lead2.Lead_Source__c= 'Others';
        lead2.Title__c= 'Mr';
        lead2.Preferred_Email__c= '<EMAIL>';
        lead2.Primary_Country_Code__c= 91;
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead2.Primary_Contact__c= ''+randomNumber ;
        lead2.RecordTypeId =LeadRecordTypeIdIPV;
        lead2.Relationship_Owner__c = UserInfo.getUserId();
        lead2.Are_They_Part_of_Other_Platform__c = 'No';
        lead2.App_signup_date__c = null;
        lead2.Total_Referred_Account_Leads__c = 13;
        lead2.Total_Number_of_Founder_Calls__c = 0;
        lead2.Total_Number_of_Investor_Calls__c = 0;
        lead2.Referred_By_Lead__c = lead1.Id;
        lead2.Total_Number_of_Offline_Online_event__c = 9;
        lead2.College_Tier__c = 'Tier 2';
        lead2.Designation_Band__c = 'Band 2';
        lead2.Primary_Country_Code__c = 91;
        lead2.Bot_Input__c = null;
        lead2.Bot_Journey_Stage__c = null;
        lead2.lead_Source__c = 'Linkedin';
        leadList.add(lead2);
        
        
        lead3.Name = 'Test3';
        lead3.Lead_Source__c= 'Others';
        lead3.Title__c= 'Mr';
        lead3.Preferred_Email__c= '<EMAIL>';
        lead3.Primary_Country_Code__c= 91;
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead3.Primary_Contact__c= ''+randomNumber ;
        lead3.RecordTypeId =LeadRecordTypeIdIPV;
        lead3.Relationship_Owner__c = UserInfo.getUserId();
        lead3.Are_They_Part_of_Other_Platform__c = null;
        lead3.App_signup_date__c = null;
        lead3.Total_Referred_Account_Leads__c = 9;
        lead3.Total_Number_of_Founder_Calls__c = 30;
        lead3.Referred_By_Lead__c = lead1.Id;
        lead3.Primary_Country_Code__c = 1;
        lead3.Total_Number_of_Investor_Calls__c = 20;
        lead3.Total_Number_of_Offline_Online_event__c = 0;
        lead3.College_Tier__c = 'Tier 3';
        lead3.Designation_Band__c = 'Band 3';
        lead3.Bot_Input__c = 'Interacted -Chat with RM';
        lead3.lead_Source__c = 'Facebook';
        leadlist.add(lead3);
        
        
        lead4.Name = 'Test4';
        lead4.Lead_Source__c= 'Others';
        lead4.Title__c= 'Mr';
        lead4.Preferred_Email__c= '<EMAIL>';
        lead4.Primary_Country_Code__c= 91;
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead4.Primary_Contact__c= ''+randomNumber ;
        lead4.RecordTypeId =LeadRecordTypeIdIPV;
        lead4.Relationship_Owner__c = UserInfo.getUserId();
        lead4.Are_They_Part_of_Other_Platform__c = null;
        lead4.App_signup_date__c = null;
        lead4.Referred_By_Lead__c = lead1.Id;
        lead4.Total_Referred_Account_Leads__c = 9;
        lead4.Total_Number_of_Founder_Calls__c = 7;
        lead4.Total_Number_of_Investor_Calls__c = 3;
        lead4.Total_Number_of_Offline_Online_event__c = 0;
        lead4.College_Tier__c = null;
        lead4.Designation_Band__c = 'Band 4';
        lead4.Bot_Input__c = null;
        lead4.Bot_Journey_Stage__c = 'First Message Initiated';
        lead4.lead_Source__c = 'App';
        leadlist.add(lead4);
        
        
        lead5.Name = 'Test5';
        lead5.Lead_Source__c= 'Others';
        lead5.Title__c= 'Mr';
        lead5.Preferred_Email__c= '<EMAIL>';
        lead5.Primary_Country_Code__c= 91;
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead5.Primary_Contact__c= ''+randomNumber ;
        lead5.RecordTypeId =LeadRecordTypeIdIPV;
        lead5.Relationship_Owner__c = UserInfo.getUserId();
        lead5.Are_They_Part_of_Other_Platform__c = null;
        lead5.App_signup_date__c = null;
        lead5.Total_Referred_Account_Leads__c = 1;
        lead5.Total_Number_of_Founder_Calls__c = 13;
        lead5.Total_Number_of_Investor_Calls__c = 3;
        lead5.Referred_By_Lead__c = lead1.Id;
        lead5.Total_Number_of_Offline_Online_event__c = 0;
        lead5.College_Tier__c = null;
        lead5.Designation_Band__c = null;
        lead5.Bot_Input__c = 'Interacted -Chat with RM';
        lead5.lead_Source__c = 'App Referral';
        leadlist.add(lead5);
        
        
        lead6.Name = 'Test6';
        lead6.Lead_Source__c= 'Others';
        lead6.Title__c= 'Mr';
        lead6.Preferred_Email__c= '<EMAIL>';
        lead6.Primary_Country_Code__c= 91;
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead6.Primary_Contact__c= ''+randomNumber ;
        lead6.RecordTypeId =LeadRecordTypeIdIPV;
        lead6.Relationship_Owner__c = UserInfo.getUserId();
        lead6.Are_They_Part_of_Other_Platform__c = null;
        lead6.App_signup_date__c = null;
        lead6.Total_Referred_Account_Leads__c = 1;
        lead6.Total_Number_of_Founder_Calls__c = 13;
        lead6.Total_Number_of_Investor_Calls__c = 3;
        lead6.Referred_By_Lead__c = lead1.Id;
        lead6.Total_Number_of_Offline_Online_event__c = 0;
        lead6.College_Tier__c = null;
        lead6.Designation_Band__c = null;
        lead6.Bot_Input__c = 'Interacted -Chat with RM';
        lead6.lead_Source__c = 'B2B-Corporates';
        leadlist.add(lead6);
        
        
        lead7.Name = 'Test7';
        lead7.Lead_Source__c= 'Others';
        lead7.Title__c= 'Mr';
        lead7.Preferred_Email__c= '<EMAIL>';
        lead7.Primary_Country_Code__c= 91;
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead7.Primary_Contact__c= ''+randomNumber ;
        lead7.RecordTypeId =LeadRecordTypeIdIPV;
        lead7.Relationship_Owner__c = UserInfo.getUserId();
        lead7.Are_They_Part_of_Other_Platform__c = null;
        lead7.App_signup_date__c = null;
        lead7.Total_Referred_Account_Leads__c = 1;
        lead7.Total_Number_of_Founder_Calls__c = 13;
        lead7.Total_Number_of_Investor_Calls__c = 3;
        lead7.Referred_By_Lead__c = lead1.Id;
        lead7.Total_Number_of_Offline_Online_event__c = 0;
        lead7.College_Tier__c = null;
        lead7.Designation_Band__c = null;
        lead7.Bot_Input__c = 'Interacted -Chat with RM';
        lead7.lead_Source__c = 'Offline Events-Gated Community';
        leadlist.add(lead7);
        
        
        lead8.Name = 'Test8';
        lead8.Lead_Source__c= 'Others';
        lead8.Title__c= 'Mr';
        lead8.Preferred_Email__c= '<EMAIL>';
        lead8.Primary_Country_Code__c= 91;
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead8.Primary_Contact__c= ''+randomNumber ;
        lead8.RecordTypeId =LeadRecordTypeIdIPV;
        lead8.Relationship_Owner__c = UserInfo.getUserId();
        lead8.Are_They_Part_of_Other_Platform__c = null;
        lead8.App_signup_date__c = null;
        lead8.Total_Referred_Account_Leads__c = 1;
        lead8.Total_Number_of_Founder_Calls__c = 13;
        lead8.Total_Number_of_Investor_Calls__c = 3;
        lead8.Referred_By_Lead__c = lead1.Id;
        lead8.Total_Number_of_Offline_Online_event__c = 0;
        lead8.College_Tier__c = null;
        lead8.Designation_Band__c = null;
        lead8.Bot_Input__c = 'Interacted -Chat with RM';
        lead8.lead_Source__c = 'B2B-Credit Cards';
        leadlist.add(lead8);
        
        
        lead9.Name = 'Test9';
        lead9.Lead_Source__c= 'Others';
        lead9.Title__c= 'Mr';
        lead9.Preferred_Email__c= '<EMAIL>';
        lead9.Primary_Country_Code__c= 91;
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead9.Primary_Contact__c= ''+randomNumber ;
        lead9.RecordTypeId =LeadRecordTypeIdIPV;
        lead9.Relationship_Owner__c = UserInfo.getUserId();
        lead9.Are_They_Part_of_Other_Platform__c = null;
        lead9.App_signup_date__c = null;
        lead9.Total_Referred_Account_Leads__c = 1;
        lead9.Total_Number_of_Founder_Calls__c = 13;
        lead9.Total_Number_of_Investor_Calls__c = 3;
        lead9.Referred_By_Lead__c = lead1.Id;
        lead9.Total_Number_of_Offline_Online_event__c = 0;
        lead9.College_Tier__c = null;
        lead9.Designation_Band__c = null;
        lead9.Bot_Input__c = 'Interacted -Chat with RM';
        lead9.lead_Source__c = 'B2B-Luxury Brands';
        leadlist.add(lead9);
        
        
        lead10.Name = 'Test10';
        lead10.Lead_Source__c= 'Others';
        lead10.Title__c= 'Mr';
        lead10.Preferred_Email__c= '<EMAIL>';
        lead10.Primary_Country_Code__c= 91;
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead10.Primary_Contact__c= ''+randomNumber ;
        lead10.RecordTypeId =LeadRecordTypeIdIPV;
        lead10.Relationship_Owner__c = UserInfo.getUserId();
        lead10.Are_They_Part_of_Other_Platform__c = null;
        lead10.App_signup_date__c = null;
        lead10.Total_Referred_Account_Leads__c = 4;
        lead10.Total_Number_of_Founder_Calls__c = 13;
        lead10.Total_Number_of_Investor_Calls__c = 3;
        lead10.Referred_By_Lead__c = lead1.Id;
        lead10.Total_Number_of_Offline_Online_event__c = 0;
        lead10.College_Tier__c = null;
        lead10.Designation_Band__c = null;
        lead10.Bot_Input__c = 'Interacted -Chat with RM';
        lead10.lead_Source__c = 'Organic-social media';
        leadlist.add(lead10);
        
        
        lead11.Name = 'Test11';
        lead11.Lead_Source__c= 'Others';
        lead11.Title__c= 'Mr';
        lead11.Preferred_Email__c= '<EMAIL>';
        lead11.Primary_Country_Code__c= 91;
        len = 10;
        str = string.valueof(Math.abs(Crypto.getRandomLong()));
        randomNumber =  str.substring(0, len);
        LeadRecordTypeIdIPV = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        lead11.Primary_Contact__c= ''+randomNumber ;
        lead11.RecordTypeId =LeadRecordTypeIdIPV;
        lead11.Relationship_Owner__c = UserInfo.getUserId();
        lead11.Are_They_Part_of_Other_Platform__c = null;
        lead11.App_signup_date__c = null;
        lead11.Total_Referred_Account_Leads__c = null;
        lead11.Total_Number_of_Founder_Calls__c = null;
        lead11.Total_Number_of_Investor_Calls__c = null;
        lead11.Referred_By_Lead__c = lead1.Id;
        lead11.Total_Number_of_Offline_Online_event__c = null;
        lead11.College_Tier__c = null;
        lead11.Designation_Band__c = null;
        lead11.Bot_Input__c = 'Interacted -Chat with RM';
        lead11.lead_Source__c = 'B2B Real Estate';
        leadlist.add(lead11);
        
        
        insert leadList;
        leadList.add(lead1);
        
        
        return leadList;
    }
}