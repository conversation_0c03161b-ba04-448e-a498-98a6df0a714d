/*Test Class for ExitInvestmentCtrl and OldExitInvestmentCtrl*/
@isTest(SeeAllData=false)
Public class ExitInvestmentCtrlTest
{

    @testSetup
    static void setup() {
        API_Setting__c setting = new API_Setting__c();
        setting.Name = 'Test Setting';
        setting.Enable_Investment_API_Call__c = true;
        setting.Enable_Investor_API_Call__c = true;
        setting.Enable_Startup_API_Call__c = true;
        setting.End_URL__c = 'test.com';
        insert setting;

        Startup__c st = TestFactory.createStartUp();
        st.Date_of_Investor_Call__c = Date.newInstance(2016, 12, 9);
        insert st;   
        
        List<Account> accList = new List<Account>();
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            insert accList;
            
            List<Contact> conList = new List<Contact>();
            integer i = 1;
            for(Account acc : accList)
            {
                Contact cont = new Contact();
                cont.FirstName='Test';
                cont.LastName='Test';
                cont.Accountid= acc.id;
                cont.Investor_s_PAN__c = 'ABCDE'+i+'173D';
                cont.Investment_in_Own_Name_Family_Member__c = 'Own Name';
                conList.add(cont);
                i++;
            }
            insert conList;
            
            Startup__c stObj = [select id from Startup__c limit 1];
            
            Startup_Round__c strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,stObj.Id);
            strObj.Date_of_Investor_Call__c = Date.newInstance(2016, 12, 9);
            strObj.Date_of_sending_out_call_for_money__c = Date.newInstance(2016, 12, 9);
            strObj.Issue_Price__c = 100;
            insert strObj;   
            
            strObj = [select id,name from Startup_Round__c limit 1];
            
            List<Investment__c> invList = new list<Investment__c >();       
            Investment__c inv = TestFactory.createInvestment(accList[0].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_Amount__c = 111;
            inv.Issue_Type__c = 'Primary';
            inv.Number_Of_Shares__c = 10;

            invList.add(inv);
            
            /*
            inv = TestFactory.createInvestment(accList[0].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_in_Own_Name_Family_Member__c = 'Own Name';
            inv.Is_Investor_Exists__c= true;
            inv.Investment_Amount__c = 111;
            inv.Issue_Type__c = 'Primary';
            invList.add(inv);
            
            //Joints Scenario
            inv = TestFactory.createInvestment(accList[0].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_in_Own_Name_Family_Member__c = 'Joint A/c - Outside';
            inv.Primary_Holder_Contact__c= accList[0].Primary_Contact__c;
            inv.Is_Primary__c=true;
            inv.Investment_Amount__c = 111;
            inv.Issue_Type__c = 'Primary';
            invList.add(inv);
            
            inv = TestFactory.createInvestment(accList[1].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_in_Own_Name_Family_Member__c = 'Joint A/c - Outside';
            inv.Primary_Holder_Contact__c= accList[0].Primary_Contact__c;
            inv.Is_Primary__c=false;
            inv.Investment_Amount__c = 111;
            inv.Issue_Type__c = 'Primary';
            invList.add(inv);
            
            inv = TestFactory.createInvestment(accList[2].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_in_Own_Name_Family_Member__c = 'Joint A/c - Outside';
            inv.Primary_Holder_Contact__c= accList[0].Primary_Contact__c;
            inv.Is_Primary__c=false;
            inv.Investment_Amount__c = 111;
            inv.Issue_Type__c = 'Primary';
            invList.add(inv);
             */
            
            insert invList;               
    }
    
    static testMethod void invExitTest()
    {
        test.startTest();
            List<Startup__c> startupList = [SELECT id,Public_Name__c,Date_of_Investor_Call__c,Total_Investment_In_INR__c FROM Startup__c order by Public_Name__c];
            System.assertNotEquals(startupList ,null);
            system.debug('startupList>>>>>>>'+startupList);
            OldExitInvestmentCtrl exitObj = new OldExitInvestmentCtrl();
            exitObj.getInvesment();
            
            exitObj.selectedstartup = startupList[0].Id;
            exitObj.selectedExitType = 'Exit';
            exitObj.acc.Amount_Paid__c = 123;
            exitObj.acc.Date_of_Payment__c = Date.newInstance(2021, 12, 9);
            exitObj.acc.Designation__c = 'test';
            exitObj.acc.NumberOfEmployees = 100;
            exitObj.getInvesment();

            for(OldExitInvestmentCtrl.InvesmentWrapper inv : exitObj.InvestmentList)
                inv.checked = true;
            System.assertNotEquals(exitObj.InvestmentList ,null);
            
            exitObj.saveExit();
            exitObj.updateEntries();
            exitObj.selectedExitRound = ''+[select id from Startup_Round__c limit 1][0].Id;    
            exitObj.saveExit();
            exitObj.Beginning();
            exitObj.Previous();
            exitObj.Next();
            exitObj.End();
            exitObj.getDisablePrevious();
            exitObj.getDisableNext();
            exitObj.getTotal_size();
            exitObj.getPageNumber();
            exitObj.getTotalPages();
            
            exitObj.selectedstartup = startupList[0].Id;
            exitObj.startupChange();
            
        test.stopTest();
    }
    static testMethod void internalTransferTest()
    {
        test.startTest();
            List<Startup__c> startupList = [SELECT id,Public_Name__c,Date_of_Investor_Call__c,Total_Investment_In_INR__c FROM Startup__c order by Public_Name__c];
            System.assertNotEquals(startupList ,null);
            system.debug('startupList>>>>>>>'+startupList);
            OldExitInvestmentCtrl exitObj = new OldExitInvestmentCtrl();
            exitObj.getInvesment();
            
            exitObj.selectedstartup = startupList[0].Id;
            
            exitObj.acc.Amount_Paid__c = 123;
            exitObj.acc.Date_of_Payment__c = Date.newInstance(2021, 12, 9);
            exitObj.acc.Designation__c = 'test';
            exitObj.acc.NumberOfEmployees = 100;
            exitObj.selectedExitType = 'Internal Transfer';
            exitObj.getInvesment();
           
            for(OldExitInvestmentCtrl.InvesmentWrapper inv : exitObj.InvestmentList)
            {    inv.checked = true;
                 inv.inv.Internal_Transfer_Investor__c = [select id from contact limit 1][0].Id;
                 inv.shareToBeExit = 1;
            }    
            System.assertNotEquals(exitObj.InvestmentList,null);
            exitObj.saveExit();
        test.stopTest();
    }
    
    static testMethod void internalTransferIPVTest()
    {
        test.startTest();
            List<Startup__c> startupList = [SELECT id,Public_Name__c,Date_of_Investor_Call__c,Total_Investment_In_INR__c FROM Startup__c order by Public_Name__c];
            system.debug('startupList>>>>>>>'+startupList);
            OldExitInvestmentCtrl exitObj = new OldExitInvestmentCtrl();
            exitObj.getInvesment();
            
            exitObj.selectedstartup = startupList[0].Id;
            
            exitObj.acc.Amount_Paid__c = 123;
            exitObj.acc.Date_of_Payment__c = Date.newInstance(2021, 12, 9);
            exitObj.acc.Designation__c = 'test';
            exitObj.acc.NumberOfEmployees = 100;
            exitObj.selectedExitType = 'Internal Transfer - IPV';
            exitObj.getInvesment();
           
            for(OldExitInvestmentCtrl.InvesmentWrapper inv : exitObj.InvestmentList)
            {    inv.checked = true;
                 inv.inv.Internal_Transfer_Investor__c = [select id from contact limit 1][0].Id;
                 inv.shareToBeExit = 1;
            }    
            
            exitObj.saveExit();
          
        test.stopTest();
    }
    
    static testMethod void partialExitTest()
    {
        List<Investment__c> invList = [select id,Number_Of_Shares__c from Investment__c];    
        for(Investment__c inv1 : invList)   
            inv1.Number_Of_Shares__c = 10;
        update invList;
        
        test.startTest();
            List<Startup__c> startupList = [SELECT id,Public_Name__c,Date_of_Investor_Call__c,Total_Investment_In_INR__c FROM Startup__c order by Public_Name__c];
            System.assertNotEquals(startupList ,null);
            system.debug('startupList>>>>>>>'+startupList);
            OldExitInvestmentCtrl exitObj = new OldExitInvestmentCtrl();
            exitObj.getInvesment();
            
            exitObj.selectedstartup = startupList[0].Id;
            exitObj.selectedExitType = 'Exit';
            exitObj.acc.Amount_Paid__c = 123;
            exitObj.acc.Date_of_Payment__c = Date.newInstance(2021, 12, 9);
            exitObj.acc.Designation__c = 'test';
            exitObj.acc.NumberOfEmployees = 100;
            exitObj.getInvesment();

            for(OldExitInvestmentCtrl.InvesmentWrapper inv : exitObj.InvestmentList)
            {
                inv.checked = true;
                inv.shareToBeExit = 1;
            }    
            exitObj.saveExit();
        test.stopTest();
            
    }
    
    
    
}