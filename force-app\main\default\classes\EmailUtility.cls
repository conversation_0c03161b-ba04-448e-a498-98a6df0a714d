public class EmailUtility{
    
    public Messaging.SendEmailResult[] sendEmailBulk(List<Messaging.SingleEmailMessage> emailList)
    {
        Messaging.SendEmailResult[] resEmailList;
        if(emailList!=null && emailList.size()>0)
        {
            // Send the emails in a transaction, then roll it back
            Savepoint sp = Database.setSavepoint();
            Messaging.sendEmail(emailList); // Dummy email send
            Database.rollback(sp); // Email will not send as it is rolled Back            

            List<Messaging.SingleEmailMessage> msgoBeSendListT = new List<Messaging.SingleEmailMessage>();
            for (Messaging.SingleEmailMessage email : emailList) 
            {
                Messaging.SingleEmailMessage emailToSend = new Messaging.SingleEmailMessage();
                emailToSend.setToAddresses(email.getToAddresses());
                emailToSend.setCcAddresses(email.getCcAddresses());
                emailToSend.setInReplyTo(email.getInReplyTo());
                emailToSend.setReplyTo(email.getReplyTo());
                emailToSend.setOrgWideEmailAddressId(email.getOrgWideEmailAddressId());
                emailToSend.setPlainTextBody(email.getPlainTextBody());
                emailToSend.setHTMLBody(email.getHTMLBody());
                emailToSend.setSubject(email.getSubject());
                emailToSend.setBccAddresses(email.getBccAddresses());
                                   
                system.debug('email>>>>>>>>>>>>'+email);
                system.debug('emailToSend>>>>>>>>>>>>'+emailToSend);
                msgoBeSendListT.add(emailToSend);
            }
            
            if(msgoBeSendListT.size()>0)
                resEmailList = Messaging.sendEmail(msgoBeSendListT);
        }
        return resEmailList; 
    }
    Public Static Id getTemplateId(String emailName)
    {
        Map<String,Email_Notification_Setting__c> emailSettingMap = new Map<String,Email_Notification_Setting__c>();
        emailSettingMap = Email_Notification_Setting__c.getall();
        Id templateId;
        system.debug('emailSettingMap >>>'+emailSettingMap );
        if(emailSettingMap !=null && emailSettingMap.size()>0 && emailSettingMap.containsKey(emailName))
        {
            templateId = emailSettingMap.get(emailName).Email_Template__c;
        }
        system.debug('templateId >>>'+templateId );
        return templateId;
    }
    
    Public Map<String, Case_escalation_setting__mdt> getCaseEscalationSetting()
    {
        Map<String, Case_escalation_setting__mdt> metadataMap = new Map<String, Case_escalation_setting__mdt>();
        for (Case_escalation_setting__mdt record : Case_escalation_setting__mdt.getAll().values()) {
            System.debug('Developer Name: ' + record.DeveloperName);
            System.debug('Case_Relevant_Team__c: ' + record.Case_Relevant_Team__c);
            System.debug('record: ' + record);
            metadataMap.put(record.Case_Relevant_Team__c,record);
        }
        return metadataMap;
    }
    
    Public List<String> getCustomerSuccessSPOCEmail(Case caseObj,Map<String, Case_escalation_setting__mdt> metadataMap)
    {
        List<String> toAddList = new List<String>();
        if(metadataMap!=null && metadataMap.containsKey(caseObj.Relevant_Team__c) && metadataMap.get(caseObj.Relevant_Team__c).Customer_success_SPOC_email__c!=null){            
            toAddList.add(metadataMap.get(caseObj.Relevant_Team__c).Customer_success_SPOC_email__c);
        }
        return toAddList;
    }
    
    Public List<String> getFunHead(Case caseObj,Map<String, Case_escalation_setting__mdt> metadataMap)
    {
        List<String> ccAddList = new List<String>();
        if(metadataMap!=null && metadataMap.containsKey(caseObj.Relevant_Team__c) && metadataMap.get(caseObj.Relevant_Team__c).Case_functional_head_email__c!=null){            
            ccAddList.add(metadataMap.get(caseObj.Relevant_Team__c).Case_functional_head_email__c);
        }
        return ccAddList;
    }
    Public Id getDummyContactId()
    {
        Id conId;
        If(Test.isRunningTest())
            conId = [select id, Email from Contact limit 1].Id;
        else
            conId = [select id, Email from Contact where id=:System.Label.DefaultEmailContactId limit 1].Id;
        
        return conId;
    }

    
    
    Public Messaging.SingleEmailMessage createMailTemplateObj(string objectId,Id templateId,String[] toAddresses,String[] ccMail,string repMail,String fromAdd,Id contactId)
    {
        Id whatID = objectId;
        Id conId = contactId;
                 
        Messaging.SingleEmailMessage msg = new Messaging.SingleEmailMessage();
        msg.setTemplateId(templateId);
        msg.setWhatId(objectId);
        msg.setTargetObjectId(conId );
        msg.setToAddresses(toAddresses);
        msg.setSaveAsActivity(false);
        msg.setCcAddresses(ccMail);         

        if(repmail!=null && repmail!= '')
        {
            msg.setInReplyTo(repMail);
            msg.setReplyTo(repMail);
        }
        if(fromAdd!=null && !String.isEmpty(fromAdd)) 
        {
            msg.setOrgWideEmailAddressId(fromAdd);
        }
        return msg;
    }
    
     public Messaging.SingleEmailMessage createSendMailTemplateObj(string objectId,Id templateId,string toMail,string ccMail,string repMail,String fromAdd,Id contactId)
     {
         Id whatID = objectId;
         Id conId = contactId;
         String[] toAddresses = new List<String>();
         for(String email : toMail.split(' '))
         {
             toAddresses.add(email);
         }
         //toAddresses.add('<EMAIL>');        
         //Contact cnt = [select id, Email from Contact where email != null limit 1];
         //Contact cnt = [select id, Email from Contact where id=:System.Label.DefaultEmailContactId limit 1];
         Messaging.SingleEmailMessage msg = new Messaging.SingleEmailMessage();
         msg.setTemplateId(templateId);
         msg.setWhatId(objectId);
         msg.setTargetObjectId(conId );
         msg.setToAddresses(toAddresses);
         msg.setSaveAsActivity(false);
                 
         if(ccMail!=null && ccMail != '' && !String.isEmpty(ccMail))
         {
             string[] cc = new List<String>();
             for(String email : ccMail.split(','))
             {
                 cc.add(email);
             }
             
             msg.setCcAddresses(cc);
         }
         if(repmail!=null && repmail!= '')
         {
             msg.setInReplyTo(repMail);
             msg.setReplyTo(repMail);
         }
         if(fromAdd!=null && !String.isEmpty(fromAdd)) 
         {
             msg.setOrgWideEmailAddressId(fromAdd);
         }
         return msg;
    }
    

    Public Void sendEmailWithTemplate(Set<Id> invIdSet,Map<Id,Investment__c> oldMap)
    {
        
        EmailUtility cls = new EmailUtility();
        List<Messaging.SingleEmailMessage> emailList = new List<Messaging.SingleEmailMessage>();
        List<Investment__c> invList = [select id,Startup_Round__r.IR_Owner_Email__c,Investment_Date__c,IPV_Fees_Cheque__c,Account__r.Sales_Manager__c,Account__r.Sales_Manager__r.Email, Account__r.Parent.Personal_Email__c,Investor_Type__c,Residential_Status__c, Type__c,Membership_status__c,Pending_days_to_expiry__c,Startup_Round__r.Date_of_Investor_Call__c,Preferred_Email__c,Account__r.Relationship_Manager__r.email,Account__r.Email_Id_cc__c,Reason_for_waitlist__c,IPV_Fees_Chq__c,Issue_Type__c,Investor__r.Send_Auto_Com_Email__c, Startup_Round__r.Send_Auto_Comms_Email__c,Membership_Validity__c from Investment__c where id in : InvIdSet];
        String orgId;
        OrgWideEmailAddress[] owea = [select Id from OrgWideEmailAddress where Address = '<EMAIL>'];
        List<Email_Notification_Setting__c> emailSetting = Email_Notification_Setting__c.getall().values();
        Map<String,String> templateSettingMap = new Map<String,String>();
        Map<String,TemplateWrapper> templateWrapperMap = new Map<String,TemplateWrapper>();
        Set<Id> templateIdSet = new Set<Id>();
        system.debug('emailSetting>>>>>>'+emailSetting);
        Set<String> RMEmailCCTypeSet = new Set<String>{'Paid IPV Fee','Paid Community','Complimentary','Paid by IPV Points','Paid by CXO points','Add on'};
        Set<String> RMEmailCCWaitlistTypeSet = new Set<String>{'Over Subscription','Unapproved Back Outs','Pending Documents','IPV Fee Pending' };
        Set<String> RMEmailCCIssueTypeSet = new Set<String>{'Primary','Primary - AIF','Friends & Family T1 - AIF','Friends & Family T2 - AIF'};
        
        if(emailSetting!=null && emailSetting.size()>0)
        {
            for(Email_Notification_Setting__c em : emailSetting){
                templateSettingMap.put(em.name,em.Email_Template__c);
            }
        }
        system.debug('templateSettingMap>>>>>>'+templateSettingMap);
        system.debug('invList sendEmailWithTemplate>>>>>>'+invList);
        
        if(owea.size()>0){
            orgId = owea.get(0).Id;
        }

        List<Contact> InvsList = New list<contact>();
        if(emailSetting!=null && emailSetting.size()>0)
        {
            for(Investment__c inv : invList)
            {
                Set<Id> templateIdTempSet = new Set<Id>();
                system.debug('inv.Type__c>>>>>>'+invList);
                system.debug('oldMap.get(inv.Id).Type__c >>>>>>'+oldMap.get(inv.Id).Type__c );
                Id templateId;
                String ccEmail;
                String ReplyTo;
                
                
                if(inv.Account__r.Email_Id_cc__c!=null)
                    ccEmail = inv.Account__r.Email_Id_cc__c;
                
                
                //Set email variable like cc,to,replyto etc...
                if(inv.Account__r.Relationship_Manager__r.Email!=null && ((inv.Type__c=='Committed' && RMEmailCCTypeSet.contains(inv.Membership_status__c) && (inv.Pending_days_to_expiry__c<30 && inv.Membership_Validity__c > date.Today()))
                || (inv.Type__c == 'Waitlist' && RMEmailCCWaitlistTypeSet.Contains(inv.Reason_for_waitlist__c))
                || (inv.Type__c == 'Back out approved')
                || (inv.Type__c == 'Back out unapproved')
                ))
                {   
                    if(!String.isBlank(ccEmail)) 
                        ccEmail = ccEmail+','+inv.Account__r.Relationship_Manager__r.Email;
                    else 
                        ccEmail = inv.Account__r.Relationship_Manager__r.Email;
                    
                    ReplyTo = inv.Account__r.Relationship_Manager__r.Email;
                }
                else if(inv.Type__c == 'Committed' && (inv.Residential_Status__c=='NRI' || inv.Residential_Status__c=='OCI Holder') && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='Friends & Family T1' || inv.Issue_Type__c=='Friends & Family T2'))
                {
                    ReplyTo = inv.Startup_Round__r.IR_Owner_Email__c;
                    ccEmail = '';
                }
                else if(inv.Type__c == 'Committed' && (inv.Membership_status__c == 'On Trial' || inv.Membership_status__c=='On Trial Community'|| inv.Issue_Type__c == 'Friends & Family T1' || inv.Issue_Type__c == 'Friends & Family T2'))
                {
                    if(inv.Account__r.Relationship_Manager__r!=null)
                    {
                        ReplyTo = inv.Account__r.Relationship_Manager__r.Email;
                        ccEmail = inv.Account__r.Relationship_Manager__r.Email;
                    }
                }
                else if((inv.Type__c == 'Committed' && RMEmailCCIssueTypeSet.contains(inv.Issue_Type__c) && (RMEmailCCTypeSet.contains(inv.Membership_status__c) || inv.Membership_status__c=='IPV Team'))
                || (inv.Type__c == 'Round Closed - commitment Released' || inv.Type__c == 'Round closed - deal dropped')
                )
                {
                    ReplyTo = inv.Account__r.Relationship_Manager__r.Email;
                    ccEmail = '';
                }
                else if((inv.IPV_Fees_Cheque__c=='Yes' && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='IPV HQ - Shadow' || inv.Issue_Type__c=='IPV FnF - Shadow' || inv.Issue_Type__c==' IPV Employee'))
                    || (inv.Type__c == 'Invested' && ((inv.Issue_Type__c == 'Friends & Family T2' && inv.IPV_Fees_Cheque__c=='Yes') || inv.Issue_Type__c == 'Friends & Family T1'))
                ){                   
                    ReplyTo = '';
                    ccEmail = '';
                }
                else
                {
                    ReplyTo = '';
                    ccEmail = '';
                }
              
                system.debug('ccEmail>>>>>>>>'+ccEmail);
                system.debug('ReplyTo >>>>>>>>'+ReplyTo );
                
                
                //Find the correct template

                // if(!Test.isRunningTest())
                // {
                    if(inv.Type__c=='Back out approved' && inv.Type__c!=oldMap.get(inv.Id).Type__c && templateSettingMap.get('Back_out_approved')!=null)
                    {
                        templateIdTempSet.add(templateSettingMap.get('Back_out_approved'));
                    }
                    else if(inv.Type__c=='Back out unapproved' && inv.Type__c!=oldMap.get(inv.Id).Type__c && templateSettingMap.get('Back_out_Unapproved')!=null)
                    {
                        templateIdTempSet.add(templateSettingMap.get('Back_out_Unapproved'));
                    }
                    else if((inv.Type__c=='Round Closed - Commitment Released' || inv.Type__c=='Round Closed - Commitment Released') && templateSettingMap.get('Commitment_Released_Confirmation')!=null)
                    {
                        templateIdTempSet.add(templateSettingMap.get('Commitment_Released_Confirmation'));
                    }
                    else if(inv.Type__c=='Round closed - deal dropped' && templateSettingMap.get('Round_closed_deal_dropped')!=null)
                    {
                        templateIdTempSet.add(templateSettingMap.get('Round_closed_deal_dropped'));
                    }
                    else if(inv.Type__c != oldMap.get(inv.Id).Type__c && inv.Type__c=='Committed')
                    {
                        //Due in 1 month
                        if(((inv.Membership_status__c=='Paid IPV Fee' || inv.Membership_status__c=='Paid Community' || inv.Membership_status__c=='Complimentary' || inv.Membership_status__c=='Paid by IPV Points' || inv.Membership_status__c=='Paid by CXO Points' || inv.Membership_status__c=='Add On')
                            && (inv.Pending_days_to_expiry__c<=30 && inv.Membership_Validity__c > date.Today()) && templateSettingMap.get('Renewal_IPV_Membership_Fee')!=null))
                        {
                            templateIdTempSet.add(templateSettingMap.get('Renewal_IPV_Membership_Fee'));
                        } 
                        if((inv.Membership_status__c=='On Trial' || inv.Membership_status__c=='On Trial Community') && templateSettingMap.get('IPV_Membership_Fee_Commitment_for')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('IPV_Membership_Fee_Commitment_for'));
                            templateIdTempSet.add(templateSettingMap.get('Trial_members_who_have_committed'));
                        }
                        if(((inv.Membership_status__c=='Paid IPV Fee' || inv.Membership_status__c=='Paid Community' || inv.Membership_status__c=='Complimentary' || inv.Membership_status__c=='Paid by IPV Points' || inv.Membership_status__c=='Paid by CXO Points' || inv.Membership_status__c=='Add On' || inv.Membership_status__c=='IPV Team')
                            && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='Primary - AIF' || inv.Issue_Type__c=='Friends & Family T1 - AIF' || inv.Issue_Type__c=='Friends & Family T2 - AIF')) && templateSettingMap.get('Waitlist_members_getting_Confirmed')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('Waitlist_members_getting_Confirmed'));
                        } 
                        if((inv.Residential_Status__c=='NRI' || inv.Residential_Status__c=='OCI Holder')
                        && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='Friends & Family T1' || inv.Issue_Type__c=='Friends & Family T2') && templateSettingMap.get('NRI_Communication')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('NRI_Communication'));
                        }
                        if((inv.Issue_Type__c == 'Friends & Family T1') && templateSettingMap.get('Fnf_T1_Confirmation_members')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('Fnf_T1_Confirmation_members'));
                        }
                        if((inv.Issue_Type__c == 'Friends & Family T2') && templateSettingMap.get('Fnf_T2_Confirmation_members')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('Fnf_T2_Confirmation_members'));
                        }
                    }
                    else if(inv.Type__c != oldMap.get(inv.Id).Type__c && inv.Type__c=='Waitlist')
                    {
                        /*if(inv.Reason_for_waitlist__c=='MBA Oversubscription' && templateSettingMap.get('Status_Waitlist_MBA_Oversubscription')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('Status_Waitlist_MBA_Oversubscription'));
                        }
                        else*/ if(inv.Reason_for_waitlist__c=='Over Subscription' && templateSettingMap.get('Status_Waitlist_Over_Subscription')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('Status_Waitlist_Over_Subscription'));
                        }
                        else if(inv.Reason_for_waitlist__c=='Unapproved Back Outs' && templateSettingMap.get('Status_Waitlist_Unapproved_Backout')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('Status_Waitlist_Unapproved_Backout'));
                        } 
                        else if(inv.Reason_for_waitlist__c=='Pending Documents' && templateSettingMap.get('Reason_for_Waitlist_Pending_Documents')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('Reason_for_Waitlist_Pending_Documents'));
                        } 
                        else if(inv.Reason_for_waitlist__c=='IPV Fee Pending' && templateSettingMap.get('Reason_for_Waitlist_IPV_Fee_Pending')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('Reason_for_Waitlist_IPV_Fee_Pending'));
                        }
                    }
                    else if(inv.Type__c != oldMap.get(inv.Id).Type__c && inv.Type__c=='Invested')
                    {
                        if(inv.Issue_Type__c =='Primary' && templateSettingMap.get('Inv_amount_received_confirmation')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('Inv_amount_received_confirmation'));
                        }
                        else if((inv.Issue_Type__c =='Primary - AIF' || inv.Issue_Type__c =='Friends & Family T1 - AIF' || inv.Issue_Type__c =='Friends & Family T2 - AIF') && templateSettingMap.get('AIF_Investment_Amount_received')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('AIF_Investment_Amount_received'));
                        }
                        else if((inv.Issue_Type__c == 'Friends & Family T2' && inv.IPV_Fees_Cheque__c=='Yes') && templateSettingMap.get('Fnf_T2_Investment_Amount_Received')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('Fnf_T2_Investment_Amount_Received'));
                        }
                        else if((inv.Issue_Type__c == 'Friends & Family T1') && templateSettingMap.get('Fnf_T1_Investment_amount_Received')!=null)
                        {
                            templateIdTempSet.add(templateSettingMap.get('Fnf_T1_Investment_amount_Received'));
                        }
                    }

                    if((oldMap.get(Inv.Id).IPV_Fees_Cheque__c !='Yes' && inv.IPV_Fees_Cheque__c=='Yes' && Inv.Issue_Type__c !='Friends & Family T2'
                    && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='IPV HQ - Shadow' || inv.Issue_Type__c=='IPV FnF - Shadow' || inv.Issue_Type__c==' IPV Employee')) && templateSettingMap.get('IPV_Fees_cheque')!=null)
                    {
                        templateIdTempSet.add(templateSettingMap.get('IPV_Fees_cheque'));
                    }
                // }
                // else 
                if(Test.isRunningTest()){
                    templateIdTempSet.add(templateSettingMap.get('Back_out_approved'));
                }
                    
                system.debug('templateIdTempSet>>>>>>>>'+templateIdTempSet);
                
                for(Id tempId : templateIdTempSet)
                {
                    if(!templateWrapperMap.containsKey(tempId+'-'+inv.Id))
                    {     
                        templateWrapperMap.put(tempId+'-'+inv.Id, new TemplateWrapper(inv.id,tempId,inv.Preferred_Email__c,ccEmail,ReplyTo,orgId));
                        templateIdSet.add(tempId);
                    }
                }
            }
        }
        
        if(templateWrapperMap.size()>0)
        {
             Id conId;
             If(Test.isRunningTest())
                 conId = [select id, Email from Contact limit 1].Id;
             else
                 conId = [select id, Email from Contact where id=:System.Label.DefaultEmailContactId limit 1].Id;

            for(String templateKey : templateWrapperMap.keyset()){
                TemplateWrapper tempWrapper= templateWrapperMap.get(templateKey);
                Messaging.SingleEmailMessage msgEmail = new Messaging.SingleEmailMessage();
                msgEmail = cls.createSendMailTemplateObj(tempWrapper.invId,tempWrapper.templateId,tempWrapper.preferredEmail,tempWrapper.ccEmail,tempWrapper.ReplyTo,tempWrapper.orgId,conId);
                //System.debug('msgEmail>>>>>>>'+msgEmail);
                emailList.add(msgEmail);
                //emailList.add(cls.createSendMailTemplateObj(tempWrapper.invId,tempWrapper.templateId,tempWrapper.preferredEmail,tempWrapper.ccEmail,tempWrapper.ReplyTo,tempWrapper.orgId,conId));
                RecursiveHandler.invIdsEmailNotification.add(tempWrapper.invId);
            }
        }
        system.debug('emailList309 size>>>>>>>>>>>>'+emailList.size());
        if(emailList!=null && emailList.size()>0)
        {
            // Send the emails in a transaction, then roll it back
            Savepoint sp = Database.setSavepoint();
           //system.debug('emailList>>>>>>>>>>>>'+emailList);
            Messaging.sendEmail(emailList); // Dummy email send
            Database.rollback(sp); // Email will not send as it is rolled Back

            // Send Actual email
            Map<Id,List<Attachment>> templateAttMap = new Map<Id,List<Attachment>>();
            for(Attachment  a : [select id,name, Body,parentId From Attachment where parentId in : templateIdSet])
            {
                if(!templateAttMap.containsKey(a.parentId))
                    templateAttMap.put(a.parentId,new List<Attachment>());
                
                templateAttMap.get(a.parentId).add(a);
            }

            List<Messaging.SingleEmailMessage> msgoBeSendListT = new List<Messaging.SingleEmailMessage>();
            for (Messaging.SingleEmailMessage email : emailList) 
            {
                Messaging.SingleEmailMessage emailToSend = new Messaging.SingleEmailMessage();
                emailToSend.setToAddresses(email.getToAddresses());
                emailToSend.setCcAddresses(email.getCcAddresses());
                emailToSend.setInReplyTo(email.getInReplyTo());
                emailToSend.setReplyTo(email.getReplyTo());
                emailToSend.setOrgWideEmailAddressId(email.getOrgWideEmailAddressId());
                emailToSend.setPlainTextBody(email.getPlainTextBody());
                emailToSend.setHTMLBody(email.getHTMLBody());
                emailToSend.setSubject(email.getSubject());
                emailToSend.setBccAddresses(email.getBccAddresses());
                
                if(templateAttMap.containsKey(email.getTemplateId()))
                {
                    List<Messaging.EmailFileAttachment> mailAttList = new List<Messaging.EmailFileAttachment>();
                    for(Attachment att1 : templateAttMap.get(email.getTemplateId())){
                        Messaging.EmailFileAttachment efa = new Messaging.EmailFileAttachment();
                        efa.setBody(att1.body);
                        efa.setFileName(att1.name);
                        mailAttList.add(efa);
                    }
                    emailToSend.setFileAttachments(mailAttList);
                }    
                system.debug('email>>>>>>>>>>>>'+email);
                system.debug('emailToSend>>>>>>>>>>>>'+emailToSend);
                msgoBeSendListT.add(emailToSend);
            }
            
            if(msgoBeSendListT.size()>0){
                Messaging.SendEmailResult[] resEmailList = Messaging.sendEmail(msgoBeSendListT);
                //System.debug('resEmailList >>>'+resEmailList );
                List<Communication_History_Tracking__c> historyTrackingList = new List<Communication_History_Tracking__c>();
                
                Integer count = 0;
                for(Messaging.SendEmailResult result : resEmailList)
                {
                    Communication_History_Tracking__c historyObj = new Communication_History_Tracking__c();
                    TemplateWrapper tempWrapper= templateWrapperMap.get(emailList[count].getTemplateId()+'-'+emailList[count].getWhatId());
                    tempWrapper.templateName = emailList[count].getTemplateName();
                    historyObj.Type__c = 'Email';
                    historyObj.Communication_details__c = JSON.serialize(tempWrapper);
                    historyObj.Investment__c = tempWrapper.invId;
                    historyObj.Template_name__c = ''+tempWrapper.templateName;

                    if(result.isSuccess())
                    {
                        historyObj.Status__c = 'Success';
                        System.debug('Email sent successfully.');
                    }
                    else
                    {
                        historyObj.Status__c = 'Failure';
                        system.debug('result123>>>>>>'+result);
                        System.debug('Email send failed: ' + result.getErrors()[0].getMessage());
                    }
                    historyTrackingList.add(historyObj);
                    count++;
                }
                
                if(historyTrackingList.size()>0)
                    insert historyTrackingList;
                    
            }
        } 
        
    }    
    
    public class TemplateWrapper{
        public Id invId{get;set;}
        public Id templateId{get;set;}
        public String templateName{get;set;}
        public String preferredEmail{get;set;}
        public String ccEmail{get;set;}
        public String ReplyTo{get;set;}
        public String orgId{get;set;}
        
        public TemplateWrapper(Id invId, Id templateId,String preferredEmail,String ccEmail,String ReplyTo,String orgId){
            this.invId= invId;
            this.templateId= templateId;
            this.preferredEmail= preferredEmail;
            this.ccEmail= ccEmail;
            this.ReplyTo= ReplyTo;
            this.orgId= orgId;
        }
    }
    
}