/************************************************************************
    Test Class for EventTrigger and EventTriggerHandler.
************************************************************************/
@isTest(SeeAllData=false)
private class EventAttendanceTriggerTestClass{
    @testSetup static void setup(){
        Startup__c st = TestFactory.createStartUp();
        st.Portfolio_map_Sector__c = 'Saas';
        insert st;  
             
        List<Account> accList = new List<Account>();
        accList.add(TestFactory.createAccount());
        accList.add(TestFactory.createAccount());
       
        insert accList;
                        
        Startup_Round__c strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,st.Id);
        
        insert strObj;
        
 
    }
        
    static testMethod void EventTriggerMethod()
    {
        test.startTest();
            Startup_Round__c srObj = [select id,name from Startup_Round__c limit 1];
            Events__c evObj = TestFactory.createEvents(''+srObj.Name);   
            insert evObj; 
            evObj = [select id,Startup_Round__c from Events__c where id=:evObj.Id];
            System.assertEquals(evObj.Startup_Round__c,srObj.Id);            
        test.stopTest();
    }
    
   /* static testMethod void AttendanceTriggerMethod()
    {
        test.startTest();
            Startup_Round__c srObj = [select id,name from Startup_Round__c limit 1];
            Events__c evObj = TestFactory.createEvents(''+srObj.Name);
            insert evObj;
            evObj = [select id,Startup_Round__c from Events__c where id=:evObj.Id];
            System.assertEquals(evObj.Startup_Round__c,srObj.Id); 
            Account accObj = [select id,Primary_Contact__c,Secondary_Contact__c,Personal_Email__c,Official_Email__c,RecordTypeId from Account limit 1];           
        	
            List<Attendance__c> attObjList = new List<Attendance__c >();
            Attendance__c attObj = TestFactory.createAttendance(accObj.Primary_Contact__c,accObj.Personal_Email__c,evObj.Id);
            attObj.Account__c = accObj.id;
            attObjList.add(attObj);
            attObj = TestFactory.createAttendance(accObj.Primary_Contact__c+'1','a'+accObj.Personal_Email__c,evObj.Id);
        	attObj.Account__c = accObj.id;
            attObjList.add(attObj);
           
        	Lead__c ld = TestFactory.createLead();
            ld.Relationship_Owner__c = userInfo.getuserid();
        	insert ld;
        	lead__c ldObj = [select id,Primary_Contact__c,Secondary_Contact__c,Personal_Email__c,Official_Email__c,RecordTypeId from lead__C limit 1]; 
            Attendance__c attObj1 = TestFactory.createAttendance(ldObj.Primary_Contact__c,ldObj.Personal_Email__c,evObj.Id);
        	attObj1.Personal_Email__c = '<EMAIL>';
            attObj1.Lead__c = ldObj.Id;
            attObjList.add(attObj1);
            
            insert attObjList;
            System.assertEquals(attObjList.size(),3);
            
        test.stopTest();
    }*/
    
    static testMethod void AttendanceTriggerMethodWithCountUpdate() {
        test.startTest();
        Startup_Round__c srObj = [select id, name from Startup_Round__c limit 1];
        
        list<events__c> evtlist = new list<events__c>();
        Events__c evObj = TestFactory.createEvents('' + srObj.Name);
        evObj.Startup_Round__c = srObj.Id;
        evobj.Event_Type__c = 'Founder Call';
        evtlist.add(evObj);
        
        Events__c evObj1 = TestFactory.createEvents('' + srObj.Name);
        evobj1.Event_Type__c = 'Investor Call';
        evtlist.add(evObj1);
        
        Events__c evObj2 = TestFactory.createEvents('' + srObj.Name);
        evObj2.Event_Type__c = 'Offline Events';
        evObj2.Participant_Status__c = 'Attended';
        evtlist.add(evObj2);
        
        Events__c evObj3 = TestFactory.createEvents('' + srObj.Name);
        evObj3.Event_Type__c = 'Online Events';
        evObj3.Participant_Status__c = 'Attended';
        evtlist.add(evObj3);
        
        insert evtlist;
                
        evObj = [select id, Startup_Round__c from Events__c where id = :evObj.Id];
        System.assertEquals(evObj.Startup_Round__c, srObj.Id);
        
        Account accObj = [select id, Primary_Contact__c, Personal_Email__c, Official_Email__c, RecordTypeId from Account limit 1];
        List<Attendance__c> attObjList = new List<Attendance__c>();

        // Create an Attendance__c record for Account
        Attendance__c attObj = TestFactory.createAttendance(accObj.Primary_Contact__c, accObj.Official_Email__c, evObj1.Id); 
        attObj.Account__c = accObj.id;
        attObjList.add(attObj);

        Attendance__c attObj2 = TestFactory.createAttendance(accObj.Primary_Contact__c + '1', 'a' + accObj.Personal_Email__c, evtlist[1].Id); // need to add index
        attObj2.Account__c = accObj.id;
        attObjList.add(attObj2);
        
        Attendance__c attObj3 = TestFactory.createAttendance(accObj.Primary_Contact__c + '9', 'z' + accObj.Official_Email__c, evtlist[2].Id); 
        attObj.Account__c = accObj.id;
        attObjList.add(attObj3);

        Attendance__c attObj4 = TestFactory.createAttendance(accObj.Primary_Contact__c + '4', 'a' + accObj.Personal_Email__c, evtlist[2].Id); // need to add index
        attObj2.Account__c = accObj.id;
        attObjList.add(attObj4);
             
        // Create an Attendance__c record for Lead
        Lead__c ld = TestFactory.createLead();
        ld.Relationship_Owner__c = userInfo.getuserid();
        insert ld;
        lead__c ldObj = [select id, Primary_Contact__c, Secondary_Contact__c, Personal_Email__c, Official_Email__c, RecordTypeId from lead__C limit 1]; 
        Attendance__c attObj1 = TestFactory.createAttendance(ldObj.Primary_Contact__c, ldObj.Personal_Email__c, evObj.Id);
        attObj1.Personal_Email__c = '<EMAIL>';
        attObj1.Lead__c = ldObj.Id;
        attObjList.add(attObj1);
        
        insert attObjList;
        //System.assertEquals(attObjList.size(), 3);

        // Call the updateCountOnAccount method to update Account records
        AttendanceTriggerHandler handler = new AttendanceTriggerHandler();
        handler.updateCountOnAccount(attObjList);
        
        // Calling AfterInsert Method of EventTriggerHandler Class To Cover The Test Coverage -- Added By Sahilparvat on 20-02-2025 
        EventTriggerHandler evHandler = new EventTriggerHandler();
        evHandler.afterInsert(evtlist);
        
        
        //Account updatedAcc = [SELECT Total_Number_of_Founder_Calls__c FROM Account WHERE Id = :accObj.Id];
        //System.assertEquals(2, updatedAcc.Total_Number_of_Founder_Calls__c, 'Total_Number_of_Founder_Calls__c not updated correctly');
        
        test.stopTest();
    }        
 }