@isTest
public with sharing class RenewalMembershipAmountAPITest {
    @isTest
    static void testValidInput() {
        // Setup test data
        Account testAccount1= TestFactory.createAccount();
        testAccount1.Name ='Test Account 1';
        testAccount1.Primary_Contact__c = '**********';
        testAccount1.Primary_Country_Code__c= 91;
        testAccount1.Membership_Status__c = 'Platinum';
        testAccount1.Membership_Slab__c = 'Gold';
        insert testAccount1;
        
        Account testAccount2= TestFactory.createAccount();
        testAccount2.Membership_Slab__c = 'Silver';
        testAccount2.Name ='Test Account 2';
        testAccount2.Membership_Status__c = 'Platinum';
        insert testAccount2;
        
        Account testAccount3= TestFactory.createAccount();
        testAccount3.Membership_Slab__c = 'Bronze';
        testAccount3.Name ='Test Account 3';
        insert testAccount3;

         Map<String, Integer> renewalAmount = new Map<String, Integer>();
         List<Amount_Payable_For_Renewal__mdt> renewalCustomMetaData = [
             SELECT Id, Bronze_Slab_Discount__c, Silver_Slab_Discount__c, Gold_Slab_Discount__c, CXO_Genie_Membership_Discount__c, Price_Per_Point__c, GST__c, One_Year_excl_GST__c, Two_Years_excl_GST__c, Three_Years_excl_GST__c, Four_Years_excl_GST__c, Five_Years_excl_GST__c 
             FROM Amount_Payable_For_Renewal__mdt LIMIT 1
         ];

         if (!renewalCustomMetaData.isEmpty()) {
             Amount_Payable_For_Renewal__mdt metaDataRecord = renewalCustomMetaData[0];
            
             // Describe and loop over fields to populate renewalAmount map
             Schema.DescribeSObjectResult describeResult = metaDataRecord.getSObjectType().getDescribe();
             for (Schema.SObjectField field : describeResult.fields.getMap().values()) {
                 String fieldName = field.getDescribe().getName();
                 if (fieldName.endsWith('__c')) {
                     Object fieldValue = metaDataRecord.get(field);
                     if (fieldValue != null && fieldValue instanceof Decimal) {
                         renewalAmount.put(fieldName, ((Decimal) fieldValue).intValue());
                     }
                 }
             }
         }

        // Create request body
        RenewalMembershipAmountAPI.RequestWrapper requestBody = new RenewalMembershipAmountAPI.RequestWrapper();
        RenewalMembershipAmountAPI.ObjPrimaryContactWrapper contact = new RenewalMembershipAmountAPI.ObjPrimaryContactWrapper();
        contact.primaryNumber = '**********';
        contact.countryCode = '91';
        requestBody.primaryContactList = new List<RenewalMembershipAmountAPI.ObjPrimaryContactWrapper>{contact};
        
        // Serialize request and set it to RestContext
        RestContext.request = new RestRequest();
        RestContext.response = new RestResponse();
        RestContext.request.requestBody = Blob.valueOf(JSON.serialize(requestBody));

        // Call the method
        Test.startTest();
        RenewalMembershipAmountAPI.getRenewalMembershipDetails();
        Test.stopTest();

        // Validate response
        RestResponse response = RestContext.response;
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response.responseBody.toString());

    }

    @isTest
    static void testEmptyPrimaryNumber() {
        // Create request body with empty primaryNumber
        RenewalMembershipAmountAPI.RequestWrapper requestBody = new RenewalMembershipAmountAPI.RequestWrapper();
        RenewalMembershipAmountAPI.ObjPrimaryContactWrapper contact = new RenewalMembershipAmountAPI.ObjPrimaryContactWrapper();
        contact.primaryNumber = '**********';  
        contact.countryCode = '91';
        requestBody.primaryContactList = new List<RenewalMembershipAmountAPI.ObjPrimaryContactWrapper>{contact};

        RestContext.request = new RestRequest();
        RestContext.response = new RestResponse();
        RestContext.request.requestBody = Blob.valueOf(JSON.serialize(requestBody));

        // Call the method
        Test.startTest();
        RenewalMembershipAmountAPI.getRenewalMembershipDetails();
        Test.stopTest();

        // Validate response
        RestResponse response = RestContext.response;
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response.responseBody.toString());
    }

    @isTest
    static void testInvalidCountryCode() {
        // Create request body with invalid country code
        RenewalMembershipAmountAPI.RequestWrapper requestBody = new RenewalMembershipAmountAPI.RequestWrapper();
        RenewalMembershipAmountAPI.ObjPrimaryContactWrapper contact = new RenewalMembershipAmountAPI.ObjPrimaryContactWrapper();
        contact.primaryNumber = '**********';
        contact.countryCode = '909';  // Non-numeric country code
        requestBody.primaryContactList = new List<RenewalMembershipAmountAPI.ObjPrimaryContactWrapper>{contact};

        RestContext.request = new RestRequest();
        RestContext.response = new RestResponse();
        RestContext.request.requestBody = Blob.valueOf(JSON.serialize(requestBody));

        // Call the method
        Test.startTest();
        RenewalMembershipAmountAPI.getRenewalMembershipDetails();
        Test.stopTest();

        // Validate response
        RestResponse response = RestContext.response;
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response.responseBody.toString());
    }

    @isTest
    static void testNoRecordsFound() {
        // Create request body with no matching records
        RenewalMembershipAmountAPI.RequestWrapper requestBody = new RenewalMembershipAmountAPI.RequestWrapper();
        RenewalMembershipAmountAPI.ObjPrimaryContactWrapper contact = new RenewalMembershipAmountAPI.ObjPrimaryContactWrapper();
        contact.primaryNumber = '**********'; // Non-existing primary number
        contact.countryCode = '91';
        requestBody.primaryContactList = new List<RenewalMembershipAmountAPI.ObjPrimaryContactWrapper>{contact};

        RestContext.request = new RestRequest();
        RestContext.response = new RestResponse();
        RestContext.request.requestBody = Blob.valueOf(JSON.serialize(requestBody));

        // Call the method
        Test.startTest();
        RenewalMembershipAmountAPI.getRenewalMembershipDetails();
        Test.stopTest();

        // Validate response
        RestResponse response = RestContext.response;
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response.responseBody.toString());
    }
}