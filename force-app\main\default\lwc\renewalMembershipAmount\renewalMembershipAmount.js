import { LightningElement, wire, api } from 'lwc';
import getRenewalMembershipAmountData from '@salesforce/apex/RenewalMembershipAmountController.renewalMembershipAmount';

export default class RenewalMembershipAmount extends LightningElement {

    @api recordId;
    membershipData;

    @wire(getRenewalMembershipAmountData, { recordId: '$recordId' })
    wiredMembershipData({ error, data }) {
        if (data) {
            this.membershipData = data;
        } else if (error) {
            // Handle error
            console.error('Error retrieving membership data', error);
        }
    }

    get year1Data() {
        return this.membershipData ? this.membershipData['Year 1'] : {};
    }

    get year2Data() {
        return this.membershipData ? this.membershipData['Year 2'] : {};
    }

    get year3Data() {
        return this.membershipData ? this.membershipData['Year 3'] : {};
    }

    get year4Data() {
        return this.membershipData ? this.membershipData['Year 4'] : {};
    }

    get year5Data() {
        return this.membershipData ? this.membershipData['Year 5'] : {};
    }
}