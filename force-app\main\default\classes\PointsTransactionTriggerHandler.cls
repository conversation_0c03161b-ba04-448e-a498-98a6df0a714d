public class PointsTransactionTriggerHandler {
    public static Boolean isFirstTime = true;
    public static void bulkAfter(List<Points_Transaction__c> PTList){
        system.debug('isFirstTime ::'+isFirstTime);
        if(isFirstTime){
            isFirstTime = false;
            set<ID> accId = new set<ID>();
            map<ID,Points_Transaction__c> mapAccId = new map<ID,Points_Transaction__c>();
            map<ID,Points_Transaction__c> mapDebitAccId = new map<ID,Points_Transaction__c>();
            for(Points_Transaction__c PT : PTList){
                accId.add(PT.Credit_To__c);
                accId.add(PT.Debit_From__c);
                mapAccId.put(PT.Credit_To__c,PT);
                mapDebitAccId.put(PT.Debit_From__c,PT);
            }
            List<Account> acList = [SELECT Id,Total_Refer_a_Startup_Points__c,Total_Investor_Call_Points__c,Total_Investment_Points__c,
                                    Total_Form_Complete_Points__c,Total_Backed_Out_Points__c,Total_Referal_Conversions__c,
                                    Total_Renew_Membership_Points__c,Total_Build_Communities_Points__c,Total_External_Event_Points__c,
                                    Total_Help_as_SME_Points__c,Total_Worked_as_a_Lead_Member_Points__c,Total_Worked_as_a_Co_lead_Points__c,
                                    Total_Write_a_thought_leadership_article__c,Total_Startup_Voting_Points__c,
                                    Total_LetsGrow_SIP_Call_Points__c,Total_LetsGrow_Review_Deck_Points__c,
                                    Total_LetsGrow_Goal_Achieved_Points__c,Total_LetsGrow_Fund_Raised_Points__c,
                                    Total_LetsGrow_Business_Connected_Points__c,Invest_5L_and_above__c,
                                    Bring_a_friend_family_member_along_to_IP__c,Achieve_Goals_for_LetsGrow_program_for_a__c,
                                    Complete_action_items__c,Business_Connect__c,Total_Points_redeemed_for_renewal__c,
                                    Investment_Fee_paid_via_Points__c,Adding_Angel_Investor_on_LinkedIn__c,
                                    Investor_Recording_Video_Testimonial__c,Total_Points_Help_as_SME_during_DD__c,
                                    Total_Help_as_SME_Points_and_present__c
                                    FROM Account WHERE ID IN : accId];
            system.debug('accId ::'+accId);
            system.debug('mapAccId ::'+mapAccId);
            system.debug('mapDebitAccId ::'+mapDebitAccId);
            for(Account ac : acList){
                if(mapAccId != null){
                    if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Refer a Startup'){
                        ac.Total_Refer_a_Startup_Points__c = ac.Total_Refer_a_Startup_Points__c != null ? ac.Total_Refer_a_Startup_Points__c+mapAccId.get(ac.Id).Points_Alloted__c : mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Investor Call'){
                        ac.Total_Investor_Call_Points__c = ac.Total_Investor_Call_Points__c != null ? ac.Total_Investor_Call_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Investment'){
                        ac.Total_Investment_Points__c = ac.Total_Investment_Points__c != null ? ac.Total_Investment_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Form Complete'){
                        ac.Total_Form_Complete_Points__c = ac.Total_Form_Complete_Points__c != null ? ac.Total_Form_Complete_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Backed Out'){
                        ac.Total_Backed_Out_Points__c = ac.Total_Backed_Out_Points__c != null ? ac.Total_Backed_Out_Points__c-mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Referal Conversions'){
                        ac.Total_Referal_Conversions__c = ac.Total_Referal_Conversions__c != null ? ac.Total_Referal_Conversions__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Renew Membership'){
                        ac.Total_Renew_Membership_Points__c = ac.Total_Renew_Membership_Points__c != null ? ac.Total_Renew_Membership_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Help Build Communities'){
                        ac.Total_Build_Communities_Points__c = ac.Total_Build_Communities_Points__c != null ? ac.Total_Build_Communities_Points__c+mapAccId.get(ac.Id).Points_Alloted__c : 100; //mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'External Event'){
                        ac.Total_External_Event_Points__c = ac.Total_External_Event_Points__c != null ? ac.Total_External_Event_Points__c+mapAccId.get(ac.Id).Points_Alloted__c : 100; //mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Help as SME'){
                        ac.Total_Help_as_SME_Points__c = ac.Total_Help_as_SME_Points__c != null ? ac.Total_Help_as_SME_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Worked as a Lead Member'){
                        ac.Total_Worked_as_a_Lead_Member_Points__c = ac.Total_Worked_as_a_Lead_Member_Points__c != null ? ac.Total_Worked_as_a_Lead_Member_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Worked as a Co-lead'){
                        ac.Total_Worked_as_a_Co_lead_Points__c = ac.Total_Worked_as_a_Co_lead_Points__c != null ? ac.Total_Worked_as_a_Co_lead_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Write a thought leadership article to be published on IPV website'){
                        ac.Total_Write_a_thought_leadership_article__c = ac.Total_Write_a_thought_leadership_article__c != null ? ac.Total_Write_a_thought_leadership_article__c+mapAccId.get(ac.Id).Points_Alloted__c :100;//mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Startup Voting'){                     //  The Logic of Multiply Points Given Below is Added by Sahil As Disscused With Mauli Ma'am on 15.10.2024
                        ac.Total_Startup_Voting_Points__c = ac.Total_Startup_Voting_Points__c != null ? ac.Total_Startup_Voting_Points__c+mapAccId.get(ac.Id).Points_Alloted__c : 5 * mapAccId.get(ac.Id).Points_Alloted__c;//mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'LetsGrow SIP Call'){
                        ac.Total_LetsGrow_SIP_Call_Points__c = ac.Total_LetsGrow_SIP_Call_Points__c != null ? ac.Total_LetsGrow_SIP_Call_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'LetsGrow Review Deck'){
                        ac.Total_LetsGrow_Review_Deck_Points__c = ac.Total_LetsGrow_Review_Deck_Points__c != null ? ac.Total_LetsGrow_Review_Deck_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :50;//mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'LetsGrow Goal Achieved'){
                        ac.Total_LetsGrow_Goal_Achieved_Points__c = ac.Total_LetsGrow_Goal_Achieved_Points__c != null ? ac.Total_LetsGrow_Goal_Achieved_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :175;//mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'LetsGrow Fund Raised'){
                        ac.Total_LetsGrow_Fund_Raised_Points__c = ac.Total_LetsGrow_Fund_Raised_Points__c != null ? ac.Total_LetsGrow_Fund_Raised_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :100;//mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'LetsGrow Business Connected'){
                        ac.Total_LetsGrow_Business_Connected_Points__c = ac.Total_LetsGrow_Business_Connected_Points__c != null ? ac.Total_LetsGrow_Business_Connected_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :25;//mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Invest 5L and above'){
                        ac.Invest_5L_and_above__c = ac.Invest_5L_and_above__c != null ? ac.Invest_5L_and_above__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Bring a friend/family member along to IPV Events'){
                        ac.Bring_a_friend_family_member_along_to_IP__c = ac.Bring_a_friend_family_member_along_to_IP__c != null ? ac.Bring_a_friend_family_member_along_to_IP__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Achieve Goals for LetsGrow program for a startup'){
                        ac.Achieve_Goals_for_LetsGrow_program_for_a__c = ac.Achieve_Goals_for_LetsGrow_program_for_a__c != null ? ac.Achieve_Goals_for_LetsGrow_program_for_a__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'LetsGrow Complete'){
                        ac.Complete_action_items__c = ac.Complete_action_items__c != null ? ac.Complete_action_items__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    	system.debug('inn :'+ac.Complete_action_items__c);
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Business Connect'){
                        ac.Business_Connect__c = ac.Business_Connect__c != null ? ac.Business_Connect__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Points Redeemed For Renewal'){
                        ac.Total_Points_redeemed_for_renewal__c = ac.Total_Points_redeemed_for_renewal__c != null ? ac.Total_Points_redeemed_for_renewal__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Points redeemed for investment fee'){
                        ac.Investment_Fee_paid_via_Points__c = ac.Investment_Fee_paid_via_Points__c != null ? ac.Investment_Fee_paid_via_Points__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Adding Angel Investor on LinkedIn'){
                        ac.Adding_Angel_Investor_on_LinkedIn__c = ac.Adding_Angel_Investor_on_LinkedIn__c != null ? ac.Adding_Angel_Investor_on_LinkedIn__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Investor Recording Video Testimonial'){
                        ac.Investor_Recording_Video_Testimonial__c = ac.Investor_Recording_Video_Testimonial__c != null ? ac.Investor_Recording_Video_Testimonial__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Help as SME during DD'){
                        ac.Total_Points_Help_as_SME_during_DD__c = ac.Total_Points_Help_as_SME_during_DD__c != null ? ac.Total_Points_Help_as_SME_during_DD__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapAccId.containsKey(ac.Id) && mapAccId.get(ac.Id).Point_Type__c == 'Help as SME during DD and present findings on the Investor Call'){
                        ac.Total_Help_as_SME_Points_and_present__c = ac.Total_Help_as_SME_Points_and_present__c != null ? ac.Total_Help_as_SME_Points_and_present__c+mapAccId.get(ac.Id).Points_Alloted__c :mapAccId.get(ac.Id).Points_Alloted__c;
                    }
                }
                
                if(mapDebitAccId != null){
                    if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Refer a Startup'){
                        ac.Total_Refer_a_Startup_Points__c = ac.Total_Refer_a_Startup_Points__c != null ? ac.Total_Refer_a_Startup_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c : 0-mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Investor Call'){
                        ac.Total_Investor_Call_Points__c = ac.Total_Investor_Call_Points__c != null ? ac.Total_Investor_Call_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :-mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Investment'){
                        ac.Total_Investment_Points__c = ac.Total_Investment_Points__c != null ? ac.Total_Investment_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Form Complete'){
                        ac.Total_Form_Complete_Points__c = ac.Total_Form_Complete_Points__c != null ? ac.Total_Form_Complete_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Backed Out'){
                        ac.Total_Backed_Out_Points__c = ac.Total_Backed_Out_Points__c != null ? ac.Total_Backed_Out_Points__c+mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Referal Conversions'){
                        ac.Total_Referal_Conversions__c = ac.Total_Referal_Conversions__c != null ? ac.Total_Referal_Conversions__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Renew Membership'){
                        ac.Total_Renew_Membership_Points__c = ac.Total_Renew_Membership_Points__c != null ? ac.Total_Renew_Membership_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Help Build Communities'){
                        ac.Total_Build_Communities_Points__c = ac.Total_Build_Communities_Points__c != null ? ac.Total_Build_Communities_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'External Event'){
                        ac.Total_External_Event_Points__c = ac.Total_External_Event_Points__c != null ? ac.Total_External_Event_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Help as SME'){
                        ac.Total_Help_as_SME_Points__c = ac.Total_Help_as_SME_Points__c != null ? ac.Total_Help_as_SME_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Worked as a Lead Member'){
                        ac.Total_Worked_as_a_Lead_Member_Points__c = ac.Total_Worked_as_a_Lead_Member_Points__c != null ? ac.Total_Worked_as_a_Lead_Member_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Worked as a Co-lead'){
                        ac.Total_Worked_as_a_Co_lead_Points__c = ac.Total_Worked_as_a_Co_lead_Points__c != null ? ac.Total_Worked_as_a_Co_lead_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Write a thought leadership article to be published on IPV website'){
                        ac.Total_Write_a_thought_leadership_article__c = ac.Total_Write_a_thought_leadership_article__c != null ? ac.Total_Write_a_thought_leadership_article__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Startup Voting'){
                        ac.Total_Startup_Voting_Points__c = ac.Total_Startup_Voting_Points__c != null ? ac.Total_Startup_Voting_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'LetsGrow SIP Call'){
                        ac.Total_LetsGrow_SIP_Call_Points__c = ac.Total_LetsGrow_SIP_Call_Points__c != null ? ac.Total_LetsGrow_SIP_Call_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'LetsGrow Review Deck'){
                        ac.Total_LetsGrow_Review_Deck_Points__c = ac.Total_LetsGrow_Review_Deck_Points__c != null ? ac.Total_LetsGrow_Review_Deck_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'LetsGrow Goal Achieved'){
                        ac.Total_LetsGrow_Goal_Achieved_Points__c = ac.Total_LetsGrow_Goal_Achieved_Points__c != null ? ac.Total_LetsGrow_Goal_Achieved_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'LetsGrow Fund Raised'){
                        ac.Total_LetsGrow_Fund_Raised_Points__c = ac.Total_LetsGrow_Fund_Raised_Points__c != null ? ac.Total_LetsGrow_Fund_Raised_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'LetsGrow Business Connected'){
                        ac.Total_LetsGrow_Business_Connected_Points__c = ac.Total_LetsGrow_Business_Connected_Points__c != null ? ac.Total_LetsGrow_Business_Connected_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Invest 5L and above'){
                        ac.Invest_5L_and_above__c = ac.Invest_5L_and_above__c != null ? ac.Invest_5L_and_above__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Bring a friend/family member along to IPV Events'){
                        ac.Bring_a_friend_family_member_along_to_IP__c = ac.Bring_a_friend_family_member_along_to_IP__c != null ? ac.Bring_a_friend_family_member_along_to_IP__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Achieve Goals for LetsGrow program for a startup'){
                        ac.Achieve_Goals_for_LetsGrow_program_for_a__c = ac.Achieve_Goals_for_LetsGrow_program_for_a__c != null ? ac.Achieve_Goals_for_LetsGrow_program_for_a__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'LetsGrow Complete'){
                        ac.Complete_action_items__c = ac.Complete_action_items__c != null ? ac.Complete_action_items__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Business Connect'){
                        ac.Business_Connect__c = ac.Business_Connect__c != null ? ac.Business_Connect__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Points Redeemed For Renewal'){
                        ac.Total_Points_redeemed_for_renewal__c = ac.Total_Points_redeemed_for_renewal__c != null ? ac.Total_Points_redeemed_for_renewal__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Points redeemed for investment fee'){
                        ac.Investment_Fee_paid_via_Points__c = ac.Investment_Fee_paid_via_Points__c != null ? ac.Investment_Fee_paid_via_Points__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Adding Angel Investor on LinkedIn'){
                        ac.Adding_Angel_Investor_on_LinkedIn__c = ac.Adding_Angel_Investor_on_LinkedIn__c != null ? ac.Adding_Angel_Investor_on_LinkedIn__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Investor Recording Video Testimonial'){
                        ac.Investor_Recording_Video_Testimonial__c = ac.Investor_Recording_Video_Testimonial__c != null ? ac.Investor_Recording_Video_Testimonial__c-mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Help as SME during DD'){
                        ac.Total_Points_Help_as_SME_during_DD__c = ac.Total_Points_Help_as_SME_during_DD__c != null ? ac.Total_Points_Help_as_SME_during_DD__c+mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }else if(mapDebitAccId.containsKey(ac.Id) && mapDebitAccId.get(ac.Id).Point_Type__c == 'Help as SME during DD and present findings on the Investor Call'){
                        ac.Total_Help_as_SME_Points_and_present__c = ac.Total_Help_as_SME_Points_and_present__c != null ? ac.Total_Help_as_SME_Points_and_present__c+mapDebitAccId.get(ac.Id).Points_Alloted__c :mapDebitAccId.get(ac.Id).Points_Alloted__c;
                    }
                }
                
            }
            if(!acList.isEmpty()){
                update acList;   
            }
        }
    }
    
    public static void beforeDelete(List<Points_Transaction__c> PTList){
        set<ID> accId = new set<ID>();
        Set<ID> deletedTransactionsID = new Set<ID>();
        map<ID,Points_Transaction__c> mapAccId = new map<ID,Points_Transaction__c>();
        map<ID,Points_Transaction__c> mapDebitAccId = new map<ID,Points_Transaction__c>();
        Map<Id , List<Points_Transaction__c>> pointsToDeleteFromCreditedAccount = new Map<Id , List<Points_Transaction__c>>();

        for(Points_Transaction__c PT : PTList){
            if(PT.Point_Type__c == 'Backed Out'){
                accId.add(PT.Credit_To__c);
                accId.add(PT.Debit_From__c);
                mapAccId.put(PT.Credit_To__c,PT);
                mapDebitAccId.put(PT.Debit_From__c,PT);
            }           
            deletedTransactionsID.add(PT.Id); 

            if(PT.Credit_To__c != null)
            {
                if(!pointsToDeleteFromCreditedAccount.containsKey(PT.Credit_To__c)){
                    pointsToDeleteFromCreditedAccount.put(PT.Credit_To__c, new List<Points_Transaction__c>());
                }
                pointsToDeleteFromCreditedAccount.get(PT.Credit_To__c).add(PT);
                // pointsToDeleteFromCreditedAccount.put(PT.Credit_To__c, PT);
            }
        }   
        if(mapAccId != null){
            List<Account> acList = [SELECT Id,Total_Backed_Out_Points__c FROM Account WHERE ID IN : mapAccId.keyset()];
            for(Account ac : acList){
                ac.Total_Backed_Out_Points__c = ac.Total_Backed_Out_Points__c + 200;
            }
            update acList;
        }

        System.debug('deletedTransactionsID :: '+deletedTransactionsID);
        //  Added by Sahilparvat on 04.04.2025
        //  To send IDs of the deleted transactions to the BackEnd/API to remove the transaction from the database as well.
        if(deletedTransactionsID != null && deletedTransactionsID.size() > 0){
            DeletedPointTransactionDetailsAPI.sendDeletedPointTransactionDetails(deletedTransactionsID);
        }

        //  Added by Sahilparvat on 07.04.2025 To Delete the points from the credited account and to add the points to the debit account
        //  when the transaction is deleted.
        if(pointsToDeleteFromCreditedAccount != null && pointsToDeleteFromCreditedAccount.size() > 0){
            deletePointFromAcocunt(pointsToDeleteFromCreditedAccount);
        }
    }

    //  Added by Sahilparvat on 07.04.2025 To Delete the points from the credited account and to add the points to the debit account
    //  when the transaction is deleted.
    public Static void deletePointFromAcocunt(Map<Id , List<Points_Transaction__c>> pointsToDeleteFromCreditedAccount){
       /* 
        List<Account> creditedAccountToUpdate = new List<Account>();
        List<Account> debitAccountToUpdate = new List<Account>();
        Boolean creditFlag;
        Boolean debitFlag;
        if(pointsToDeleteFromCreditedAccount != null){
            List<Account> accountList = [SELECT Id,Total_Refer_a_Startup_Points__c,Total_Investor_Call_Points__c,Total_Investment_Points__c,
                                    Total_Form_Complete_Points__c,Total_Backed_Out_Points__c,Total_Referal_Conversions__c,
                                    Total_Renew_Membership_Points__c,Total_Build_Communities_Points__c,Total_External_Event_Points__c,
                                    Total_Help_as_SME_Points__c,Total_Worked_as_a_Lead_Member_Points__c,Total_Worked_as_a_Co_lead_Points__c,
                                    Total_Write_a_thought_leadership_article__c,Total_Startup_Voting_Points__c,
                                    Total_LetsGrow_SIP_Call_Points__c,Total_LetsGrow_Review_Deck_Points__c,
                                    Total_LetsGrow_Goal_Achieved_Points__c,Total_LetsGrow_Fund_Raised_Points__c,
                                    Total_LetsGrow_Business_Connected_Points__c,Invest_5L_and_above__c,
                                    Bring_a_friend_family_member_along_to_IP__c,Achieve_Goals_for_LetsGrow_program_for_a__c,
                                    Complete_action_items__c,Business_Connect__c,Total_Points_redeemed_for_renewal__c,
                                    Investment_Fee_paid_via_Points__c,Adding_Angel_Investor_on_LinkedIn__c,
                                    Investor_Recording_Video_Testimonial__c,Total_Points_Help_as_SME_during_DD__c,
                                    Total_Help_as_SME_Points_and_present__c FROM Account WHERE ID IN : pointsToDeleteFromCreditedAccount.keyset()];

            for(Account account : accountList){
                
                creditFlag = false;
                debitFlag = false;

                List<Points_Transaction__c> pointTransactionList = pointsToDeleteFromCreditedAccount.get(account.Id);

                for(Points_Transaction__c pointTransaction : pointTransactionList){
                    
                    if(pointTransaction != null && pointTransaction.Points_Alloted__c != null){

                        Account debitAccount;

                        if(pointTransaction.Debit_From__c != null)
                        {
                            debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                        }

                        if(pointTransaction.Point_Type__c == 'Refer a Startup'){
                            account.Total_Refer_a_Startup_Points__c = account.Total_Refer_a_Startup_Points__c != null ? account.Total_Refer_a_Startup_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);


                            if(debitAccount != null)
                            {
                                debitAccount.Total_Refer_a_Startup_Points__c = debitAccount.Total_Refer_a_Startup_Points__c != null ? debitAccount.Total_Refer_a_Startup_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Investor Call'){
                            account.Total_Investor_Call_Points__c = account.Total_Investor_Call_Points__c != null ? account.Total_Investor_Call_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(debitAccount != null)
                            {
                                debitAccount.Total_Investor_Call_Points__c = debitAccount.Total_Investor_Call_Points__c != null ? debitAccount.Total_Investor_Call_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Investment'){
                            account.Total_Investment_Points__c = account.Total_Investment_Points__c != null ? account.Total_Investment_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(debitAccount != null)
                            {
                                debitAccount.Total_Investment_Points__c = debitAccount.Total_Investment_Points__c != null ? debitAccount.Total_Investment_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Form Complete'){
                            account.Total_Form_Complete_Points__c = account.Total_Form_Complete_Points__c != null ? account.Total_Form_Complete_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_Form_Complete_Points__c = debitAccount.Total_Form_Complete_Points__c != null ? debitAccount.Total_Form_Complete_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Referal Conversions'){
                            account.Total_Referal_Conversions__c = account.Total_Referal_Conversions__c != null ? account.Total_Referal_Conversions__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_Referal_Conversions__c = debitAccount.Total_Referal_Conversions__c != null ? debitAccount.Total_Referal_Conversions__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Renew Membership'){
                            account.Total_Renew_Membership_Points__c = account.Total_Renew_Membership_Points__c != null ? account.Total_Renew_Membership_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_Renew_Membership_Points__c = debitAccount.Total_Renew_Membership_Points__c != null ? debitAccount.Total_Renew_Membership_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Help Build Communities'){
                            account.Total_Build_Communities_Points__c = account.Total_Build_Communities_Points__c != null ? account.Total_Build_Communities_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_Build_Communities_Points__c = debitAccount.Total_Build_Communities_Points__c != null ? debitAccount.Total_Build_Communities_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'External Event'){
                            account.Total_External_Event_Points__c = account.Total_External_Event_Points__c != null ? account.Total_External_Event_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_External_Event_Points__c = debitAccount.Total_External_Event_Points__c != null ? debitAccount.Total_External_Event_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Help as SME'){
                            account.Total_Help_as_SME_Points__c = account.Total_Help_as_SME_Points__c != null ? account.Total_Help_as_SME_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_Help_as_SME_Points__c = debitAccount.Total_Help_as_SME_Points__c != null ? debitAccount.Total_Help_as_SME_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Worked as a Lead Member'){
                            account.Total_Worked_as_a_Lead_Member_Points__c = account.Total_Worked_as_a_Lead_Member_Points__c != null ? account.Total_Worked_as_a_Lead_Member_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_Worked_as_a_Lead_Member_Points__c = debitAccount.Total_Worked_as_a_Lead_Member_Points__c != null ? debitAccount.Total_Worked_as_a_Lead_Member_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Worked as a Co-lead'){
                            account.Total_Worked_as_a_Co_lead_Points__c = account.Total_Worked_as_a_Co_lead_Points__c != null ? account.Total_Worked_as_a_Co_lead_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_Worked_as_a_Co_lead_Points__c = debitAccount.Total_Worked_as_a_Co_lead_Points__c != null ? debitAccount.Total_Worked_as_a_Co_lead_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Write a thought leadership article to be published on IPV website'){
                            account.Total_Write_a_thought_leadership_article__c = account.Total_Write_a_thought_leadership_article__c != null ? account.Total_Write_a_thought_leadership_article__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_Write_a_thought_leadership_article__c = debitAccount.Total_Write_a_thought_leadership_article__c != null ? debitAccount.Total_Write_a_thought_leadership_article__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Startup Voting'){
                            account.Total_Startup_Voting_Points__c = account.Total_Startup_Voting_Points__c != null ? account.Total_Startup_Voting_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_Startup_Voting_Points__c = debitAccount.Total_Startup_Voting_Points__c != null ? debitAccount.Total_Startup_Voting_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'LetsGrow SIP Call'){
                            account.Total_LetsGrow_SIP_Call_Points__c = account.Total_LetsGrow_SIP_Call_Points__c != null ? account.Total_LetsGrow_SIP_Call_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_LetsGrow_SIP_Call_Points__c = debitAccount.Total_LetsGrow_SIP_Call_Points__c != null ? debitAccount.Total_LetsGrow_SIP_Call_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'LetsGrow Review Deck'){
                            account.Total_LetsGrow_Review_Deck_Points__c = account.Total_LetsGrow_Review_Deck_Points__c != null ? account.Total_LetsGrow_Review_Deck_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditFlag = true;
                            // creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_LetsGrow_Review_Deck_Points__c = debitAccount.Total_LetsGrow_Review_Deck_Points__c != null ? debitAccount.Total_LetsGrow_Review_Deck_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitFlag = true;
                                // debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'LetsGrow Goal Achieved'){
                            account.Total_LetsGrow_Goal_Achieved_Points__c = account.Total_LetsGrow_Goal_Achieved_Points__c != null ? account.Total_LetsGrow_Goal_Achieved_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_LetsGrow_Goal_Achieved_Points__c = debitAccount.Total_LetsGrow_Goal_Achieved_Points__c != null ? debitAccount.Total_LetsGrow_Goal_Achieved_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'LetsGrow Fund Raised'){
                            account.Total_LetsGrow_Fund_Raised_Points__c = account.Total_LetsGrow_Fund_Raised_Points__c != null ? account.Total_LetsGrow_Fund_Raised_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_LetsGrow_Fund_Raised_Points__c = debitAccount.Total_LetsGrow_Fund_Raised_Points__c != null ? debitAccount.Total_LetsGrow_Fund_Raised_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'LetsGrow Business Connected'){
                            account.Total_LetsGrow_Business_Connected_Points__c = account.Total_LetsGrow_Business_Connected_Points__c != null ? account.Total_LetsGrow_Business_Connected_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_LetsGrow_Business_Connected_Points__c = debitAccount.Total_LetsGrow_Business_Connected_Points__c != null ? debitAccount.Total_LetsGrow_Business_Connected_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'LetsGrow Complete'){
                            account.Complete_action_items__c = account.Complete_action_items__c != null ? account.Complete_action_items__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Complete_action_items__c = debitAccount.Complete_action_items__c != null ? debitAccount.Complete_action_items__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Invest 5L and above'){
                            account.Invest_5L_and_above__c = account.Invest_5L_and_above__c != null ? account.Invest_5L_and_above__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Invest_5L_and_above__c = debitAccount.Invest_5L_and_above__c != null ? debitAccount.Invest_5L_and_above__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Bring a friend/family member along to IPV Events'){
                            account.Bring_a_friend_family_member_along_to_IP__c = account.Bring_a_friend_family_member_along_to_IP__c != null ? account.Bring_a_friend_family_member_along_to_IP__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Bring_a_friend_family_member_along_to_IP__c = debitAccount.Bring_a_friend_family_member_along_to_IP__c != null ? debitAccount.Bring_a_friend_family_member_along_to_IP__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Achieve Goals for LetsGrow program for a startup'){
                            account.Achieve_Goals_for_LetsGrow_program_for_a__c = account.Achieve_Goals_for_LetsGrow_program_for_a__c != null ? account.Achieve_Goals_for_LetsGrow_program_for_a__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Achieve_Goals_for_LetsGrow_program_for_a__c = debitAccount.Achieve_Goals_for_LetsGrow_program_for_a__c != null ? debitAccount.Achieve_Goals_for_LetsGrow_program_for_a__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Business Connect'){
                            account.Business_Connect__c = account.Business_Connect__c != null ? account.Business_Connect__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Business_Connect__c = debitAccount.Business_Connect__c != null ? debitAccount.Business_Connect__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Points Redeemed For Renewal'){
                            account.Total_Points_redeemed_for_renewal__c = account.Total_Points_redeemed_for_renewal__c != null ? account.Total_Points_redeemed_for_renewal__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_Points_redeemed_for_renewal__c = debitAccount.Total_Points_redeemed_for_renewal__c != null ? debitAccount.Total_Points_redeemed_for_renewal__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Points redeemed for investment fee'){
                            account.Investment_Fee_paid_via_Points__c = account.Investment_Fee_paid_via_Points__c != null ? account.Investment_Fee_paid_via_Points__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Investment_Fee_paid_via_Points__c = debitAccount.Investment_Fee_paid_via_Points__c != null ? debitAccount.Investment_Fee_paid_via_Points__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Adding Angel Investor on LinkedIn'){
                            account.Adding_Angel_Investor_on_LinkedIn__c = account.Adding_Angel_Investor_on_LinkedIn__c != null ? account.Adding_Angel_Investor_on_LinkedIn__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Adding_Angel_Investor_on_LinkedIn__c = debitAccount.Adding_Angel_Investor_on_LinkedIn__c != null ? debitAccount.Adding_Angel_Investor_on_LinkedIn__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Investor Recording Video Testimonial'){
                            account.Investor_Recording_Video_Testimonial__c = account.Investor_Recording_Video_Testimonial__c != null ? account.Investor_Recording_Video_Testimonial__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Investor_Recording_Video_Testimonial__c = debitAccount.Investor_Recording_Video_Testimonial__c != null ? debitAccount.Investor_Recording_Video_Testimonial__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Help as SME during DD'){
                            account.Total_Points_Help_as_SME_during_DD__c = account.Total_Points_Help_as_SME_during_DD__c != null ? account.Total_Points_Help_as_SME_during_DD__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {
                                Account debitAccount = new Account(Id = pointTransaction.Debit_From__c);
                                debitAccount.Total_Points_Help_as_SME_during_DD__c = debitAccount.Total_Points_Help_as_SME_during_DD__c != null ? debitAccount.Total_Points_Help_as_SME_during_DD__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                        else if(pointTransaction.Point_Type__c == 'Help as SME during DD and present findings on the Investor Call'){
                            account.Total_Help_as_SME_Points_and_present__c = account.Total_Help_as_SME_Points_and_present__c != null ? account.Total_Help_as_SME_Points_and_present__c - pointTransaction.Points_Alloted__c : 0 - pointTransaction.Points_Alloted__c;
                            creditedAccountToUpdate.add(account);

                            if(pointTransaction.Debit_From__c != null)
                            {

                                debitAccount.Total_Help_as_SME_Points_and_present__c = debitAccount.Total_Help_as_SME_Points_and_present__c != null ? debitAccount.Total_Help_as_SME_Points_and_present__c + pointTransaction.Points_Alloted__c : pointTransaction.Points_Alloted__c;
                                debitAccountToUpdate.add(debitAccount);
                            }
                        }
                    }
                }
            }
        }

        if(!creditedAccountToUpdate.isEmpty() && creditedAccountToUpdate.size() > 0) {
            update creditedAccountToUpdate;
        }

        if(!debitAccountToUpdate.isEmpty() && debitAccountToUpdate.size() > 0) {
            update debitAccountToUpdate;
        }
        
        */
    }
}