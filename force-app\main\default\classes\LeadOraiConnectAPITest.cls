@isTest
private class LeadOraiConnectAPITest {

    @isTest
    static void testGetLeadDetails() {
        Lead__c testLead = new Lead__c(
            Relationship_Owner__c = USerInfo.getUserId(),
            Primary_Contact__c = '9988998800',
            Lead_Source__c = 'Facebook'
        );
        insert testLead;

        Test.startTest();
        RestRequest request = new RestRequest();
        request.requestURI = '/services/apexrest/LeadConnectAPI/getLeadDetails/9988998800';
        request.httpMethod = 'GET';
        RestContext.request = request;
        LeadOraiConnectAPI.LeadWrapper response = LeadOraiConnectAPI.getLeadDetails();
        Test.stopTest();

    }

    @isTest
    static void testUpdateLeadDetails() {
        Lead__c testLead = new Lead__c(
            Primary_Contact__c = '9988998801',
            Lead_Source__c = 'Facebook',
            Relationship_Owner__c = USerInfo.getUserId());
        insert testLead;

        Test.startTest();
        RestRequest request = new RestRequest();
        request.requestURI = '/services/apexrest/LeadConnectAPI/updateLeadDetails';
        request.httpMethod = 'POST';
        request.addParameter('phone', '9988998801');
        request.addParameter('option_selected', 'Test Option');
        request.addParameter('status', 'Test Status');
        RestContext.request = request;
        String response = LeadOraiConnectAPI.updateLeadDetails('9988998801', 'Test Option', 'Test Status');
        Test.stopTest();

    }
}