trigger contentDocumentLinkTrigger on ContentDocumentLink (after insert) {
    list<ContentDocumentLink> listCdl = new list<ContentDocumentLink >();
    for(ContentDocumentLink cdl : trigger.New)
    {    
        String objId = ''+cdl.LinkedEntityId;
        if(!objId.startsWith('005'))
        {
            listCdl.add(cdl);
        }
        system.debug('LinkedEntityId>>>>>'+cdl.LinkedEntityId);
    }
    system.debug('listCdl >>>>>'+listCdl );

    List<ContentVersion> contVer1 = [select title,VersionData from ContentVersion where ContentDocumentId =:listCdl[0].ContentDocumentId];
    system.debug('hjhjhLinkTrigger>>>>'+contVer1);
    
    Blob csvFileBody;
    String csvFullData;
    
    csvFileBody = contVer1[0].VersionData;
    system.debug('csvFileBody >>>>'+csvFileBody );
    
    AttachmentTrg_Handler.createRecordNew(csvFileBody,''+listCdl[0].LinkedEntityId);
    csvFullData = ''+csvFileBody.toString();
    system.debug('csvFullDatahjhj>>>>'+csvFullData);
}