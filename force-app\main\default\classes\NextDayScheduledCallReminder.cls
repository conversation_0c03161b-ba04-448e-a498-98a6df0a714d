global class NextDayScheduledCallReminder implements Schedulable {

    global void execute(SchedulableContext ctx) {
        scheduledCallReminder();
    }

    public static void scheduledCallReminder() {
      
        Set<Id> IPVRecordTypeIds = new Set<Id>{
            Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId(),
            Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId(),
            Schema.SObjectType.Case.getRecordTypeInfosByName().get('IPV').getRecordTypeId()
        };

        Date nextDay = Date.today().addDays(1);
        System.Debug('Next Day >>> ' + nextDay);
        
		List<Task> taskList  = [SELECT Id, OwnerId, WhatId, ActivityDate, Status, Subject, Type__c, Task_Due_Date_and_Time__c FROM Task
                                WHERE What.RecordTypeId IN :IPVRecordTypeIds
                                AND Status != 'Completed'
                                AND Task_Due_Date_and_Time__c != null
                                AND Task_Due_Date_and_Time__c >= :nextDay
                                AND Task_Due_Date_and_Time__c < :nextDay.addDays(1)
                                ORDER BY Task_Due_Date_and_Time__c ASC];

        Map<Id, List<Task>> tasksByOwner = new Map<Id, List<Task>>();
        
        for (Task t : taskList) {
            if (!tasksByOwner.containsKey(t.OwnerId)) {
                tasksByOwner.put(t.OwnerId, new List<Task>());
            }
            tasksByOwner.get(t.OwnerId).add(t);
        }
        
        Map<Id, User> userMap = new Map<Id, User>([
            SELECT Id, Name, Email FROM User WHERE Id IN :tasksByOwner.keySet()
        ]);

        // Query related SObjects (Account, Lead, Case)
        Set<Id> accountIds = new Set<Id>();
        Set<Id> leadIds = new Set<Id>();
        Set<Id> caseIds = new Set<Id>();
            
        for (Task t : taskList) {
            String sObjectName = String.valueOf(t.WhatId.getSObjectType());
            if (sObjectName == 'Account') accountIds.add(t.WhatId);
            else if (sObjectName == 'Lead__c') leadIds.add(t.WhatId);
            else if (sObjectName == 'Case') caseIds.add(t.WhatId);
        }
       
        Map<Id, Account> accountMap = new Map<Id, Account>([
            SELECT Id, Name FROM Account WHERE Id IN :accountIds
        ]);
        Map<Id, Lead__c> leadMap = new Map<Id, Lead__c>([
            SELECT Id, Name FROM Lead__c WHERE Id IN :leadIds
        ]);
        Map<Id, Case> caseMap = new Map<Id, Case>([
            SELECT Id, CaseNumber FROM Case WHERE Id IN :caseIds
        ]);

        List<Messaging.SingleEmailMessage> emailList = new List<Messaging.SingleEmailMessage>();

        for (Id ownerId : tasksByOwner.keySet()) {
            User user = userMap.get(ownerId);
            if (user == null || String.isEmpty(user.Email)) continue;

            // Build email content for the user
            String htmlBody = '<html><body>Hi <b>' + user.Name + '</b>,<br><br>';
            htmlBody += 'Below are the Task/s scheduled for tomorrow to plan your day. Please find the Task/s below:<br><br>';
            Integer taskNumber = 1;

            for (Task t : tasksByOwner.get(ownerId)) {

                String memberName = null;
                String fullURL = URL.getOrgDomainURL().toExternalForm() + '/' + t.WhatId;

                String sObjectName = String.valueOf(t.WhatId.getSObjectType());
                if (sObjectName == 'Account') memberName = accountMap.get(t.WhatId)?.Name;
                else if (sObjectName == 'Lead__c') memberName = leadMap.get(t.WhatId)?.Name;
                else if (sObjectName == 'Case') memberName = caseMap.get(t.WhatId)?.CaseNumber;

                String formatedCallTime = t.Task_Due_Date_and_Time__c != null ? t.Task_Due_Date_and_Time__c.format('HH:mm') : 'N/A';
    
                    htmlBody += 'Task ' + taskNumber + ':<br>';
                    htmlBody += 'Member Name: <b>' + memberName + '</b><br>';
                    htmlBody += 'Call Type: <b>' + t.Type__c + '</b><br>';
                    htmlBody += 'Call Time: <b>' + formatedCallTime + '</b><br>';
                    htmlBody += 'For Member/’s Details, click the link below: </b><br>';
                    htmlBody += 'Salesforce Link to Account/Lead/Case : <a href="' + fullURL + '">View Details</a><br><br>';
                    taskNumber++;
                }
    
                htmlBody += '</body></html>';
    
                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                email.setToAddresses(new String[]{user.Email});
                email.setSubject('Reminder: Your Tasks for tomorrow ' + nextDay.format());
                email.setHtmlBody(htmlBody);
                emailList.add(email);
            }
    
       if (!emailList.isEmpty()) {
           Messaging.sendEmail(emailList);
       }
    }
}