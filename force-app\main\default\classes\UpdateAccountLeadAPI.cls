@RestResource(urlMapping='/UpdateAccountLeadAPI/*')
global with sharing class UpdateAccountLeadAPI{

    @HttpPost
    global Static ResponseWrapper getAccounts()
    {
        ResponseWrapper wResponse = new ResponseWrapper();
 
        try{
            RestRequest req = RestContext.request;
            RestResponse res = Restcontext.response;
            string jsonReqString=req.requestBody.tostring();
            Set<Integer> objectIdSet = new Set<Integer>();
            Set<Integer> countryCodeSet = new Set<Integer>();
            Set<String> primaryNumberSet = new Set<String>();
            Map<String,objectRequestWrapper> primaryNumberMap = new Map<String,objectRequestWrapper>();
            Map<String,Boolean> resultMap = new Map<String,Boolean>();
              system.debug('wRequestEE11111>>>>>>>'+jsonReqString);          
            RequestWrapper wRequest =(RequestWrapper) JSON.deserialize(jsonReqString,RequestWrapper.class);
            
            system.debug('wRequest>>>>>>>'+wRequest);
            
            
                List<sObject> sObjectList = new List<sObject>();
                for(objectRequestWrapper acc: wRequest.objectList)
                {
                
                    system.debug('acc>>>'+acc);
                    If(acc.countryCode==null || acc.countryCode=='' || !acc.countryCode.isNumeric())
                        acc.countryCode = '91';
                    
                    if(String.IsNotBlank(acc.primaryNumber))
                    {
                        primaryNumberSet.add(acc.primaryNumber);
                    }
                    if(String.IsNotBlank(acc.countryCode) && acc.countryCode.isNumeric())
                    {
                        countryCodeSet.add(Integer.valueof(acc.countryCode.Trim()));
                    }
                    
                    if(String.IsNotBlank(acc.primaryNumber) && String.IsNotBlank(acc.countryCode) && acc.countryCode.isNumeric())
                    {
                        primaryNumberMap.put(Integer.valueof(acc.countryCode.Trim())+'-'+acc.primaryNumber,acc);
                        resultMap.put(Integer.valueof(acc.countryCode.Trim())+'-'+acc.primaryNumber,false);    
                    }
                    
                   // if(acc.Gain_from_Joining_IPV != null){
                    //    Multi_Select_Picklist_Field(acc.Gain_from_Joining_IPV);
                   // }
                }
                if(countryCodeSet.size()>0 && primaryNumberSet.size()>0)
                {
                    for(Lead__c ld : [Select id,Name,Personal_Email__c,Primary_Country_Code__c,Primary_Contact__c,Full_Primary_Contact__c,App_LinkedIn_ID__c,RecordType.Name from Lead__c where (Primary_Contact__c in :primaryNumberSet AND Primary_Country_Code__c in :countryCodeSet AND RecordType.Name = 'IPV' ) OR (Full_Primary_Contact__c in :primaryNumberSet)])
                    {
                        if(primaryNumberMap.containsKey(ld.Primary_Country_Code__c+'-'+ld.Primary_Contact__c) || primaryNumberSet.contains(''+ld.Full_Primary_Contact__c))
                        {
                            objectRequestWrapper crw = primaryNumberMap.get(ld.Primary_Country_Code__c+'-'+ld.Primary_Contact__c);
                           /* if(crw.App_signup_date!=null)
                                system.debug('crw.App_signup_date>>>>>>>'+crw.App_signup_date);
                                ld.App_signup_date__c = crw.App_signup_date;
                            if(crw.App_Status!=null)
                                system.debug('crw.App_Status>>>>>>>'+crw.App_Status);
                                ld.App_Status__c = crw.App_Status;
                            if(crw.NDA_Consent_APP!=null)
                                system.debug('crw.NDA_Consent_APP>>>>>>>'+crw.NDA_Consent_APP);
                                ld.NDA_Consent_APP__c = Boolean.valueOf(crw.NDA_Consent_APP);
                            if(crw.NDA_Consent_Date_APP!=null)
                                system.debug('crw.NDA_Consent_Date_APP>>>>>>>'+crw.NDA_Consent_Date_APP);
                                ld.NDA_Consent_Date_APP__c = crw.NDA_Consent_Date_APP;*/
                            
                            // added by ankush to avoid updation from orai on modified name and email. 27.01.23.
                            if(ld.name !='IPV-ORAI Test1'  && (crw.membername != null && Crw.membername == 'IPV-ORAI Test1'))
                                 ld.name = ld.Name;
                            
                            else if(crw.membername != null) 
                                ld.name = Crw.membername;
                            
                            if(crw.Company != null)
                                ld.company__C = crw.company;
                            if(crw.designation != null)
                                ld.Designation__c = crw.Designation;
                            if(crw.Are_you_a_first_time_investor != null)
                                ld.Are_you_a_first_time_investor__c = crw.Are_you_a_first_time_investor;
                            
                            if(ld.Personal_Email__c !='<EMAIL>' && (Crw.PersonalEmail !=null && Crw.PersonalEmail =='<EMAIL>'))
                                ld.Personal_Email__c = ld.Personal_Email__c;
                            
                            else if(Crw.PersonalEmail != null)
                                ld.Personal_Email__c = Crw.PersonalEmail;
                            If(crw.Gain_from_Joining_IPV != null){
                                ld.Gain_from_Joining__c = PreferedSectorAndGainIPV(crw.Gain_from_Joining_IPV);
                            }
                            if(crw.Preferred_Sectors_to_Invest != null){
                                ld.Preferred_Sectors_to_Invest__c = PreferedSectorAndGainIPV(crw.Preferred_Sectors_to_Invest);
                            }
                            if(crw.Preferred_Sub_Sectors_to_Invest != null){
                                ld.Preferred_Sub_Sectors_to_Invest__c = crw.Preferred_Sub_Sectors_to_Invest;
                            }
                             //Added as disscussed with Kishan 16.8.23
                             if(Crw.IRS_Date != null)
                            	ld.IRS_Date__c = Crw.IRS_Date;
                             If(Crw.IRS_Time != null)
                            	ld.IRS_Time__c = Crw.IRS_Time;
                             If(Crw.App_LinkedIn_ID != null)
                            	ld.App_LinkedIn_ID__c = Crw.App_LinkedIn_ID;
                             If(Crw.App_City != null)
                            	ld.App_City__c = Crw.App_City;
							If(Crw.App_Postal_Code != null)
                            	ld.App_Postal_Code__c = Crw.App_Postal_Code;
                            //Added By Bharat as disscussed With Ashish 29-08-23
                            If(Crw.App_PAN_Number != null)
                                ld.App_PAN_Number__c = Crw.App_PAN_Number;
                            If(Crw.App_Company != null)
                                ld.App_Company__c = Crw.App_Company;
                            If(Crw.App_Designation != null)
                                ld.App_Designation__c = Crw.App_Designation;
                            If(Crw.App_Address != null)
                                ld.App_Address__c = Crw.App_Address;
                            If(Crw.App_Industry != null)
                                ld.App_Industry__c = Crw.App_Industry;
                            If(Crw.App_Expertise != null)
                                ld.App_Expertise__c = Crw.App_Expertise;
                            If(Crw.App_Full_Name != null)
                                ld.App_Full_Name__c = Crw.App_Full_Name;
                            If(Crw.App_Email != null)
                                ld.App_Email__c = Crw.App_Email;
                            If(Crw.App_Country != null)
                                ld.App_Country__c = Crw.App_Country;
                            If(Crw.APP_Referred_By_Referral_Code != NULL){
                                Account referredByAccount = [SELECT Id , Name , Referred_By_Referral_Code__c , Unique_referral_code__c FROM Account WHERE Unique_referral_code__c =: Crw.APP_Referred_By_Referral_Code LIMIT 1] ;
                                ld.Referred_By__c = referredByAccount.Id ;
                                ld.Referred_By_Referral_Code__c = referredByAccount.Unique_referral_code__c;
                            }
                               resultMap.put(ld.Primary_Country_Code__c+'-'+ld.Primary_Contact__c,true);    
                            sObjectList.add(ld);
                        }
                    }
                    
                    for(Account ld : [Select id,Name,Personal_Email__c,Primary_Country_Code__c,Primary_Contact__c,Full_Primary_Contact__c,App_LinkedIn_ID__c,RecordType.Name from Account where (Primary_Contact__c in :primaryNumberSet AND Primary_Country_Code__c in :countryCodeSet AND RecordType.Name = 'IPV' ) OR (Full_Primary_Contact__c in :primaryNumberSet)])
                    {
                        if(primaryNumberMap.containsKey(ld.Primary_Country_Code__c+'-'+ld.Primary_Contact__c) || primaryNumberSet.contains(''+ld.Full_Primary_Contact__c))
                        {
                            objectRequestWrapper crw = primaryNumberMap.get(ld.Primary_Country_Code__c+'-'+ld.Primary_Contact__c);
                           /* if(crw.App_signup_date!=null)
                                system.debug('crw.App_signup_date>>>>>>>'+crw.App_signup_date);
                                ld.App_signup_date__c = crw.App_signup_date;
                            if(crw.App_Status!=null)
                                system.debug('crw.App_Status>>>>>>>'+crw.App_Status);
                                ld.App_Status__c = crw.App_Status;
                            if(crw.NDA_Consent_APP!=null)
                                 system.debug('crw.NDA_Consent_APP>>>>>>>'+crw.NDA_Consent_APP);
                                ld.NDA_Consent_APP__c = Boolean.valueOf(crw.NDA_Consent_APP);
                            if(crw.NDA_Consent_Date_APP!=null)
                                system.debug('crw.NDA_Consent_Date_APP>>>>>>>'+crw.NDA_Consent_Date_APP);
                                ld.NDA_Consent_Date_APP__c = crw.NDA_Consent_Date_APP; */
                           
                            // changed by ankush to avoid updation on modified name. 27.01.23.
                            if(ld.name !='IPV-ORAI Test1' && (crw.membername != null && Crw.membername == 'IPV-ORAI Test1'))
                                 ld.name = ld.Name;
                            
                            else if(crw.membername != null) 
                                ld.name = Crw.membername;
                            
                            if(crw.designation != null)
                                ld.Designation__c = crw.Designation;
                            if(crw.Company != null)
                                ld.company__C = crw.company;
                            if(crw.Are_you_a_first_time_investor != null)
                               ld.Are_you_a_first_time_investor_c__c = crw.Are_you_a_first_time_investor;
                            
                            if(ld.Personal_Email__c !='<EMAIL>' && (Crw.PersonalEmail !=null && Crw.PersonalEmail =='<EMAIL>'))
                                ld.Personal_Email__c = ld.Personal_Email__c;
                            
                            else if(Crw.PersonalEmail != null)
                                ld.Personal_Email__c = Crw.PersonalEmail;
                            If(crw.Gain_from_Joining_IPV != null){
                                ld.Gain_from_Joining_IPV__c = PreferedSectorAndGainIPV(crw.Gain_from_Joining_IPV);
                            }
                            if(crw.Preferred_Sectors_to_Invest != null){
                                ld.Preferred_Sectors_to_Invest__c = PreferedSectorAndGainIPV(crw.Preferred_Sectors_to_Invest);
                            }
                            if(crw.Preferred_Sub_Sectors_to_Invest != null){
                                ld.Preferred_Sub_Sectors_to_Invest__c = crw.Preferred_Sub_Sectors_to_Invest;
                            }
                            //Added as disscussed with Kishan 16.8.23  App_Postal_Code
                             if(Crw.IRS_Date != null)
                            	ld.IRS_Date__c = Crw.IRS_Date;
                            If(Crw.IRS_Time != null)
                            	ld.IRS_Time__c = Crw.IRS_Time;
                             If(Crw.App_LinkedIn_ID != null)
                            	ld.App_LinkedIn_ID__c = Crw.App_LinkedIn_ID;
                            If(Crw.App_City != null)
                            	ld.App_City__c = Crw.App_City;
                            If(Crw.App_Postal_Code != null)
                            	ld.App_Postal_Code__c = Crw.App_Postal_Code;
                            //Added By Bharat as disscussed With Ashish 29-08-23
                            If(Crw.App_PAN_Number != null)
                                ld.App_PAN_Number__c = Crw.App_PAN_Number;
                            If(Crw.App_Company != Null)
                                ld.App_Company__c = Crw.App_Company;
                            If(Crw.App_Designation != null)
                                ld.App_Designation__c = Crw.App_Designation;
                            If(Crw.App_Address != null)
                                ld.App_Address__c = Crw.App_Address;
                            If(Crw.App_Industry != null)
                                ld.App_Industry__c = Crw.App_Industry;
                            If(Crw.App_Expertise != null)
                                ld.App_Expertise__c = Crw.App_Expertise;
                            If(Crw.App_Full_Name != null)
                                ld.App_Full_Name__c = Crw.App_Full_Name;
                            If(Crw.App_Email != null)
                                ld.App_Email__c = Crw.App_Email;
							If(Crw.App_Country != null)
                                ld.App_Country__c = Crw.App_Country; 
                            if(Crw.APP_Referred_By_Referral_Code != NULL){
                                Account referredByAccount = [SELECT Id , Name , Referred_By_Referral_Code__c , Unique_referral_code__c FROM Account WHERE Unique_referral_code__c =: Crw.APP_Referred_By_Referral_Code LIMIT 1] ;
                                ld.ParentId = referredByAccount.Id ;
                                ld.Referred_By_Referral_Code__c = referredByAccount.Unique_referral_code__c;
                            }
                            resultMap.put(ld.Primary_Country_Code__c+'-'+ld.Primary_Contact__c,true);    
                            sObjectList.add(ld);
                        }
                    }
                }
                
            if(sObjectList.size()>0){
                    system.debug('objectlist>>'+sObjectList);
                    update sObjectList;
            }
            
            return createResponse(true,'Records are updated successfully.',resultMap);
        }
        catch(exception e)
        {
            system.debug('Update Lead error: '+e.getMessage());
            return createResponse(False,'Exception:'+e.getMessage(),null);
        }
    }
    
    public static ResponseWrapper createResponse(Boolean resFlag,String msg, Map<String,Boolean> dataMap)
    {
        ResponseWrapper res = new ResponseWrapper();
        res.isSuccess = resFlag;
        res.message = msg;
        
        if(dataMap!=null && dataMap.size()>0)
        {
            res.resultDataFound= dataMap;
        }
        return res;
    } 
    
    global class ResponseWrapper{
        global boolean isSuccess;
        global string message;
        global Map<String,Boolean> resultDataFound;
    }

        
    global class objectRequestWrapper{

       global String primaryNumber;
       global String countryCode;
       global string memberName;
       global string company;
       global string Designation;
       global string Are_you_a_first_time_investor;
       global String PersonalEmail;
       Global String Gain_from_Joining_IPV;
       Global String Preferred_Sectors_to_Invest;
       Global String Preferred_Sub_Sectors_to_Invest;
       Global Date IRS_Date;
	   Global Time IRS_Time;
       Global string App_LinkedIn_ID;
       global String App_City;
       Global Decimal App_Postal_Code;
       Global String App_PAN_Number;
       Global String App_Company;
       Global String App_Designation;
       Global String App_Address;
       Global String App_Industry;
       Global String App_Expertise;
       Global String App_Full_Name;
       Global String App_Email;
       Global String App_Country;
       //global Date App_signup_date;
       //global String App_Status;
       //global String NDA_Consent_APP;
       //global Date NDA_Consent_Date_APP;
       global String APP_Referred_By_Referral_Code;
    }
    global class requestWrapper{
        global List<objectRequestWrapper> objectList;    
    } 
    
   // Added by ankush for Gain from IPV and preferrd Sector field needs to be update based integer 23.8.23.
    public static String PreferedSectorAndGainIPV (String inputNumber) {
    Map<String, String> sectorMap = new Map<String, String>{
        '1' => 'I want to Generate Wealth through curated startup investments',
        '2' => 'I want to share experience and mentor startup founders',
        '3' => 'I want to learn/contribute to startup evaluation through due diligence',
        '4' => 'I want to expand network by connecting with like-minded individuals',
        '5' => 'I want to stay updated on evolving technologies and business models',
        '6' => 'I want to discover new products/services and partner with startups',
        '7' => 'Agri-Tech','8' => 'Clean-Tech','9' => 'Content/ Social','10' => 'D2C','11' => 'Deep-Tech','12' => 'Ecommerce','13' => 'Edutech',
        '14' => 'Fin-Tech','15' => 'Grocery-Tech','16' => 'Health-Tech','17' => 'HR-Tech','18' => 'Logistics','19' => 'Mobility','20' => 'Property-Tech',
        '21' => 'Retail-Tech','22' => 'Saas','23' => 'Sports & Fitness'
    	};

    String[] receivedValues = inputNumber.split(',');
    List<String> valuesToUpdate = new List<String>();

    for (String receivedValue : receivedValues) {
        if (sectorMap.containsKey(receivedValue)) {
            valuesToUpdate.add(sectorMap.get(receivedValue));
        	}
    	}

    	String multiSelectPicklistField = String.join(valuesToUpdate, ';');
    	return multiSelectPicklistField;
	}
}