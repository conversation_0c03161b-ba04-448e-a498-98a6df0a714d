@isTest
public class QuarterlyEmailTest {
    
    testmethod  Public static void QuarterlyMailTest(){
        
        List<Account> accountList1 = new List<Account>(); 
        List<Account> accountList2 = new List<Account>(); 
        List<Lead__c> LeadList1 = new List<Lead__c>(); 
        User rm = new User(
            FirstName = 'Test',
            LastName = 'Manager',
            <PERSON>as = 'tman',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Relationship Manager' LIMIT 1].Id,
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            Contact_No__c = '09' + String.valueOf(Crypto.getRandomInteger())
        );
        insert rm;
        
        Account mainParentAccount = TestFactory.createAccount();
        mainParentAccount.Personal_Email__c = '<EMAIL>';
        mainParentAccount.Relationship_Manager__c =rm.id;
        mainParentAccount.Date_of_receiving_lead__c =date.today().addDays(-30);
        mainParentAccount.Membership_Status__c ='Paid IPV Fee';
        
        mainParentAccount.Has_Been_Emailed__c = false;
        mainParentAccount.Date_of_Payment__c =date.today().addDays(-1);
        
        insert mainParentAccount;
        Account mainParentAccount2 = TestFactory.createAccount();
        mainParentAccount2.Personal_Email__c = '<EMAIL>';
        mainParentAccount2.Relationship_Manager__c =rm.id;
        mainParentAccount2.Date_of_receiving_lead__c =date.today().addDays(-30);
        mainParentAccount2.Membership_Status__c ='Paid IPV Fee';
        
        mainParentAccount2.Has_Been_Emailed__c = true;
        mainParentAccount2.Date_of_Payment__c =date.today().addDays(-1);
        
        insert mainParentAccount2;
        
        
        Account childAccount = TestFactory.createAccount();
        childAccount.ParentId = mainParentAccount.Id;
        childAccount.Personal_Email__c = '<EMAIL>';
        childAccount.Date_of_receiving_lead__c =date.today().addDays(-30);
        childAccount.Membership_Status__c ='Paid IPV Fee';
        childAccount.Date_of_Payment__c =date.today().addDays(-1);
        accountList1.add(childAccount);
        
        insert accountList1;
        
        
        
        
        Lead__c childLead = TestFactory.createLead();
        childLead.Referred_By__c = mainParentAccount.Id;
        childLead.Personal_Email__c = '<EMAIL>';
        childLead.Date_of_receiving_lead__c =date.today().addDays(-30);
        
        
        LeadList1.add(childLead);
        
        insert LeadList1;
        
        
        string jobName = 'Quarterly Email Scheduler Test';Test.startTest();
        QuarterlyEmailScheduler scheduler = new QuarterlyEmailScheduler();
        
        String cronExpression = '0 0 * * * ?';
        System.schedule(jobName, cronExpression, scheduler);
        
        string jobName2 = 'Quarterly Email  Reset Scheduler Test';
        QuarterlyEmailResetScheduler Resetscheduler = new QuarterlyEmailResetScheduler();
        
        String cronExpression2 = '0 0 * * * ?'; 
        System.schedule(jobName2, cronExpression2, Resetscheduler);
        
        
        
        Test.stopTest();
        QuarterlyEmailBatch batch =new QuarterlyEmailBatch();
        database.executeBatch(batch,10);
        
        QuarterlyEmailResetBatch batch2 =new QuarterlyEmailResetBatch();
        database.executeBatch(batch2,10);
    }
    
}