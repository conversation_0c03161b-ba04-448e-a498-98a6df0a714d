trigger InvestmentTrigger on Investment__c (before insert,before update,After Insert,After Update,before Delete,After Delete,After UnDelete) {
    
    /*
    for(Investment__c inv : trigger.New)
    {        
        if(inv.Id=='a0B0l00000C8MmOEAV')
            integer i = 1/0;
    }
    */
    
    List<System_Setting__c> settingList = System_Setting__c.getall().values();
    
    if(settingList!=null && settingList.size()>0 && !settingList[0].InvestmentTriggerActivated__c)
    {
        system.debug('Returning from Investment Trigger because of custom setting>>>'+settingList);
        return;
    }
    
    InvestmentTriggerHandler invCtrl = new InvestmentTriggerHandler();
    
    if(trigger.isInsert && trigger.isBefore)
    {
        invCtrl.beforeInsert(trigger.New);
        InvestmentTriggerHandler.caMappingBasedOnFundType(trigger.New);
    }
    else if(trigger.isInsert && trigger.isAfter)
    {
        invCtrl.afterInsert(trigger.New);
       
        
    }
    else if(trigger.isUpdate && trigger.isBefore)
    {
        if(!System.isBatch()){
        	invCtrl.beforeUpdate(trigger.New,trigger.OldMap);
            InvestmentTriggerHandler.caMappingBasedOnFundType(trigger.New);
            
        }
    }
    else if(trigger.isUpdate && trigger.isAfter)
    {
        if(!System.isBatch()){
        	invCtrl.afterUpdate(trigger.New,trigger.OldMap);
            
            //invCtrl.createPointTransaction(trigger.New,trigger.OldMap);
        
        }
    }
    else if(trigger.isDelete && trigger.isBefore)
    {
        //invCtrl.afterDelete(trigger.old);
        //Added by Karan 19/09/2022:
    }else if(trigger.isDelete && trigger.isAfter)
    {
        invCtrl.afterDelete(trigger.old);
    }else if(trigger.isUnDelete && trigger.isAfter)
    {
        invCtrl.afterInsert(trigger.New);
    }
   
}