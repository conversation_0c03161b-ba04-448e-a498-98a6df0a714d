/*****************************************************************************************************************
 This test class is for NextDayScheduledCallReminder & TodayScheduledCallReminder created on 18th April 2024. 
 *****************************************************************************************************************/
@isTest
public class NextDayScheduledCallReminderTest {

    @isTest
    static void testSchedulable() {
        
        Profile huntingProfile = [SELECT Id , Name FROM Profile WHERE Name = '_Hunting'];
        Profile farmingProfile = [SELECT Id , Name FROM Profile WHERE Name = '_Farming'];
        
        User huntingUser = new user();
        huntingUser.Email = '<EMAIL>';
        huntingUser.FirstName = 'Test';
        huntingUser.LastName ='Userxyz';
        huntingUser.Contact_No__c ='9988776655';
        huntingUser.MobilePhone = '1234566789';
        huntingUser.Alias ='Tuse';
        huntingUser.TimeZoneSidKey = 'Asia/Kolkata';
        huntingUser.LocaleSidKey ='en_US';
        huntingUser.EmailEncodingKey ='ISO-8859-1';
        huntingUser.LanguageLocaleKey ='en_US';
        huntingUser.Username = '<EMAIL>';
        huntingUser.ProfileId = huntingProfile.Id;
        insert huntingUser;
        
        User farmingUser = new user();
        farmingUser.Email = '<EMAIL>';
        farmingUser.FirstName = 'Test';
        farmingUser.LastName ='Userabc';
        farmingUser.Contact_No__c ='**********';
        farmingUser.MobilePhone = '**********';
        farmingUser.Alias ='Tuse';
        farmingUser.TimeZoneSidKey = 'Asia/Kolkata';
        farmingUser.LocaleSidKey ='en_US';
        farmingUser.EmailEncodingKey ='ISO-8859-1';
        farmingUser.LanguageLocaleKey ='en_US';
        farmingUser.Username = '<EMAIL>';
        farmingUser.ProfileId = farmingProfile.Id;
        insert farmingUser;
        
        Account testAccount = TestFactory.createAccount();
        insert testAccount;
         
        Lead__c testLead = TestFactory.createLead();
        insert testLead;
        
        /*
        Case testCase = new Case();
        testCase.Issue_raised_By__c = testAccount.Id;
        testCase.Date_Issue_Raised__c = Date.Today();
        insert testCase;
        */
        
        list<task> tasklist = new list<task>();
        
        Task testTask1 = new Task(WhatId = testAccount.Id,Subject ='Call',Type ='L1',Outcome__c ='Call Done',Status ='Completed' , OwnerId = huntingUser.Id ,Task_Due_Date_and_Time__c = Date.Today().addDays(1));
        tasklist.add(testTask1);
        
        Task testTask2 = new Task(WhatId = testLead.Id,Subject ='Call',Type ='L2',Outcome__c ='Call Done',Status ='Completed' , OwnerId = farmingUser.Id , Task_Due_Date_and_Time__c = Date.Today().addDays(1));
        tasklist.add(testTask2);
        
        Task testTask3 = new Task(WhatId = testAccount.Id,Subject = 'Call',Type ='L2',Outcome__c ='Call Done',Status ='Open', OwnerId = huntingUser.Id ,Task_Due_Date_and_Time__c = Date.Today().addDays(1));
        tasklist.add(testTask3); 
        
        Task testTask4 = new Task(WhatId = testAccount.Id,Subject ='Call',Type ='L1',Outcome__c ='Call Done',Status ='Completed' , OwnerId = huntingUser.Id ,Task_Due_Date_and_Time__c = Date.Today());
        tasklist.add(testTask4);
        
        Task testTask5 = new Task(WhatId = testLead.Id,Subject ='Call',Type ='L2',Outcome__c ='Call Done',Status ='Completed' , OwnerId = farmingUser.Id , Task_Due_Date_and_Time__c = Date.Today());
        tasklist.add(testTask5);
        
        Task testTask6 = new Task(WhatId = testAccount.Id,Subject = 'Call',Type ='L2',Outcome__c ='Call Done',Status ='Open', OwnerId = huntingUser.Id ,Task_Due_Date_and_Time__c = Date.Today());
        tasklist.add(testTask6); 
        
        insert tasklist;
        
        Test.startTest();
        NextDayScheduledCallReminder nextDayScheduled = new NextDayScheduledCallReminder();
        nextDayScheduled.execute(null);
        
        TodayScheduledCallReminder todayScheduled = new TodayScheduledCallReminder();
        todayScheduled.execute(null);
        Test.stopTest();
    }
}