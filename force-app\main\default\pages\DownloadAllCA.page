<apex:page controller="DownloadAllCA" standardStylesheets="false" lightningStylesheets="true">
   
    <style>
        /* Add your custom styles here */
        .modal {
        display: none;
        position: fixed;
        z-index: 1;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.4);
        }
        .modal-content {
        background-color: #fefefe;
        margin: 15% auto;
        padding: 20px;
        border: 1px solid #888;
        width: 80%;
        }
    </style>
    <script type="text/javascript">
    function openPopup() {
        event.preventDefault(); 
        var modal = document.getElementById('myModal');
        modal.style.display = 'block';
    }
    
    function closePopup() {
        var modal = document.getElementById('myModal');
        modal.style.display = 'none';
    }
    
    </script>
    <apex:form >
        <apex:pageBlock >
        	<apex:pageBlockSection >
            Please provide the file in following format:
                <table border="1">
                    <tr>
                        <th>CA no</th>
                        <th>From Date</th>
                        <th>To Date</th>
                    </tr>
                    <tr>
                        <th>CA0001</th>
                        <th>15/05/2022</th>
                        <th>15/05/2023</th>
                    </tr>
                    
                </table>
            </apex:pageBlockSection>
       
        <div id="myModal" class="modal fade" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">Provide CA No.</h4>
                      <!--  <button type="button" class="close" data-dismiss="modal">&times;</button>-->
                    </div>
                    <div class="modal-body">
                        <p><input value="{!CANo}"/></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" onClick="closePopup()" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        <apex:inputFile value="{!csvFileBody}"  filename="{!csvAsString}"/>
        
   <!--     <apex:commandButton value="Download Specific CA" onclick="openPopup()" />-->
        <apex:commandButton value="Mulitple CA Download" action="{!downloadAll}"/>
             </apex:pageBlock>
    </apex:form>
</apex:page>