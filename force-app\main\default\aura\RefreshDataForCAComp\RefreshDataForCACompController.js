({
    refreshTransfer : function(component, event, helper) {
        component.set("v.showSpinner", true);
        var action = component.get("c.refreshTransfers");
        action.setCallback(this,function(response){
            var state = response.getState();
            if (state === "SUCCESS") {     
            	alert('Records updated successfully..');
                component.set("v.showSpinner", false);
            }else{
                alert('Error Occured..');
                component.set("v.showSpinner", false);
            }
        });
        $A.enqueueAction(action);
    },
    refreshInvestment : function(component, event, helper) {
        component.set("v.showSpinner", true);
        var action = component.get("c.refreshInvestments");
        action.setCallback(this,function(response){
			var state = response.getState();
            if (state === "SUCCESS") {            
                //alert(response.getReturnValue());    
                var jobId = response.getReturnValue();
                component.set("v.jobId", jobId);
                helper.pollBatchStatus(component, jobId);
            }else{
                alert('Error Occured..');
                component.set("v.showSpinner", false);
            }
        });
        $A.enqueueAction(action);
    }
})