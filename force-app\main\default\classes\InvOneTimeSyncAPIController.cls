global class InvOneTimeSyncAPIController implements Database.Batchable<sObject>,Database.AllowsCallouts {
    Set<Id> invForAPISet = new Set<Id>();
    global Database.QueryLocator start(Database.BatchableContext BC)
    {
        return Database.getQueryLocator([SELECT Id, Name FROM Investment__c order by createddate desc limit 10]);
    }
    
    global void execute(Database.BatchableContext BC, List<sObject> scope)
    {
        for(sObject obj : scope) 
        {
            invForAPISet.add(obj.Id);
        }
        system.debug('invForAPISet>>>>>>>>>>>>>>>>>'+invForAPISet);
        if(invForAPISet.size()>0){
            //InvestmentRestAPIController.sendInvestmentDetails(invForAPISet,false);
            System.enqueueJob(new InvOneTimeSyncQueueableClass(invForAPISet));

        }
    }
    
    global void finish(Database.BatchableContext BC)
    {
    }
}