@isTest(SeeAllData=false)
public class StartupTriggerTestClassNew {
    
    @testSetup static void setup() {
        
        API_Setting__c setting = new API_Setting__c();
        setting.Name = 'Test Setting';
        setting.Enable_Investment_API_Call__c = true;
        setting.Enable_Investor_API_Call__c = true;
        setting.Enable_Startup_API_Call__c = true;
        setting.End_URL__c = 'test.com';
        insert setting;
    }
    @isTest
    static void startupTrigger()
    {
        Account acc = TestFactory.createAccount();
        insert acc;
        
        List<Account> accList = new List<Account>();
        accList.add(TestFactory.createAccount());
        accList.add(TestFactory.createAccount());
        insert accList;
        
        Startup__c stObj = TestFactory.createStartUp();
        stobj.Date_of_Investor_Call__c =Date.newInstance(2022, 08, 2);
        stObj.Referred_By_Internal__c = acc.Id;
        insert stObj;
        
        Startup__c stObj1 = TestFactory.createStartUp();
        stobj1.Date_of_Investor_Call__c =Date.newInstance(2022, 08, 2);
        stObj1.Referred_By_Internal__c = acc.Id;
        insert stObj1;
        
        Startup_Round__c strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,stObj.Id);
        strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 08, 2);
        strObj.Date_of_sending_out_call_for_money__c = Date.Today();
        strObj.Issue_Price__c = 111;
        strObj.SME_1__c = accList[1].ID;
        insert strObj;
        
        stObj.Description__c = 'testtest';
        update stObj;
    }
}