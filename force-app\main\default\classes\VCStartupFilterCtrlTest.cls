/************************************************************************
    Test Class for VCStartupFilterCtrl
************************************************************************/
@isTest(SeeAllData=false)
private class VCStartupFilterCtrlTest{
    
    @testSetup static void setup() {
        Startup__c st = TestFactory.createStartUp();
        st.Geographical_Focus__c = 'Both';
        st.Investment_Stage__c = 'Growth';
        st.Sector_Focus__c = 'B2B';
        st.Industry__c = 'Banking/ Financial Services';
        st.Round__c = 'Pre Series';
        insert st;         
         
        Venture_Connect__c vc = TestFactory.createVentureConnect();
        vc.Investment_Size_From__c = 1;
        vc.Investment_Size_To__c = 111;
        vc.Geographical_Focus__c = 'Both';
        vc.Investment_Stage__c = 'Growth';
        vc.Sector_Focus__c = 'B2B';
        vc.Industry__c = 'Banking/ Financial Services';
        vc.Series__c = 'Pre Series';

        insert vc;          
    }
    
    static testMethod void startupObjcase()
    {
        test.startTest();
            Startup__c stObj = [select id from Startup__c limit 1];
                
            ApexPages.StandardController sc = new ApexPages.StandardController(stObj);
            VCStartupFilterCtrl ctrlObj = new VCStartupFilterCtrl(sc); 
            
            
            stObj.Investment_Stage__c = 'Pre Revenue';
            //stObj.Broad_Category__c = 'B2B';
            stObj.Round__c = 'A';
            update stObj;
            
            sc = new ApexPages.StandardController(stObj);
            ctrlObj = new VCStartupFilterCtrl(sc); 
            
            
            
            //ctrlObj.getventureCList();
        test.stopTest();
    }   
    
    static testMethod void VCObjcase()
    {
        test.startTest();
            Venture_Connect__c stObj = [select id from Venture_Connect__c limit 1];
            Startup__c stObj1 = [select id from Startup__c limit 1];

            ApexPages.StandardController sc = new ApexPages.StandardController(stObj);
            VCStartupFilterCtrl ctrlObj = new VCStartupFilterCtrl(sc); 
            
            
            stObj.Investment_Stage__c = 'Pre Revenue';
            stObj.Sector_Focus__c = 'B2B';
            stObj.Series__c = 'A';
            update stObj;   

            stObj1.Fundraise__c = 10;
            update stObj1;               
            
            sc = new ApexPages.StandardController(stObj);
            ctrlObj = new VCStartupFilterCtrl(sc); 
            
            ctrlObj.getTotalPages();
            ctrlObj.getPageNumber();
            //ctrlObj.getTotal_size();
            ctrlObj.getDisableNext();
            ctrlObj.getDisablePrevious();
            ctrlObj.Next();
            ctrlObj.End();
            ctrlObj.Previous();
            ctrlObj.Beginning();
            //ctrlObj.getstartupList();
        test.stopTest();
    }    
}