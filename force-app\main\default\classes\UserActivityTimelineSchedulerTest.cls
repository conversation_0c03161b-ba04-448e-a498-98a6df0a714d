@isTest
global class UserActivityTimelineSchedulerTest {
    
  
    global class MockHttpResponseGenerator implements HttpCalloutMock {
       global HttpResponse respond(HttpRequest req) {
         HttpResponse res = new HttpResponse();
    res.setStatusCode(200);
  
    List<Account> accounts = [SELECT Id FROM Account];
    List<Lead__c> leads = [SELECT Id FROM Lead__c];

    List<String> ids = new List<String>();

    for(Account acc : accounts) {
        ids.add(acc.Id);
    }


    for(Lead__c lead : leads) {
        ids.add(lead.Id);
    }

   
    String responseBody = '{"data": {"summaries": [';

    for (Integer i = 0; i < ids.size(); i++) {
        responseBody += '{' +
            '"salesforce_user_account_id": "' + ids[i] + '", ' +
            '"Marking Startup as Favourite": {"count": 1}, ' +
            '"Startup": {"count": 29, "tab_taps": 23}, ' +
            '"Portfolio": {"count": 70, "tab_taps": 70}, ' +
            '"Sign IN": {"count": 84, "sign_ins": 81, "logouts": 3, "total_logout_duration": 102}, ' +
            '"Contact your RM": {"count": 3}, ' +
            '"Leaderboard": {"count": 3}, ' +
             '"Marking Startup as Favourite": {"count": 3}, ' +
             '"Refer a Startup": {"count": 3}, ' +
             '"Startup Download": {"count": 3}, ' +
            '"Referral": {"count": 6}, ' +
            '"Query Raised": {"count": 8}, ' +
            '"Agenda": {"count": 28, "tab_taps": 25}' +
            '}';

        if (i < ids.size() - 1) {
            responseBody += ',';
        }
    }

    responseBody += ']}}';

    res.setBody(responseBody);

    return res;
       }
    }

    @isTest
    static void testUserActivityTimelineBatch() {
      
        List<Account> accounts = new List<Account>();
        for (Integer i = 0; i < 5; i++) {
            account acc = TestFactory.createAccount();
            acc.Name = 'Test Account ' + i;
            acc.App_signup_date__c = Date.today().addDays(-i);
            accounts.add(acc);
        }
        insert accounts;
         List<Lead__c> testLeads = new List<Lead__c>();
        for (Integer i = 0; i < 5; i++) {
            Lead__c ld = TestFactory.createLead();
            testLeads.add(new Lead__c(
            Relationship_Owner__c = USerInfo.getUserId(),
            Primary_Contact__c = '*********'+i,
            App_signup_date__c = Date.today().addDays(-i),
            Lead_Source__c = 'Facebook'
            ));
        }
        insert testLeads;
        
        
       
    
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());

        Test.startTest();
        UserActivityTimelineBatchForAccount batch = new UserActivityTimelineBatchForAccount();
        Database.executeBatch(batch, 200);
        UserActivityTimelineBatchForLead Leads =new UserActivityTimelineBatchForLead();
        Database.executeBatch(Leads, 200);
        Test.stopTest();

    }

    @isTest
    static void testUserActivityTimelineScheduler() {
      
        Test.startTest();
        
        String jobName = 'User Activity Timeline Scheduler Test';
        UserActivityTimelineScheduale scheduler = new UserActivityTimelineScheduale();
       
        String cronExpression = '0 0 * * * ?'; // Every hour
        System.schedule(jobName, cronExpression, scheduler);
        
        Test.stopTest();

        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());
        
        UserActivityTimelineBatchForAccount accountBatch = new UserActivityTimelineBatchForAccount();
        Database.executeBatch(accountBatch, 200);
        UserActivityTimelineBatchForLead Leadsbatch =new UserActivityTimelineBatchForLead();
        Database.executeBatch(Leadsbatch, 200);

    }
}