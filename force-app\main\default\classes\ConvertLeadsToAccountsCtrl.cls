public class ConvertLeadsToAccountsCtrl{
    public List<Lead__c> selLeadList;
    public set<String> recIds;
    public boolean showCancelBtn{get;set;}
    public List<Sobject> targetObjectList{get;set;}
    
    public ConvertLeadsToAccountsCtrl(ApexPages.StandardSetController cntlr ) {
        selLeadList = cntlr.getSelected(); //get selected records from account list view
        recIds = new set<String>();
        system.debug('selLeadList>>>>'+selLeadList);
        
        for(sObject acc : selLeadList){
            recIds.add(acc.Id);//build list of ids string concatenated with comma                         
        }
        //recIds = recIds.removeEnd(','); 
        system.debug('recIds>>>>'+recIds );
    }
    
    public Pagereference convert()
    {
        system.debug('Save Task recIds>>>>'+recIds );
        if(recIds !=null && recIds.size()>0)
        {
            try{
                Id recordId = new List<String>(recIds).get(0); 
                //String sobjectType = recordId.getSObjectType().getDescribe().getName();
                String targetObjName = 'Account';
                DescribeSObjectResult describeResult = recordId.getSObjectType().getDescribe();
                List<String> fieldNames = new List<String>( describeResult.fields.getMap().keySet());
                List<String> fieldNamesUseList = new List<String>();
                targetObjectList = new List<Sobject>();
                Map<String,Schema.SObjectField> schemaMap = new Map<String,Schema.SObjectField>();
                Map<Id,sObject> sourceDestObjMap = new Map<Id,sObject>();
                List<Task> taskList = new List<Task>();
                List<SQL_Details__c> sqlDetailsList = new List<SQL_Details__c>();
                List<Transaction__c> transList = new List<Transaction__c>();
                List<Attendance__c> AttenList = new list<Attendance__c>();
                List<User_Activity__c> userActivitiesList = new List<User_Activity__c>();
                id IPVrecordTypeId = Schema.SObjectType.lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
                id CXOrecordTypeId = Schema.SObjectType.lead__c.getRecordTypeInfosByName().get('CXO Genie').getRecordTypeId();
                id IPVAccRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
                id CXOAccRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('CXO Genie').getRecordTypeId();
                Map<String , Id> leadPrimaryContactMap = New Map<String , Id>();
                Map<Id , Id> leadToAccountMap = New Map<Id , Id>();
                
                String query =
                    ' SELECT ' +
                    String.join( fieldNames, ',' ) +
                    ' FROM ' +
                    describeResult.getName() +
                    ' WHERE ' +
                    ' id in : recIds';
                
                system.debug('query>>>>'+query);
                
                List<SObject> sourceObjList = Database.query(query);
                System.debug('records>>>>>'+ sourceObjList);
                
                sObject targetObj = Schema.getGlobalDescribe().get(targetObjName).newSObject() ;
                schemaMap = targetObj.getSobjectType().getDescribe().fields.getMap();
                System.debug('schemaMap>>>>>'+ schemaMap);
                
                
                for(String f : fieldNames)
                {
                    if(schemaMap.containsKey(f) && schemaMap.get(f).getDescribe().isCreateable())
                    {
                        fieldNamesUseList.add(f);
                    }
                }
                
                for(SObject sObj : sourceObjList)
                {    
                    targetObj = Schema.getGlobalDescribe().get(targetObjName).newSObject();
                    
                    for(String f : fieldNamesUseList)
                    {
                        targetObj.put(f, sObj.get(f));
                    }
                    
                    // Added by Sahil on 27.11.2023 
                    // For Validation on City field.
                    if(sObj.get('City__c')!= null)
                    {
                        String lowercaseCity = (String)sObj.get('City__c');
                        lowercaseCity = lowercaseCity.toLowerCase();
                        String accountCityPicklist = 'City__c';
                        System.debug('Lead City >>>>> '+lowercaseCity);
                        
                        if (sObj.get('City__c')!= null && sObj.get('RecordTypeId') == IPVrecordTypeId && !isValidCity(lowercaseCity, accountCityPicklist))
                        {   
                            ApexPages.addMessage(new ApexPages.Message( ApexPages.Severity.ERROR,'Please update the city field according to the standard set of cities. You can access the list by clicking on the “Standardized city list” field on the lead object'));  
                            return null; 
                        }
                        else
                        {
                            if(sObj.get('City__c')!= null && sObj.get('RecordTypeId') == IPVrecordTypeId)
                            {
                                
                                targetobj.put('City__c',lowercaseCity);
                            }
                        }
                    }
                    else if(sObj.get('City__c') == null && sObj.get('RecordTypeId') == IPVrecordTypeId)
                    {
                        ApexPages.addMessage(new ApexPages.Message( ApexPages.Severity.ERROR,'City Field Can Not Be Blank While Converting Lead To Account'));  
                        return null;
                    }
                    
                    if(sObj.get('RecordTypeId')!= null && sObj.get('RecordTypeId') == IPVrecordTypeId)
                        targetobj.put('RecordTypeId',IPVAccRecordTypeId);
                    
                    else if(sObj.get('RecordTypeId')!= null && sObj.get('RecordTypeId') == CXOrecordTypeId)
                        targetobj.put('RecordTypeId',CXOAccRecordTypeId);
                    
                    //targetObj.put('RecordTypeId','0120l000001PyTUAA0');
                    targetObj.put('Is_Converted_From_Lead__c',True);
                    targetObj.put('Membership_Status__c','On Trial');
                    
                    if(sObj.get('Community_Group_Name__c')==null) 
                        targetObj.put('Membership_Status__c','On Trial');
                    
                    if(sObj.get('Primary_Group_Name__c')==null) 
                        targetObj.put('Membership_Status__c','On Trial Community');
                    
                    //Copy the refer by field on new Accounts.
                    if(sObj.get('Referred_By__c')!=null) 
                        targetObj.put('ParentId',sObj.get('Referred_By__c'));
                    
                    //Copy Leads' Relationship_Owner__c to Relationship_Manager__c on new Accounts.
                    if((sObj.get('RecordTypeId')!= null && sObj.get('RecordTypeId') == IPVrecordTypeId))
                    {
                        if(sObj.get('Relationship_Owner__c')!=null) 
                            targetObj.put('Relationship_Manager__c',sObj.get('Relationship_Owner__c'));
                    }
                    else if(sObj.get('RecordTypeId')!= null && sObj.get('RecordTypeId') == CXOrecordTypeId)
                    {
                        if(sObj.get('Relationship_Manager__c')!=null) 
                            targetObj.put('Relationship_Manager__c',sObj.get('Relationship_Manager__c'));
                    }
                    
                    // Added by ankush 19/01/22
                    if(sObj.get('Type_of_Lead__c') != null)
                        TargetObj.put('Type_of_Lead__c',Sobj.get('Type_of_Lead__c'));
                    
                    if(sobj.get('Are_you_a_first_time_investor__c')!=null)
                        TargetObj.put('Are_you_a_first_time_investor_c__c', Sobj.get('Are_you_a_first_time_investor__c'));
                    
                    if(Sobj.get('Date_of_Assignment_to_FiveS_RM__c') !=null)
                        TargetObj.put('Date_of_Assignment_to_FiveS_RM__c',Sobj.get('Date_of_Assignment_to_FiveS_RM__c'));
                    
                    if(Sobj.get('Date_of_Assignment_to_IPV_RM__c') != null)
                        targetObj.put('Date_of_Assignment_to_IPV_RM__c',Sobj.get('Date_of_Assignment_to_IPV_RM__c'));
                    
                    if(Sobj.get('utm_campaign__c') != null)
                        targetObj.put('UTM_Campaign__C',Sobj.get('utm_campaign__c'));
                    
                    if(Sobj.get('utm_medium__c') != null)
                        targetObj.put('UTM_Medium__C',Sobj.get('utm_medium__c'));
                    
                    if(Sobj.get('utm_source__c') != null)
                        targetObj.put('UTM_Source__C',Sobj.get('utm_source__c'));
                    
                    if(Sobj.get('Company__c') != null)
                        targetObj.put('Company__c',Sobj.get('Company__c'));
                    
                    // Bot journey fields added 8.11.23
                    if(Sobj.get('Bot_Input__c') != null)
                        targetObj.put('Bot_Input__c',Sobj.get('Bot_Input__c'));
                    
                    if(Sobj.get('Bot_Journey_Stage__c') != null)
                        targetObj.put('Bot_Journey_Stage__c',Sobj.get('Bot_Journey_Stage__c'));
                    
                    if(Sobj.get('BOT_Journey_Switch__c') == true)
                        targetObj.put('BOT_Journey_Switch__c',Sobj.get('BOT_Journey_Switch__c'));
                    
                    // App fields added by ankush 10-jan-2024        
                    if(Sobj.get('Gain_from_Joining__c') != null)
                        targetObj.put('Gain_from_Joining_IPV__c',Sobj.get('Gain_from_Joining__c'));
                    
                    if(Sobj.get('Preferred_Sectors_to_Invest__c') != null)
                        targetObj.put('Preferred_Sectors_to_Invest__c',Sobj.get('Preferred_Sectors_to_Invest__c'));
                    
                    if(Sobj.get('Preferred_Sub_Sectors_to_Invest__c') != null)
                        targetObj.put('Preferred_Sub_Sectors_to_Invest__c',Sobj.get('Preferred_Sub_Sectors_to_Invest__c'));
                    
                    if(Sobj.get('App_Full_Name__c') != null)
                        targetObj.put('App_Full_Name__c',Sobj.get('App_Full_Name__c'));
                    
                    if(Sobj.get('App_Address__c') != null)
                        targetObj.put('App_Address__c',Sobj.get('App_Address__c'));
                    
                    if(Sobj.get('App_City__c') != null)
                        targetObj.put('App_City__c',Sobj.get('App_City__c'));
                    
                    if(Sobj.get('App_Expertise__c') != null)
                        targetObj.put('App_Expertise__c',Sobj.get('App_Expertise__c'));
                    
                    if(Sobj.get('App_Industry__c') != null)
                        targetObj.put('App_Industry__c',Sobj.get('App_Industry__c'));
                    
                    if(Sobj.get('App_LinkedIn_ID__c') != null)
                        targetObj.put('App_LinkedIn_ID__c',Sobj.get('App_LinkedIn_ID__c'));
                    
                    if(Sobj.get('App_PAN_Number__c') != null)
                        targetObj.put('App_PAN_Number__c',Sobj.get('App_PAN_Number__c'));
                    
                    if(Sobj.get('App_Postal_Code__c') != null)
                        targetObj.put('App_Postal_Code__c',Sobj.get('App_Postal_Code__c'));
                    
                    if(Sobj.get('App_Status__c') != null)
                        targetObj.put('App_Status__c',Sobj.get('App_Status__c'));
                    
                    if(Sobj.get('App_Household_Income__c') != null)
                        targetObj.put('App_Household_Income__c',Sobj.get('App_Household_Income__c'));
                    
                    if(Sobj.get('App_Country__c') != null)
                        targetObj.put('App_Country__c',Sobj.get('App_Country__c'));
                    
                    if(Sobj.get('App_Company__c') != null)
                        targetObj.put('App_Company__c',Sobj.get('App_Company__c'));
                    
                    if(Sobj.get('App_Email__c') != null)
                        targetObj.put('App_Email__c',Sobj.get('App_Email__c'));
                    
                    if(Sobj.get('App_Designation__c') != null)
                        targetObj.put('App_Designation__c',Sobj.get('App_Designation__c'));
                    
                    if(Sobj.get('App_Age__c') != null)
                        targetObj.put('App_Age__c',Sobj.get('App_Age__c'));
                    
                    //  Added By Sahil On 22.01.2024
                    if(sObj.get('Referral_Taken_By__c')!=null) 
                        targetObj.put('Referral_Taken_By__c',sObj.get('Referral_Taken_By__c'));
                    
                    //  Added By Sahil On 28.10.2024
                    if(sObj.get('App_signup_date__c')!=null) 
                        targetObj.put('App_signup_date__c',sObj.get('App_signup_date__c'));
                    
                    //  Added By Akush on 23.05.2024 For "SBI Landing Page" Lead Source
                    if((sObj.get('Lead_Source__c') == 'SBI Landing Page' && sObj.get('couponCode__c') != null && sObj.get('RecordTypeId') == IPVrecordTypeId))
                    {
                        targetObj.put('Lead_Source__c','SBI Landing Page');  
                        targetObj.put('Membership_Validity__c' , Date.Today().addDays(365));
                        targetObj.put('Membership_Status__c' , 'Complimentary');
                        targetObj.put('Campaign__c' , 'SBI Partnership');
                        targetObj.put('Relationship_Manager__c',sObj.get('Relationship_Owner__c'));
                    }
                    
                    //Added by bharat for flow referred by details 
                    if(sObj.get('Referred_By_Referral_Code__c')!=null) 
                        targetObj.put('Referred_By_Referral_Code__c',sObj.get('Referred_By_Referral_Code__c'));
                    
                    targetObjectList.add(targetObj);
                    sourceDestObjMap.put(''+sObj.get('Id'),targetObj);
                    
                    //Added By Bharat For Flow Transaction Details from Lead To Account on 11-02-2024
                    leadPrimaryContactMap.Put(String.ValueOf(sObj.get('Primary_Contact__c')) , sObj.Id );

                    // Added By Sahilparvat on 12.03.2025 to map the "Lead From External Platform" Status From Lead to Account
                    if(sObj.get('Lead_From_External_Platform__c') != null)
                    {
                        targetObj.put('Lead_From_External_Platform__c',sObj.get('Lead_From_External_Platform__c'));
                    }

                    // Added By Sahilparvat on 12.03.2025 to map the "LinkedIn Questions And Answers"
                    if(sObj.get('LinkedIn_Question_1__c') != null)
                    {
                        targetObj.put('LinkedIn_Question_1__c',sObj.get('LinkedIn_Question_1__c'));
                    }
                    if(sObj.get('LinkedIn_Question_2__c') != null)
                    {
                        targetObj.put('LinkedIn_Question_2__c',sObj.get('LinkedIn_Question_2__c'));
                    }
                    if(sObj.get('LinkedIn_Question_3__c') != null)
                    {
                        targetObj.put('LinkedIn_Question_3__c',sObj.get('LinkedIn_Question_3__c'));
                    }

                    if(sObj.get('LinkedIn_Answer_1__c') != null)
                    {
                        targetObj.put('LinkedIn_Answer_1__c',sObj.get('LinkedIn_Answer_1__c'));
                    }
                    if(sObj.get('LinkedIn_Answer_2__c') != null)
                    {
                        targetObj.put('LinkedIn_Answer_2__c',sObj.get('LinkedIn_Answer_2__c'));
                    }
                    if(sObj.get('LinkedIn_Answer_3__c') != null)
                    {
                        targetObj.put('LinkedIn_Answer_3__c',sObj.get('LinkedIn_Answer_3__c'));
                    }

                    // Added By Sahilparvat on 01.05.2025 to map the Country Name of The LinkedIn Leads
                    if(sObj.get('Country_Name__c') != null)
                    {
                        targetObj.put('Country_Name__c',sObj.get('Country_Name__c'));
                    }
                }
                system.debug('targetObjectList>>>>'+targetObjectList);
                system.debug('sourceDestObjMap>>>>'+sourceDestObjMap);
                System.debug('leadPrimaryContactMap>>>>>>>' + leadPrimaryContactMap);
                insert targetObjectList;
                system.debug('sourceDestObjMap111>>>>'+sourceDestObjMap);
                System.debug('AccountConvertedList>>>' +targetObjectList);
                
                //Added By Bharat For Flow Transaction Details from Lead To Account on 11-02-2024
                For( sObject sObj : targetObjectList){
                    leadToAccountMap.Put(leadPrimaryContactMap.get(String.Valueof(sObj.get('Primary_Contact__c'))) , sObj.Id );
                }
                System.debug('leadToAccountMap>>>>>>>>>>' + leadToAccountMap);
                
                taskList = [select id,WhatId from Task where WhatId in:sourceDestObjMap.keyset()];
                system.debug('taskList>>>>'+taskList);
                
                if(taskList!=null && taskList.size()>0)
                {
                    for(Task tObj : taskList)
                    {
                        tObj.WhatId = ''+sourceDestObjMap.get(tObj.WhatId).get('Id');
                    }
                    update taskList;
                }
                
                //Added by ankush for attendance transfer.
                AttenList = [SELECT Id, Account__c,Lead__c  FROM Attendance__c WHERE Lead__c IN :sourceDestObjMap.keySet()];
                Map<Id, Attendance__c> updatedAttenMap = new Map<Id, Attendance__c>();
                
                for (Attendance__c atten : attenList) {
                    Id leadId = atten.Lead__c;
                    
                    if (sourceDestObjMap.containsKey(leadId)) {
                        SObject targetObjAcc = sourceDestObjMap.get(leadId);
                        if (targetObjAcc != null && targetObjAcc.Id != null) {
                            Attendance__c updatedAtten = new Attendance__c(
                                Id = atten.Id,
                                Account__c = (Id) targetObjAcc.get('Id') // Update the Account__c field
                            );
                            updatedAttenMap.put(updatedAtten.Id, updatedAtten); // Add the updated Attendance__c record to the map
                        }
                    }
                }
                // Update all the Attendance__c records in the map
                update updatedAttenMap.values();
                system.debug('Updated AttenList>>>>'+AttenList); 
                system.debug('Updated taskList>>>>'+taskList);
                
                //Added by Bharat for SQL Details transfer.
                sqlDetailsList = [select id,Account_Name__c , Lead_Name__c from SQL_Details__c where Lead_Name__c in:sourceDestObjMap.keyset()];
                system.debug('sqlDetailsList>>>>'+sqlDetailsList);
                
                if(sqlDetailsList!=null && sqlDetailsList.size()>0)
                {
                    for(SQL_Details__c sqlObj : sqlDetailsList)
                    {
                        sqlObj.Account_Name__c = (Id)leadToAccountMap.get(sqlObj.Lead_Name__c);
                        sqlObj.Lead_Name__c = null ;
                    }
                    update sqlDetailsList;
                }
                system.debug('Updated sqlDetailsList>>>>'+sqlDetailsList); 
                
                //Added by Bharat for Transaction transfer.
                
                transList = [select id,Account_Name__c , Leads_Name__c from Transaction__c where Leads_Name__c in:sourceDestObjMap.keyset()];
                system.debug('transList>>>>'+ transList);
                
                if(transList!=null && transList.size()>0)
                {
                    for(Transaction__c transObj : transList)
                    {
                        transObj.Account_Name__c = (Id)leadToAccountMap.get(transObj.Leads_Name__c);
                        transObj.Leads_Name__c = null;
                        
                    }
                    update transList;
                }
                system.debug('Updated transList>>>>'+transList);

                //Added by jay dabhi fro User activity Transfer
                userActivitiesList = [ SELECT Id, Related_Account__c, Related_Lead__c FROM User_Activity__c
                                      WHERE Related_Lead__c in :sourceDestObjMap.keyset() ];
                system.debug('userActivitiesList>>>>'+ userActivitiesList);
                
                if(userActivitiesList!=null && userActivitiesList.size()>0)
                {
                    for (User_Activity__c activity : userActivitiesList)
                    {
                        activity.Related_Account__c = (Id)leadToAccountMap.get(activity.Related_Lead__c); 
                        activity.Related_Lead__c = null; 
                    }
                    update userActivitiesList;
                }
                system.debug('Updated userActivitiesList>>>>'+userActivitiesList);
                
                delete sourceObjList;
                
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.CONFIRM,'Leads has been converted to Accounts successfully.'));
                
            }
            catch(Exception e)
            {
                system.debug('Exception>>>>'+e.getMessage());
                targetObjectList = new List<Sobject>();
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,''+e.getMessage())); 
            }
        }
        else
        {
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'Please go back and select at least one record to create the activity.')); 
        }
        
        return null;
    }
    public Pagereference cancelBtn()
    {
        return new PageReference('/a0F');
    }
    
    /*
public boolean doesFieldExist(String objName, string fieldName)
{
try{
SObject so = Schema.getGlobalDescribe().get(objName).newSObject();

Map<String,Schema.SObjectField> mapSchema = so.getSobjectType().getDescribe().fields.getMap();

if(mapSchema.containsKey(fieldName) && mapSchema.get(fieldName).getDescribe().isCreateable())
return true;
else
return false;
}
catch(Exception ex) {
showCancelBtn = true;
ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'The following exception has occurred: '+ex.getMessage()));
}

return false;
}
*/
    
    // Added by Sahil on 27.11.2023 
    // Method to check if the City value is valid in the picklist
    private boolean isValidCity(String lowercaseCity, String accountCityPicklist)
    {
        Set<String> validCities = new Set<String>{     
            'abu dhabi', 'agra', 'ahmedabad', 'ajmer', 'akola', 'alappuzha', 'ambala', 'amravati', 'angul', 'balasore',
                'banda', 'bangkok', 'bantval', 'barcelona', 'basel', 'bellaire', 'bendigo', 'bengaluru', 'bhilai', 'bhopal',
                'bhubaneshwar', 'bhubaneswar', 'bijapur', 'bikaner', 'calgary', 'california', 'chandigarh', 'chandrapur',
                'chennai', 'chicago', 'chittorgarh', 'cochin', 'coimbatore', 'columbus', 'copenhagen', 'cuttack', 'darmstadt',
                'dehradun', 'delft', 'delhi', 'denmark', 'dhule', 'dimapur', 'doha', 'dombivali', 'dubai', 'dublin', 'duliajan',
                'eindhoven', 'faridabad', 'frankfurt', 'fremont', 'gadag', 'gandhinagar', 'ghaziabad', 'ghazipur', 'goa', 'guntur',
                'gurgaon', 'guwahati', 'gwalior', 'haldwani', 'hamburg', 'harare', 'haridwar', 'ho chin', 'hong kong', 'hosur',
                'houston', 'hubballi', 'hyderabad', 'indonesia', 'indore', 'irkalgada', 'jabalpur', 'jaipur', 'jakarta',
                'jalandhar', 'jalna', 'jammu', 'jamnagar', 'jamshedpur', 'jhajjar', 'jharkhand', 'jharsuguda', 'joda', 'jodhpur',
                'johannesburg', 'kakinada', 'kanpur', 'kathmandu', 'kirkland', 'kochi', 'kolhapur', 'kolkata', 'kollam', 'kota',
                'kothamangalam', 'kottayam', 'kuala lumpur', 'kurnool', 'lagos', 'las vegas', 'latur', 'lausanne', 'london',
                'lucknow', 'ludhiana', 'malda', 'manama', 'mangalore', 'margao', 'maryland', 'massachusetts', 'mississauga',
                'mohali', 'moradabad', 'muktsar', 'mumbai', 'muscat', 'mysuru', 'nagpur', 'narnaul', 'nashik', 'navsari', 'nawada',
                'new jersey', 'new york', 'newark', 'noida', 'oman', 'oslo', 'panchkula', 'panipat', 'panjim', 'patna', 'pune',
                'raipur', 'rajkot', 'rajsamand', 'ramgarh', 'rampura phul', 'ranchi', 'remote', 'rewari', 'riffa', 'robbinsville',
                'rohtak', 'rourkela', 'sahibzada ajit singh nagar', 'sainthia', 'salem', 'san francisco', 'santa clara', 'satna',
                'seattle', 'secunderabad', 'seoul', 'shimoga', 'siliguri', 'silvassa', 'singapore', 'singrauli', 'sirsa', 'songadh',
                'srikakulam', 'state qatar - city not mentioned', 'stockholm', 'sukteri', 'surat', 'surrey', 'sydney', 'telangana',
                'texas', 'thane', 'thiruvananthapuram', 'thrissur', 'tiruvannamalai', 'toronto', 'uae', 'udaipur', 'ujjain', 'up',
                'us', 'usa', 'vadodara', 'valsad', 'varanasi', 'vellore', 'vijayawada', 'visakhapatnam', 'warangal', 'wassenaar',
                'yamunanagar', 'zirakpur' , 'not available'
                };
                    
                    // Check if the lowercaseCity is a valid city
                    return validCities.contains(lowercaseCity);
    }
    
}