public class StartupPipeline<PERSON>rigger<PERSON>and<PERSON> {
    private static Bo<PERSON>an hasAlreadyRun = false;
    
    public void beforeInsert(List<Startup_Pipeline__c> triggerNew ){
        
        for( Startup_Pipeline__c sp : triggerNew){
            
            if(sp.SP_Source__c != null && sp.SP_Source__c == 'Website'){
                sp.Date_of_Introduction_to_Founder__c = sp.SP_Startup_Received_on__c;
            }
            
            if(sp.Status_of_1st_Meeting__c != null && sp.Status_of_Follow_up_Call__c != null){
                sp.Final_Status__c = checkFinalStatus(sp.Status_of_Follow_up_Call__c);
            }else if(sp.Status_of_1st_Meeting__c != null && sp.Status_of_Follow_up_Call__c == null){
                sp.Final_Status__c = checkFinalStatus(sp.Status_of_1st_Meeting__c);
            }
            
            if(sp.Response_Time_TAT__c != null ){
                if(sp.Response_Time_TAT__c > 15){
                    sp.SLA_Status__c = 'Breached'; 
                }else If(sp.Response_Time_TAT__c <= 15){
                    sp.SLA_Status__c = 'Non Breached';
                }
            }
        }
    }
    
    public void beforeUpdate (List<Startup_Pipeline__c> triggerNew , Map<Id , Startup_Pipeline__c> oldMap){
        
        for(Startup_Pipeline__c sp : triggerNew){
            
            Startup_Pipeline__c oldRecord = oldMap.get(sp.Id);
            
            if(sp.SP_Source__c != null && sp.SP_Source__c == 'Website'){
                sp.Date_of_Introduction_to_Founder__c = sp.SP_Startup_Received_on__c;
            }
            if(sp.Status_of_1st_Meeting__c != null && sp.Status_of_Follow_up_Call__c != null && oldRecord.Status_of_Follow_up_Call__c != sp.Status_of_Follow_up_Call__c){
                sp.Final_Status__c = checkFinalStatus(sp.Status_of_Follow_up_Call__c);
            }else if(sp.Status_of_1st_Meeting__c != null && sp.Status_of_Follow_up_Call__c == null && oldRecord.Status_of_1st_Meeting__c != sp.Status_of_1st_Meeting__c){
                sp.Final_Status__c = checkFinalStatus(sp.Status_of_1st_Meeting__c);
            }
            
            if(sp.Response_Time_TAT__c != null && oldRecord.Response_Time_TAT__c != sp.Response_Time_TAT__c){
                System.debug('Response_Time_TAT__c>>>>>>>>>' + sp.Response_Time_TAT__c);
                if(sp.Response_Time_TAT__c > 15){
                    sp.SLA_Status__c = 'Breached';
                }else If(sp.Response_Time_TAT__c <= 15){
                    sp.SLA_Status__c = 'Non Breached';
                }
            }
        }
    }
    
    public void afterUpdate (List<Startup_Pipeline__c> triggerNew , Map<Id , Startup_Pipeline__c> oldMap){
        
        for (Startup_Pipeline__c sp : triggerNew){
            
            Startup_Pipeline__c oldRecord = oldMap.get(sp.Id);
            if(sp.SP_Source__c != null && sp.SP_Source__c == 'Website'){
                if(sp.Status_Post_Voting__c != oldRecord.Status_Post_Voting__c && sp.Status_Post_Voting__c == 'Shortlisted'){
                    System.debug('called onboarded>>>>>>>>>');
                    StartupPipelineEmailHandler.sendOnboardEmail(sp);
                }else If(sp.Status_Post_Voting__c != oldRecord.Status_Post_Voting__c && sp.Status_Post_Voting__c == 'Rejected'){
                    System.debug('called Rejectionn>>>>>>>>>');
                    StartupPipelineEmailHandler.sendRejectionEmail(sp);
                }
            }
        }
    }
    
    public string checkFinalStatus (String status ){
        
        System.debug('status>>>>>>>>>>>>>>>>>>' + status);
        String finalStatus;
        if(status == 'Call Scheduled' || status == 'Call Done' || status == 'Follow up call to be scheduled' || status == 'Feedback for samples awaited'){
            finalStatus = 'Pipeline';
        }else If(status == 'Call yet to be scheduled'){
            finalStatus = 'To be move to pipeline';
        }else If(status == 'DOI negotiations in progress'){
            finalStatus = status;
        }else If(status == 'DOI signed' || status == 'Awaiting scheduling' ||status == 'Founder\'s Call scheduled'){
            finalStatus = 'FC Scheduled / FC this Quarter';
        }else If(status == 'Founder\'s Call Done - Under DD'){
            finalStatus = 'DD in Progress / IC this Quarter';
        }else If(Status == 'No response from Founder' || status == 'Terms not agreed by founder' || status == 'Deferred by founder' || status == 'On Founder'){
            finalStatus = 'On Founder';
        }else If(status == 'Tracking Progress' || status =='On Hold'){
            finalStatus = 'On Hold';
        }else If(status =='Dropped' || status == 'Dropped due to Low Feedback' || status == 'Dropped post DD' || status == 'Dropped by founder after signing DOI'){
            finalStatus = 'Dropped';
        }else If(status == 'Investor\'s Call Done'){
            finalStatus = 'IC Done / CFM to be sent this Quarter';
        }else If(status == 'Moved to Physis'){
            finalStatus = status;
        }
        
        System.debug('finalStatus>>>>>>>>>>>>>>>>>>' + finalStatus);
        return finalStatus;
    }
}