/************************************************************************
    Test Class for OutboundMessageAPIController
************************************************************************/
@isTest(SeeAllData=false)
private class OutboundMessageAPIControllerTestClass{
    
    private class RestMock implements HttpCalloutMock {
        
        public HTTPResponse respond(HTTPRequest req) {
            String fullJson = 'your Json Response';
            Map<String,Object> credMAp1 = new Map<String,Object>();
            credMAp1.put('token','token111');
            
            Map<String,Object> credMAp = new Map<String,Object>();
            credMAp.put('data',credMAp1);
            credMAp.put('mobile','8866104284');
            credMAp.put('country_code','+91');
            fullJson = JSON.serialize(credMAp);
            
            HTTPResponse res = new HTTPResponse();
            res.setHeader('Content-Type', 'text/json');
            res.setBody(fullJson);
            res.setStatusCode(200);
            return res;
        }
    }
    
    @testSetup static void setup() {
        API_Setting__c setting = new API_Setting__c();
        setting.Name = 'Test Setting';
        setting.Enable_Investment_API_Call__c = true;
        setting.Enable_Investor_API_Call__c = true;
        setting.Enable_Startup_API_Call__c = true;
        setting.End_URL__c = 'test.com';
        setting.Whatsapp_from_address__c= '************';
        setting.Whatsapp_sId__c= 'HXAP1694460718IN';
        setting.Whatsapp_api_key__c= 'A0da16b6b946124f5b3e6ef6cb7b2e630';
        setting.Whatsapp_API_end_URL__c = 'https://orailap.azurewebsites.net/api/KORAIWhatsappNotification';
        setting.Whatsapp_API_Lead_Template_Name__c = 'https://orailap.azurewebsites.net/api/KORAIWhatsappNotification';
        insert setting;
        
        List<Account> accList = new List<Account>();
        account acc = TestFactory.createAccount();
        acc.Membership_Status__c = 'Complimentary';
        acc.Relationship_Manager__c = UserInfo.getUserId();
        accList.add(acc);
         
        insert accList;       
         
        
        Startup__c st = TestFactory.createStartUp();
        insert st;   
        
        Startup_Round__c strObj = TestFactory.createStartUpRound(accList[0].Id,accList[0].Id,st.Id);
        strObj.Date_of_sending_out_call_for_money__c = Date.newInstance(2022, 12, 1);
        strObj.Date_of_sending_out_call_for_money_AIF__c= Date.newInstance(2022, 12, 1);
        strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 12, 9);
        insert strObj;   
            
        strObj = [select id,name from Startup_Round__c limit 1];
            
       
        Lead__c ldObj = TestFactory.createLead();
        ldObj.Preferred_Email__c = accList[0].Personal_Email__c;
        ldObj.Relationship_Owner__c= UserInfo.getUserId();
        ldObj.Date_of_receiving_lead__c = Date.newInstance(2022, 12, 1);
        ldObj.Membership_Status__c = 'New Lead';
        insert ldObj;   
        
        List<Investment__c> invList = new list<Investment__c >();       
        
        Investment__c inv1 = TestFactory.createInvestment(accList[0].Id); 
        inv1 = TestFactory.createInvestment(accList[0].Id); 
        inv1.Investor_s_PAN__c = '**********'; 
        inv1.Startup_Round_Name__c = strObj.Name;
        inv1.Investment_in_Own_Name_Family_Member__c = 'LLP';
        inv1.Primary_Holder_Contact__c= accList[0].Primary_Contact__c;
        inv1.Is_Primary__c=false;
        inv1.Investor_Type__c = 'Via AIF';
        inv1.Type__c= 'Invested';
        inv1.Issue_Type__c = 'Friends & Family T1 - AIF';
        //inv1.Investment_Date__c = Date.newInstance(2022, 12, 1);    
        invList.add(inv1);

        insert invList;
    }
        
    static testMethod void invTrigger()
    {
        Test.setMock(HttpCalloutMock.class, new RestMock());

        test.startTest();
            set<Id> ldSet = new set<Id>();
            Map<Id, List<String>> ldSeinvIdTemplateMap = new Map<Id, List<String>>();
            List<ID> lstinvid = new List<ID>();
            for(Lead__c ld : [select id from Lead__c ])
                ldSet.add(ld.Id);
            OutboundMessageAPIController.sendWhatsappAPILead(ldSet);
            OutboundMessageAPIController.sendWhatsappAPIForNonRefLead(ldSet);
        for(Investment__c inv: [select id from Investment__c ]){
            lstinvid.add(inv.id);

            if (!ldSeinvIdTemplateMap.containsKey(inv.Id)) {
                ldSeinvIdTemplateMap.put(inv.Id, new List<String>());   
            }
            ldSeinvIdTemplateMap.get(inv.Id).add('back_out_request');
            ldSeinvIdTemplateMap.get(inv.Id).add('ipv_investment_feereceived_confirmation');
            ldSeinvIdTemplateMap.get(inv.Id).add('unapproved_back_out_pdf');
            ldSeinvIdTemplateMap.get(inv.Id).add('aif_investment_amount_received_confirmation');
            ldSeinvIdTemplateMap.get(inv.Id).add('nri_communication');
        }
        	
            OutboundMessageAPIController.sendWhatsappAPIInvPB(lstinvid);
        	// String jsonData = JSON.serialize(ldSeinvIdTemplateMap);
            // OutboundMessageAPIController.sendWhatsappAPIInv(jsonData);

            if(ldSeinvIdTemplateMap.size()>0)
            {
                String jsonData = JSON.serialize(ldSeinvIdTemplateMap);
                OutboundMessageAPIController.sendWhatsappAPIInv(jsonData);
                // OutboundMessageAPIController.sendWhatsappAPIInv(invWhatsAppAPIFeeChequeMap);
            }
        test.stopTest();
    }
     
}