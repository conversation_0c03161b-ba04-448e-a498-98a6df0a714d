@IsTest
public class CreateMembershipPaymentAPITest {

    @TestSetup
    static void setup() {
        Account acc1= TestFactory.createAccount();
        acc1.Name ='Test Acc';
        insert acc1;
        
        Lead__c ld1 = TestFactory.createLead();
        ld1.Name = 'Test Lead';
        insert ld1;
    }
    
    @IsTest
    static void testCreateTransaction() {
        
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        
        CreateMembershipPaymentRecordAPI.requestWrapper requestWrapper = new CreateMembershipPaymentRecordAPI.requestWrapper();
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper1 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        
        transactionWrapper1.payment_amount = 100.00;
        transactionWrapper1.payment_id = 'pay_test_123';
        transactionWrapper1.order_id = 'order_test_456';
        transactionWrapper1.transaction_at = DateTime.now();
        transactionWrapper1.is_renew = false;
        transactionWrapper1.is_promo_code = true;
        transactionWrapper1.promo_code = 'Test Promo';
        transactionWrapper1.salesforce_user_account_id = [SELECT Id FROM Lead__c LIMIT 1].Id;
        
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper2 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        transactionWrapper2.payment_amount = 200.00;
        transactionWrapper2.payment_id = 'pay_test_789';
        transactionWrapper2.order_id = 'order_test_101';
        transactionWrapper2.transaction_at = DateTime.now().addDays(-1);
        transactionWrapper2.is_renew = true;
        transactionWrapper2.total_used_points = 100;
        transactionWrapper2.total_year_of_payment = 1;
        transactionWrapper2.is_promo_code = false;
        transactionWrapper2.promo_code = '';
        transactionWrapper2.salesforce_user_account_id = [SELECT Id FROM Account LIMIT 1].Id;
        
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper3 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        transactionWrapper3.payment_amount = 200.00;
        transactionWrapper3.payment_id = 'pay_test_789';
        transactionWrapper3.order_id = 'order_test_101';
        transactionWrapper3.transaction_at = DateTime.now().addDays(-1);
        transactionWrapper3.is_renew = true;
        transactionWrapper3.total_used_points = 100;
        transactionWrapper3.total_year_of_payment = 2;
        transactionWrapper3.is_promo_code = false;
        transactionWrapper3.promo_code = '';
        transactionWrapper3.salesforce_user_account_id = [SELECT Id FROM Account LIMIT 1].Id;
        
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper4 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        transactionWrapper4.payment_amount = 200.00;
        transactionWrapper4.payment_id = 'pay_test_789';
        transactionWrapper4.order_id = 'order_test_101';
        transactionWrapper4.transaction_at = DateTime.now().addDays(-1);
        transactionWrapper4.is_renew = true;
        transactionWrapper4.total_used_points = 100;
        transactionWrapper4.total_year_of_payment = 3;
        transactionWrapper4.is_promo_code = false;
        transactionWrapper4.promo_code = '';
        transactionWrapper4.salesforce_user_account_id = [SELECT Id FROM Account LIMIT 1].Id;
        
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper5 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        transactionWrapper5.payment_amount = 200.00;
        transactionWrapper5.payment_id = 'pay_test_789';
        transactionWrapper5.order_id = 'order_test_101';
        transactionWrapper5.transaction_at = DateTime.now().addDays(-1);
        transactionWrapper5.is_renew = true;
        transactionWrapper5.total_used_points = 100;
        transactionWrapper5.total_year_of_payment = 4;
        transactionWrapper5.is_promo_code = false;
        transactionWrapper5.promo_code = '';
        transactionWrapper5.salesforce_user_account_id = [SELECT Id FROM Account LIMIT 1].Id;
        
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper6 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        transactionWrapper6.payment_amount = 200.00;
        transactionWrapper6.payment_id = 'pay_test_789';
        transactionWrapper6.order_id = 'order_test_101';
        transactionWrapper6.transaction_at = DateTime.now().addDays(-1);
        transactionWrapper6.is_renew = true;
        transactionWrapper6.total_used_points = 100;
        transactionWrapper6.total_year_of_payment = 5;
        transactionWrapper6.is_promo_code = false;
        transactionWrapper6.promo_code = '';
        transactionWrapper6.salesforce_user_account_id = [SELECT Id FROM Account LIMIT 1].Id;
        
        requestWrapper.dataList = new List<CreateMembershipPaymentRecordAPI.transactionRequestWrapper>{ transactionWrapper1 , transactionWrapper2 , transactionWrapper3 , transactionWrapper4 , transactionWrapper5 , transactionWrapper6 };
        String jsonReq = JSON.serialize(requestWrapper);
        req.requestURI = '/services/apexrest/CreateMembershipPaymentRecordAPI/';
        req.httpMethod = 'POST';
        req.requestBody = Blob.valueOf(jsonReq);
        RestContext.request = req;
        RestContext.response = res;
        
        CreateMembershipPaymentRecordAPI.ResponseWrapper response = CreateMembershipPaymentRecordAPI.createTransaction();
    }
}