public class CaseUserTimeLineActivityHandler {
    
    // Added by jay dab<PERSON>on 6th september 2024 for handling user activity based on Case Type changes
    public static void handleinsertCaseActivity(List<Case> newCaseList) {
        List<User_Activity__c> userActivityRecords = new List<User_Activity__c>();
        id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        
        for (Case caseRecord : newCaseList) {
            system.debug('Processing Case Insert: ' + caseRecord);
            
            
            if (caseRecord.Id != null && caseRecord.Status == 'WIP') { // New case ID check
                String CaseUrl;
                CaseUrl = URL.getOrgDomainURL().toExternalForm() + '/' + caseRecord.Id;
                User_Activity__c activity = new User_Activity__c();
                activity.Related_Account__c = caseRecord.Issue_raised_By__c;
                activity.Activity_Type__c = 'Raised a query via Raise a query or Case created on CRM manually';
                activity.Activity_Detail_RICH__c = 'Case Raised: ' + (CaseUrl != '' ? '<a href="' + CaseUrl + '" target="_blank">' + caseRecord.CaseNumber + '</a>' :caseRecord.CaseNumber);
                activity.Time_Stamp__c = caseRecord.Date_Issue_Raised__c ;
                userActivityRecords.add(activity);
                system.debug('Adding Case Raised Activity for Case: ' + caseRecord.Id + ' Details: ' + activity);
            } else {
                system.debug('Skipped Case Insert for Case: ' + caseRecord.Id + ' Status: ' + caseRecord.Status);
            }
        }
        if (!userActivityRecords.isEmpty()) {
            try {
                insert userActivityRecords;
                system.debug('User Activity records inserted: ' + userActivityRecords);
            } catch (Exception e) {
                system.debug('Error during User Activity record insertion: ' + e.getMessage());
            }
        } else {
            system.debug('No User Activity records to insert.');
        }
    }

    public static void handleUpdateCaseActivity(List<Case> newCaseList, Map<Id, Case> oldCAseMap) { 
        List<User_Activity__c> userActivityRecords = new List<User_Activity__c>();
        id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        if (oldCAseMap != null) {
            for (Case caseRecord : newCaseList) {
                Case oldCase = oldCAseMap.get(caseRecord.Id);
                system.debug('Processing Case Update: ' + caseRecord);
                
                Set<String> validStatus = new Set<String>{'Closed', 'Closed - SOP updated'};
                          
                    if (oldCase != null && oldCase.Status != caseRecord.Status && validStatus.contains(caseRecord.Status)) {
                        User_Activity__c activity = new User_Activity__c();
                        String CaseUrl;
                        CaseUrl = URL.getOrgDomainURL().toExternalForm() + '/' + Case.Id;
                        activity.Related_Account__c = caseRecord.Issue_raised_By__c;
                        activity.Activity_Type__c = 'Query Resolved';
                        activity.Activity_Detail_RICH__c = 'Case Resolved : ' + (CaseUrl != '' ? '<a href="' + CaseUrl + '" target="_blank">' + caseRecord.CaseNumber + '</a>' :caseRecord.CaseNumber);
                        activity.Time_Stamp__c =  caseRecord.Date_Issue_Resolved__c;
                        userActivityRecords.add(activity);
                        system.debug('Adding Case Resolved Activity for Case: ' + caseRecord.Id + ' Details: ' + activity);
                    } else {
                        system.debug('Skipped Case Update for Case: ' + caseRecord.Id + ' Old Status: ' + oldCase.Status + ' New Status: ' + caseRecord.Status);
                    }
            }
            if (!userActivityRecords.isEmpty()) {
                try {
                    insert userActivityRecords;
                    system.debug('User Activity records inserted: ' + userActivityRecords);
                } catch (Exception e) {
                    system.debug('Error during User Activity record insertion: ' + e.getMessage());
                }
            } else {
                system.debug('No User Activity records to insert.');
            }
        }
    }
}