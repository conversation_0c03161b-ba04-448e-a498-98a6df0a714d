public class MembershipSlabCalculationBatch implements Database.Batchable < sObject > , Database.Stateful {

    private Integer batchSize;
    Id IPVrecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
    
    public MembershipSlabCalculationBatch(Integer batchSize) {
        this.batchSize = batchSize;
    }

    public Database.QueryLocator start(Database.BatchableContext context) {
        return Database.getQueryLocator([SELECT Id, Membership_Slab__c, Membership_Status__c, Total_Point__c , Total_Amount_Invested_in_Last_12_Months__c, Date_of_Slab_Updation__c, Membership_Slab_Validity_Upto__c
                                         FROM Account
                                         WHERE(Total_Point__c != null OR Total_Amount_Invested_in_Last_12_Months__c != null)
                                         AND(Membership_Status__c NOT IN('On Trial', 'On Trial Community', 'Rotated by IPV', 'Exited by Own', 'Exited Member Reactivated', 'Add On', 'Old Yet to Pay', 'SIC', 'Physis Contributor', 'Physis Prospect'))
                                         AND RecordTypeId =: IPVrecordTypeId]);
    }

    public void execute(Database.BatchableContext context, List < Account > accountList) {
        
        Boolean isLast12MothInvestmentExecuted = last12MonthInvestment();
        
        if (last12MonthInvestment()) {
            
            List <Account> accountsToUpdate = new List <Account>();
			List <Account> sendToSiverMembershipEmailList = new List<Account>();
            
            for (Account acc: accountList) {
                Boolean flag = false;
                Decimal totalPoints, investmentAmount;
                Date slabUpdationDate, slabValidityDate;

				totalPoints = acc.Total_Point__c != null ? acc.Total_Point__c : 0;
                
                investmentAmount = acc.Total_Amount_Invested_in_Last_12_Months__c != null ? acc.Total_Amount_Invested_in_Last_12_Months__c : 0;

                slabUpdationDate = acc.Date_of_Slab_Updation__c != null ? acc.Date_of_Slab_Updation__c : null;

                slabValidityDate = acc.Membership_Slab_Validity_Upto__c != null ? acc.Membership_Slab_Validity_Upto__c : null;

                if (slabValidityDate == null) {
                    if (totalPoints < 500 || investmentAmount < 1000000) {
                        if (acc.Membership_Slab__c != 'Bronze') {
                            acc.Membership_Slab__c = 'Bronze';
                            acc.Date_of_Slab_Updation__c = Date.Today();
                            acc.Membership_Slab_Validity_Upto__c = null;
                            flag = true;
                        }
                    } 
                    if ((totalPoints >= 500 && totalPoints <= 999) || (investmentAmount >= 1000000 && investmentAmount < 3000000)) {
                        if (acc.Membership_Slab__c != 'Silver') {
                            acc.Membership_Slab__c = 'Silver';
                            acc.Date_of_Slab_Updation__c = Date.Today();
                            acc.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
                            flag = true;
                        }
                    }
                    if (totalPoints >= 1000 || investmentAmount > 3000000) {
                        if (acc.Membership_Slab__c != 'Gold') {
                            acc.Membership_Slab__c = 'Gold';
                            acc.Date_of_Slab_Updation__c = Date.Today();
                            acc.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
                            flag = true;
                        }
                    }
                } else {
                    if (acc.Membership_Slab__c == 'Silver') {
                        if ((totalPoints < 500 || investmentAmount < 1000000) && slabValidityDate < Date.Today()) {
                            acc.Membership_Slab__c = 'Bronze';
                            acc.Date_of_Slab_Updation__c = Date.Today();
                            acc.Membership_Slab_Validity_Upto__c = null;
                            flag = true;
                        }
                        if (((totalPoints > 500 && totalPoints <= 999) || (investmentAmount >= 1000000 && investmentAmount < 3000000)) && slabValidityDate < Date.Today()) {
                            acc.Membership_Slab__c = 'Silver';
                            acc.Date_of_Slab_Updation__c = Date.Today();
                            acc.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
                            flag = true;
                        }
                        if (totalPoints >= 1000 || investmentAmount > 3000000) {
                            acc.Membership_Slab__c = 'Gold';
                            acc.Date_of_Slab_Updation__c = Date.Today();
                            acc.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
                            flag = true;
                        }
                    }
                    if (acc.Membership_Slab__c == 'Gold') {
                        if ((totalPoints < 1000 || investmentAmount < 3000000) && slabValidityDate < Date.Today()) {
                            acc.Membership_Slab__c = 'Silver';
                            acc.Date_of_Slab_Updation__c = Date.Today();
                            acc.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
                            flag = true;
                        }
                        if ((totalPoints >= 1000 || investmentAmount > 3000000) && slabValidityDate < Date.Today()) {
                            acc.Membership_Slab__c = 'Gold';
                            acc.Date_of_Slab_Updation__c = Date.Today();
                            acc.Membership_Slab_Validity_Upto__c = Date.Today().addDays(365);
                            flag = true;
                        }
                    }
                }
                
                if(flag)
                {
                    accountsToUpdate.add(acc);
                }
            }
			
            if (!accountsToUpdate.isEmpty()) {
                update accountsToUpdate;
                System.debug('Updated Membership Accounts >>>>> ' + accountsToUpdate.size());
            }
        }
    }

    public void finish(Database.BatchableContext context) {

    }
    
	public static boolean last12MonthInvestment() {
        Date lastYear = Date.Today().addYears(-1);
        Id IPVrecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        System.debug('Last 12 Months From Today Is >>>> ' + lastYear );

        List<Investment__c> investmentList = [SELECT Id, Account__c, Investment_Amount__c FROM Investment__c WHERE Investment_Date__c >= :lastYear AND Type__c = 'Invested' AND Investment_Amount__c != null AND Account__r.RecordTypeId = :IPVrecordTypeId];
        
        Map<Id, Decimal> accountInvestmentTotalMap = new Map<Id, Decimal>();
        
        for (Investment__c inv : investmentList) {
            if (inv.Account__c != null) {
                Decimal investmentAmount = accountInvestmentTotalMap.containsKey(inv.Account__c) ? accountInvestmentTotalMap.get(inv.Account__c) : 0;
                investmentAmount += inv.Investment_Amount__c;
                accountInvestmentTotalMap.put(inv.Account__c, investmentAmount);
            }
        }
        
        System.debug('Number of Unique Accounts with Investments Modified in Last 12 Months >>>> ' + accountInvestmentTotalMap.size());

        List<Account> accountsToUpdate = new List<Account>();
        for (Account acc : [SELECT Id, Total_Amount_Invested_in_Last_12_Months__c FROM Account WHERE Id IN :accountInvestmentTotalMap.keySet()]) {
            Decimal totalInvestment = accountInvestmentTotalMap.get(acc.Id);
                       
            if (acc.Total_Amount_Invested_in_Last_12_Months__c != totalInvestment) {
                acc.Total_Amount_Invested_in_Last_12_Months__c = totalInvestment.round();
                accountsToUpdate.add(acc);
            }
        }        
        
        if (!accountsToUpdate.isEmpty()) {
            update accountsToUpdate;
            System.debug('Total Amount Invested in Last 12 Months Updated On ' + accountsToUpdate.size() + ' Account records.');
        }
        
       	System.debug('Calculated >>> ');
        return true;
    }
}