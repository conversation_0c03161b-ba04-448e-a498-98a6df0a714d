global with sharing class KycRestApiController {
    
        public PageReference verifyKYC() {
         Id panNumber = ApexPages.currentPage().getParameters().get('id'); // Get Contact Id from URL parameters
         Contact contactRecord = [SELECT Id, Investor_s_PAN__c,Investor_Full_Name__c,Kyc_Verify_Details__c FROM Contact WHERE Id = :panNumber LIMIT 1];
         String panNumber1 = contactRecord.Investor_s_PAN__c;
     //    String investor_s_PAN = contactRecord.Investor_s_PAN__c;        
            verifyKYCWithAWS(panNumber1);
        	return null; // Redirect to same page or any other page if needed
    }

    /*
     * Akash: AWS API call start
	*/
    global static void verifyKYCWithAWS(string pan){
        Map<String, Object> requestBodyMap = new Map<String, Object>{
            'requestType' => 'PAN',
            'requestBody' => new Map<String, Object>{
                'id_no' => pan
            }
        };
        String requestBodyJSON = JSON.serialize(requestBodyMap);
        AWSSignature aws=new AWSSignature();
        aws.setAWS('lambda','********************','u/dEXdmT6dOjK/r35Va0Jkt3PenUE7QgyQRaCCni'); // fetch it from Named Creds. 
        aws.setUrl('bqdcji6ua3munvcdj7mu5xt3ka0etojt.lambda-url.ap-south-1.on.aws');
        aws.setMethod('POST');
       	aws.setPayloadString(requestBodyJSON);
        HttpRequest req = aws.createSigntaureRequest();
		req.setHeader('content-type','application/json');
        system.debug(req.getBody());
        system.debug(req.getHeader('Authorization'));
        Http http = new Http();
        HTTPResponse res = http.send(req);
        system.debug('response :'+res.getBody());
        if (res.getStatusCode() == 200) {
            responseWrapper wResp = (responseWrapper)JSON.deserialize(res.getBody(), responseWrapper.class);
            system.debug('response>>>>> :'+wResp);
            if (wResp != null && wResp.data != null && wResp.data.success) {
                List<Contact> updateContactList = new List<Contact>();
                ContactRequestWrapper data = wResp.data.data;
                if (data != null) {
                    // Query the contact record using the PAN from the response
                    Contact[] contactsToUpdate = [SELECT Id, Investor_s_PAN__c, Investor_Full_Name__c, Kyc_Verify_Details__c,KYC_Status__c, Kyc_Verify_Date__c FROM Contact WHERE Investor_s_PAN__c = :pan LIMIT 1];
                    if (!contactsToUpdate.isEmpty()) {
                    // Update the fields of the contact record
                    for (Contact Inv : contactsToUpdate) {
                        // Ensure that the response data is correctly formatted
                        String kycDetails = data.pan + ',' + data.full_name + ',' + data.category + ',' + data.status;
                        String kycStatus =  data.status;
                        Inv.Kyc_Verify_Details__c = kycDetails;
                        Inv.KYC_Status__c = kycStatus;
                        Inv.Kyc_Verify_Date__c = Datetime.now();
                         updateContactList.add(Inv);
                    }
                    // Perform the update operation
                    update contactsToUpdate;
                }
                }
                if (!updateContactList.isEmpty()) {
                    update updateContactList;
                }
            }
            updateContactFields(pan, wResp);
        }
    }
    /*
     * Akash: AWS API call END
	*/
    
    // Method to update contact fields if API call is successful
    global static void updateContactFields(String pan, responseWrapper WResponse) {
        try {
            if (WResponse != null && WResponse.data != null && WResponse.data.success) {
            // Check if data object is not null and contains a valid record
            ContactRequestWrapper data = WResponse.data.data;
            if (data != null) {
                // Query the contact record using the PAN from the response
                Contact[] contactsToUpdate = [SELECT Id, Investor_s_PAN__c, Investor_Full_Name__c, KYC_Status__c, Kyc_Verify_Details__c, Kyc_Verify_Date__c FROM Contact WHERE Investor_s_PAN__c = :pan LIMIT 1];
                if (!contactsToUpdate.isEmpty()) {
                    // Update the fields of the contact record
                    for (Contact contactToUpdate : contactsToUpdate) {
                        // Ensure that the response data is correctly formatted
                        String kycDetails = data.pan + ',' + data.full_name + ',' +  data.category + ',' + data.status;
                        String kycStatus =  data.status;
                        if(kycDetails != null && kycDetails != ''){
                        contactToUpdate.Kyc_Verify_Details__c = kycDetails;
                        }else{
                            contactToUpdate.Kyc_Verify_Details__c = contactToUpdate.Kyc_Verify_Details__c;
                        }
                        if(kycDetails != null && kycDetails != ''){
                        contactToUpdate.KYC_Status__c = kycStatus;
                        }else{
                          contactToUpdate.KYC_Status__c = contactToUpdate.KYC_Status__c;
                        }
                        contactToUpdate.Kyc_Verify_Date__c = Datetime.now();
                    }
                    // Perform the update operation
                    update contactsToUpdate;
                } else {
                    System.debug('No contact record found for PAN: ' + pan);
                }
            } else {
                System.debug('No data found in the response.');
            }
        } else {
            System.debug('Response or data object is null, or success flag is false.');
        }
        } catch (Exception e) {
            // Handle any exceptions here
            System.debug('An error occurred while updating contact fields: ' + e.getMessage());
        }
    }

    // Method to generate SHA-256 hash
    global static String generateSHA256(String input) {
        Blob hash = Crypto.generateDigest('SHA-256', Blob.valueOf(input));
        return EncodingUtil.convertToHex(hash);
    }    
    
    //Added by Karan 
    global class ContactRequestWrapper 
    {
        global String full_name;
    	global String pan;
    	global String category;
    	global String status;
    }
    
    global Class requestWrapper
    {
			global List<ContactRequestWrapper> InvestorList;
    }
    
    global class responseWrapper
    {
        global String Status;
        global String Message;
        global DataObject data;
    }
    global class DataObject {
    	global Boolean success;
        global ContactRequestWrapper data;
	}
    
}