<aura:component controller="RefreshDataController" implements="flexipage:availableForAllPageTypes" access="global" >
    <aura:attribute name="showSpinner" type="Boolean" default="false"/>
    <aura:attribute name="jobId" type="String" default=""/>
    <aura:if isTrue="{!v.showSpinner}">
        <lightning:spinner alternativeText="Loading" size="large"/>
    </aura:if>
    <lightning:button label="Refresh Transfer Data" onclick="{!c.refreshTransfer}"/>
    <lightning:button label="Refresh Investment Data" onclick="{!c.refreshInvestment}"/>
</aura:component>