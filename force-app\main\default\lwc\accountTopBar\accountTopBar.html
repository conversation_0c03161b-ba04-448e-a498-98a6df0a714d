<!-- <template>
    <lightning-card>
        <template if:true={account.data}>
            <table style="width: 99%; margin-left: 1%; ">
                <tbody>
                    <tr>
                        <th><b>Primary Contact</b></th>
                        <th><b>Postal City</b></th>
                        <th><b>Account Premier Status</b></th>
                        <th><b>Membership Slab</b></th>
                        <th><b>Total Amount Committed</b></th>
                        <th><b>Total Points</b></th>
						<th><b>LinkedIn</b></th>
					</tr>
                    <tr>
                        <td>{account.data.fields.Primary_Contact__c.value}</td>
                        <td>{account.data.fields.BillingCity.value}</td>
                        <td style="max-width:150px; overflow:hidden; text-overflow:ellipsis; white-space:normal;">{account.data.fields.Account_Premier_Status__c.value}</td>
                        <td>{account.data.fields.Membership_Slab__c.value}</td>
                        <td>{formattedTotalAmountCommitted}</td>
                        <td>{formattedTotalPoints}</td>
						<template
                            if:true={account.data.fields.Linkedin__c.value}>
                            <td
                                style="max-width: 80px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap;">
                                <a href={account.data.fields.Linkedin__c.value}
                                    target="_blank" title={account.data.fields.Linkedin__c.value}>{account.data.fields.Linkedin__c.value}</a>
                            </td>
                        </template>
                        <template
                            if:false={account.data.fields.Linkedin__c.value}>
                            <td>&nbsp;</td>
                        </template>
                    </tr>

                    <tr>
                        <td colspan="9">&nbsp;</td>
                    </tr>
					
					<tr>
						
						<th><b>Relationship Manager</b></th>
						<th><b>Membership Status</b></th>
                        <th><b>AIM + Status</b></th>
                        <th><b>Other Membership Type</b></th>
                        <th><b>Total Amount Invested</b></th>
						<th><b>Lead Source</b></th>
						<th><b>App Status</b></th>
                        
					</tr>
					
					<tr>
						
						<td>{relationshipManagerName}</td>
						<td>{account.data.fields.Membership_Status__c.value}</td>
                        <td style="max-width:150px; overflow:hidden; text-overflow:ellipsis; white-space:normal;">{account.data.fields.AIM__c.value}</td>
                        <td style="max-width:150px; overflow:hidden; text-overflow:ellipsis; white-space:normal;">{account.data.fields.Other_Membership_type__c.value}</td>
                        <td>{formattedTotalAmountInvested}</td>
                        <td>{account.data.fields.Lead_Source__c.value}</td>
                        <td>{account.data.fields.App_Status__c.value}</td>                        
					</tr>
					
					<tr>
                        <td colspan="9">&nbsp;</td>
                    </tr>
					
                    <tr>
						<th><b>Date Of Addition</b></th>
						<th><b>Referrals Paid</b></th>
                        <th><b>Referrals in Trial</b></th>
						<th><b>Referrals in Leads</b></th>
						<th><b>Referrals Rotated</b></th>
						<th><b>Lead Quality Score</b></th>
                        <th><b>Lead Quality Score %</b></th>
                    </tr>
                    <tr>
						<td>{account.data.fields.Date_of_Addition__c.value}</td>
                        <template
                            if:true={account.data.fields.Referrals_Paid__c.value}>
                            <td>
                                {account.data.fields.Referrals_Paid__c.value}
                            </td>
                        </template>
                        <template
                            if:false={account.data.fields.Referrals_Paid__c.value}>
                            <td>0</td>
                        </template>
                        <template
                            if:true={account.data.fields.Referrals_in_Trials__c.value}>
                            <td>
                                {account.data.fields.Referrals_in_Trials__c.value}
                            </td>
                        </template>
                        <template
                            if:false={account.data.fields.Referrals_in_Trials__c.value}>
                            <td>0</td>
                        </template>
                        <template
                        if:true={account.data.fields.Referred_leads__c.value}>
                        <td>
                            {account.data.fields.Referred_leads__c.value}
                        </td>
                    </template>
                    <template
                        if:false={account.data.fields.Referred_leads__c.value}>
                        <td>0</td>
                    </template>
                    <template
                            if:true={account.data.fields.Referrals_Rotated__c.value}>
                            <td>
                                {account.data.fields.Referrals_Rotated__c.value}
                            </td>
                        </template>
                        <template
                            if:false={account.data.fields.Referrals_Rotated__c.value}>
                            <td>0</td>
                        </template>
						<td>{account.data.fields.Lead_Quality_Score__c.value}</td>
                        <td>{formattedLeadQualityScore}</td>
                    </tr>
                </tbody>
            </table>
        </template>
    </lightning-card>
</template> -->

<template>
    <lightning-card>
        <template if:true={accountData}>
            <table style="width: 99%; margin-left: 1%; ">
                <tbody>
                    <tr>
                        <th><b>Primary Contact</b></th>
                        <th><b>Postal City</b></th>
                        <th><b>Account Premier Status</b></th>
                        <th><b>Membership Slab</b></th>
                        <th><b>Total Amount Committed</b></th>
                        <th><b>Total Points</b></th>
                        <th><b>LinkedIn</b></th>
                    </tr>
                    <tr>
                        <td>{accountData.Primary_Contact__c}</td>
                        <td style="max-width:150px; overflow:hidden; text-overflow:ellipsis; white-space:normal;">{accountData.BillingCity}</td>
                        <td style="max-width:150px; overflow:hidden; text-overflow:ellipsis; white-space:normal;">{accountData.Account_Premier_Status__c}</td>
                        <td>{accountData.Membership_Slab__c}</td>
                        <td>{formattedTotalAmountCommitted}</td>
                        <td>{formattedTotalPoints}</td>
                        <template if:true={accountData.Linkedin__c}>
                            <td style="max-width: 80px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap;">
                                <a href={accountData.Linkedin__c} target="_blank" title={accountData.Linkedin__c}>{accountData.Linkedin__c}</a>
                            </td>
                        </template>
                        <template if:false={accountData.Linkedin__c}>
                            <td>&nbsp;</td>
                        </template>
                    </tr>

                    <tr>
                        <td colspan="9">&nbsp;</td>
                    </tr>
                    
                    <tr>
                        <th><b>Relationship Manager</b></th>
                        <th><b>Membership Status</b></th>
                        <th><b>AIM + Status</b></th>
                        <th><b>Other Membership Type</b></th>
                        <th><b>Total Amount Invested</b></th>
                        <th><b>Lead Source</b></th>
                        <th><b>App Status</b></th>
                    </tr>
                    
                    <tr>
                        <td>{accountData.Relationship_Manager__c}</td>
                        <td>{accountData.Membership_Status__c}</td>
                        <td style="max-width:150px; overflow:hidden; text-overflow:ellipsis; white-space:normal;">{accountData.AIM__c}</td>
                        <td style="max-width:150px; overflow:hidden; text-overflow:ellipsis; white-space:normal;">{accountData.Other_Membership_type__c}</td>
                        <td>{formattedTotalAmountInvested}</td>
                        <td>{accountData.Lead_Source__c}</td>
                        <td>{accountData.App_Status__c}</td>
                    </tr>
                    
                    <tr>
                        <td colspan="9">&nbsp;</td>
                    </tr>
                    
                    <tr>
                        <th><b>Date Of Addition</b></th>
                        <th><b>Referrals Paid</b></th>
                        <th><b>Referrals in Trial</b></th>
                        <th><b>Referrals in Leads</b></th>
                        <th><b>Referrals Rotated</b></th>
                        <th><b>Lead Quality Score</b></th>
                        <th><b>Lead Quality Score %</b></th>
                    </tr>
                    <tr>
                        <td>{formattedDateOfAddition}</td>
                        <template if:true={accountData.Referrals_Paid__c}>
                            <td>{accountData.Referrals_Paid__c}</td>
                        </template>
                        <template if:false={accountData.Referrals_Paid__c}>
                            <td>0</td>
                        </template>
                        <template if:true={accountData.Referrals_in_Trials__c}>
                            <td>{accountData.Referrals_in_Trials__c}</td>
                        </template>
                        <template if:false={accountData.Referrals_in_Trials__c}>
                            <td>0</td>
                        </template>
                        <template if:true={accountData.Referred_leads__c}>
                            <td>{accountData.Referred_leads__c}</td>
                        </template>
                        <template if:false={accountData.Referred_leads__c}>
                            <td>0</td>
                        </template>
                        <template if:true={accountData.Referrals_Rotated__c}>
                            <td>{accountData.Referrals_Rotated__c}</td>
                        </template>
                        <template if:false={accountData.Referrals_Rotated__c}>
                            <td>0</td>
                        </template>
                        <td>{accountData.Lead_Quality_Score__c}</td>
                        <td>{formattedLeadQualityScore}</td>
                    </tr>
                </tbody>
            </table>
        </template>
    </lightning-card>
</template>