trigger ContactTrigger on Contact (before insert,before update,after insert,after update,after delete) 
{

    List<System_Setting__c> settingList = System_Setting__c.getall().values();
    
    if(settingList!=null && settingList.size()>0 && !settingList[0].InvestorTriggerActivated__c)
    {
        system.debug('Returning from Investor Trigger because of custom setting>>>'+settingList);
        return;
    }

    ContactTriggerHandler handler = new ContactTriggerHandler();

    if(trigger.isInsert && trigger.isBefore)
    {
        handler.beforeInsert(trigger.New);
    }
    
    if(trigger.isInsert && trigger.isAfter)
    {
        handler.afterInsert(trigger.New);
         handler.hanleInvestorActivities(trigger.New,new Map<Id, Contact>());
        
    }
    if(trigger.isUpdate && trigger.isBefore)
    {
        handler.beforeUpdate(trigger.New,trigger.OldMap);
    }
    if(trigger.isUpdate && trigger.isAfter)
    {
        handler.afterUpdate(trigger.New,Trigger.oldMap);
        handler.hanleInvestorActivities(trigger.New,Trigger.oldmap);
    }
    if(trigger.isDelete && trigger.isAfter)
    {
        handler.afterDelete(trigger.Old);
    }
}