<apex:page controller="OldExitInvestmentCtrl" lightningStylesheets="true">
    <c:spinner spinner_name="fading-circle"  spinner_color="Indigo" />
    <script type="text/javascript">
        function selectAllCheckboxes(obj,receivedInputID){
            var inputCheckBox = document.getElementsByTagName("input");                  
            for(var i=0; i<inputCheckBox.length; i++){          
                if(inputCheckBox[i].id.indexOf(receivedInputID)!=-1){                                     
                    inputCheckBox[i].checked = obj.checked;
                }
            }
        }
        
        function dropdownChange(){
            ddChange();
        }
        function showspinner(){
            km_spin(true);
        }
        function hidespinner(){
            km_spin(false);
        }
    </script>
    <apex:form id="frm">

        <apex:actionFunction action="{!startupChange}" name="ddChange" id="ddChange" reRender="roundDropdown,transferDropdown"/>
        <apex:pageBlock id="selectionPB">
            <apex:pageBlockSection columns="1">
                 <apex:selectList size="1" value="{!selectedstartup}" label="Startup Name" required="true" onchange="dropdownChange()"> 
                    <apex:selectOptions value="{!startupOptionList}" />  
                </apex:selectList>
                <apex:selectList size="1" value="{!selectedExitType}" label="Type Of Exit"  > 
                    <apex:selectOptions value="{!exitTypeList}"/>  
                </apex:selectList> 
                <apex:selectList size="1" value="{!selectedEventType}" label="Type Of Event"  > 
                    <apex:selectOptions value="{!eventTypeList}"/>  
                </apex:selectList> 
                <apex:selectList size="1" value="{!selectedExitRound}" label="Existing Exit Round"  id="roundDropdown"> 
                    <apex:selectOptions value="{!exitRoundOptionList}" />  
                </apex:selectList>
                 
                <apex:selectList size="1" value="{!selectedTransferRound}" label="Exiting Internal Transfers Round"  id="transferDropdown"> 
                    <apex:selectOptions value="{!transferRoundOptionList}" />  
                </apex:selectList> 
                
                <apex:inputField value="{!acc.Amount_Paid__c}" label="Exit Price"/>
                <apex:inputField value="{!acc.Date_of_Payment__c}" label="Date Of Exit"/>
                <apex:inputField value="{!acc.Designation__c}" label="Buyer Name" />
               
            </apex:pageBlockSection>
            <apex:pageBlockButtons >
                <apex:commandButton value="Get Investment" action="{!getInvesment}"  />
            </apex:pageBlockButtons>
            
         </apex:pageBlock>
        <apex:pageMessages />
        <apex:pageBlock rendered="{!investmentList.size>0}" id="table">
                <div align="center" draggable="false" >
                    <apex:outputLabel style="color:#4a4a56;text-align: right;font-size: 91%;font-weight: bold;">Share To Be Exited(Mass)</apex:outputLabel>&nbsp;
                    <apex:inputField value="{!acc.NumberOfEmployees}" label="Share To Be Exited(Mass)" /> &nbsp;&nbsp;&nbsp;&nbsp;
                    <apex:commandButton value="Update Selected Entries" action="{!updateEntries}"/> &nbsp;&nbsp;&nbsp;&nbsp;
                    <apex:commandButton value="Save" action="{!saveExit}" onclick="showspinner();" oncomplete="hidespinner();" rerender="frm"/>
                </div>  
                
            <apex:pageBlockSection columns="1" id="tableSec">
                <apex:pageblockTable var="inv" value="{!investmentList}" rendered="{!investmentList.size>0}">
                    <apex:column width="25px">
                        <apex:facet name="header">
                            <apex:inputCheckbox onclick="selectAllCheckboxes(this,'inputId')"/>
                        </apex:facet>
                        <apex:inputCheckbox value="{!inv.checked}" id="inputId"/>
                     </apex:column>
                    <apex:column value="{!inv.inv.Account__r.name}"/>
                    <apex:column value="{!inv.investorName}" headerValue="Investor Name"/>
                    <apex:column value="{!inv.inv.Startup_Round__r.Name}"/>
                    <apex:column value="{!inv.inv.Number_Of_Shares__c}" title="Total Available Shares" headerValue="Total Available Shares"/>
                    
                    <apex:column title="Share To Be Exited" headerValue="{!If(selectedExitType=='Internal Transfer - IPV' ,"Balance Shares With IPV",If(selectedExitType=='Internal Transfer' ,"Shares to be transferred","Share To Be Exited"))}">
                        <apex:inputText value="{!inv.shareToBeExit}" label="Share To Be Exited"/>
                    </apex:column>
                    <apex:column headerValue="Transfer to Investor" rendered="{!If(selectedExitType=='Internal Transfer' ,true,false) }"> 
                        <apex:inputfield value="{!inv.inv.Internal_Transfer_Investor__c}" label="Transfer to Investor"/>
                    </apex:column>
                    <apex:column value="{!inv.inv.Issue_Type__c}"/>
                    <apex:column value="{!inv.inv.Type__c}"/>
                    <apex:column value="{!inv.inv.Membership_status__c}"/>
                    <apex:column value="{!inv.inv.GPOA_Taken__c}"/>
                    <apex:column value="{!inv.inv.Is_Primary__c}"/>
                    <apex:column value="{!inv.inv.Primary_Holder_Contact__c}"/>
                    <apex:column value="{!inv.inv.Investment_in_Own_Name_Family_Member__c}"/>
                    <apex:column value="{!inv.inv.AIF_Contributor__c}"/>
                </apex:pageblockTable>
                <apex:outputPanel id="myButtons" style="align:canter;" rendered="{!investmentList.size>0}">
                    <apex:commandButton action="{!Beginning}" title="Beginning" value="<<" disabled="{!disablePrevious}" reRender="myPanel,myButtons"/>
                    <apex:commandButton action="{!Previous}" title="Previous" value="<" disabled="{!disablePrevious}" reRender="myPanel,myButtons"/>        
                    <apex:commandButton action="{!Next}" title="Next" value=">"  disabled="{!disableNext}" reRender="myPanel,myButtons"/>
                    <apex:commandButton action="{!End}" title="End" value=">>" disabled="{!disableNext}" reRender="myPanel,myButtons"/>  
                </apex:outputPanel>
            </apex:pageBlockSection>
        </apex:pageBlock>
    </apex:form>
</apex:page>