<apex:page controller="PdfTimelineActivityController" renderAs="pdf">
    <div style="font-family: Arial, sans-serif; margin: 20px;">

        <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="font-size: 18px; margin-bottom: 5px;">Account Activities Report</h2>
            <p style="font-size: 14px; margin: 0;"><strong>Account Name:</strong> {!accountName}</p>
        </div>

      
        <apex:repeat value="{!activitiesByDate}" var="dateGroup">
         
            <div style="font-weight: bold; margin-top: 20px; margin-bottom: 10px; font-size: 13px; color: #333;">
                Activity Date: {!dateGroup.groupDate}
            </div>

          
            <table style="border-collapse: collapse; width: 100%; font-size: 12px; margin-bottom: 20px;">
                <thead>
                    <tr style="background-color: #f9f9f9;">
                        <th style="border: 1px solid #ccc; padding: 8px; text-align: left; width: 20%;">Activity Type</th>
                        <th style="border: 1px solid #ccc; padding: 8px; text-align: left; width: 60%;">Activity Detail</th>
                        <th style="border: 1px solid #ccc; padding: 8px; text-align: left; width: 20%;">Date</th>
                    </tr>
                </thead>
                <tbody>
                    <apex:repeat value="{!dateGroup.activities}" var="activity">
                        <tr>
                            <td style="border-top: 1px solid #eee; padding: 6px; width: 20%;">{!activity.activityType}</td>
                            <td style="border-top: 1px solid #eee; padding: 6px; width: 60%;">
                                <apex:outputText escape="false" value="{!activity.activityDetail}"/>
                            </td>
                            <td style="border-top: 1px solid #eee; padding: 6px; width: 20%;">{!activity.formattedDate} &nbsp;{!activity.formattedTime}</td>
                        </tr>
                    </apex:repeat>
                </tbody>
            </table>
        </apex:repeat>
    </div>
</apex:page>