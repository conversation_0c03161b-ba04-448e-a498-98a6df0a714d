trigger StartupPipelineTrigger on Startup_Pipeline__c (before insert , after insert , before update , after update) {

    StartupPipelineTriggerHandler spth = new StartupPipelineTriggerHandler();
    
    if(trigger.isInsert && trigger.isBefore ){
        spth.beforeInsert(trigger.new);
    }
    
    if(trigger.isUpdate && trigger.isBefore){
        spth.beforeUpdate(trigger.new , trigger.oldMap);
    }
    if(trigger.isUpdate && trigger.isAfter){
        spth.afterUpdate(trigger.new , trigger.oldMap);
    }
}