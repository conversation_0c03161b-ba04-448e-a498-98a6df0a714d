global class StartupPipelineEmailScheduler implements Schedulable {

    global void execute(SchedulableContext sc) {
        
        Date datethirteenDaysAgo = Date.today().addDays(-13);
        Date datefifteenDaysAgo = Date.today().addDays(-15);
        Date today = Date.today();
        System.debug('datethirteenDaysAgo>>>>>' + datethirteenDaysAgo);
        System.debug('datefifteenDaysAgo>>>>>' + datefifteenDaysAgo);
        
        List<Startup_Pipeline__c> evolutionReminderRecord = New List<Startup_Pipeline__c>();
        List<Startup_Pipeline__c> finalAlertRecord = New List<Startup_Pipeline__c>();
        List<Startup_Pipeline__c> startupRecords = [SELECT Id, Name , SP_Startup_Name__c,  SP_Startup_Received_on__c, 
                                                    Response_Time_TAT__c , SP_Founder_Email__c
                                                    FROM Startup_Pipeline__c WHERE (Response_Time_TAT__c = NULL) AND
                                                    ( SP_Startup_Received_on__c =: datethirteenDaysAgo OR 
                                                     SP_Startup_Received_on__c =: datefifteenDaysAgo )];
        
        if(startupRecords != null && startupRecords.size() > 0){
            for(Startup_Pipeline__c sp : startupRecords){
                system.debug('daysBetween>>>>>>>>>>> ' +  Date.today().daysBetween(sp.SP_Startup_Received_on__c));
                System.debug('datethirteenDaysAgo>>>>>' + datethirteenDaysAgo);
                System.debug('datefifteenDaysAgo>>>>>' + datefifteenDaysAgo);
                
                if(sp.SP_Startup_Received_on__c.daysBetween(Date.today()) == 13){
                    evolutionReminderRecord.add(sp);
                }else If(sp.SP_Startup_Received_on__c.daysBetween(Date.today()) == 15){
                    finalAlertRecord.add(sp);
                }
            }
        }
        system.debug('startupRecords>>>>>>>>>>> ' +  startupRecords);
        system.debug('evolutionReminderRecord>>>>>>>>>>> ' +  evolutionReminderRecord);
        system.debug('finalAlertRecord>>>>>>>>>>> ' +  finalAlertRecord);
        
        if(evolutionReminderRecord != null && evolutionReminderRecord.size()>0){
            sendReminderEmail(evolutionReminderRecord , 13);
        }
        
        if(finalAlertRecord != null && finalAlertRecord.size()>0){
            sendReminderEmail(finalAlertRecord , 15);
        }
    }
    
    public void sendReminderEmail(List<Startup_Pipeline__c> startupRecords , Integer daysDifference) {
        String emailSubject;
        String emailBody;
        String startupLinkList = '';
        String StartupNameList = '';
        String baseURL = URL.getOrgDomainURL().toExternalForm() ;
        system.debug('baseURL>>>>>>>>>>> ' +  baseURL);
        
        for(Integer i = 0; i < startupRecords.size(); i++){
            String recordUrl = baseURL + '/lightning/r/Startup_Pipeline__c/' + startupRecords[i].Id + '/view' ;
            startupLinkList += (i + 1) + '. <a href="' + recordUrl + '" target="_blank"><b>' 
                            + recordUrl + '</b></a><br/>';
            StartupNameList += (i + 1) + '. <b>' + startupRecords[i].SP_Startup_Name__c + '</b><br/>';
        }
        system.debug('startupLinkList>>>>>>>>>>> ' +  startupLinkList);
        system.debug('StartupNameList>>>>>>>>>>> ' +  StartupNameList);
        
        if(daysDifference == 13 ){
            
            emailSubject = 'Evaluation Reminder - Day 13';
            emailBody = 'Hi Team,<br/>'
                		+'<p>This is a reminder that the evaluation for the following startups has been ongoing for 13 days and is still pending:</p>'
                		+ '<p>' + StartupNameList + '</p>'
                		+ '<p>Please ensure to follow up on these evaluations to keep the process moving.</p>'
                		+ 'Thank you!<br/><br/>'
                		+ startupLinkList +'<br/>';
            
        }else If(daysDifference == 15){
            
            emailSubject = 'Final Day Alert - Immediate Action Needed';
            emailBody = 'Hi Team,<br/>'
                		+ '<p>Below is the list of startup/s which have been on board since 15 days, and action is needed:</p>'
                		+ '<p>' + StartupNameList + '</p>'
                		+ '<p>Please take immediate action to finalize their evaluation and avoid breaching our SLA.</p>'
                		+ 'Thank you!<br/><br/>'
                		+ startupLinkList +'<br/>';
        }
        
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setToAddresses(new String[] {'<EMAIL>'});
        mail.setSubject(emailSubject);
        mail.setHtmlBody(emailBody);
        mail.setSaveAsActivity(false);

        Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
    }
    /*
    public void sendFinalAlertEmail(List<Startup_Pipeline__c> startupRecords){
        
        String formattedList = '';
        String emailSubject = 'Final Day Alert - Immediate Action Needed';
        String baseURL = URL.getOrgDomainURL().toExternalForm() ;
        system.debug('baseURL>>>>>>>>>>> ' +  baseURL);
        
        for(Integer i = 0; i < startupRecords.size(); i++){
            String recordUrl = baseURL + '/' + startupRecords[i].SP_Startup_Name__c ;
            formattedList += (i + 1) + '. <a href="' + recordUrl + '" target="_blank"><b>' 
                            + startupRecords[i].SP_Startup_Name__c + '</b></a><br/>';
        }
        system.debug('formattedList>>>>>>>>>>> ' +  formattedList);
        
        String emailBody = 'Hi Team,<br/><br/>'
            + '<p>Below is the list of startup/s which have been on board since 15 days, and action is needed:</p>'
            + '<p>' + formattedList + '</p>'
            + '<p>Please take immediate action to finalize their evaluation and avoid breaching our SLA.</p>'
            + '<br/>Thank you!<br/><br/>';
        
        //Tomporery proccess
        String currentUserEmail = UserInfo.getUserEmail();
        User systemAdmin = [SELECT Email FROM User WHERE Profile.Name = 'System Administrator' LIMIT 1];
        
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setToAddresses(new String[] { startupRecords[0].SP_Founder_Email__c });
        mail.setCcAddresses(new String[] { systemAdmin.Email });
        //mail.setToAddresses(new String[] {'<EMAIL>'});
        mail.setSubject(emailSubject);
        mail.setHtmlBody(emailBody);
        mail.setSaveAsActivity(false);

        Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
    }*/
}