Public class StartupRoundTriggerNameUpdateOnTime{
    
    Public static void updateRoundName(Set<Id> startupIdSet){
        List<Startup_Round__c> startupRoundList = new List<Startup_Round__c>();
        Map<Id,List<Startup_Round__c>> roundStartupMap = new Map<Id,List<Startup_Round__c>>();
        startupRoundList = [SELECT id,name,createddate,Round_type__c,Startup__c,Startup__r.Public_Name__c,Pre_Emptive_Deal__c,Startup_name_auto__c FROM Startup_Round__c where Startup__c in :startupIdSet order by createddate asc];
        List<Startup_Round__c> roundUpdateList = new List<Startup_Round__c>();
        
        for(Startup_Round__c str : startupRoundList)
        {
            if(!roundStartupMap.containsKey(str.Startup__c)){
                roundStartupMap.put(str.Startup__c,new List<Startup_Round__c>());
            }
            roundStartupMap.get(str.Startup__c).add(str);
        }
        
        for(Id startupId : roundStartupMap.keyset()){
            Integer noforPreEm = 1;
            Integer noforNonPreEm = 1;
            Integer noforExit = 1;
            Integer noforIntTrans = 1;
            Integer blankType= 1;
            for(Startup_Round__c stRound : roundStartupMap.get(startupId)){
                String publicStr =''+stRound.Startup__r.Public_Name__c;
                System.debug('publicStr>>>'+publicStr);
                if(publicStr.length()>5)
                {
                    if(publicStr.length()>7)
                        publicStr = publicStr.substring(0, 8);                        
                }
                publicStr = publicStr.remove('-');
                    
                if(stRound.round_type__c =='Raise')
                {
                    if(stRound.Pre_Emptive_Deal__c){
                        stRound.Startup_name_auto__c = publicStr +'_P'+noforPreEm;
                        noforPreEm++;
                    }
                    else{
                        stRound.Startup_name_auto__c = publicStr +'_R'+noforNonPreEm;
                        noforNonPreEm++;
                    }
                }
                else if(stRound.round_type__c =='Exit')
                {
                    stRound.Startup_name_auto__c = publicStr +'_E'+noforExit;
                    noforExit++;
                }
                else if(stRound.round_type__c =='Internal Transfers')
                {
                    stRound.Startup_name_auto__c = publicStr +'_IT'+noforIntTrans;
                    noforIntTrans++;
                }
                else{
                    stRound.Startup_name_auto__c = publicStr +'_'+blankType;
                    blankType++;
                }
                
                roundUpdateList.add(stRound);
            }
        }
        
        update roundUpdateList;
        
    }
}