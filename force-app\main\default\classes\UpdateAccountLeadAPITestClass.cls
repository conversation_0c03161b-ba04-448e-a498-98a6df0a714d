/*************************************************************************
 Test class for : UpdateAccountLeadAPI.
**************************************************************************/

@isTest(SeeAllData=False)
public class UpdateAccountLeadAPITestClass {

    @Testsetup Static Void Setup()
    {
        String accRecordTypeID = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        String gsRecordTypeID = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        Account acc= TestFactory.createAccount();
        acc.Name ='TestUpdate';
        acc.Primary_Contact__c = '**********';
        acc.RecordTypeID = accRecordTypeID;
        insert acc; 
        
        Lead__c ld = TestFactory.CreateLead();
        ld.Name ='TestUpdate';
        ld.Title__c ='Ms';
        ld.Personal_Email__c = '<EMAIL>';
        ld.Relationship_Owner__c = UserInfo.getUserId();
        //ld.Primary_Country_Code__c = acc.Primary_Country_Code__c;
        ld.Primary_Contact__c = '**********';
       // String gsRecordTypeID = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('CXO Genie').getRecordTypeId();
        ld.RecordTypeID = gsRecordTypeID;

        Insert Ld;
    }
    
    @isTest
    Static void UpdateleadRecordAPITest()
    {
       Account Acc =[Select id,Name,Personal_email__c, Primary_contact__c ,Primary_country_code__c,full_primary_contact__c,RecordType.Name from Account where RecordType.Name = 'IPV' Limit 1];
       System.AssertNotEquals(Null,Acc);
       
       UpdateAccountLeadAPI.requestWrapper reqWrap = new UpdateAccountLeadAPI.requestWrapper();
       UpdateAccountLeadAPI.objectRequestWrapper ldWrap = new UpdateAccountLeadAPI.objectRequestWrapper();
        
        ldWrap.PrimaryNumber = acc.Primary_Contact__c;
        ldWrap.countrycode ='91';
        ldwrap.membername ='TestName';
        ldwrap.company='';
        ldwrap.Designation='Test';
        ldwrap.Are_you_a_first_time_investor ='';
        ldwrap.PersonalEmail = '<EMAIL>';
        //Added By Bharat for increase a code coverage
        ldwrap.Preferred_Sectors_to_Invest = 'HR-Tech';
        ldwrap.Preferred_Sub_Sectors_to_Invest = 'Customer Service';
        ldwrap.IRS_Time = Time.newInstance(12,5,2,7);
        ldwrap.IRS_Date = Date.today();
        ldwrap.App_PAN_Number = '**********';
        ldwrap.App_Industry = 'IT';
        ldwrap.App_City = 'Gj';
        ldwrap.App_Company = '';
        ldwrap.App_Designation = 'Test';
        ldwrap.App_Expertise = 'Leadership';
        ldwrap.App_Full_Name = ldwrap.membername;
        ldwrap.App_Email = ldwrap.PersonalEmail;
        ldwrap.App_Address = 'Gj';
        ldwrap.App_Postal_Code = 673002 ;
        ldwrap.App_Country = 'Ind' ;
            
        UpdateAccountLeadAPI.objectRequestWrapper ldWrap1 = new UpdateAccountLeadAPI.objectRequestWrapper();
        ldWrap1.PrimaryNumber = '**********';
        ldWrap1.countrycode ='91';
        ldwrap1.membername ='TestName1';
        ldwrap1.company='';
        ldwrap1.Designation='Test';
        ldwrap1.Are_you_a_first_time_investor ='';
        ldwrap1.PersonalEmail = '<EMAIL>';
        //Added By Bharat for increase a code coverage
        ldwrap1.Preferred_Sectors_to_Invest = 'HR-Tech';
        ldwrap1.Preferred_Sub_Sectors_to_Invest = 'Customer Service';
        ldwrap1.IRS_Time = Time.newInstance(12,5,2,7);
        ldwrap1.IRS_Date = Date.today();
        ldwrap1.App_PAN_Number = '**********';
        ldwrap1.App_Industry = 'IT';
        ldwrap1.App_City = 'Gj';
        ldwrap1.App_Company = '';
        ldwrap1.App_Designation = 'Test';
        ldwrap1.App_Expertise = 'Leadership';
        ldwrap1.App_Full_Name = ldwrap1.membername;
        ldwrap1.App_Email = ldwrap1.PersonalEmail;
        ldwrap1.App_Address = 'Gj';
        ldwrap1.App_Postal_Code = 673002 ;
        ldwrap1.App_Country = 'Ind' ;
        
        List<UpdateAccountLeadAPI.objectRequestWrapper> AccountleadList1 = new List<UpdateAccountLeadAPI.objectRequestWrapper>();
        AccountleadList1.add(ldWrap);
        AccountleadList1.add(ldWrap1);
        reqWrap.objectList = AccountleadList1;
        
        String myJSON = JSON.serialize(reqWrap);
        RestRequest request = new RestRequest();
        request.requestUri = URL.getSalesforceBaseUrl().toExternalForm()+'/services/apexrest/UpdateAccountLeadAPI';
        request.httpMethod = 'POST';
        request.requestBody = Blob.valueof(myJSON);
        
        RestContext.request = request;
        UpdateAccountLeadAPI.getAccounts(); 
        
        
    }
}