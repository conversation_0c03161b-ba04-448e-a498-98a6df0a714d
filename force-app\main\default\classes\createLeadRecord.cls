@RestResource(urlMapping='/createLeadRecord/*')

    global with sharing class createLeadRecord {

    @HttpPost
    global Static string createLead(){
        try
        {
            
            RestRequest req = RestContext.request;
    
            RestResponse res = Restcontext.response;
    
            string jsonString=req.requestBody.tostring();
        
            responseWrapper wResp=(responseWrapper) JSON.deserialize(jsonString,responseWrapper.class);
    
            Lead__c obj=new Lead__c();
    
            obj.Name=wResp.name;
            //obj.Member_Name__c = wResp.memberName;
            obj.Membership_Status__c = wResp.status;
            Insert obj;
            return 'Success Vishal testing';
        }
        catch(exception e)
        {
            return 'Fail:'+e.getMessage();
        }

      }

      global class responseWrapper{

       global string name;
       global string memberName;
       global string status;

      }

   }