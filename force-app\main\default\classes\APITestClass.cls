/******************************************************************
    Test class for : CreateLeadRecordAPI,CreateCaseRecordAPI,createInvestmentRecordAPI and AccountSyncAPI
******************************************************************/
@isTest(SeeAllData=false)
public class APITestClass 
{
    @testSetup static void setup() {
        Account acc1= TestFactory.createAccount();
        acc1.Name ='Test';
        acc1.Lead_External_ID_App__c = '**********';
        insert acc1;
        
        Contact  ct = new Contact();
        ct.AccountId = acc1.id;
        ct.LastName = 'TestName';
        ct.Investor_s_PAN__c ='**********';
        ct.Investor_Premier_Status__c = 'Onboarded on Premier +';
        ct.AIF_Contributor__c = True;
        insert ct;
        
        List<startup__c> Stlist = new list<Startup__c>();
        Startup__c st = TestFactory.createStartUp();
        StList.add(st);
        insert Stlist;  
        
        List<Startup_Round__c> strlist = new list<Startup_round__c>();
        Startup_Round__c strObj = new startup_round__c();
        Strobj.Startup__c = stList[0].id;
        strobj.Lead_Analyst__c = acc1.Id;
        Strobj.Lead_Member__c = acc1.Id;
        Strobj.Date_Of_Founders_Call__c =Date.newInstance(2022, 12, 16);
        strobj.Pre_Money_Valuation__c = 10000;
        StrObj.Doi_Percent_Equity__c = 2;
        Strobj.Doi_Percent_Fee__c = 2;
        strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 12, 9);
        strlist.add(strObj);
        insert strlist;
       
    }
    
    @isTest
    static void CreateCaseRecordAPITest(){
        
        Account acc1= [select id,Primary_Contact__c from account limit 1];
        System.assertNOTEquals(null,acc1);
        Test.setMock(HttpCalloutMock.class, new AppFutureCreateCaseAPIMock());
        CreateCaseRecordAPI.caseRequestWrapper reqWrap = new CreateCaseRecordAPI.caseRequestWrapper();
        reqWrap.feedbackId = acc1.id;
        reqWrap.status = 'NameTest';
        reqWrap.description = '**********';
        reqWrap.origin = 'Rua x';
        reqWrap.lifecyclePhase = 'Rua x';
        reqWrap.title = 'Rua x';
        reqWrap.issueRaisedByContact = ''+acc1.Primary_Contact__c;
        reqWrap.issueRaisedByCountryCode = '91';
        
        List<CreateCaseRecordAPI.caseRequestWrapper> cReqList = new List<CreateCaseRecordAPI.caseRequestWrapper>();
        cReqList.add(reqWrap);
        CreateCaseRecordAPI.requestWrapper req = new CreateCaseRecordAPI.requestWrapper();
        req.caseList = cReqList;
        
        String myJSON = JSON.serialize(req);

        RestRequest request = new RestRequest();
        request.requestUri = URL.getSalesforceBaseUrl().toExternalForm()+'/services/apexrest/CreateCaseRecordAPI';
        request.httpMethod = 'POST';
        request.requestBody = Blob.valueof(myJSON);
         
        RestContext.request = request;
        CreateCaseRecordAPI.createCase();        
    }
    
    @isTest
    static void AccountSyncAPITest(){
        
        Account acc = [select id,name,Primary_Country_Code__c,Primary_Contact__c,Lead_External_ID_App__c from account limit 1];
        System.assertNOTEquals(null,acc );
        
        AccountSyncAPI.RequestWrapper reqWrap = new AccountSyncAPI.RequestWrapper();
        
        List<string> colList = new List<string>();
        colList.add('Id');
        colList.add('Primary_Contact__c');
        colList.add('Name');
        colList.add('Primary_Country_Code__c');
        colList.add('Relationship_Manager__r.Name');
        colList.add('Lead_External_ID_App__c');
                
        List<AccountSyncAPI.ObjPrimaryContactWrapper> priList = new List<AccountSyncAPI.ObjPrimaryContactWrapper>();
        AccountSyncAPI.ObjPrimaryContactWrapper priObj = new AccountSyncAPI.ObjPrimaryContactWrapper();
        priObj.countryCode = ''+acc.Primary_Country_Code__c;
        priObj.primaryNumber = ''+acc.Primary_Contact__c;
        priObj.leadIdFromApp ='' +acc.Lead_External_ID_App__c;
        priList.add(priObj);
        
        System.debug('priObj>>>>>>>.' + priObj);
        
        reqWrap.columns = colList;
        reqWrap.primaryContactList = priList;
        reqWrap.objName = 'Account';
                
        String myJSON = JSON.serialize(reqWrap);

        RestRequest request = new RestRequest();
        request.requestUri = URL.getSalesforceBaseUrl().toExternalForm()+'/services/apexrest/AccountSyncAPI';
        request.httpMethod = 'POST';
        request.requestBody = Blob.valueof(myJSON);
        
        RestContext.request = request;
        AccountSyncAPI.getAccounts();        
    }
    
    @isTest
    static void CreateLeadRecordAPITest(){
        
        Account accObj = [select id,name,Primary_Country_Code__c,Primary_Contact__c ,Unique_referral_code__c   from account limit 1];
        System.assertNOTEquals(null,accObj);

        CreateLeadRecordAPI.requestWrapper reqWrap = new CreateLeadRecordAPI.requestWrapper();
        CreateLeadRecordAPI.leadRequestWrapper ldWrap = new CreateLeadRecordAPI.leadRequestWrapper();
        ldWrap.memberName = 'App111';
        ldWrap.leadSource = 'App Sign Up';
        //ldWrap.PersonalEmail = 'Personal';
        //ldWrap.leadCountryCode = '91';
        ldWrap.leadPrimaryContact = '*********';
        ldWrap.referredByContact = ''+accObj.Primary_Contact__c;
        ldWrap.referredByCountryCode = ''+accObj.Primary_Country_Code__c;
        ldWrap.mStatus = '';
        ldwrap.Campaign = '';
        ldwrap.Dateofreceivinglead = date.newInstance(2022, 11, 15);
        //ldWrap.referDate = '';
        ldWrap.leadIdFromApp = '123321';
        ldWrap.StartupName = 'test startup';
        ldWrap.IRS_Date = DATE.Today();
        ldWrap.App_Company = 'Test Comapny';
        ldWrap.App_Designation = 'Dev';
        ldWrap.App_Address = 'Ahmedabad';
        ldWrap.App_Postal_Code = 001122;
        ldWrap.App_Industry = 'Test Industry';
        ldWrap.App_Expertise = 'Testinng Lead';
        ldWrap.App_Full_Name = 'App111';
        ldWrap.App_Country = 'India';
        ldWrap.App_Email = '<EMAIL>';
        ldWrap.App_City = 'Ahemdabad';
        ldWrap.APP_Referred_By_Referral_Code = accObj.Unique_referral_code__c ;
        
        List<CreateLeadRecordAPI.leadRequestWrapper> leadList1 = new List<CreateLeadRecordAPI.leadRequestWrapper>();
        leadList1.add(ldWrap);
        reqWrap.leadList = leadList1;
        
        String myJSON = JSON.serialize(reqWrap);

        RestRequest request = new RestRequest();
        request.requestUri = URL.getSalesforceBaseUrl().toExternalForm()+'/services/apexrest/CreateLeadRecordAPI';
        request.httpMethod = 'POST';
        request.requestBody = Blob.valueof(myJSON);
        
        RestContext.request = request;
         Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());

       
        Test.startTest();
        
        CreateLeadRecordAPI.createLead(); 
        Test.stopTest();       
        
        
        reqWrap = new CreateLeadRecordAPI.requestWrapper();
        ldWrap = new CreateLeadRecordAPI.leadRequestWrapper();
        ldWrap.memberName = 'App111';
        ldWrap.leadSource = 'App';
        //ldWrap.PersonalEmail = 'Personal';
        //ldWrap.leadCountryCode = '91';
        ldWrap.leadPrimaryContact =''+accObj.Primary_Contact__c;
        ldWrap.referredByContact = ''+accObj.Primary_Contact__c;
        ldWrap.referredByCountryCode = ''+accObj.Primary_Country_Code__c;
        ldWrap.mStatus = '';
        //ldWrap.referDate = '';
        ldWrap.leadIdFromApp = '123321';

        leadList1 = new List<CreateLeadRecordAPI.leadRequestWrapper>();
        leadList1.add(ldWrap);
        reqWrap.leadList = leadList1;
        
        myJSON = JSON.serialize(reqWrap);

        request = new RestRequest();
        request.requestUri = URL.getSalesforceBaseUrl().toExternalForm()+'/services/apexrest/CreateLeadRecordAPI';
        request.httpMethod = 'POST';
        request.requestBody = Blob.valueof(myJSON);
        
        RestContext.request = request;
          
          Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());

       
        Test.startTest();
        
        CreateLeadRecordAPI.createLead(); 
        Test.stopTest();
        
    }
    @IsTest
    static void createInvestmentRecordAPI(){
        
        Contact cc =[Select id,AccountId,Investor_s_PAN__C from Contact limit 1];
        Startup_round__c st =[Select id from Startup_round__c limit 1];
        
        System.assertNOTEquals(null,cc);
        System.assertNOTEquals(null,st);
        
        CreateInvestmentRecordAPI.requestWrapper reqWrap = new CreateInvestmentRecordAPI.requestWrapper();
        CreateInvestmentRecordAPI.investmentRequestWrapper InvWrap = new CreateInvestmentRecordAPI.investmentRequestWrapper();
        
        //InvWrap.InvName ='TestName';
        //InvWrap.Investor = cc.id;
        InvWrap.roundId = st.id;
        InvWrap.commitedAmount= 50000;
        InvWrap.memberSalesforceId = cc.AccountId;
        
        List<CreateInvestmentRecordAPI.investmentRequestWrapper> InvList1 = new List<CreateInvestmentRecordAPI.investmentRequestWrapper>();
        InvList1.add(InvWrap);
        reqWrap.InvList = InvList1;
        
        String myJSON = JSON.serialize(reqWrap);

        RestRequest request = new RestRequest();
        request.requestUri = URL.getSalesforceBaseUrl().toExternalForm()+'/services/apexrest/CreateInvestmentRecordAPI';
        request.httpMethod = 'POST';
        request.requestBody = Blob.valueof(myJSON);
        
        RestContext.request = request;
        CreateInvestmentRecordAPI.CreateInvestment();
    }
     private class MockHttpResponseGenerator implements HttpCalloutMock {
        public HTTPResponse respond(HttpRequest req) {
           
            HTTPResponse res = new HTTPResponse();
            res.setStatusCode(200);
            res.setBody('{"data": {"token": "test_token"}}');
            return res;
        }
     }
}