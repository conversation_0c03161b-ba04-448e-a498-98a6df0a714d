trigger LeadCustomObjTrigger on Lead__c (before insert,before update,after insert, after delete , after update) {
    
    List<System_Setting__c> settingList = System_Setting__c.getall().values();
    
    if(settingList!=null && settingList.size()>0 && !settingList[0].LeadTriggerActivated__c)
    {
        system.debug('Returning from LeadTrigger because of custom setting>>>'+settingList);
        return;
    }
    
    LeadCustomObjTriggerHandler ldHandler = new LeadCustomObjTriggerHandler();
    
    if(Trigger.isInsert && Trigger.isBefore)
    {
        ldHandler.beforeInsert(trigger.New);
     
    }
    else if(Trigger.isUpdate && Trigger.isBefore)
    {
        ldHandler.beforeUpdate(trigger.New,Trigger.OldMap);
        
    }
    else if(Trigger.isInsert && Trigger.isAfter)
    {
        ldHandler.afterInsert(trigger.New);
        ldHandler.updateAccountCounts(trigger.New);
        ldHandler.handleLeadActivity(Trigger.new,Null);
       

    }
    // Added by ankush for Account daily update ICPM 123. 01.9.23
    else if(Trigger.isAfter && Trigger.isdelete){
        ldHandler.updateAccountCounts(trigger.old);
    }
    // Added by Sahil for Referred by (Lead) Count on Update ICPM 171. 01.03.2024
    else if(Trigger.isAfter && Trigger.isUpdate)
    {
        ldHandler.updateReferredLeadCounts(trigger.New);
        System.debug('Inside After Update Method >>>>>');
         ldHandler.handleLeadActivity(Trigger.new,Trigger.OldMap);
    }
}