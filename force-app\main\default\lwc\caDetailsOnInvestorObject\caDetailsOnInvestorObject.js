import { LightningElement, api, wire, track } from 'lwc';
import getContributionAgreements from '@salesforce/apex/CADetailsOnTable.getContributionAgreementsForInvestorObject';
import { NavigationMixin } from 'lightning/navigation';

export default class caDetailsOnInvestorObject extends NavigationMixin(LightningElement) {
    @api recordId;
    @track agreements = [];
    @track error;
    hasData = false;
    isEmpty = false;

    columns = [
        {
            label: 'Investor Name',
            fieldName: 'investorUrl',
            type: 'url',
            typeAttributes: {
                label: { fieldName: 'investorName' },
                target: '_blank'
            }
        },
        {
            label: 'Fund Onboarded on',
            fieldName: 'fundOnboardedOn'
        },
        {
            label: 'CA Number',
            fieldName: 'caUrl',
            type: 'url',
            typeAttributes: {
                label: { fieldName: 'caName' },
                target: '_blank'
            }
        },
        {
            label: 'Currency',
            fieldName: 'currencyField'
        },
        {
            label: 'Type',
            fieldName: 'type'
        },
        {
            label: 'Total Contribution Amount',
            fieldName: 'totalContributionFormatted'
        },
        {
            label: 'Total Drawdown',
            fieldName: 'totalDrawdownFormatted'
        },
        {
            label: 'Balance Amount',
            fieldName: 'balanceAmountFormatted'
        },
        {
            label: 'Carry %',
            fieldName: 'carryPercent'
        },
        {
            label: 'Class',
            fieldName: 'classValue'
        },
        {
            label: 'Premier Onboarding Date',
            fieldName: 'premierOnboardingDateFormatted'
        }
    ];

    @wire(getContributionAgreements, { investorId: '$recordId' })
    wiredAgreements({ error, data }) {
        if (data) {
            this.agreements = data.map(ca => ({
                ...ca,
                investorUrl: '/' + ca.investorId,
                caUrl: '/' + ca.caId,
                totalContributionFormatted: this.formatAmount(ca.totalContribution),
                totalDrawdownFormatted: this.formatAmount(ca.totalDrawdown),
                balanceAmountFormatted: this.formatAmount(ca.balanceAmount),
                premierOnboardingDateFormatted: this.formatDate(ca.premierOnboardingDate)
            }));
            this.hasData = this.agreements.length > 0;
            this.isEmpty = this.agreements.length === 0;
            this.error = undefined;
        } else if (error) {
            this.error = error.body?.message || 'Unknown error';
            this.agreements = [];
            this.hasData = false;
            this.isEmpty = true;
        }
    }

    formatAmount(value) {
        if (value === null || value === undefined) return '';
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(value);
    }

    formatDate(dateStr) {
        if (!dateStr) return '';
        const dateObj = new Date(dateStr);
        const day = String(dateObj.getDate()).padStart(2, '0');
        const month = String(dateObj.getMonth() + 1).padStart(2, '0');
        const year = dateObj.getFullYear();
        return `${day}/${month}/${year}`;
    }
}