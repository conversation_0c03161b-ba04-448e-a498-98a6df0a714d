/************************************************************************
    Test Class for StartupTrigger and StartupTriggerHandler.
    Test Class for StartupRound and StartupRoundTrigger.
************************************************************************/
@isTest(SeeAllData=false)
private class StartupAndRoundTriggerTestClass{
    
    @testSetup static void setup() {
        Startup__c st = TestFactory.createStartUp();
        insert st;          
        
         API_Setting__c setting = new API_Setting__c();
        setting.Name = 'Test Setting';
        setting.Enable_Investment_API_Call__c = true;
        setting.Enable_Investor_API_Call__c = true;
        setting.Enable_Startup_API_Call__c = true;
        setting.End_URL__c = 'test.com';
        insert setting;
    }
    
    static testMethod void startupTrigger()
    {
        test.startTest();
        
        	Account acc = TestFactory.createAccount();
        	insert acc;
            Startup__c stObj = TestFactory.createStartUp();
            stobj.Date_of_Investor_Call__c =Date.newInstance(2022, 08, 2);
        	stObj.Referred_By_Internal__c = acc.Id;
            insert stObj;
        
        	stObj.Description__c = 'testtest';
        	update stObj;
        
        test.stopTest();
        
    }
    
    static testMethod void startupRoundTrigger()
    {
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseController());
        test.startTest();
            List<Account> accList = new List<Account>();
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            insert accList;
            
            Startup__c stObj = [select id from Startup__c limit 1];
            
            Startup_Round__c strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,stObj.Id);
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 08, 2);
            strObj.SME_1__c = accList[0].ID;
        	strObj.SME_2__c = accList[1].ID;
        	strObj.SME_3__c = accList[1].ID;
        	strObj.SME_4__c = accList[1].ID;
            strObj.SME_5__c = accList[1].ID;
            insert strObj;          
            strObj.SME_1__c = accList[1].ID;
            update strObj;
            Investment__c inv = TestFactory.createInvestment(accList[0].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = [select id,name from Startup_Round__c limit 1].Name;
            
            insert inv;
                    
            strObj.Date_of_sending_out_call_for_money__c = Date.Today();
            strObj.Issue_Price__c = 111;
            update strObj;
        test.stopTest();
    }
    
    static testMethod void startupRoundTriggerRaise()
    {
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseController());
        test.startTest();
            List<Account> accList = new List<Account>();
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            insert accList;
            
            Startup__c stObj = [select id from Startup__c limit 1];
            
            Startup_Round__c strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,stObj.Id);
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 08, 2);
            strObj.round_type__c = 'Raise';
            insert strObj;          
            
            strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,stObj.Id);
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 08, 2);
            strObj.round_type__c = 'Raise';
            insert strObj;  
            
            strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,stObj.Id);
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 08, 2);
            strObj.round_type__c = 'Raise';
            strObj.Pre_Emptive_Deal__c = true;
            insert strObj;  
                    
            /*
            Investment__c inv = TestFactory.createInvestment(accList[0].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = [select id,name from Startup_Round__c limit 1].Name;
            
            insert inv;
            */
                    
            strObj.Date_of_sending_out_call_for_money__c = Date.Today();
            strObj.Issue_Price__c = 111;
            update strObj;
        test.stopTest();
    }
    static testMethod void startupRoundTriggerPreEmptiveDeal()
    {
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseController());
        test.startTest();
            List<Account> accList = new List<Account>();
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            insert accList;
            
            Startup__c stObj = [select id from Startup__c limit 1];
            
            Startup_Round__c strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,stObj.Id);
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 08, 2);
            strObj.round_type__c = 'Raise';
            strObj.Pre_Emptive_Deal__c = true;
        	strObj.Issue_Price__c = 1234;
            insert strObj;          
            
            strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,stObj.Id);
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 08, 2);
            strObj.round_type__c = 'Raise';
            strObj.Pre_Emptive_Deal__c = true;
        	strObj.Startup_Converted__c = true;
            strObj.Date_of_sending_out_call_for_money__c = Date.Today();
        	strObj.Issue_Price__c = 1234;
            insert strObj;  
            
            Investment__c inv = TestFactory.createInvestment(accList[0].Id); 
            inv.Investor_s_PAN__c = '**********'; 
        	inv.Exit_amount_transferred__c = 100;
            inv.Startup_Round_Name__c = [select id,name from Startup_Round__c where id =:strObj.ID limit 1].Name;
            inv.Type__c ='Invested';  
            
            insert inv;
            
            Investment__c inv1 = TestFactory.createInvestment(accList[0].Id); 
            inv1.Investor_s_PAN__c = '**********'; 
            inv1.Startup_Round_Name__c = [select id,name from Startup_Round__c where id =:strObj.ID limit 1].Name;
            inv1.Type__c ='Exit';  
            inv1.Parent_Investment__c = inv.ID;
            inv1.Number_Of_Shares__c = 10;
            inv1.Exit_Price__c = 10;
	        insert inv1;
              
            strObj.Date_of_sending_out_call_for_money__c = Date.Today().addDays(1);
            strObj.Issue_Price__c = 211;
            strObj.round_type__c = 'Exit';
            strObj.Date_Of_Exit__c = Date.Today();
            strObj.Date_of_Investor_Call__c = Date.Today().addDays(-2);
            /*
            strObj.SME_1__c = accList[1].ID;
            strObj.SME_2__c = accList[1].ID;
            strObj.SME_3__c = accList[1].ID;
            strObj.SME_4__c = accList[1].ID;
            strObj.SME_5__c = accList[1].ID;
            */
        update strObj;
        test.stopTest();
    }
    
    
}