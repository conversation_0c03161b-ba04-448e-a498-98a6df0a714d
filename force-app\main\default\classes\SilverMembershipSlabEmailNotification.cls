public class SilverMembershipSlabEmailNotification {
	
    public static void silverMembershipEmail(List<Account> accountList)
     {
        List<Messaging.SingleEmailMessage> emailList = new List<Messaging.SingleEmailMessage>();
        Date currentDay = Date.Today();
        Date nextYear = Date.Today().addDays(365);
        //Map<Id, Account> oldAccountMap = (Map<Id, Account>) Trigger.oldMap;
        
        for(Account account : accountList)
        {
            //Account oldAccount = oldAccountMap.get(account.Id);
            //if(oldAccount.Membership_Slab__c != null && account.Membership_Slab__c == 'Silver')
            //{
				Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                if(account.Relationship_Manager__c != null)
                {
                    String fullURL = URL.getOrgDomainURL().toExternalForm() + '/' + account.Id;
                    User usr = [SELECT Id , Name , Email FROM User WHERE Id =: account.Relationship_Manager__c LIMIT 1];  
                    String htmlContent = '<!DOCTYPE html>' +
                                         '<html lang="en">' +
                                         '<head></head>' +
                                         '<body>' +
                                                 '<p>Hii <b>' + usr.Name + ',</b></p>' +
                                                 '<p>Hope this E-Mail finds you well.</p>' +
                                                 '<p>' +
                                                     'This is to inform you that membership slab of <b>' + account.Name + '</b> has ' +
                                                     //'changed from ' + oldAccount.Membership_Slab__c + ' to ' + account.Membership_Slab__c + ' on '+ account.Date_of_Slab_Updation__c + 
                                                     'The slab will be valid till ' + account.Membership_Slab_Validity_Upto__c.Format() +
                                                 '</p>' +
                                                 '<p>Link of Member Account: <a href=" ' + fullURL + '"> ' + fullURL + ' </a></p>' +
                                             '</body>' +
                                         '</html>';

                    email.setToAddresses(new String[]{usr.Email});
                    email.setSubject('Change of Membership Slab From ' + /*oldAccount.Membership_Slab__c*/'' + ' To ' + account.Membership_Slab__c + ' - ' + account.Name );
                    email.setHtmlBody(htmlContent);
                }
                emailList.add(email);
                System.debug('Account Name And Email >>>> ' + account.Name );
                
            //}
        }
        
        if (!emailList.isEmpty()) 
        {
            Messaging.sendEmail(emailList);
	        System.debug('Mail Sent >>> ');
        }
    }
}