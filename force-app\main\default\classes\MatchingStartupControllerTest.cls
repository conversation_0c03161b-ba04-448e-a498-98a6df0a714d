@isTest
public class MatchingStartupControllerTest {

    @isTest
    public static void testGetMatchingStartups() {
        
        List<Capital_Development__c> capitalDevelopmentList = new List<Capital_Development__c>();
        
        Capital_Development__c testCapitalDevelopment1 = new Capital_Development__c(Name = 'Test Capital Development1' , Industry_Sector__c = 'Gaming;Animation', Minimum_Transaction_Size__c = 1 , Maximum_Transaction_Size__c = 100);
        Capital_Development__c testCapitalDevelopment2 = new Capital_Development__c(Name = 'Test Capital Development2' , Industry_Sector__c = null, Minimum_Transaction_Size__c = 1 , Maximum_Transaction_Size__c = 100);
        Capital_Development__c testCapitalDevelopment3 = new Capital_Development__c(Name = 'Test Capital Development3' , Industry_Sector__c = 'Gaming;Animation', Minimum_Transaction_Size__c = null , Maximum_Transaction_Size__c = 100);
        Capital_Development__c testCapitalDevelopment4 = new Capital_Development__c(Name = 'Test Capital Development4' , Industry_Sector__c = 'Gaming;Animation', Minimum_Transaction_Size__c = 1 , Maximum_Transaction_Size__c = null);
        Capital_Development__c testCapitalDevelopment5 = new Capital_Development__c(Name = 'Test Capital Development5' , Industry_Sector__c = 'Gaming;Animation', Minimum_Transaction_Size__c = null , Maximum_Transaction_Size__c = null);
        Capital_Development__c testCapitalDevelopment6 = new Capital_Development__c(Name = 'Test Capital Development6' , Industry_Sector__c = null , Minimum_Transaction_Size__c = null , Maximum_Transaction_Size__c = 100);
		Capital_Development__c testCapitalDevelopment7 = new Capital_Development__c(Name = 'Test Capital Development7' , Industry_Sector__c = null, Minimum_Transaction_Size__c = 0.5 , Maximum_Transaction_Size__c = null);
	
        
        capitalDevelopmentList.add(testCapitalDevelopment1);
        capitalDevelopmentList.add(testCapitalDevelopment2);
        capitalDevelopmentList.add(testCapitalDevelopment3);
        capitalDevelopmentList.add(testCapitalDevelopment4);
        capitalDevelopmentList.add(testCapitalDevelopment5);
        capitalDevelopmentList.add(testCapitalDevelopment6);
        capitalDevelopmentList.add(testCapitalDevelopment7);
        
        insert capitalDevelopmentList; 
        
        MatchingStartupController.getMatchingInvestmentBanking(testCapitalDevelopment1.Id);
        MatchingStartupController.getMatchingInvestmentBanking(testCapitalDevelopment2.Id);
        MatchingStartupController.getMatchingInvestmentBanking(testCapitalDevelopment3.Id);
        MatchingStartupController.getMatchingInvestmentBanking(testCapitalDevelopment4.Id);
        MatchingStartupController.getMatchingInvestmentBanking(testCapitalDevelopment5.Id);
        MatchingStartupController.getMatchingInvestmentBanking(testCapitalDevelopment6.Id);
        MatchingStartupController.getMatchingInvestmentBanking(testCapitalDevelopment7.Id);
        

        
    }
}