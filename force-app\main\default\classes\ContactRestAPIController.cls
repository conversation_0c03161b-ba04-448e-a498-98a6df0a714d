global class ContactRestAPIController {
    
    private static String CLASS_NAME = 'ContactRestAPIController';
    global static string accessToken;
    
    @future(callout=true)
    public static void sendInvestorDetails(set<Id> contactId){
        try{
            String jsonData;
            String endURLSetting;
            Map<String,String> credMAp = new Map<String,String>();
            credMAp.put('password','hello');
            credMAp.put('mobile','8866104284');
            credMAp.put('country_code','+91');
            
            system.debug('--id +'+contactId);
            
            List<API_Setting__c> settingList = API_Setting__c.getall().values();
            system.debug('Contact rest API ctrl API_Setting__c>>>'+settingList);
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].End_URL__c))
            {
                endURLSetting = ''+settingList[0].End_URL__c;
            }
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Username__c))
            {
                credMAp.put('mobile',''+settingList[0].Mobile_Username__c);
            }
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Password__c))
            {
                credMAp.put('password',''+settingList[0].Mobile_Password__c);
            }
        
            system.debug('--credMAp--'+credMAp);
            system.debug('--endURLSetting--'+endURLSetting);
            
            List<Contact> contacts = [ SELECT Id,Name,
                                      Account.Primary_Contact__c, Account.Primary_Country_Code__c,
                                      Investment_in_Own_Name_Family_Member__c,Investor_s_PAN__c,
                                      MailingStreet, MailingCity, MailingState, MailingPostalCode, MailingCountry,
                                      Postal_Address__c,CreatedBy.Name,LastModifiedBy.Name,Residential_Status__c,
                                      Investor_Father_Husband_s_name__c,Amount_Agreed_for_Contribution__c,AIF_Contributor__c,GPOA_Taken_New__c,Email
                                      FROM Contact 
                                      where id IN :contactId ];
            
            if(!contacts.isEmpty()){
                JSONGenerator jsonGen = JSON.createGenerator(true);
                jsonGen.writeStartObject(); 
                jsonGen.writeFieldName('users');
                jsonGen.writeStartArray();
                for(Contact con : contacts ){
                    integer relationID = getOutputString(con.Investment_in_Own_Name_Family_Member__c);
                    String address = con.MailingStreet + ',' + con.MailingCity + ',' + con.MailingState + ','
                        + con.MailingPostalCode + ',' + con.MailingCountry;
                    jsonGen.writeStartObject(); 
                    
                    string countryCode = '+' + con.Account.Primary_Country_Code__c;
                    if(con.account.Primary_Contact__c != null) jsonGen.writeStringField('primary_mobile',con.Account.Primary_Contact__c);
                    if(con.Account.Primary_Country_Code__c != null) jsonGen.writeStringField('primary_mobile_country_code',countryCode);
                    
                    
                    jsonGen.writeFieldName('investors');
                    jsonGen.writeStartArray();
                    jsonGen.writeStartObject(); 
                    
                    if(con.Name != null) jsonGen.writeStringField('fullname',con.Name);                    
                    if(relationID != null)
                        jsonGen.writeNumberField('investor_relation_type_id',relationID);
                    else                        
                        jsonGen.writeNumberField('investor_relation_type_id',10); 
                    
                    //jsonGen.writeNumberField('investor_relation_type_id',1); 
                    if(con.Investor_s_PAN__c != null) jsonGen.writeStringField('pan',con.Investor_s_PAN__c);
                    //jsonGen.writeStringField('documentArray','');
                    
                    
                    if(con.Postal_Address__c != null) jsonGen.writeStringField('address',con.Postal_Address__c);
                    //jsonGen.writeStringField('nomineeName','');
                    //if(con.Investor_Father_Husband_s_name__c != null) jsonGen.writeStringField('nomineeFathername',con.Investor_Father_Husband_s_name__c);            
                    //jsonGen.writeStringField('relation_with_nominee','');
                    //jsonGen.writeStringField('nomineeDob','');
                    jsonGen.writeBooleanField('is_active',true);
                    jsonGen.writeStringField('investor_salesforce_id',con.Id);
                    // jsonGen.writeStringField('createdBy',con.CreatedBy.Name);
                    // jsonGen.writeStringField('updatedBy',con.LastModifiedBy.Name);
                    
                    // Added by Ankush for app flow for AIF CheckBOX
                    if(con.AIF_Contributor__c && con.Amount_Agreed_for_Contribution__c != null)
                        jsonGen.writeNumberField('aif_contribution',con.Amount_Agreed_for_Contribution__c); 
                    else
                        jsonGen.writeNumberField('aif_contribution',0); 
                    if(con.GPOA_Taken_New__c != null)
                        jsonGen.writeStringField('GPOA_Taken',Con.GPOA_Taken_New__c);
                    
                    // Added by Bharat for capital gain and loss on 18-04-2024
                    if(con.Residential_Status__c != null){
                        jsonGen.writeStringField('residential_status',Con.Residential_Status__c);
                    }
                    
                    //Added by bharat for secondary Module requirement 15-05-2025
                    if(con.AIF_Contributor__c){
                        jsonGen.writeBooleanField('is_aif_contributor' , TRUE);
                    }else{
                        jsonGen.writeBooleanField('is_aif_contributor' , FALSE);
                    }
                    
                    if(con.Email != null){
                        jsonGen.writeStringField('investor_email' , con.Email);
                    }else{
                        jsonGen.writeNullField('investor_email');
                    }
                    
                    jsonGen.writeEndObject();
                    jsonGen.writeEndArray();
                    
                    
                    jsonGen.writeEndObject();
                }
                jsonGen.writeEndArray();
                jsonGen.writeEndObject();
                
                jsonData = jsonGen.getAsString();
                System.debug('Json Data - ' + jsonData);
            }
            
            HttpRequest req = new HttpRequest();
            String enddURL = endURLSetting+'/userAuthentication/login-admin';
            
            String bodyy =  JSON.serialize(credMAp);
            system.debug('---'+bodyy);
            req.setEndpoint(enddURL);
            req.setHeader('Content-Type','application/json');
            req.setMethod('POST');
            req.setTimeout(120000);
            req.setBody(bodyy);
            
            Http http = new Http();
            HTTPResponse res = http.send(req);
            system.debug('****');
            System.debug(res.getStatusCode());
            System.debug('res---'+res.toString());
            System.debug('STATUS:'+res.getStatus());
            
            if(res.getStatusCode() == 200){
                
                system.debug('Data sent'+res.getBody());
                String jsonstr = JSON.serialize(res.getBody());
                JSONParser parser = JSON.createParser(jsonStr);
                Map<String, Object> results = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                String tkn = JSON.serialize(results.get('data'));
                Map<String, Object> token = (Map<String, Object>) JSON.deserializeUntyped(tkn);
                accessToken = String.valueOF(token.get('token'));
                system.debug('accessToken  '+token.get('token'));
                system.debug('accessToken in add  '+accessToken);
                //HttpRequest req = new HttpRequest();
                
                //  accessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************.2XZZgnS25HcSOI1Uld0KDwCtvDHXtWB4bwhcHfCRvTc';
                enddURL = endURLSetting+'/userInvestorRoutes/userInvestorBulkAdd';
                HttpRequest request = new HttpRequest();
                request.setEndpoint(enddURL);
                request.setHeader('Authorization','bearer '+accessToken);
                request.setHeader('Content-Type','application/json');
                request.setHeader('Accept-Encoding','gzip, deflate, br');
                request.setHeader('Connection','keep-alive');
                request.setHeader('Accept','*/*');
                request.setMethod('POST');
                request.setTimeout(120000);
                request.setBody(jsonData);
                
                system.debug('**request**'+request);
                system.debug('**currentTimeMillis**'+system.currentTimeMillis());
                
                Http http1 = new Http();
                HTTPResponse res1 = http1.send(request);
                system.debug('****');
                system.debug('**currentTimeMillis111**'+system.currentTimeMillis());
                System.debug(res1.getStatusCode());
                System.debug('res--add-'+res1.getBody());
                System.debug('STATUS:'+res1.getStatus());
                if(res1.getStatusCode() != 200){
                    Error_Log__c log =  new Error_Log__c();
                    log.Class_Name__c = CLASS_NAME;
                    log.method__c = 'updateInvestorDetails';
                    log.Error_Message__c =  res1.getStatusCode() + ',' + res1.getBody() ;
                    insert log;
                }
                
                
            }
        }
        catch(Exception ex){
            system.debug('Error '+ex);
            Error_Log__c log =  new Error_Log__c();
            log.Class_Name__c = CLASS_NAME;
            log.method__c = 'sendInvestorDetails';
            log.Error_Message__c = ex.getMessage();
            insert log;
        }
    }
    
 
    private static Integer getOutputString(String relation){
        switch on relation {
            when 'Own Name' {       
                return 1;
            }
            when 'Family Member' {      
                return 2;
            }
            when 'Partnership' {
                return 8;
            }
            when 'LLP' {        
                return 9;
            }
            when 'Company' {        
                return 7;
            }
            when 'HUF' {        
                return 10;
            }
            when 'Trust' {      
                return 10;
            }
            when 'Joint A/c' {      
                return 10;
            }
            when 'Others' {     
                return 10;
            }
            when 'IPV' {        
                return 10;
            }
            when else {       
                return null;
            }
        }
    }
    
}