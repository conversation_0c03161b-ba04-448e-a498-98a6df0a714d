public class CARelatedListController {

    @auraEnabled(cacheable=true)
    public static List<contact> getInvestors(Id recordId){
        set<ID> invId = new set<ID>();
        List<Contribution_Agreement__c> lstCA = [SELECT Id,Investor1__c,Investor2__c from Contribution_Agreement__c where Id = :recordID];
        For(Contribution_Agreement__c objCA :lstCA){
            invId.add(objCA.Investor1__c);
            invId.add(objCA.Investor2__c);
        }
        List<contact> investors = [SELECT Id,Name,email,AccountId,Account.Name,Phone,Investor_s_PAN__c,Residential_Status__c
                                          FROM contact WHERE Id IN :invId];
        system.debug('investors ***'+investors);
        if(!investors.isEmpty()){
            return investors;
        }
        return null;
    }
    
    @auraEnabled(cacheable=true)
    public static List<Bank_Account__c> getBankAccount(Id recordId){
        set<ID> invId = new set<ID>();
        List<Bank_Account__c> banks = [SELECT Id,Name,Account_Holder_Name__c,Date_of_PD__c,Mapped_with_Demat__c, Bank_Account_Type__c,
                                       Bank_Name__c,Account_Number__c,IFSC__c,Penny_Drop__c from Bank_Account__c where Contribution_Agreement__c = :recordID];
        system.debug('banks ***'+banks);
        if(!banks.isEmpty()){
            return banks;
        }
        
        return null;
    }
    
    @auraEnabled(cacheable=true)
    public static List<Investment__c> getInvestments(Id recordId){
        List<Investment__c> investments = [SELECT Id,Name,Startup_Round__c,Type__c,Startup_Round__r.Round_Type__c,Startup_Round__r.Name,Startup_Round__r.Unit_Price__c,Startup_Round__r.Startup_Name__c,Date_of_Allotment__c,
                                          Number_of_Units_Held__c,Final_Commitment_Amount__c,Investment_AIF_Class__c,Investment_Amount_Balance__c,Exit_Date__c,Date_of_transaction__c,
                                          Investment_Amount__c,Date_on_Transfer_Deed__c,Committed_Amount__c,Considered_Investment_Amount__c,Investment_Fee_Received__c FROM Investment__c WHERE Contribution_Agreement__c = :recordId 
                                          AND ((Type__c = 'Invested' OR Type__c = 'Committed') and Investor_Type__c = 'Via AIF')];
        system.debug('investments ***'+investments);
        if(!investments.isEmpty()){
            for(Investment__c inv : investments){
                if(inv.Startup_Round__r.Round_Type__c == 'Internal Transfers'){                   
                    inv.Investment_Amount__c = inv.Considered_Investment_Amount__c;
                }
            }
            return investments;
        }
        return null;
    }
    
    @auraEnabled(cacheable=true)
    public static List<Investment__c> getExit(Id recordId){
        List<Investment__c> investments = [SELECT Id,Name,Startup_Round__c,Startup_Round__r.Round_Type__c,Startup_Round__r.Unit_Price__c,Type__c,Startup_Round__r.Name,Startup_Round__r.Startup_Name__c,Date_of_Allotment__c,
                                          Number_of_Units_Held__c,Investment_AIF_Class__c,Investment_Amount_Balance__c,Exit_Date__c,Date_of_transaction__c,
                                          Investment_Amount__c,Parent_Investment__c,Exit_Fee_received__c,Date_on_transfer_deed__c,Committed_Amount__c,Investment_Fee_Received__c FROM Investment__c WHERE Contribution_Agreement__c = :recordId 
                                          AND Parent_Investment__c = null
                                          AND ((((Type__c = 'Invested' OR Type__c = 'Internal Transfers') 
                                          and Investor_Type__c = 'Via AIF') 
                                          AND (Startup_Round__r.Round_Type__c = 'Raise' OR Startup_Round__r.Round_Type__c = 'Pre-emptive'))
                                              OR 
                                              ((Type__c = 'Exit' OR Type__c = 'Partial Exit') and Investor_Type__c = 'Via AIF' AND Startup_Round__r.Round_Type__c = 'Raise' 
                                              ))
                                          ];
        system.debug('investments ***'+investments);
        if(!investments.isEmpty()){
            for(Investment__c inv : investments){
               /* if(inv.Type__c == 'Exit' || inv.Type__c == 'Partial Exit'){
                    inv.date_of_transaction__c = inv.Exit_Date__c;
                    //inv.Investment_Fee_Received__c = inv.Exit_Fee_received__c;
                }*/
            }            
            return investments;
        }
        return null;
    }

    @auraEnabled(cacheable=true)
    public static List<Investment__c> getRePayments(Id recordId){
        List<Investment__c> investments = [SELECT Id,Name,Startup_Round__c,Startup_Round__r.Round_Type__c,Startup_Round__r.Unit_Price__c,Type__c,Startup_Round__r.Name,Startup_Round__r.Startup_Name__c,Date_of_Allotment__c,
                                          Number_of_Units_Held__c,Investment_AIF_Class__c,Exit_amount_to_be_transferred__c,Investment_Amount_Balance__c,Exit_Date__c,Date_of_transaction__c,
                                          Date_of_SH_4__c,Investment_Amount__c,Exit_Fee_received__c,Date_on_transfer_deed__c,Committed_Amount__c,Investment_Fee_Received__c,
                                          Carry_fee_received__c,Total_Amount_Paid_Including_TDS__c,TDS_Deducted__c,Net_Amount_Paid__c 
                                          FROM Investment__c WHERE Contribution_Agreement__c = :recordId 
                                          AND (Type__c = 'Exit' AND Investor_Type__c = 'Via AIF' AND Startup_Round__r.Round_Type__c = 'Exit')
                                          AND ((Startup_Round__r.Type_of_Event__c = 'Full Redemption of Units')
                                               OR (Startup_Round__r.Type_of_Event__c = 'Partial Redemption of Units')) 
                                          ];
        system.debug('investments ***'+investments);
        if(!investments.isEmpty()){                     
            return investments;
        }
        return null;
    }
    
    @auraEnabled(cacheable=true)
    public static List<Investment__c> getOffMarketInv(Id recordId){
        List<Investment__c> investments = [SELECT Id,Name,Startup_Round__c,Type__c,Startup_Round__r.Name,Startup_Round__r.Unit_Price__c,Startup_Round__r.Startup_Name__c,Date_of_Allotment__c,
                                          Number_of_Units_Held__c,Investment_AIF_Class__c,Investment_Amount_Balance__c,Exit_Date__c,Date_of_transaction__c,Date_on_Transfer_Deed__c,
                                          Investment_Amount__c,Startup_Round__r.Round_Type__c,Committed_Amount__c,Considered_Investment_Amount__c,Investment_Fee_Received__c FROM Investment__c WHERE Contribution_Agreement__c = :recordId 
                                          AND 
                                           (((Type__c = 'Invested' and Investor_Type__c = 'Via AIF') AND (Startup_Round__r.Round_Type__c = 'Internal Transfers'))
                                              OR ((Startup_Round__r.Round_Type__c = 'Exit' AND Startup_Round__r.Type_of_Event__c = 'Internal Transfer of Units')
                                              AND (Type__c = 'Exit' and Investor_Type__c = 'Via AIF')))];
        system.debug('investments ***'+investments);
        if(!investments.isEmpty()){            
            return investments;
        }
        return null;
    }
    
    @auraEnabled(cacheable=true)
    public static List<Investment__c> getInvestmentsRedemption(Id recordId){
        List<Investment__c> investments = [SELECT Id,Name,Startup_Round__c,Startup_Round__r.Startup_Name__c,Startup_Round__r.Name,Startup_Round__r.Unit_Price__c,Exit_Date__c,Date_of_transaction__c,
                                          Date_of_SH_4__c,Number_of_Units_Held__c,Investment_AIF_Class__c,Investment_Amount_Balance__c,Investment_Amount__c,Type__c
                                          FROM Investment__c WHERE Contribution_Agreement__c = :recordId 
                                          AND (Type__c = 'Exit' AND Investor_Type__c = 'Via AIF' AND Startup_Round__r.Round_Type__c = 'Exit')
                                          AND ((Startup_Round__r.Type_of_Event__c = 'Full Redemption of Units')
                                               OR (Startup_Round__r.Type_of_Event__c = 'Partial Redemption of Units'))];
        system.debug('investments Red ***'+investments);
        if(!investments.isEmpty()){
            return investments;
        }
        return null;
    }
    
    @auraEnabled(cacheable=true)
    public static List<Document__c> getDocuments(Id recordId){
        system.debug('CAId ***'+recordId);
        List<Document__c> documents = [SELECT Id,Name,Document_Name__c,AIF_Document_Amount__c,Stamp_Paper__c,
                                          AIF_Document_Initiation_Date__c,AIF_Document_Number__c,Contributor_Signing_Date__c,
                                          FPC_Signing_Date__c,Trustee_Signing_Date__c
                                          FROM Document__c WHERE Contribution_Agreement__c = :recordId 
                                          ORDER BY Name ASC];
        
        if(!documents.isEmpty()){
            system.debug('documents ***'+documents);
            return documents;
        }
        return null;
    }
    
    @auraEnabled(cacheable=true)
    public static List<Transaction__c> getTransactions(Id recordId){
        system.debug('CAId ***'+recordId);
        List<Transaction__c> transactions = [SELECT Id,Name,Transaction_Date__c,Virtual_Account_Number__c,
                                          Remitters_Name__c,Remitters_Bank_A_C__c,Remitters_IFSC__c,
                                          Amount__c,Mode_of_Payment__c,Remarks__c,UTR_Number__c
                                          FROM Transaction__c WHERE Contribution_Agreement__c = :recordId ];
        
        if(!transactions.isEmpty()){
            system.debug('transactions ***'+transactions);
            return transactions;
        }
        return null;
    }
}