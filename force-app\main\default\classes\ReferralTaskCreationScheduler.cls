public class ReferralTaskCreationScheduler  implements Schedulable {
    public static void execute(SchedulableContext sc) {
        
        Date sixMonthsAgo=date.today().AddMonths(-6);
        
        
        List<Account> AccLst = [select id,Date_of_1st_Payment__c,Relationship_Manager__c from Account where Date_of_1st_Payment__c != null];
        List<Task> TaskLst =new List<Task>();
        if(!AccLst.isempty()){
            for (Account  Acc :AccLst){
                date ThreeMonthCompletionDate =Acc.Date_of_1st_Payment__c.addDays(90);
                date TodayDate=System.today();
                
                if((Acc.Date_of_1st_Payment__c != Null && ThreeMonthCompletionDate ==  TodayDate)){
                    Task tsk =new Task();
                    tsk.Referral_Trigger_Status__c='Pending';
                    tsk.Referral_Trigger_Type__c = '3 Month Milestone';
                    tsk.Subject ='Refferal Trigger';
                    tsk.OwnerId =Acc.Relationship_Manager__c;
                    tsk.WhatId = Acc.Id;
                    tsk.ActivityDate =System.today().AddDays(+2);
                    TaskLst.add(tsk);
                    system.debug('Task for Membership Slab>>>>'+tsk);
                }
                List<Lead__c> leadReferrals = [
                    SELECT Id
                    FROM Lead__c
                    WHERE Referred_By__c = :Acc.Id
                    AND CreatedDate >= :sixMonthsAgo
                ];
                List<Account> Accreferrals = [
                    SELECT Id
                    FROM Account
                    WHERE ParentId = :Acc.Id
                    AND CreatedDate >= :sixMonthsAgo
                ];
                
                
                integer  MonthSince =Acc.Date_of_1st_Payment__c.monthsBetween(TodayDate);
                if(Accreferrals.isEmpty() && leadReferrals.isempty()){
                    if(Math.MOD(MonthSince, 6) == 0 && Acc.Date_of_1st_Payment__c != Null){
                        Task tsk =new Task();
                        tsk.Referral_Trigger_Status__c='Pending';
                        tsk.Referral_Trigger_Type__c = 'Nudge for referrals every 6 months with IPV';
                        tsk.Subject ='Refferal Trigger';
                        tsk.OwnerId =Acc.Relationship_Manager__c;
                        tsk.WhatId = Acc.Id;
                        tsk.ActivityDate =System.today().AddDays(+2);
                        TaskLst.add(tsk);
                        system.debug('Task for Membership Slab>>>>'+tsk);
                    }
                }
            }
            insert  TaskLst;
            
        }
        
    }   
        
    }