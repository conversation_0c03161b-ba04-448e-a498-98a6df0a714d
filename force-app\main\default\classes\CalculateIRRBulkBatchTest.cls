@isTest(SeeAllData=false)
public class CalculateIRRBulkBatchTest {

    static testMethod void calculateTestMethod(){
        
        System_Setting__c customSetting = new System_Setting__c();
        customSetting.AccountTriggerActivated__c = false;
        customSetting.CaseTriggerActivated__c = false;
        customSetting.InvestmentTriggerActivated__c = false;
        customSetting.InvestorTriggerActivated__c = false;
        customSetting.LeadTriggerActivated__c = false;
        customSetting.StartupRoundTriggerActivated__c = false;
        customSetting.StartupTriggerActivated__c = false;
        customSetting.name = 'testingSetting';
        insert customSetting;
        
        List<Account> accList = New list<Account>();
        List<Contact> conList = new List<Contact>();
        Account Acc = TestFactory.createAccount();
        Insert acc;
        accList.add(Acc);
        
        for(Account acct : accList)
        {
                Contact cont = new Contact();
                cont.FirstName='Test';
                cont.LastName='Test';
                cont.Accountid= acct.id;
                cont.Investor_s_PAN__c = '**********';
                cont.Investment_in_Own_Name_Family_Member__c = 'Own Name';
                conList.add(cont);
        }
        Insert conList;
        
        Startup__c St = TestFactory.createStartUp();
        Insert St;
        
        Startup_Round__c str = new Startup_Round__c();
        str.Lead_Analyst__c = Acc.Id;
        str.Lead_Member__c = Acc.Id;
        str.Startup__c = st.id;
        str.Date_Of_Founders_Call__c = date.newInstance(2020, 9, 15);
        str.Pre_Money_Valuation__c = 10;
        str.Doi_Percent_Fee__c = 11;
        str.Doi_Percent_Equity__c = 12;
        str.Date_of_Investor_Call__c = Date.newInstance(2022, 05, 02);
        str.Date_of_sending_out_call_for_money__c = Date.newInstance(2022,08,05);
        Str.Exit_Price__c = 2000;
        
        Insert str;
       
        Investment__c invParent = TestFactory.createInvestment(accList[0].Id); 
        invParent.Investor_s_PAN__c = '**********'; 
        //invParent.Startup_Round_Name__c = str.Name;
        invParent.Startup_Round__c =Str.Id;
        invParent.Investor_Type__c ='Via Platform';
        //Added for lead tracker.
        invParent.Investment_Amount__c = 10;
        invParent.Number_Of_Shares__c =25;
        invParent.Investment_Amount_Due__c = 20;
        invParent.Date_of_FnF_CFM__c = date.newInstance(2022, 07, 23);
        invParent.Call_For_Money_Sent__c = true;
        invParent.Issue_Type__c ='Primary';
        invParent.Intransfer_balance_share__c = false;
        invParent.IPVFnF_Shadow_Balance_share__c = true;
        
        insert invParent;
                
        List<Investment__c> invList = new list<Investment__c >(); 
        for(Integer i=0; i<200; i++) 
        {
            invList.add(new investment__c(Exit_Date__c =Date.newInstance(2023,03,05),Exit_amount_to_be_transferred__c= 200000,Number_Of_Shares__c =100,Exit_Price__c = 2000,Parent_Investment__C =invParent.Id,Startup_Round__c =Str.Id,Investor_Type__c='Via Platform',Type__c = 'Exit',Account__c =Acc.id, Investor__c = Conlist[0].id,Investor_Name__c = 'test inv name'+i,Investor_s_PAN__c = Conlist[0].Investor_s_PAN__c));
        } 
        
        insert invList;
        
        System.assertNOTEquals(null,invList[0].Parent_Investment__C);
        Test.startTest();
            String solqStr = 'select id,Type__c,Parent_Investment__c,Exit_Date__c,Exit_amount_to_be_transferred__c,Number_Of_Shares__c,Exit_Price__c,IRR_Value__c,Startup_Round__c ,Investment_Year__c from Investment__c limit 200';
    
            // Create instance of batch job
            CalculateIRRBulkBatch batchJob = new CalculateIRRBulkBatch(solqStr);
            batchJob.csvBody = 'test,test';
            // Start batch job
            ID batchProcessId = Database.executeBatch(batchJob);
       
        Test.stopTest();
        
    }
}