public class EventTriggerHandler
{
   public void beforeInsert(List<Events__c> evList)
    {
        updateStartupRound(evList);
        updateEventUniqueName(evList);
    }
     
    public void afterInsert(List<Events__c> evList)
    {
        createPointTransaction(evList);
       
    }
    
    public void updateEventUniqueName(List<Events__c> evList)
    {
            Map<Id, Startup_Round__c> masterChildMap = new Map<Id, Startup_Round__c>();
            Map<Id, Integer> masterChildCountMap = new Map<Id, Integer>();

            for(Events__c sr : evList)
            {
                masterChildMap.put(sr.Startup_Round__c,null);
            }
            system.debug('1. masterChildMap>>>>'+masterChildMap);
            // Added by Karan for Too many query issue 22/08/2023.
            List<Startup_Round__c>  strAgret = [SELECT id,Startup__r.Public_Name__c,name, (SELECT id, Name , Event_Unique_Name__c FROM Events__r order by Sequence__c desc limit 1) FROM Startup_Round__c where id in : masterChildMap.keyset()];
            System.debug('strAgret>>>>>>>' + strAgret);
        
            for(Startup_Round__c sr : strAgret)
            {
                Integer lastCount = 0;
                //system.debug(sr.Startup_Round__r.totalSize);
                system.debug(sr.Events__r.size());
                System.debug('Total Events>>>>>>>'+ sr.Events__r);
                masterChildMap.put(sr.Id,sr);
                
                //masterChildCountMap.put(sr.Id,sr.Events__r.size());
                if(sr.Events__r!=null && sr.Events__r.size()>0)
                {
                    lastCount = getLastCountFromName(sr.Events__r[0].Event_Unique_Name__c);
                }
                masterChildCountMap.put(sr.Id,lastCount);
            }
            
            system.debug('2. masterChildMap>>>>'+masterChildMap);

            for(Events__c sr : evList)
            {
                if(masterChildMap.get(sr.Startup_Round__c)!=null && masterChildMap.get(sr.Startup_Round__c).Startup__r.Public_Name__c!=null)
                {
                    String publicStr = masterChildMap.get(sr.Startup_Round__c).name; 
                    Integer lastNo = masterChildCountMap.get(sr.Startup_Round__c) + 1;        
                       
                    if(publicStr !=null && publicStr !='')
                    {
                        /*
                        if(publicStr.length()>5)
                        {
                            publicStr = publicStr.substring(0, 5);
                        }
                        publicStr = publicStr.remove('-');
                        */
                        sr.Event_Unique_Name__c = 'EV-'+publicStr +'-'+lastNo;
                        system.debug('testt>>>'+sr);
                    }
                    else
                    {
                        sr.Event_Unique_Name__c = 'EV-'+lastNo;
                    }     
                    masterChildCountMap.put(sr.Startup_Round__c,lastNo);        
                }
            }  
    }
    
    Public Integer getLastCountFromName(String name){
        
        if(name.contains('-'))
        {
            Integer lastIndex = name.lastIndexOf('-');
            
            if (lastIndex != -1) {
                String substringAfterLastHyphen = name.substring(lastIndex + 1);
                System.debug(substringAfterLastHyphen);
                return integer.valueof(substringAfterLastHyphen);
            } else {
                return 0;
            }
        }
        else{
            return 0;
        }
    }
    public void updateStartupRound(List<Events__c> evList)
    {
        //Map Start up round at a time of data import from dataloader
        Map<String,Id> startupRoundMap = new Map<String,Id>();
        for(Events__c ev : evList)
        {
            if(ev.Startup_Round__c==null || true)
            {
                if(ev.Startup_Round_Name__c!=null && ev.Startup_Round_Name__c!='')
                    startupRoundMap.put(ev.Startup_Round_Name__c,null);
            }
        }
        
        if(startupRoundMap!=null && startupRoundMap.size()>0)
        {            
            for(Startup_Round__c sr : [select id,name from Startup_Round__c where name in : startupRoundMap.keyset()])
            {
                startupRoundMap.put(sr.name,sr.Id);
            }
        }
        
        for(Events__c ev : evList)
        {
            if(ev.Startup_Round_Name__c!=null && startupRoundMap!=null && startupRoundMap.get(ev.Startup_Round_Name__c)!=null)
            {
                ev.Startup_Round__c = startupRoundMap.get(ev.Startup_Round_Name__c);
            }
        }
    }
    
    public void createPointTransaction(List<Events__c> evList){
        set<Id> setEVId = new set<Id>();
        system.debug('in trigger ::');
        for(Events__c ev : evList){
            if(ev.Event_Type__c == 'Investor Call' || ev.Event_Type__c == 'Feedback Form Complete')
                setEVId.add(ev.Id);
        }
        system.debug('in trigger ::'+setEVId);
        if(setEVId != null){
            List<Attendance__c> attList = [SELECT Id,Event_Date__c,Account__c,Events__c,Events__r.Startup_Round__c,Events__r.Startup_Round__r.Date_of_Investor_Call__c,Events__r.Event_Type__c, Events__r.Startup_Round__r.Startup__c FROM Attendance__c WHERE Events__c IN :setEVId];
            system.debug('in trigger ::'+attList);
            if(!attList.isEmpty()){
                List<Points_Transaction__c> points = new List<Points_Transaction__c>();
                for(Attendance__c at : attList){
                    if(at.Account__c !=  null){
                        Points_Transaction__c point =  new Points_Transaction__c();
                        point.Credit_To__c = at.Account__c;
                        point.Debit_From__c = Label.PT_Debit_Account;
                        point.Points_Alloted__c = 10;
                        point.Event_Date__c = at.Events__r.Startup_Round__r.Date_of_Investor_Call__c;
                        point.Event_Name__c = at.Events__c;
                        point.Startup__c = at.Events__r.Startup_Round__r.Startup__c;
                        point.Startup_Round__c = at.Events__r.Startup_Round__c;
                        point.Point_Type__c = 'Investor Call';
                        if(at.Events__r.Event_Type__c == 'Feedback Form Complete'){
                            point.Points_Alloted__c = 20;
                            point.Point_Type__c = 'Form Complete';
                        }
                        points.add(point);
                    }                
                }
                if(!points.isEmpty()){
                    system.debug('in trigger ::'+points);
                    insert points; 
                }
            }
        }
        
    }
}