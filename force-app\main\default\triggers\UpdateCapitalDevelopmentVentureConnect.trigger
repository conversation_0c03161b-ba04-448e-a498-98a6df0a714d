trigger UpdateCapitalDevelopmentVentureConnect on Transaction_Advisory_History__c (after insert, after update) {
    /*Map<Id, List<String>> capitalDevelopmentToVentureConnectNames = new Map<Id, List<String>>();

    // Collect Venture Connect names for each related Capital Development
    for (Transaction_Advisory_History__c trans : Trigger.new) {
        if (trans.Venture_Connect__c != null) {
            if (!capitalDevelopmentToVentureConnectNames.containsKey(trans.Capital_Development__c)) {
                capitalDevelopmentToVentureConnectNames.put(trans.Capital_Development__c, new List<String>());
            }
            
            System.debug('>>>>>>> ' + trans.Venture_Connect__r.Name);
            capitalDevelopmentToVentureConnectNames.get(trans.Capital_Development__c).add(trans.Venture_Connect__r.Name);
        }
    }

    // Update Capital Development records with concatenated Venture Connect names
    List<Capital_Development__c> capitalDevelopmentsToUpdate = new List<Capital_Development__c>();
    String concatenatedNames;
    for (Id capitalDevelopmentId : capitalDevelopmentToVentureConnectNames.keySet()) {
        List<String> ventureConnectNames = capitalDevelopmentToVentureConnectNames.get(capitalDevelopmentId);
        concatenatedNames = String.join(ventureConnectNames, ' , ');
        
        Capital_Development__c capitalDevelopment = new Capital_Development__c(Id = capitalDevelopmentId);
        capitalDevelopment.VC_Network_Route_to_market__c = concatenatedNames;
        capitalDevelopmentsToUpdate.add(capitalDevelopment);
    }
	
    System.debug('>>>>>>> ' + concatenatedNames);
    
    // Update related Capital Development records
    if (!capitalDevelopmentsToUpdate.isEmpty()) {
        update capitalDevelopmentsToUpdate;
    }
*/}