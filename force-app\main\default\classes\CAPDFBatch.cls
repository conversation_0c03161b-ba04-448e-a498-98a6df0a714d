global class CAPDFBatch implements Database.Batchable<sObject>,Database.Stateful,Database.AllowsCallouts {
    public String query;
    global string sessionId;
    global List<string> caids;
    global date fromDate;
    global date toDate;
    global CAPDFBatch(){
        this.sessionId  = sessionId;     
        query = 'Select Id,Name,Email_ID__c FROM Contribution_Agreement__c WHERE Name IN :caids ';
    }
    global Database.QueryLocator start(Database.BatchableContext BC){
        System.debug('=========='+Database.getQueryLocator(query));
        return Database.getQueryLocator(query);
    }
    
    global void execute(Database.BatchableContext BC, List<Contribution_Agreement__c> scope) {
        system.debug('scope ::'+scope);
        Id emailId = [SELECT Id FROM OrgWideEmailAddress where Address ='<EMAIL>' limit 1].Id;
        List<Messaging.Email> messages = new List<Messaging.Email>();
        for(Contribution_Agreement__c ca : scope){
            // CAPDFRestController.attachPdfToRecord(ca.id,sessionId);
            PageReference pdfPage = new PageReference('/apex/GeneratePDF?Id='+ca.Id+'&fromDate='+fromDate+'&toDate='+toDate);
            pdfPage.getParameters().put('id',ca.Id);
            Blob pdf;
            if(!test.isRunningTest()){
                pdf = pdfPage.getContentAsPdf(); 
            }else{
                pdf = blob.valueof('TEST');
            }
            Messaging.EmailFileAttachment efa = new Messaging.EmailFileAttachment();
            efa.setFileName('FirstPort Capital - Unit Statement - '+ ca.Name + ' - '+ DateTime.newInstance(
                toDate.year(), toDate.month(), toDate.day()).format('dd-MMM-YYYY') +'.pdf');
            efa.setContentType('application/pdf');
            efa.setBody(pdf);
            
            // Create the Singal Email Message
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setSubject('FirstPort Capital - Unit Statement - '+DateTime.newInstance(
                toDate.year(), toDate.month(), toDate.day()).format('dd-MMM-YYYY'));
            email.setOrgWideEmailAddressId(emailId);
            email.setBccAddresses(new String[] { '<EMAIL>','<EMAIL>' });
            if(ca.Email_ID__c !=  null)
            	email.setToAddresses(new String[] {ca.Email_ID__c});//add account email id
            else
            	email.setToAddresses(new String[] { '<EMAIL>','<EMAIL>'});//add account email id
            email.setHtmlBody( 'Dear Investor,<br/><br/> Please find attached the unit statement for your investments in FirstPort'
									+' Capital as of '+DateTime.newInstance(
                toDate.year(), toDate.month(), toDate.day()).format('dd-MMM-YYYY')+'. <br/><br/>'+
									+'This statement is for transactions done before '+DateTime.newInstance(
                toDate.year(), toDate.month(), toDate.day()).format('dd-MMM-YYYY')+' and where Units '+
                                   'have been allotted.<br/><br/> In case of any questions or discrepancies, kindly <NAME_EMAIL>.'+
                                   '<br/><br/>Regards,<br/> Investor Relations<br/>FirstPort Capital');
            email.setFileAttachments(new Messaging.EmailFileAttachment[] {efa});
            messages.add(email);
        }
        Messaging.sendEmail(messages);
    }
    
    global void finish(Database.BatchableContext BC){     
    }
}