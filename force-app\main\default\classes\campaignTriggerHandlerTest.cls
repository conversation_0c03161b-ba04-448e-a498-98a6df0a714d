//-------------------------------------- This is the test class for campaignTriggerHandler , campaignTrigger , promoCodeAPI

@isTest
public class campaignTriggerHandlerTest {
    
    public class PromoCodeAPIMock implements HttpCalloutMock {
        
        public HTTPResponse respond(HTTPRequest req) {
            String fullJson = 'your Json Response';
            Map<String,Object> credMAp1 = new Map<String,Object>();
            credMAp1.put('token','token111');
            
            Map<String,Object> credMAp = new Map<String,Object>();
            credMAp.put('data',credMAp1);
            credMAp.put('mobile','8866104284');
            credMAp.put('country_code','+91');
            credMAp.put('password','hello');	
            fullJson = JSON.serialize(credMAp);
            
            HTTPResponse res = new HTTPResponse();
            res.setHeader('Content-Type', 'text/json');
            res.setBody(fullJson);
            res.setStatusCode(200);
            return res;
        }
    }
    
     @isTest
    static void testSendCampaignDetails() {
        
        List<Campaign> testCampaign = new List<Campaign>{
            new Campaign(Name = 'Test Campaign 1', 
                         Campaign_Code__c = 'CODE123', 
                         Campaign__c = 'Test Campaign',
                         Campaign_logo_url__c = 'https://example.com/logo.png', 
                         Discount_Percentage__c = 20,
                         Visibility__c = 'Public', 
                         //Status_of_Campaign_Code__c = 'Active', 
                         //Final_Amount_to_be_paid_incl_GST__c = 100.0,
                         StartDate = Date.today(), 
                         EndDate = Date.today().addDays(10), 
                         Headline_Text__c = 'Test Headline', 
                         Description = 'Test Description')
        };
        insert testCampaign;
        
        Set<Id> campaignIds = new Set<Id>();
        List<Campaign> campaignToUpdate = new List<Campaign>();
        for (Campaign cmp : testCampaign) {
            //update operation for coverage of trigger handler
            cmp.Description = 'campaogn update successfully';
            campaignIds.add(cmp.Id);
            campaignToUpdate.add(cmp);
        }
        update campaignToUpdate;
        
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseController());
        Test.startTest();
        PromoCodeAPI.sendCampaignDetails(campaignIds);
        Test.stopTest();

    }
}