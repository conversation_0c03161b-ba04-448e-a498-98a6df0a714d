public class DownloadAllCA {
    public Blob csvFileBody{get;set;}
    public String csvAsString{get;set;}
    public String[] csvFileLines{get;set;}
    public set<string> CAIDs;
    public date fromDate;
    public date toDate;
    public String CANo{get;set;}
   
    
    public PageReference downloadAll(){
        CAIDs = new set<string>();
        List<Contribution_Agreement__c> listCA = new List<Contribution_Agreement__c>();
        if(csvFileBody != null){
            csvAsString = csvFileBody.toString();
            csvAsString = csvAsString.replaceAll('(\r\n|\r)','\n');
            csvAsString = String.valueOf(csvAsString);
            String[] csvFileLines = csvAsString.split('\n');
            system.debug('csvFileLines ::'+csvFileLines.size());
            system.debug('csvFileLines ::'+csvFileLines[0]);
            for(Integer i=1; i <= csvFileLines.size() - 1; i++){
                String[] csvRecordData = csvFileLines[i].split(',');
                String[] csvRecordDateData = csvFileLines[1].split(',');
                system.debug('csvRecordData[0] ::'+csvRecordData[0]+'::');
                CAIDs.add(csvRecordData[0]);
                if(csvRecordDateData[1] != null && csvRecordDateData[2] != null){
                    fromDate = date.parse(csvRecordDateData[1]);
                	toDate = Date.parse(csvRecordDateData[2]);
                }
                
                system.debug('CAIDs ::'+CAIDs+'::');
            }
        }
        if(CAIDs != null){
            listCA = [SELECT Id,Name FROM Contribution_Agreement__c where Name in : CAIDs]; 
        }
        else{
            listCA = [SELECT Id,Name FROM Contribution_Agreement__c limit 1];
        }
        ID entryID;
        List<Data_Entry__c> entry = new List<Data_Entry__c>();
        entry = [SELECT Id FROM Data_Entry__c];
        if(!entry.isEmpty() && entry.size() >0 ){
            entryID = entry[0].Id;
        }
        else{
            entryID = 'a0N9C000000VBpoUAG';
        }
        if(entryID != null)
        delete [SELECT ContentDocumentId FROM ContentDocumentLink WHERE LinkedEntityId = :entryID];
        //PageReference pdf = new pagereference('/apex/GeneratePDF?id='+listCA[0].Id+'&fromDate='+fromDate+'&toDate='+toDate);
        //pdf.getParameters().put('id', listCA[0].ID);
        system.debug('list  listCA--- '+listCA);
        List<contentVersion> contentDocumentsToInsert = new List<contentVersion>();
        List<Id> contentDocumentIds = new List<Id>();
        for(Contribution_Agreement__c ca : listCA){
            system.debug('list  ca--- '+ca);
            Blob pdfFileData;
            //return new pagereference('/apex/GeneratePDF?id='+ca.Id);
            PageReference PDf =  Page.GeneratePDF;//Replace attachmentPDf with the page you have rendered as PDF
            PDf.getParameters().put('id',ca.Id);
            PDf.getParameters().put('fromDate',String.valueOF(fromDate));
            PDf.getParameters().put('toDate',String.ValueOF(toDate));
            PDf.setRedirect(true);
            if(!test.isRunningTest()){
                pdfFileData = PDf.getContent();
            }else{
                pdfFileData = blob.valueof('TEST');
            }
             // Replace with your PDF file data
            String pdfFileName = ca.Name+'.pdf'; // Replace with your PDF file name
            
            ContentVersion contentVersion = new ContentVersion(
                Title = pdfFileName,
                PathOnClient = pdfFileName,
                VersionData = pdfFileData,
                IsMajorVersion = true
            );
            contentDocumentsToInsert.add(contentVersion);
        }
        insert contentDocumentsToInsert;
        
        List<ContentDocumentLink> contentDocumentLinksToInsert = new List<ContentDocumentLink>();
        for(ContentVersion cv : contentDocumentsToInsert){
            Id conDocId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id =:cv.Id].ContentDocumentId;
            ContentDocumentLink cdl = New ContentDocumentLink();
            cdl.LinkedEntityId = entryID;
            cdl.ContentDocumentId = conDocId;
            cdl.shareType = 'V';
            contentDocumentLinksToInsert.add(cdl);
        }
        insert contentDocumentLinksToInsert;
        
        List<ContentDocumentLink> contentLinks = [
            SELECT ContentDocumentId
            FROM ContentDocumentLink
            WHERE LinkedEntityId = :entryID//:contentFolderId
        ];
        system.debug('contentLinks ::'+contentLinks);
        for(ContentDocumentLink cdl : contentLinks) {
            contentDocumentIds.add(cdl.ContentDocumentId);
        }
        List<String> downloadUrls = new List<String>();
        for (Id docId : contentDocumentIds) {
            String downloadUrl = System.Label.downloadURL +
                'sfc/servlet.shepherd/document/download/' + docId +'/?';
            downloadUrls.add(docId);
        }
        //string downloadUrl = downloadUrls.join('/');
        String downloadUrl = System.Label.downloadURL +
            'sfc/servlet.shepherd/document/download/' + String.join(downloadUrls,'/') +'?operationContext=S1';
        PageReference redirectPage = new PageReference(downloadUrl); // Redirect to the first URL
        redirectPage.setRedirect(true);
        return redirectPage;        
    }
}