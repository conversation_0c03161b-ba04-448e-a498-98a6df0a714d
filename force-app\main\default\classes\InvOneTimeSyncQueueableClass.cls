public class InvOneTimeSyncQueueableClass implements Queueable {
    private Set<Id> invForAPISet = new Set<Id>();
    public InvOneTimeSyncQueueableClass(Set<Id> recordIdSet) {
        this.invForAPISet = recordIdSet;
    }
    
    public void execute(QueueableContext context) {
        // Perform some asynchronous operation here
        // You can call future methods or make callouts from here
            InvestmentRestAPIController.sendInvestmentDetails(invForAPISet,false);
    }
}