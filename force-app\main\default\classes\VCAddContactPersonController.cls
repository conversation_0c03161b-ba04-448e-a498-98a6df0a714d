public with sharing class VCAddContactPersonController {
    public list<ContactPersonWrapper> cPersonWrapList{get; set;}
    public string errormsg{get; set;}
    private Integer counter = 0;
    public Venture_Connect__c vcObj;
    
    public VCAddContactPersonController(ApexPages.StandardController stdCtrl) {
        
        cPersonWrapList = new list<ContactPersonWrapper>();
        if (!Test.isRunningTest())
        {
            stdCtrl.addFields(new List<String>{'Name', 'Contact_Person_Details__c'});
            vcObj =(Venture_Connect__c)stdCtrl.getRecord();
        }
        else
        {
            vcObj =[select id,name,Contact_Person_Details__c from Venture_Connect__c limit 1];
        }
        if (String.isNotBlank(vcObj.Contact_Person_Details__c) && vcObj.Contact_Person_Details__c.trim().startsWith('[')) {
            try {
                cPersonWrapList = (List<ContactPersonWrapper>)JSON.deserialize(vcObj.Contact_Person_Details__c, List<ContactPersonWrapper>.class);
              
            } catch (Exception e) {
                errormsg = 'Error parsing contact details: ' + e.getMessage();
                addRow();
            }
        } 
        else
        {
            addRow();
        }
        
        System.debug('counter>>>>'+counter);
        System.debug('vcObj>>>>'+vcObj);
        System.debug('before ersonWrapList>>>>'+cPersonWrapList);
        
        counter = maximumIndex(cPersonWrapList);
        
        System.debug('counter1111>>>>'+counter);
        System.debug('after ersonWrapList>>>>'+cPersonWrapList);
        
    }
    
    public PageReference saveAction() {
        boolean isValid = true;
        boolean URLValid = true;
        if(cPersonWrapList!=null && cPersonWrapList.size()>0)
        {
            counter = 1;
            for(ContactPersonWrapper cWr : cPersonWrapList)
            {                     
                cWr.iIndex = counter;
                counter++;  
    
            }
            vcObj.Contact_Person_Details__c = JSON.serialize(cPersonWrapList);
            System.debug(JSON.serialize(cPersonWrapList)); 
        }
        else
        {
            vcObj.Contact_Person_Details__c = null;
        }
        
        update vcObj;  
        return null;
    }
    
    //Adding Rows 
    public PageReference addRow() {
        System.debug('addRow counter>>>>'+counter);
        
        if(cPersonWrapList!=null && cPersonWrapList.size()>0)
            counter = maximumIndex(cPersonWrapList);
        
        counter++;
        System.debug('addRow counter11>>>>'+counter);
        
        cPersonWrapList.add(new ContactPersonWrapper('','','','','','','','','','','','','',counter));
        return null;
    }
    
    public void noOp() {
    }
    
    //Removing The Rows
    public PageReference removeRow() {
        Integer iRemoveRow = Integer.valueOf(Apexpages.currentpage().getParameters().get('index'));
        for(Integer i=0; i<cPersonWrapList.size(); i++) {
            if(cPersonWrapList[i].iIndex == iRemoveRow ) {
                cPersonWrapList.remove(i);     
            }
        }
        //counter = maximumIndex(cPersonWrapList);
        if(cPersonWrapList==null || cPersonWrapList.size()==0)
            addRow();
        
        return null;
    }
    
    public Integer maximumIndex(List<ContactPersonWrapper> conPerList){
        Integer maxvalue = conPerList[0].iIndex;
        For(integer i =0;i<conPerList.size();i++)
        {
            if( conPerList[i].iIndex > maxvalue)
                maxvalue = conPerList[i].iIndex;             
        }    
        system.debug('the max value is'+maxvalue);
        return maxvalue;
    }
    
    public static Boolean validateEmail(String email) {
        Boolean isValid = true;
        if(!Pattern.matches('^[a-zA-Z0-9._|\\\\%#~`=?&/$^*!}{+-]+@[gmail.-]+\\.[com]{2,4}$', email))
            isValid = false;
        system.debug('---'+isValid);
        return isValid;
    }
    
    public List<SelectOption> getLocationOptions() {
        List<String> locations = new List<String>{
            'Agra', 'Ajmer', 'Aligarh', 'Allahabad', 'Amritsar', 'Bareilly', 'Chandigarh',
                'Dehradun', 'Delhi / NCR', 'Faridabad', 'Ghaziabad', 'Gurgaon', 'Jammu', 'Kanpur',
                'Lucknow', 'Meerut', 'Moradabad', 'Noida', 'Varanasi', 'Ahmedabad', 'Aurangabad',
                'Bhopal', 'Indore', 'Jaipur', 'Jodhpur', 'Kota', 'Nagpur', 'Nashik', 'Navi Mumbai',
                'Pune', 'Raipur', 'Rajkot', 'Surat', 'Thane', 'Vadodara', 'Ambattur', 'Bangalore',
                'Chennai', 'Coimbatore', 'Hubli and Dharwad', 'Hyderabad', 'Kochi', 'Madurai',
                'Mangalore', 'Mysore', 'Thiruvananthapuram', 'Vijayawada', 'Visakhapatnam',
                'Asansol', 'Bhubaneswar', 'Cuttack', 'Dhanbad', 'Guwahati', 'Kolkata', 'Patna',
                'Ranchi', 'Siliguri', 'Gwalior', 'Jabalpur', 'Ujjain', 'Belgaum', 'Bhavnagar',
                'Durgapur', 'Guntur', 'Jalgaon', 'Jamnagar', 'Jamshedpur', 'Kolhapur', 'Loni',
                'Pimpri & Chinchwad', 'Sangli Miraj Kupwad', 'Solapur', 'Ulhasnagar', 'Boston',
                'California', 'Charlotte', 'Chicago', 'New Jersey', 'New York', 'Palo Alto',
                'Philadelphia', 'San Diego', 'San Francisco', 'Santa Clara', 'Seattle', 'Toronto',
                'Washington', 'Brussels', 'London', 'Moscow', 'Parndroff', 'Rijswijk', 'Sheffield',
                'ST Petersburg', 'Beijing', 'Dubai', 'Hangzhou', 'Hong Kong', 'Seoul', 'Shanghai',
                'Shenzhen', 'Singapore', 'Tokyo', 'Cape Town', 'Sydney', 'Bermuda', 'Mockba',
                'Port Louis'
                };
                    
        List<SelectOption> options = new List<SelectOption>();
        options.add(new SelectOption('', '--Select--'));
        for (String loc : locations) {
            options.add(new SelectOption(loc, loc));
        }
        return options;
    }
    
    public List<SelectOption> getSectorFocus() {
        List<String> sectors = new List<String>{
            'Agri-tech','D2C', 'Deep-tech','Edu-tech','Content / Social','E-commerce','Grocery-Tech','Fin-tech',
                'HR-tech','Logistics','Mobility','Property-tech','Clean-tech','Services','Sport-Fit','B2B / SaaS',
                'Health-tech','AI','Space Tech','SIN Space'};
                    
        List<SelectOption> options = new List<SelectOption>();
        for (String sector : sectors) {
            options.add(new SelectOption(sector, sector));
        }
        return options;
    }
    
    public List<SelectOption> getMarketSegment(){
         List<String> segments = new List<String>{'B2B', 'B2B2C', 'B2C', 'D2C2', 'B2G'};
            List<SelectOption> options = new List<SelectOption>();
        for (String sg : segments) {
            options.add(new SelectOption(sg, sg));
        }
        return options;
    }
    
    
    public List<SelectOption> getMentorOptions() {
        List<SelectOption> options = new List<SelectOption>();
        options.add(new SelectOption('', '--Select--'));
        for (Account acc : [SELECT Id, Name FROM Account WHERE Name IN ('Vinay', 'Mitesh', 'Ankur')]) {
            options.add(new SelectOption(acc.Id, acc.Name));
        }
        return options;
    }
    
    public List<SelectOption> getLeadOptions() {
        List<SelectOption> options = new List<SelectOption>();
        options.add(new SelectOption('', '--Select--'));
        for (Account acc : [SELECT Id, Name FROM Account WHERE Name IN ('Vinay', 'Mitesh', 'Ankur', 'Vikram')]) {
            options.add(new SelectOption(acc.Id, acc.Name));
        }
        return options;
    }
    
    public class ContactPersonWrapper{
        public String contactPerson {get; set;}
        public String emailID {get; set;}
        public String phoneNo {get; set;}
        public String linkedINURL {get; set;}
        public String spocDesignation { get; set; }
        public String spocLocation { get; set; }
        public String spocTaggingBasis { get; set; }
        public String ipvSpocMentor { get; set; }
        public String ipvSpocLead { get; set; }
        public String ipvSpocCoLead {get; set;}
        public String ipvSpocSupport {get; set;}
        public String connected {get; set;}
        public String marketSegment {get; set;}
        public String remarks {get; set;}
        public Integer iIndex {get;set;}
        public String selectedSectors { get; set; } 
        
        public ContactPersonWrapper(String contactPerson,String emailID,String phoneNo,String linkedINURL, String spocDesignation, String spocLocation, String spocTaggingBasis, String ipvSpocMentor, String ipvSpocLead, String ipvSpocSupport, String connected, String marketSegment, String remarks, Integer iRow)
        {
            this.contactPerson = contactPerson;
            this.emailID = emailID;
            this.phoneNo = phoneNo;
            this.linkedINURL =linkedINURL;
            this.spocDesignation = spocDesignation;
            this.spocLocation = spocLocation;
            this.spocTaggingBasis = spocTaggingBasis;
            this.ipvSpocMentor = ipvSpocMentor;
            this.ipvSpocLead = ipvSpocLead;
            this.ipvSpocCoLead = ipvSpocCoLead;
            this.ipvSpocSupport = ipvSpocSupport;
            this.connected = connected;
            this.marketSegment = marketSegment;
            this.remarks = remarks;
            this.iIndex = iRow;
        }        
    }
}