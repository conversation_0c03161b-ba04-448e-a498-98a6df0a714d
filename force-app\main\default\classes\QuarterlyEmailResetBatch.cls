public class QuarterlyEmailResetBatch implements Database.Batchable<SObject> {

    public Database.QueryLocator start(Database.BatchableContext bc) {
        return Database.getQueryLocator([
            SELECT Id, Has_Been_Emailed__c, Email_Quater__c FROM Account WHERE Has_Been_Emailed__c = TRUE  AND Membership_Status__c  IN ('Paid IPV Fee','Paid Community','Paid by CXO Points','Paid by IPV Points','AIM+ Sales')]);
    }

    public void execute(Database.BatchableContext bc, List<SObject> scope) {
        List<Account> accountsToUpdate = new List<Account>();
        for (SObject s : scope) {
            Account acc = (Account)s;
            acc.Has_Been_Emailed__c = false;
            acc.Email_Quater__c = null;
            accountsToUpdate.add(acc);
        }
        if (!accountsToUpdate.isEmpty()) {
            update accountsToUpdate;
        }
    }

    public void finish(Database.BatchableContext bc) {
      
    }
}