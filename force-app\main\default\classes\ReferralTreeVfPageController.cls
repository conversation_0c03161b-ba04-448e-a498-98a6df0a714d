public class ReferralTreeVfPageController {
    public List<HierarchyWrapper> ParentHierarchy { get; set; }
    public Decimal TotalInvested { get; set; }
    public Decimal TotalComitted { get; set; }
    public Integer TotalReferred { get; set; }
    public Integer totalActive { get; set; }
    public Integer totalInactive { get; set; }
    public Account mainParentParent { get; set; }
    Id IPVRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
            

    // Default constructor without parameters
    public ReferralTreeVfPageController() {
        ParentHierarchy = new List<HierarchyWrapper>();
        TotalInvested = 0;
        TotalComitted = 0;
        TotalReferred = 0;
        totalActive = 0;
        totalInactive = 0;
    }

    // Method to initialize data
   public void initializeHierarchy() {
        String accountId = ApexPages.currentPage().getParameters().get('id');
        if (String.isNotBlank(accountId)) {
            try {
                // Fetch all related accounts and leads
                Map<Id, List<Account>> parentToChildAccountsMap = new Map<Id, List<Account>>();
                Map<Id, List<Lead__c>> accountToLeadsMap = new Map<Id, List<Lead__c>>();
                List<Account> allAccounts = [SELECT Id, Name, Membership_Status__c, Total_Amount_Invested__c, Total_Referred__c, Relationship_Manager__r.Name, Total_Amount_Committed__c, ParentId
                                             FROM Account WHERE ParentId != null];
                List<Lead__c> allLeads = [SELECT Id, Name, Membership_Status__c, Relationship_Manager__r.Name,	Relationship_Owner__r.Name, Total_Referred_Account_Leads__c, Referred_By__c 
                                          FROM Lead__c WHERE Referred_By__c != null];

                // Build maps for quick lookup
                for (Account acc : allAccounts) {
                    if (!parentToChildAccountsMap.containsKey(acc.ParentId)) {
                        parentToChildAccountsMap.put(acc.ParentId, new List<Account>());
                    }
                    parentToChildAccountsMap.get(acc.ParentId).add(acc);
                }

                for (Lead__c lead : allLeads) {
                    if (!accountToLeadsMap.containsKey(lead.Referred_By__c)) {
                        accountToLeadsMap.put(lead.Referred_By__c, new List<Lead__c>());
                    }
                    accountToLeadsMap.get(lead.Referred_By__c).add(lead);
                }

                // Fetch the main parent account and its parent
                Account mainParent = [SELECT Id, Name, Membership_Status__c, Total_Amount_Invested__c, Total_Referred__c, Relationship_Manager__r.Name, Total_Amount_Committed__c, ParentId 
                                      FROM Account WHERE Id = :accountId AND RecordTypeId =: IPVRecordTypeId LIMIT 1];

                if (mainParent != null) {
                      HierarchyWrapper mainParentWrapper = new HierarchyWrapper(mainParent);
                        ParentHierarchy.add(mainParentWrapper);
                        populateChildHierarchy(mainParentWrapper, parentToChildAccountsMap, accountToLeadsMap);
                
                    // Fetch main parent account's parent
                    mainParentParent = (mainParent.ParentId != null) ? 
                        [SELECT Id, Name, Membership_Status__c, Total_Amount_Invested__c, Total_Referred__c, Relationship_Manager__r.Name, Total_Amount_Committed__c 
                         FROM Account WHERE Id = :mainParent.ParentId LIMIT 1] : null;

                }
            
            } catch (Exception e) {
                // Handle exception (e.g., log it or display a message)
                System.debug('Error initializing hierarchy: ' + e.getMessage());
            }
        }

        // Calculate totals after hierarchy initialization
        calculateTotals();
    }

    public void populateChildHierarchy(HierarchyWrapper parentWrapper, Map<Id, List<Account>> parentToChildAccountsMap, Map<Id, List<Lead__c>> accountToLeadsMap) {
        // Populate child accounts
        if (parentToChildAccountsMap.containsKey(parentWrapper.account.Id)) {
            for (Account child : parentToChildAccountsMap.get(parentWrapper.account.Id)) {
                HierarchyWrapper childWrapper = new HierarchyWrapper(child);
                parentWrapper.Children.add(childWrapper);
                populateChildHierarchy(childWrapper, parentToChildAccountsMap, accountToLeadsMap); // Recursively populate child hierarchy
            }
        }

        // Populate leads
        if (accountToLeadsMap.containsKey(parentWrapper.account.Id)) {
            for (Lead__c lead : accountToLeadsMap.get(parentWrapper.account.Id)) {
                HierarchyWrapper leadWrapper = new HierarchyWrapper(lead);
                parentWrapper.Children.add(leadWrapper);
            }
        }
    }

    public void calculateTotals() {
        // Reset totals before calculating
        TotalInvested = 0;
        TotalComitted = 0;
        TotalReferred = 0;
        totalActive = 0;
        totalInactive = 0;

        // Recursively calculate totals from the hierarchy
        for (HierarchyWrapper wrapper : ParentHierarchy) {
            calculateTotalsRecursive(wrapper);
        }
    }

    public void calculateTotalsRecursive(HierarchyWrapper wrapper) {
        if (wrapper.account != null) {
            TotalInvested += wrapper.account.Total_Amount_Invested__c != null ? wrapper.account.Total_Amount_Invested__c : 0;
            TotalComitted += wrapper.account.Total_Amount_Committed__c != null ? wrapper.account.Total_Amount_Committed__c : 0;
            TotalReferred += wrapper.account.Total_Referred__c != null ? wrapper.account.Total_Referred__c.intValue() : 0;

            // Increment active/inactive counters based on Membership_Status__c field
            if (wrapper.isActive()) {
                totalActive++;
            } else {
                totalInactive++;
            }
        }

        if (wrapper.lead != null) {
            // Increment active/inactive counters based on Membership_Status__c field for leads
            if (wrapper.isActive()) {
                totalActive++;
            } else {
                totalInactive++;
            }
        }

        // Recursively calculate totals for child wrappers
        for (HierarchyWrapper child : wrapper.Children) {
            calculateTotalsRecursive(child);
        }
    }

    public class HierarchyWrapper {
        public Account account { get; set; }
        public Lead__c lead { get; set; }
        public List<HierarchyWrapper> Children { get; set; }
        public String MembershipStatus { get; set; } // Color for Membership_Status__c field

        // Constructor for Account
        public HierarchyWrapper(Account acc) {
            account = acc;
            lead = null;
            Children = new List<HierarchyWrapper>();
            MembershipStatus = (acc.Membership_Status__c != null && isAccountActive(acc)) ? 'green' : 'red';
        }

        // Constructor for Lead
        public HierarchyWrapper(Lead__c ld) {
            lead = ld;
            account = null;
            Children = new List<HierarchyWrapper>();
            MembershipStatus = (isLeadActive(ld)) ? 'green' : 'red';
        }

        // Method to determine if the account or lead is active
        public Boolean isActive() {
            if (account != null) {
                return isAccountActive(account);
            } else if (lead != null) {
                return isLeadActive(lead);
            }
            return false;
        }

        // Method to check if account is active based on specific values
        public Boolean isAccountActive(Account acc) {
            Set<String> activeValues = new Set<String>{'Yes', 'Paid IPV Fee','Exited Member Reactivated','AIM+ Sales','Call Scheduled', 'No Response 1','No Response 3','No Response 2','Paid Community', 'Complimentary', 'Paid by CXO Points', 'Paid by IPV Points', 'Platinum', 'On Trial', 'On Trial Community', 'VC/ IPV Portfolio Founder', 'Physis Contributor'};
            return (acc.Membership_Status__c != null && activeValues.contains(acc.Membership_Status__c));
        }

        // Method to check if lead is active based on specific values
        public Boolean isLeadActive(Lead__c ld) {
            Set<String> activeValues = new Set<String>{'New Lead','IPV RM to reach out', 'New Lead (DND)', 'To be reached out', 'Asked to call back', 'Introduction message sent', 'Awaiting Response','Call Scheduled','Call done','Payment link to be sent','Follow up sent','No Response - Call 1','No Response - Call 2','No Response - Call 3','No Response - Call 4','No Response - Call 5','Onboarding done but not moved to trial','Payment link shared','Form Refilled','Reactivated','Retargeting','DND Activated'};
            return (ld.Membership_Status__c != null && activeValues.contains(ld.Membership_Status__c));
        }
    }
}