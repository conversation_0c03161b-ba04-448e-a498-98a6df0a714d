public class ConvertLeadCtrlHD
{
    public boolean showCancelBtn{get;set;}
    
    public Id convertLeadAccount(Id recId){
        
        try{
        
            id IPVrecordTypeId = Schema.SObjectType.lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
            Id recordId = recId;
            String sourceObjectName = recordId.getSObjectType().getDescribe().getName();
            String targetObjectName = '';
            Map<String,Schema.SObjectField> schemaMap = new Map<String,Schema.SObjectField>();
            List<Task> taskList = new List<Task>();
            
            if(sourceObjectName == 'Lead__c')
                targetObjectName = 'Account';
            else
                targetObjectName = 'Lead__c';
            
            DescribeSObjectResult describeResult = recordId.getSObjectType().getDescribe();
            List<String> fieldNames = new List<String>( describeResult.fields.getMap().keySet() );
            
            String query =
              ' SELECT ' +
                  String.join( fieldNames, ',' ) +
              ' FROM ' +
                  describeResult.getName() +
              ' WHERE ' +
                  ' id = :recordId ' +
              ' LIMIT 1 '
            ;
            
            // return generic list of sobjects or typecast to expected type
            List<SObject> sourceObjList = Database.query( query );
            System.debug('sourceObjList>>>>>'+ sourceObjList);
            
            sObject targetObj = Schema.getGlobalDescribe().get(targetObjectName).newSObject() ;
            schemaMap = targetObj.getSobjectType().getDescribe().fields.getMap();

            for(String f : fieldNames)
            {
                if(schemaMap.containsKey(f) && schemaMap.get(f).getDescribe().isCreateable())
                {
                    system.debug('>>>>'+f);
                    system.debug('sourceObjList[0].get(f)>>>>'+sourceObjList[0].get(f));
                    targetObj.put(f,sourceObjList[0].get(f));
                }
            }
            
            
            // Added by Sahil on 27.11.2023 
            // Method to check if the City value is valid in the picklist
            if(sourceObjList[0].get('City__c')!=null && sourceObjList[0].get('RecordTypeId') == IPVrecordTypeId)
            {
                String lowerCity = (String)sourceObjList[0].get('City__c');
                lowerCity = lowerCity.toLowerCase();
                if(isValidCity(lowerCity))
                {
                     targetObj.put('City__c',lowerCity);
                }
                else
                {
                    ApexPages.addMessage(new ApexPages.Message( ApexPages.Severity.ERROR,'Please update the city field according to the standard set of cities. You can access the list by clicking on the “Standardized city list” field on the lead object'));  
                    return null;
                }
            }
            else if(sourceObjList[0].get('City__c') == null && (sourceObjList[0].get('RecordTypeId') == IPVrecordTypeId))
            {
                ApexPages.addMessage(new ApexPages.Message( ApexPages.Severity.ERROR,'City Field Can Not Be Blank While Converting Lead To Account'));  
                return null;
            }
            
            System.debug('targetObj>>>>>'+targetObj);
            if(targetObjectName=='Account')
            {
                targetObj.put('Membership_Status__c','On Trial');
                
                if(sourceObjList[0].get('Community_Group_Name__c')==null) 
                    targetObj.put('Membership_Status__c','On Trial');
                
                if(sourceObjList[0].get('Primary_Group_Name__c')==null) 
                    targetObj.put('Membership_Status__c','On Trial Community');
                    
                //Copy the refer by field on new Accounts.
                if(sourceObjList[0].get('Referred_By__c')!=null) 
                    targetObj.put('ParentId',sourceObjList[0].get('Referred_By__c'));
                    
                //  Added By Bharat on 09.02.2024
                //  Copy the Gain From Joining IPV on New Account
                if(sourceObjList[0].get('Gain_from_Joining__c')!=null)
                    targetObj.put('Gain_from_Joining_IPV__c',sourceObjList[0].get('Gain_from_Joining__c'));
                
                //  Added By Sahilparvat on 16.02.2024
                //  Copy The Lead Quality Score % And Lead Quality Score on New Account
                if(sourceObjList[0].get('Lead_Quality_Score__c')!=null)
                    targetObj.put('Lead_Quality_Score__c',sourceObjList[0].get('Lead_Quality_Score__c'));               

                if(sourceObjList[0].get('Lead_Quality_Score__c')!=null)
                    targetObj.put('Lead_Quality_Score_Percentage__c',sourceObjList[0].get('Lead_Quality_Score_Percentage__c'));
                
                if(sourceObjList[0].get('Designation_Band__c')!=null)
                    targetObj.put('Designation_Band__c',sourceObjList[0].get('Designation_Band__c'));
                
                if(sourceObjList[0].get('College_Tier__c')!=null)
                    targetObj.put('College_Tier__c',sourceObjList[0].get('College_Tier__c'));
                
                if(sourceObjList[0].get('Are_They_Part_of_Other_Platform__c')!=null)
                    targetObj.put('Are_They_Part_of_Other_Platform__c',sourceObjList[0].get('Are_They_Part_of_Other_Platform__c'));
                
                if(sourceObjList[0].get('Total_Referred_Account_Leads__c')!=null)
                    targetObj.put('Referred_leads__c',sourceObjList[0].get('Total_Referred_Account_Leads__c'));
                
                //Copy Leads' Relationship_Owner__c to Relationship_Manager__c on new Accounts.
                if(sourceObjList[0].get('Relationship_Owner__c')!=null) 
                    targetObj.put('Relationship_Manager__c',sourceObjList[0].get('Relationship_Owner__c'));
                
                //  Added By Sahil On 22.01.2024
                if(sourceObjList[0].get('Referral_Taken_By__c')!=null) 
                    targetObj.put('Referral_Taken_By__c',sourceObjList[0].get('Referral_Taken_By__c'));
                
                //  Added By Sahil on 27.03.2024
                if(sourceObjList[0].get('Facebook_Campaign_Name__c')!=null) 
                    targetObj.put('Facebook_Campaign_Name__c',sourceObjList[0].get('Facebook_Campaign_Name__c'));
                
                //  Added By Sahil on 12.09.2024
                if(sourceObjList[0].get('Are_you_a_first_time_investor__c')!=null) 
                    targetObj.put('Are_you_a_first_time_investor_c__c',sourceObjList[0].get('Are_you_a_first_time_investor__c'));
                
                
                if(targetObj.get('RecordTypeId')!=null){
                    Id recTypeId = Id.valueOf(''+targetObj.get('RecordTypeId'));
                    //system.debug('recTypeId>>>'+recTypeId);
                    String recordTypeDevName = Schema.SObjectType.Lead__c.getRecordTypeInfosById().get(recTypeId).getDeveloperName();
                    If(recordTypeDevName !=null){
                        if(Schema.SObjectType.Account.getRecordTypeInfosByName().ContainsKey(recordTypeDevName))                        
                        {
                            targetObj.put('RecordTypeId',Schema.SObjectType.Account.getRecordTypeInfosByName().get(recordTypeDevName).getRecordTypeId());
                        }
                    }
                }
                else 
                {
                    targetObj.put('RecordTypeId',Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId());
                }
                system.debug('recTypeId updated1>>>'+targetObj.get('RecordTypeId'));
            }
            else
            {
                //Copy the refer by field on new Lead.
                if(sourceObjList[0].get('ParentId')!=null) 
                    targetObj.put('Referred_By__c',sourceObjList[0].get('ParentId'));
                    
                //Copy Leads' Relationship_Owner__c to Relationship_Manager__c on new Lead.
                if(sourceObjList[0].get('Relationship_Manager__c')!=null) 
                    targetObj.put('Relationship_Owner__c',sourceObjList[0].get('Relationship_Manager__c'));
                
                if(targetObj.get('RecordTypeId')!=null){
                    Id recTypeId = Id.valueOf(''+targetObj.get('RecordTypeId'));
                    system.debug('recTypeId>>>'+recTypeId);
                    String recordTypeDevName = Schema.SObjectType.account.getRecordTypeInfosById().get(recTypeId).getDeveloperName();
                    If(recordTypeDevName !=null){
                        if(Schema.SObjectType.Lead__c.getRecordTypeInfosByName().ContainsKey(recordTypeDevName))                        
                        {
                            targetObj.put('RecordTypeId',Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get(recordTypeDevName).getRecordTypeId());
                        }
                    }
                }
                else 
                {
                    targetObj.put('RecordTypeId',Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId());
                }
                system.debug('recTypeId updated>>>'+targetObj.get('RecordTypeId'));
            }
            
            targetObj.put('Is_Converted_From_Lead__c',True);
            insert targetObj;
            system.debug('targetObj>>>>'+targetObj);
            
            taskList = [select id,WhatId from Task where WhatId =:recordId];
            system.debug('taskList>>>>'+taskList);
            
            if(taskList!=null && taskList.size()>0)
            {
                for(Task tObj : taskList)
                {
                    tObj.WhatId = targetObj.Id;
                }
                update taskList;
            }
            system.debug('Updated taskList>>>>'+taskList);
            Delete sourceObjList;
        
            return targetObj.Id;
    }
            catch(Exception e)
        {
            showCancelBtn = true;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'The following exception has occurred: '+e.getMessage()));
            return null;
        }
    }
    
    public pagereference convert()
    {
        Id recordId = apexpages.currentpage().getparameters().get('id');
        
        try
        {
            Id targetObjId = convertLeadAccount(recordId);
            PageReference pg = new PageReference('/'+targetObjId );
            pg.setRedirect(true);
            return pg; 
        }
        catch(Exception e)
        {
            showCancelBtn = true;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'The following exception has occurred: '+e.getMessage()));
            return null;
        }
        
    }
    
    public PageReference cancelAction() {
        Id recordId = apexpages.currentpage().getparameters().get('id');

        PageReference pg = new PageReference('/'+recordId );
        pg.setRedirect(true);
        return pg; 
    }
    /*
    public boolean doesFieldExist(String objName, string fieldName)
    {
        try{
            SObject so = Schema.getGlobalDescribe().get(objName).newSObject();
            
            Map<String,Schema.SObjectField> mapSchema = so.getSobjectType().getDescribe().fields.getMap();
            
            if(mapSchema.containsKey(fieldName) && mapSchema.get(fieldName).getDescribe().isCreateable())
                return true;
            else
                return false;
        }
        catch(Exception ex) {
            showCancelBtn = true;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'The following exception has occurred: '+ex.getMessage()));
        }
         
        return false;
    }
    */
    
    // Added by Sahil on 27.11.2023 Method to check if the City value is valid in the picklist
    private boolean isValidCity(String lowercaseCity)
    {
        List<String> validCities = new List<String>();
        
        // Describe the Account object to get the picklist values for the City field
        Schema.DescribeFieldResult fieldResult = Account.City__c.getDescribe();
        
        List<Schema.PicklistEntry> picklistValues = fieldResult.getPicklistValues();
        
        for(Schema.PicklistEntry picklistEntry : picklistValues) {
            validCities.add(picklistEntry.getValue());
        }
        
        System.debug('City >>>>> ' + validCities);
        
        // Check if the lowercaseCity is a valid city
        return validCities.contains(lowercaseCity);
    }
}