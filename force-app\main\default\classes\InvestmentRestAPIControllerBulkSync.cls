global class InvestmentRestAPIControllerBulkSync {
    private static String CLASS_NAME = 'InvestmentRestAPIControllerBulkSync';
    global static string accessToken;
    global static Integer statusCode = 0;
    
    @future(callout=true)
    public static void sendInvestmentDetails(set<Id> investmentId,boolean isInsert){
        try{
            system.debug('Future investment called ---'+isInsert+'---'+investmentId);
            String jsonData;
            String endURLSetting;
            Map<String,Map<String,List<Investment__c>>> startupInvestmentMap = new Map<String,Map<String,List<Investment__c>>>();
            
            List<API_Setting__c> settingList = API_Setting__c.getall().values();
            system.debug('Contact rest API ctrl API_Setting__c>>>'+settingList);
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].End_URL__c))
            {
                endURLSetting = ''+settingList[0].End_URL__c;
            }
            system.debug('--endURLSetting--'+endURLSetting);
            
            List<Investment__c> investments = [SELECT Id,Name,Share_Certificates_Tracking_ID__c,Share_Certificates_Scan_Sent__c,Investment_Amount_Balance__c,Share_Certificates_Delivered_Hard_Copy__c,Fund_Transfer_From__c,Investor_s_PAN__c,Repatriation__c,Investor_Name__c,SAF_soft_copy__c,PAS_4_Investor_Bankdetails_soft_copy__c,Transfer_Deed_soft_copy__c,Transfer_Deed_hard_copy__c,Deed_of_Adherence_soft_copy__c,Deed_of_Adherence_hard_copy__c,FIRC__c,Get_Physical_Certificate__c,DOA_stamp_paper_soft_copy__c,DOA_stamp_paper_hard_copy__c,GPOA_Taken__c,Investment_Fee_Received__c,Investment_Fee_Balance__c,Funds_Cheque__c,Investor__c,Createddate,Investment_Date__c,
                                               Startup_Round__r.Old_Issue_Price__c,Startup_Round__r.Date_Of_Exit__c,Startup_Round__r.Type_And_Class_Of_Shares__c,Startup_Round__r.Currency__c,Startup_Round__r.Date_of_sending_out_call_for_money_AIF__c,Startup_Round__r.Startup_Name__c,Startup_Round__r.Startup__c,Startup_Round__r.Startup_Converted__c,Old_Investment_Amount_Remitted__c,Old_Number_of_Units_Held__c,Old_Number_of_Shares__c,//Startup_Round__r.Expected_CFM_Date__c,
                                               Startup_Round__r.Date_of_sending_out_call_for_money__c,Startup_Round__r.round_type__c,Startup_Round__r.Exit_Price__c,Parent_Investment__r.Number_Of_Shares__c,Type_of_Gain_Loss__c ,Startup_Round__r.Exit_Unit_Price__c,Old_Date_of_Gross_Gain_Loss_starts__c,
                                               Startup_Round__r.Old_Type_of_Shares__c,Startup_Round__c,Final_Commitment_Amount__c,Investment_Amount__c,Date_of_transaction__c,IRR_Value__c,IRR_Percentage_Formula__c,Exit_Type__c,Carry_fee_received__c,Gross_Gain_Loss__c,Date_of_Gross_Gain_Loss_starts__c,Old_Date_of_Allotment__c,
                                               Number_Of_Shares__c,Date_of_Allotment__c,Number_of_Units_Held__c,AIF_Contributor__c,Investor__r.Amount_Agreed_for_Contribution__c,Startup_Round__r.Unit_Price__c,Exit_Date__c ,Considered_Investment_Amount__c,
                                               Total_Investments__c,Investor_Type__c,Issue_Type__c,Type__c,Investment_in_Own_Name_Family_Member__c,Startup_Round__r.Issue_Price_FnF_Val__c,Startup_Round__r.Issue_Price__c,Residential_Status__c,Exit_amount_to_be_transferred__c,Exit_amount_transferred__c,Parent_Investment__c,    
                                               Account__r.Name , Account__r.Membership_Slab__c, Account__r.Account_Premier_Status__c,Account__r.Total_Points__c,STCG_STCL_Amount__c,LTCG_LTCL_Amount__c,Current_Value__c,Investment_AIF_Class__c,Exit_Fee_received__c , Startup_Converted__c FROM Investment__c 
                                               where id IN :investmentId ];//And (Type__c='Invested' OR Type__c='Committed')];
            system.debug('records --->'+investments);
           
            String type = 'Invested';
            
            for(Investment__c objInv : investments ){
                if(!startupInvestmentMap.containsKey(objInv.Startup_Round__r.Startup__c+'-'+objInv.Startup_Round__c))
                    startupInvestmentMap.put(objInv.Startup_Round__r.Startup__c+'-'+objInv.Startup_Round__c,new Map<String,List<Investment__c>>());
                
                /*
                if(objInv.Type__c == 'Invested' || objInv.Type__c == 'Exit' || objInv.Type__c == 'Partial Exit')
                    type = 'Invested';
                else if(objInv.Type__c == 'Committed' || objInv.Type__c == 'Back out approved' || objInv.Type__c == 'Back out unapproved' || objInv.Type__c == 'Waitlist' || objInv.Type__c == 'Incomplete Data' )
                    type = 'Committed';
                */
                
                if(!startupInvestmentMap.get(objInv.Startup_Round__r.Startup__c+'-'+objInv.Startup_Round__c).containsKey(type))
                {
                    startupInvestmentMap.get(objInv.Startup_Round__r.Startup__c+'-'+objInv.Startup_Round__c).put(type,new List<Investment__c>());
                }
                startupInvestmentMap.get(objInv.Startup_Round__r.Startup__c+'-'+objInv.Startup_Round__c).get(type).add(objInv);
            }
            
            system.debug('startupInvestmentMap>>>>'+startupInvestmentMap);
            if(startupInvestmentMap.size()>0){
                JSONGenerator jsonGen = JSON.createGenerator(true);
                jsonGen.writeStartObject();  
                jsonGen.writeFieldName('startups');
                jsonGen.writeStartArray();
                
                for(String key : startupInvestmentMap.keyset())
                {
                    System.debug('Key>>>'+key);
                    System.debug('startup>>>'+key.split('-')[0]);
                    System.debug('startup Round>>>'+key.split('-')[1]);
                    
                    jsonGen.writeStartObject();                     
                    
                    if(key.split('-')[0] != null) 
                        jsonGen.writeStringField('startup_salesforce_id',key.split('-')[0]);
                    if(key.split('-')[1] != null) 
                        jsonGen.writeStringField('startup_round_id',key.split('-')[1]);
                    
                    for(String tKey : startupInvestmentMap.get(key).Keyset()){
                        if(tKey =='Invested')
                            jsonGen.writeFieldName('investment');
                        else if(tKey=='Committed')
                            jsonGen.writeFieldName('commitments');
                        else if(tKey=='Exit')
                            jsonGen.writeFieldName('exit');
                        jsonGen.writeStartArray();
                    
                        for(Investment__c objInv : startupInvestmentMap.get(key).get(tKey)){
                                                
                            /*
                            if(objInv.Type__c=='Invested')
                                jsonGen.writeFieldName('investment');
                            else if(objInv.Type__c=='Committed')
                                jsonGen.writeFieldName('commitments');
                            */    
                            jsonGen.writeStartObject(); 
                            
                            jsonGen.writeStringField('investment_salesforce_id',objInv.Id); 
                            
                            if(objInv.Investor__c != null) 
                                jsonGen.writeStringField('investor_salesforce_id',objInv.Investor__c);                      
                             
                            if(objInv.Number_Of_Shares__c != null && (objInv.Startup_Round__r.Round_type__c !='Raise' && ObjInv.Type__c !='Invested')) 
                                jsonGen.writeNumberField('no_of_share_exited',objInv.Number_Of_Shares__c);                   
                            else
                                jsonGen.writeNumberField('no_of_share_exited',0);               
                             
                            system.debug('objInv.Id>>>>>'+objInv.id);
                            system.debug('objInv.Issue_Type__c>>>>>'+objInv.Issue_Type__c);
                            
                            if(objInv.Issue_Type__c == 'Primary' || objInv.Issue_Type__c == 'Primary - AIF' || objInv.Issue_Type__c == 'IPV Employee' || objInv.Issue_Type__c == 'IPV HQ - Shadow' || objInv.Issue_Type__c == 'IPV FnF - Shadow'
                               	|| objInv.Issue_Type__c == 'Friends & Family T1'
                               	|| objInv.Issue_Type__c == 'Friends & Family T2 '
                                || objInv.Issue_Type__c == 'Friends & Family T1 - AIF'
                                || objInv.Issue_Type__c == 'Friends & Family T2 - AIF' && (ObjInv.Startup_round__r.round_type__c !='Internal Transfers' && ObjInv.Type__c !='Invested'|| ObjInv.Type__c =='Invested - Shadow' )
                              )
                            {
                                //Codition modified by bharat and added Investment Type :-Invested - Shadow on 20-01-2025
                                if(objInv.Startup_Round__r.Issue_Price__c!=null && (ObjInv.Type__c =='Invested' || ObjInv.Type__c =='Committed' || ObjInv.Type__c =='Invested - Shadow' && ObjInv.Startup_round__r.round_type__c !='Internal Transfers'))
                                {
                                    jsonGen.writeNumberField('price_per_share',objInv.Startup_Round__r.Issue_Price__c);
                                }else if(objInv.Startup_Round__r.Issue_Price__c == null){
                                    jsonGen.writeNumberField('price_per_share' , 0);
                                }
                            }
                            else if(objInv.Issue_Type__c == 'Friends & Family T1' || objInv.Issue_Type__c == 'Friends & Family T2' && (ObjInv.Type__c !='Invested' && ObjInv.Startup_round__r.round_type__c !='Internal Transfers'))
                             {   
                                 if(objInv.Startup_Round__r.Issue_Price_FnF_Val__c!=null){
                                    jsonGen.writeNumberField('price_per_share',objInv.Startup_Round__r.Issue_Price_FnF_Val__c);
                                 }else{
                                     jsonGen.writeNumberField('price_per_share' , 0);
                                 }
                             }
                            
                            if(ObjInv.Type__c =='Invested' && ObjInv.Startup_round__r.round_type__c =='Internal Transfers' && objInv.Startup_Round__r.Exit_price__c != null)
                                jsonGen.writeNumberField('price_per_share',objInv.Startup_Round__r.Exit_Price__c); 

                            if(objInv.Investment_Date__c!=null)
                                jsonGen.writeDateField('date_of_investment',objInv.Investment_Date__c);
                            
                            //  Added By Sahilparvat For CGLS Requirement On 25.06.2024
                            if(objInv.Final_Commitment_Amount__c!=null)
                              jsonGen.writeNumberField('final_commitment_amount',objInv.Final_Commitment_Amount__c); 
                            else
                                jsonGen.writeNumberField('final_commitment_amount',0);
                            
                            if(objInv.Startup_Round__r.Currency__c == 'INR')
                            {
                                jsonGen.writeNumberField('currency',1);
                            }
                            else if(objInv.Startup_Round__r.Currency__c != 'INR' && objInv.Startup_Round__r.Currency__c != null)
                            {
                                jsonGen.writeNumberField('currency',2);   
                            }
                            if(String.IsNotBlank(objInv.Startup_Round__r.Startup_Name__c))
                              jsonGen.writeStringField('startup_name',objInv.Startup_Round__r.Startup_Name__c);
                               
                            if(String.IsNotBlank(objInv.Account__r.Name))
                                jsonGen.writeStringField('account_name',objInv.account__r.Name);
                            
                            if(objInv.Carry_fee_received__c!=null)
                                jsonGen.writeNumberField('carry_fees',objInv.Carry_fee_received__c);
                            
                            if(String.IsNotBlank(objInv.Exit_Type__c)){
                                jsonGen.writeStringField('exit_type',objInv.Exit_Type__c);
                            }else{
                                jsonGen.writeNullField('exit_type');
                            }
                            
                            //Added two field for CGLS by bharat on 15-10-2024
                            if(objinv.Startup_Converted__c != null){
                                jsonGen.writeBooleanField('inv_startup_converted',objInv.Startup_Converted__c);
                            }
                            
                            if(objinv.Exit_Date__c != null){
                                jsonGen.writeDateField('inv_exit_date',objInv.Exit_Date__c);
                            }else{
                                jsonGen.writeNullField('inv_exit_date');
                            }
                            
                            if(objInv.Gross_Gain_Loss__c!=null)
                                jsonGen.writeNumberField('gross_gain_loss',objInv.Gross_Gain_Loss__c);
                            else
                                jsonGen.writeNullField('gross_gain_loss');
                            
                            if(objInv.Date_of_Gross_Gain_Loss_starts__c!=null)
                                jsonGen.writeDateField('date_of_gross_gain_loss_starts',objInv.Date_of_Gross_Gain_Loss_starts__c);
                                         
                            if(String.IsNotBlank(objInv.Type_of_Gain_Loss__c))
                                jsonGen.writeStringField('type_of_gain_loss',objInv.Type_of_Gain_Loss__c);
                            
                            if(objInv.Startup_Round__r.Exit_Unit_Price__c != null)
                                jsonGen.writeNumberField('exit_unit_price',objInv.Startup_Round__r.Exit_Unit_Price__c);
                            
                            jsonGen.writeBooleanField('startup_converted',objInv.Startup_Round__r.Startup_Converted__c);
                            
                            System.debug('objInv.Startup_Round__r.Startup_Converted__c>>>>>' + objInv.Startup_Round__r.Startup_Converted__c);
                            System.debug('ObjInv.Type__c == ' + ObjInv.Type__c);
                            if(objInv.Startup_Round__r.Startup_Converted__c && ObjInv.Type__c == 'Invested')
                            {
                                
                               if(ObjInv.Date_of_Allotment__c != null)
	                                jsonGen.writeDateField('date_of_allotment',objInv.Date_of_Allotment__c);
                                else if(ObjInv.Startup_Round__r.Date_of_sending_out_call_for_money__c != null)
                                    jsonGen.writeDateField('date_of_allotment',ObjInv.Startup_Round__r.Date_of_sending_out_call_for_money__c);
                                else if(ObjInv.Startup_Round__r.Date_of_sending_out_call_for_money_AIF__c != null)
                                    jsonGen.writeDateField('date_of_allotment',ObjInv.Startup_Round__r.Date_of_sending_out_call_for_money_AIF__c);
                                
                                if(objInv.Startup_Round__r.Old_Type_of_Shares__c != null)
                                    jsonGen.writeStringField('old_type_of_shares',objInv.Startup_Round__r.Old_Type_of_Shares__c);
                                else
                                    jsonGen.writeNullField('old_type_of_shares');
                                
                                if(objInv.Startup_Round__r.Old_Issue_Price__c != null)
                                    jsonGen.writeNumberField('old_issue_price',objInv.Startup_Round__r.Old_Issue_Price__c);
                                else
                                    jsonGen.writeNumberField('old_issue_price',0);
                                
                                if(objInv.Old_Date_of_Gross_Gain_Loss_starts__c!=null)
                                    jsonGen.writeDateField('old_date_of_gross_gain_loss_starts',objInv.Old_Date_of_Gross_Gain_Loss_starts__c);
                                else
                                    jsonGen.writeNullField('old_date_of_gross_gain_loss_starts');
                                
                                if(objInv.Old_Date_of_Allotment__c !=null)
                                    jsonGen.writeDateField('old_date_of_allotment',objInv.Old_Date_of_Allotment__c);
                                else
                                    jsonGen.writeNullField('old_date_of_allotment');
                                
                                if(objInv.Old_Number_of_Shares__c != null)
                                    jsonGen.writeNumberField('old_number_of_shares',objInv.Old_Number_of_Shares__c);
                                else
                                    jsonGen.writeNumberField('old_number_of_shares',0);
                                
                                if(objInv.Old_Number_of_Units_Held__c != null)
                                    jsonGen.writeNumberField('old_number_of_units_held',objInv.Old_Number_of_Units_Held__c);
                                else
                                    jsonGen.writeNumberField('old_number_of_units_held',0);
                                
                                if(objInv.Old_Investment_Amount_Remitted__c != null)
                                    jsonGen.writeNumberField('old_investment_amount_remitted',objInv.Old_Investment_Amount_Remitted__c);
                                else
                                    jsonGen.writeNumberField('old_investment_amount_remitted',0);
                            //condition Changed by Bharat and added investment Type exit , partial exit and internal transfer for Portfolio syncing requirement on 25-02-2025 
                            //condition Changed by Bharat and Removed objInv.Date_of_Allotment__c == null FROM Condition As Discussed With Mauli mam on 17-03-2025
                            }else if(objInv.Startup_Round__r.Startup_Converted__c == false && (ObjInv.Type__c == 'Invested' || ObjInv.Type__c == 'Internal Transfers' || ObjInv.Type__c == 'Exit' || ObjInv.Type__c == 'Partial Exit') && (ObjInv.Startup_Round__r.Type_And_Class_Of_Shares__c == 'Safe Notes' || ObjInv.Startup_Round__r.Type_And_Class_Of_Shares__c == 'Other' || ObjInv.Startup_Round__r.Type_And_Class_Of_Shares__c == 'Convertible Notes'))
                             {
                                 //added by bharat as discussed with mauli mam on 17-03-2025 
                                 if(objInv.Date_of_Allotment__c != null){
                                     jsonGen.writeDateField('date_of_allotment',objInv.Date_of_Allotment__c);
                                 }else{
                                     jsonGen.writenullfield('date_of_allotment');
                                 }
                                 /*
                                 if(ObjInv.Startup_Round__r.Date_of_sending_out_call_for_money__c != null)
                                     jsonGen.writeDateField('date_of_allotment',ObjInv.Startup_Round__r.Date_of_sending_out_call_for_money__c);
                                 else if(ObjInv.Startup_Round__r.Date_of_sending_out_call_for_money_AIF__c != null)
                                     jsonGen.writeDateField('date_of_allotment',ObjInv.Startup_Round__r.Date_of_sending_out_call_for_money_AIF__c);
								*/
                             }
                                        
                            if(objInv.Startup_Round__r.Startup_Converted__c == false && ObjInv.Type__c == 'Invested' && objInv.Date_of_Allotment__c == null && (ObjInv.Startup_Round__r.Type_And_Class_Of_Shares__c == 'CCPS' || ObjInv.Startup_Round__r.Type_And_Class_Of_Shares__c == 'CCD' || ObjInv.Startup_Round__r.Type_And_Class_Of_Shares__c == 'Equity Shares' || ObjInv.Startup_Round__r.Type_And_Class_Of_Shares__c == null))
                            {
                                jsonGen.writeBooleanField('hide_data',true);
                            }
                            else
                            {
                                jsonGen.writeBooleanField('hide_data',false);                                
                            }
                            
                            //  Added By Sahil As Dissussed With Ashish on 21.03.2024
                            if(objInv.Considered_Investment_Amount__c!=null){
                                jsonGen.writeNumberField('consideration_amount',objInv.Considered_Investment_Amount__c);
                            }else{
                                //added by bharat for null handling for app syncing on 21-11-2024
                                jsonGen.writeNumberField('consideration_amount',0);
                            }
                            
                            if(objInv.Startup_Round__r.Date_Of_Exit__c!=null){
                                jsonGen.writeDateField('exit_date',objInv.Startup_Round__r.Date_Of_Exit__c);     
                            }else{
                                jsonGen.writenullfield('exit_date');
                            }
                            
                            if(String.IsNotBlank(objInv.Investor_Name__c)){
                                jsonGen.writeStringField('investor_Name',objInv.Investor_Name__c);
                            }
                                
                            //Added by Karan investor GPOA_Taken__c field datatype changed from checkbox to picklist.
                            if(String.IsNotBlank(objInv.GPOA_Taken__c))
                                jsonGen.writeStringField('GPOA_Hard_Copy_Signed',objInv.GPOA_Taken__c);                          
                            
                            if(objInv.Investment_Fee_Received__c!=null){
                                jsonGen.writeNumberField('Investment_Fee_Paid',objInv.Investment_Fee_Received__c);
                            }
                                  
                            //if(objInv.Investment_Fee_Received__c!=null)
                                jsonGen.writeNumberField('Investment_Fee_Balance',objInv.Investment_Fee_Balance__c);
                            
                            //Added by Ankush as discussed with Shubham (ParentInv Shares,IRR Realised & unrealised,inv Amt balance). 
                            if(ObjInv.Parent_Investment__r.Number_Of_Shares__c != null && objInv.Startup_Round__r.Round_type__c !='Raise' && ObjInv.Type__c =='Exit')
                            { 
                                jsonGen.writeNumberField('no_of_share',ObjInv.Parent_Investment__r.Number_of_shares__c);
                            }else if(objInv.Number_Of_Shares__c != null &&
                                   (objInv.Startup_Round__r.Round_type__c =='Raise' || ObjInv.Startup_Round__r.Round_type__c =='Internal Transfers') &&
                                   (ObjInv.Type__c =='Invested' || ObjInv.Type__c =='Committed' || ObjInv.type__C =='Partial Exit' ||ObjInv.type__c =='Internal Transfers' || objInv.Type__c == 'Exit'))
                            {
                                jsonGen.writeNumberField('no_of_share',objInv.Number_Of_Shares__c);
                            //Added by bharat for sending shaddow investment details on 20-01-2025
                            }else if(objInv.Number_Of_Shares__c != null && ObjInv.Type__c == 'Invested - Shadow'){
                                jsonGen.writeNumberField('no_of_share',objInv.Number_Of_Shares__c);
                            }else{
                                jsonGen.writeNumberField('no_of_share', 0);  
                            }
                            
                            if(objInv.IRR_Value__c != null)
                                jsonGen.writeNumberField('realised_irr',objInv.IRR_Value__c);
                            else
                                jsonGen.writeNumberField('realised_irr',0);
                            
                            if(objInv.IRR_Percentage_Formula__c !=null)
                                jsonGen.writeNumberField('unrealised_irr',objInv.IRR_Percentage_Formula__c);
                            else
                                jsonGen.writeNumberField('unrealised_irr',0);
                                
                            if(objInv.Investment_Amount_Balance__c != null)
                                jsonGen.writeNumberField('investment_amount_balance',ObjInv.Investment_Amount_Balance__c);
                           
                            if(ObjInv.Startup_Round__r.Date_of_sending_out_call_for_money__c != null)
                            {
                                if(objInv.Funds_Cheque__c)
                                    jsonGen.writeStringField('Investment_Amount_Status','Received');
                                else
                                    jsonGen.writeStringField('Investment_Amount_Status','Not Received');
                            }
                            if(objInv.SAF_soft_copy__c)
                                jsonGen.writeStringField('SAF_Received','Received');
                            else
                                jsonGen.writeStringField('SAF_Received','Not Received');
                            
                            if(objInv.PAS_4_Investor_Bankdetails_soft_copy__c)
                                jsonGen.writeStringField('PAS_4','Received');
                            else
                                jsonGen.writeStringField('PAS_4','Not Received');
                            
                            if(objInv.Date_of_transaction__c!=null) 
                                jsonGen.writeDateField('date_of_transaction',objInv.Date_of_transaction__c);
                             
                            if(objInv.Repatriation__c!=null && (objInv.Residential_Status__c !='NRI' || objInv.Residential_Status__c !='OCI Holder') && objInv.Startup_Round__r.Date_of_sending_out_call_for_money__c != null)
                                jsonGen.writeStringField('Repatriation',objInv.Repatriation__c);
                            //else
                               //jsonGen.writeStringField('Repatriation','');

                            if(objInv.Fund_Transfer_From__c!=null&& (objInv.Residential_Status__c !='NRI' || objInv.Residential_Status__c !='OCI Holder') && objInv.Startup_Round__r.Date_of_sending_out_call_for_money__c != null)
                                jsonGen.writeStringField('Fund_Transfer_From',objInv.Fund_Transfer_From__c);
                            //else
                                //jsonGen.writeStringField('Fund_Transfer_From','');
                          
                            if(!String.isBlank(objInv.Investor_s_PAN__c)){
                                jsonGen.writeStringField('Investor_PAN',objInv.Investor_s_PAN__c);
                            }else{
                                jsonGen.writenullfield('Investor_PAN');
                            }
                            
                            /*if(objInv.Startup_Round__r.Expected_CFM_Date__c!=null) 
                                jsonGen.writeDateField('Expected_CFM_Date',objInv.Startup_Round__r.Expected_CFM_Date__c);*/
                            
                            jsonGen.writeBooleanField('Transfer_Deed_soft_copy',objInv.Transfer_Deed_soft_copy__c); 
                            jsonGen.writeBooleanField('Transfer_Deed_hard_copy',objInv.Transfer_Deed_hard_copy__c); 
                            jsonGen.writeBooleanField('Deed_of_Adherence_soft_copy',objInv.Deed_of_Adherence_soft_copy__c); 
                            jsonGen.writeBooleanField('Deed_of_Adherence_hard_copy',objInv.Deed_of_Adherence_hard_copy__c); 
                            jsonGen.writeBooleanField('DOA_stamp_paper_soft_copy',objInv.DOA_stamp_paper_soft_copy__c);
                            // Added hard Copy by ankush 24.5.23.
                            jsonGen.writeBooleanField('DOA_stamp_paper_hard_copy',objInv.DOA_stamp_paper_hard_copy__c);
                            jsonGen.writeBooleanField('FIRC',objInv.FIRC__c); 
                            jsonGen.writeBooleanField('Get_Physical_Certificate',objInv.Get_Physical_Certificate__c);

                            if(!String.isBlank(objInv.Residential_Status__c))
                                jsonGen.writeStringField('residential_status', objInv.Residential_Status__c);
                            
                            if(!String.isBlank(objInv.Share_Certificates_Scan_Sent__c))
                                jsonGen.writeStringField('Share_Certificates_Scan_Sent',objInv.Share_Certificates_Scan_Sent__c); 
                            if(!String.isBlank(objInv.Share_Certificates_Delivered_Hard_Copy__c))
                                jsonGen.writeStringField('Share_Certificates_Delivered_Hard_Copy',objInv.Share_Certificates_Delivered_Hard_Copy__c);
                            if(!String.isBlank(objInv.Share_Certificates_Tracking_ID__c))
                                jsonGen.writeStringField('Share_Certificates_Tracking_ID',objInv.Share_Certificates_Tracking_ID__c);
                            
                            // Added by ankush for IPV Investment_Portfolio 17.5.23
                              if(ObjInv.Date_of_Allotment__c != null)
                                jsonGen.writeDateField('date_of_allotment',objInv.Date_of_Allotment__c); 
                             
                            //Commented by ankush 17.5.23. as disscussed with yash (date_of_allotment,date_of_transaction).                        
                           /* if(ObjInv.Date_of_Allotment__c != null && ObjInv.Investor_Type__c !='Via AIF' && (ObjInv.Issue_Type__c !='Primary - AIF' || objInv.Issue_Type__c !='Friends & Family T1 - AIF' || objInv.Issue_Type__c !='Friends & Family T2 - AIF'))
                            {
                                jsonGen.writeDateField('date_of_allotment',objInv.Date_of_Allotment__c); 
                            
                                //if(objInv.Issue_Type__c == 'Secondary')
                                    //jsonGen.writeDateField('date_of_transaction',objInv.Date_of_Allotment__c);
                            }
                            
                            //if(objInv.Total_Investments__c != null) jsonGen.writeNumberField('total_investment_amount',objInv.Total_Investments__c); */
                            if(objInv.Investor_Type__c != null){
                                jsonGen.writeStringField('crm_investment_type',''+objInv.Investor_Type__c); 
                            }else{
                                jsonGen.writenullfield('crm_investment_type');
                            }
                            jsonGen.writeNumberField('sub_investment_type',getSubTypeEnum(objInv.Investor_Type__c)); 
                            /*
                            if(objInv.AIF_Contributor__c)
                                jsonGen.writeNumberField('aif_contribution',objInv.Investor__r.Amount_Agreed_for_Contribution__c); 
                            else
                                jsonGen.writeNumberField('aif_contribution',0); 
                            */
                            if(objInv.Issue_Type__c != null) 
                                jsonGen.writeNumberField('issue_type',getIssueTypeNum(objInv.Issue_Type__c)); 
                            
                            // Added Investment Amount for Type Exit and Invested
                            // added more OR condition by bharat as per disscussed with vishal bhai on 16-04-24 for capital gain and loss
                            //Codition modified by bharat and added Investment Type :-Invested - Shadow on 20-01-2025
                            if(objInv.Type__c == 'Invested' || objInv.Type__c == 'Exit' || objInv.Type__c == 'Partial Exit' || objInv.Type__c == 'Internal Transfers' || objInv.Type__c == 'Back out approved' || objInv.Type__c == 'Back out unapproved' || objInv.Type__c == 'Pending confirmation' || objInv.Type__c == 'Invested - Shadow')
                            {
                                //jsonGen.writeNumberField('record_type',1);
                                
                                if(objInv.Investment_Amount__c!=null)
                                    jsonGen.writeNumberField('total_investment_amount',objInv.Investment_Amount__c); 
                                else
                                    jsonGen.writeNumberField('total_investment_amount',0); 
                            }
                            else if(objInv.Type__c == 'Committed')
                            {
                                if(objInv.Final_Commitment_Amount__c!=null)
                                    jsonGen.writeNumberField('total_investment_amount',objInv.Final_Commitment_Amount__c);
                                else
                                    jsonGen.writeNumberField('total_investment_amount',0);
                            }
                            
                            if(objInv.Type__c == 'Invested' || objInv.Type__c == 'Invested - Shadow')
                            {
                                jsonGen.writeNumberField('record_type',1);
                            }
                            else if(objInv.Type__c == 'Exit')
                            {
                                jsonGen.writeNumberField('record_type',4);
    
                                if(objInv.Exit_amount_to_be_transferred__c != null)
                                    jsonGen.writeNumberField('exit_amount_to_be_transferred',objInv.Exit_amount_to_be_transferred__c); 
                                else
                                    jsonGen.writeNumberField('exit_amount_to_be_transferred',0); 
                            }
                            //  Added By Sahil As Disscussed With Ashish on 22.03.2024
                            else if(objInv.Type__c == 'Back out approved')
                            {
                                jsonGen.writeNumberField('record_type',2);
                            }
                            else if(objInv.Type__c == 'Back out unapproved')
                            {
                                jsonGen.writeNumberField('record_type',7);
                            }
                            else if(objInv.Type__c == 'Waitlist')
                            {
                                jsonGen.writeNumberField('record_type',8);
                            }
                            else if(objInv.Type__c == 'Round Closed - Commitment Released')
                            {
                                jsonGen.writeNumberField('record_type',9);
                            }
                            else if(objInv.Type__c == 'Round closed - deal dropped')
                            {
                                jsonGen.writeNumberField('record_type',10);
                            }
                            else if (objInv.Type__c =='Partial Exit')
                            {
                                system.debug('Inv Type>>>>>>>>' +objInv.Type__c);
                                jsonGen.writeNumberField('record_type',5); 
                            }
                            else if (ObjInv.Type__c == 'Internal Transfers')
                            {
                                 jsonGen.writeNumberField('record_type',6);
                            }
                            else 
                            {                               
                               if(objInv.Type__c == 'Committed')
                                   jsonGen.writeNumberField('record_type',0); 
                               else if(objInv.Type__c == 'Pending confirmation' || objInv.Type__c == 'Incomplete Data')
                                   jsonGen.writeNumberField('record_type',3);
                            }
                            
                            // Capital gain and loss 6.3.24 
                            if(objInv.STCG_STCL_Amount__c != null){
                               jsonGen.writeNumberField('stcg_stcl_amount',objInv.STCG_STCL_Amount__c); 
                            }
                            
                            if(objInv.LTCG_LTCL_Amount__c != null){
                               jsonGen.writeNumberField('ltcg_ltcl_amount',objInv.LTCG_LTCL_Amount__c); 
                            }
                            
                            if(objInv.Current_Value__c != null){
                               jsonGen.writeNumberField('current_value',objInv.Current_Value__c); 
                            }
                            
                            if(objInv.Investment_AIF_Class__c != null){
                               jsonGen.writestringfield('unit_of_class',objInv.Investment_AIF_Class__c); 
                            }
                            else
                            {
                                jsonGen.writenullfield('unit_of_class');
                            }
                            
                            if(objInv.Exit_Fee_received__c != null){
                               jsonGen.writeNumberField('exit_fees',objInv.Exit_Fee_received__c); 
                            }else{
                                jsonGen.writeNumberField('exit_fees',0);
                            }
                            
                           // Added by ankush for partial exit and internal trasfer record type 
                           /*else if (objInv.Type__c =='Partial Exit')
                            {
                                system.debug('Inv Type>>>>>>>>' +objInv.Type__c);
                                jsonGen.writeNumberField('record_type',5); 
                            }
                            
                            else if (ObjInv.Type__c == 'Internal Transfers')
                            {
                                 jsonGen.writeNumberField('record_type',6);
                            }
                            
                           else {
                               if(objInv.Type__c == 'Back out approved' || objInv.Type__c == 'Back out unapproved' || objInv.Type__c == 'Waitlist' )
                                   jsonGen.writeNumberField('record_type',2); 
                               else
                               
                               if(objInv.Type__c == 'Committed')
                                   jsonGen.writeNumberField('record_type',0); 
                               else if(objInv.Type__c == 'Pending confirmation' || objInv.Type__c == 'Incomplete Data')
                                   jsonGen.writeNumberField('record_type',3);
                                
                               if(objInv.Final_Commitment_Amount__c!=null)
                                   jsonGen.writeNumberField('total_investment_amount',objInv.Final_Commitment_Amount__c); 
                               else
                                   jsonGen.writeNumberField('total_investment_amount',0); 
                            }*/
                            String invDisplay = ''+objInv.Type__c;
                            
                            if(objInv.Type__c!=null && objInv.Investment_in_Own_Name_Family_Member__c !=null && objInv.Investor_Type__c!=null)
                                invDisplay = getInvTypeDisplayName(objInv.Type__c,objInv.Investment_in_Own_Name_Family_Member__c,objInv.Investor_Type__c);
                            
                            system.debug('invDisplay>>>>>>'+invDisplay);
                            jsonGen.writeStringField('investment_type_to_display',invDisplay); 
                            //jsonGen.writeNumberField('investment_type_id',5); 

                            if(objInv.Number_of_Units_Held__c != null){
                                jsonGen.writeNumberField('no_of_units',objInv.Number_of_Units_Held__c);
                            }else {
                                jsonGen.writeNumberField('no_of_units',0);
                            }
                            
                            if(objInv.Startup_Round__r.Unit_Price__c!= null) jsonGen.writeNumberField('price_per_unit',objInv.Startup_Round__r.Unit_Price__c);
                            //jsonGen.writeNumberField('price_per_unit',57);
                            
                            // added by bharat for capital gain and loss on 16-04-2024 
                            if(objInv.Exit_amount_transferred__c != null){
                               jsonGen.writeNumberField('exit_amount_transferred',objInv.Exit_amount_transferred__c); 
                            }
                            else
                            {
                                jsonGen.writeNullField('exit_amount_transferred');
                            }
                            if(ObjInv.Parent_Investment__c != null){
                              jsonGen.writeStringField('parent_investment_id',ObjInv.Parent_Investment__c); 
                            }
                            
                            jsonGen.writeEndObject();
                            
                        }
                        jsonGen.writeEndArray();                    
                    }
                    jsonGen.writeEndObject();
                }
                
                jsonGen.writeEndArray();
                jsonGen.writeEndObject();
                jsonData = jsonGen.getAsString();
                System.debug('Json Data - ' + jsonData);
                
                String jsonPayload = JSON.serialize(jsonData);
                Blob jsonBlob = Blob.valueOf(jsonPayload);
                Integer sizeInBytes = jsonBlob.size();
                System.debug('JSON payload size in bytes: ' + sizeInBytes);
                
                Decimal sizeInKB = sizeInBytes / 1024.0;
                System.debug('JSON payload size in KB: ' + sizeInKB);
                
                Decimal sizeInMB = sizeInBytes / (1024.0 * 1024.0);
                System.debug('JSON payload size in MB: ' + sizeInMB);
            }
            
             
            string accessToken;
            if(!Test.isRunningTest())
                accessToken = restLoginController.loginExternalSystem();
            else
                accessToken = restLoginController.loginExternalSystem();
            
            system.debug('accessToken>>>>'+accessToken );
                
            if(accessToken != null){
                //String enddURL = 'http://ec2-15-206-100-11.ap-south-1.compute.amazonaws.com/';

                String enddURL = endURLSetting;
                //if(isInsert)
                //    enddURL += '/startups/startupInvestment/addBulkStartupInvestment'; 
                //else
                //    enddURL += '/startups/startupInvestment/updateBulkStartupInvestment';
                    
                enddURL += '/startups/startupInvestment/add-bulk-investment';
                system.debug('endURL --->'+enddURL);
                HttpRequest request = new HttpRequest();
                request.setEndpoint(enddURL);
                request.setHeader('Authorization','Bearer ' + accessToken);
                request.setHeader('Content-Type','application/json');
                //if(isInsert)
                    request.setMethod('POST');
                //else
                //    request.setMethod('PUT');
                request.setTimeout(120000);
                request.setBody(jsonData);
                Http http1 = new Http();
                
                HTTPResponse res1 = http1.send(request);
                
                System.debug(res1.getStatusCode());
                System.debug('res--add-'+res1.getBody());
                System.debug('STATUS:'+res1.getStatus());
                System.debug('Got The Response At : '+ DateTime.now());

                statusCode = res1.getStatusCode();
                
            }
            
            
        } catch(Exception ex){
            system.debug('Error '+ex);
            Error_Log__c log =  new Error_Log__c();
            log.Class_Name__c = CLASS_NAME;
            log.method__c = 'sendInvestmentDetails' ;
            log.Error_Message__c = ex.getMessage();
            insert log;

            statusCode = 1;
        }
    }
    
    public static String getInvTypeDisplayName(String invStatusType,String invBy,String invType)
    {
        String retStr = '';
        invType = invType.substringAfter(' ');
        if(invType=='Platform')
            invType = 'Direct';
            
        if(invStatusType =='Invested' || invStatusType =='Committed' )
        {
            if(invBy == 'Own Name')
                retStr = invType+' - Self';
            else if(invBy == 'Family Member')
                retStr = invType+' - Family';
            else if(invBy == 'Joint A/c' || invBy == 'Joint A/c outside')
                retStr = invType+' - Joint A/c';    
            else
                retStr = invType+' - '+invBy;       
        }
        else if(invStatusType =='Pending confirmation' || invStatusType =='Incomplete Data')
        {
            retStr = 'Pending confirmation';
        }
        else
        {
            retStr = invStatusType;
        }
        return retStr;
    }
    
    public static Integer getSubTypeEnum(String invType)
    {
        Integer retVal = 0;
        if(invType =='Via Platform')
            retVal = 1;
        else if(invType =='Via LLP')
            retVal = 2;
        else if(invType =='Via AIF')
            retVal = 4;
        else //For now we do not have clarification for safe instrument kind of investment. 
            retVal = 4;
        
        return retVal;
    }
    
    public static Integer getIssueTypeNum(String issueType)
    {
        Integer retVal = 0;
        if(issueType=='Primary')
            retVal = 1;
        else if(issueType=='Friends & Family T1' || issueType=='Friends & Family T2')
            retVal = 2;
        else if(issueType=='Primary - AIF')
            retVal = 3;
        else if(issueType=='Friends & Family T1 - AIF' || issueType=='Friends & Family T2 - AIF')
            retVal = 4;
        
        return retVal;
    }   
}