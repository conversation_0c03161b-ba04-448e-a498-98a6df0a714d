import { LightningElement, wire, api, track } from 'lwc';
import { getRecord } from 'lightning/uiRecordApi';
import Membership_Slab_History__c from '@salesforce/schema/Account.Membership_Slab_History__c';

export default class MembershipSlabHistoryTable extends LightningElement {

    @api recordId;
    @track parsedData = [];

    @track columns = [
        { label: 'Previous Membership Slab', fieldName: 'oldSlab', type: 'text' },
        { label: 'New Membership Slab', fieldName: 'newSlab', type: 'text' },
        { label: 'Date of Slab Updation(DD-MM-YYYY)', fieldName: 'newUpdateDate', type: 'text', sortable: true },
        { label: 'Membership Slab Validity Up to(DD-MM-YYYY)', fieldName: 'newValidity', type: 'text', sortable: true }
    ];

    connectedCallback() {
        console.log('recordId ===> : ', this.recordId);
    }

    @wire(getRecord, { recordId: '$recordId', fields: [Membership_Slab_History__c] })
    wiredAccount({ error, data }) {
        if (data) {
            try {
                const historyData = data.fields.Membership_Slab_History__c.value;
                console.log('Field Change History:', historyData);
                const jsonArray = historyData.match(/\{[^{}]+\}/g);

                if (jsonArray) {
                    this.parsedData = jsonArray.map((jsonString, index) => {
                        const parsedObject = JSON.parse(jsonString);
                        return {
                            id: index,
                            ...parsedObject,
                            newUpdateDate: this.formatDate(parsedObject.newUpdateDate),
                            newValidity: this.formatDate(parsedObject.newValidity)
                        };
                    });
                    console.log('Parsed Data:', this.parsedData);
                } else {
                    console.log('No JSON objects found in Membership_Slab_History__c');
                }
            } catch (error) {
                console.log('Error parsing Membership_Slab_History__c:', error);
            }
        } else if (error) {
            console.log('Error fetching account history:', error);
        }
    }

    formatDate(dateString) {
        if (!dateString) {
            return '';
        }
        const parts = dateString.split('/');
        if (parts.length !== 3) {
            console.error('Invalid date format:', dateString);
            return '';
        }
        const day = parts[0];
        const month = parts[1];
        const year = parts[2];
        return `${day}-${month}-${year}`;
    }

    handleSort(event) {
        const { fieldName: sortedBy, sortDirection } = event.detail;
        const cloneData = [...this.parsedData];

        cloneData.sort((a, b) => {
            let aValue = a[sortedBy];
            let bValue = b[sortedBy];

            if (sortedBy === 'newUpdateDate' || sortedBy === 'newValidity') {
                aValue = aValue ? new Date(aValue) : null;
                bValue = bValue ? new Date(bValue) : null;
            }

            if (aValue === null && bValue !== null) {
                return sortDirection === 'asc' ? -1 : 1;
            } else if (aValue !== null && bValue === null) {
                return sortDirection === 'asc' ? 1 : -1;
            } else if (aValue === null && bValue === null) {
                return 0;
            }

            let comparison = aValue > bValue ? 1 : -1;
            return sortDirection === 'asc' ? comparison : -comparison;
        });

        this.parsedData = cloneData;
        this.sortBy = sortedBy;
        this.sortDirection = sortDirection;
    }

}