// Use .apex files to store anonymous Apex.
// You can execute anonymous Apex in VS Code by selecting the
//     apex text and running the command:
//     SFDX: Execute Anonymous Apex with Currently Selected Text
// You can also execute the entire file by running the command:
//     SFDX: Execute Anonymous Apex with Editor Contents

/*

List<Contribution_Agreement__c> caList = [SELECT Id , Fund_Onboarded_on__c, Fund_Specific_CA_Number__c FROM Contribution_Agreement__c WHERE Id = 'a0P0l0000082E90EAE'];

assignFundSpecificNumbers(caList);


public static void assignFundSpecificNumbers(List<Contribution_Agreement__c> newCAs) {
        // Filter only those with Fund_Onboarded_on__c
        Map<Id, Contribution_Agreement__c> caWithFundMap = new Map<Id, Contribution_Agreement__c>();
        Set<Id> fundModuleIds = new Set<Id>();
        
        for (Contribution_Agreement__c ca : newCAs) {
            if (ca.Fund_Onboarded_on__c != null && ca.Fund_Specific_CA_Number__c == null) {
                caWithFundMap.put(ca.Id, ca);
                fundModuleIds.add(ca.Fund_Onboarded_on__c);
            }
        }
        
        if (fundModuleIds.isEmpty()) return;
        
        // Step 1: Get Fund Identifiers for each Fund Module
        Map<Id, String> fundIdToIdentifierMap = new Map<Id, String>();
        for (Fund_Module__c fm : [
            SELECT Id, Fund_Identifier__c
            FROM Fund_Module__c
            WHERE Id IN :fundModuleIds
        ]) {
            if (String.isNotBlank(fm.Fund_Identifier__c)) {
                fundIdToIdentifierMap.put(fm.Id, fm.Fund_Identifier__c);
            }
        }
        
        // Step 2: Prepare CA grouped by Fund Identifier
        Map<String, List<Contribution_Agreement__c>> identifierToCAsMap = new Map<String, List<Contribution_Agreement__c>>();
        
        for (Contribution_Agreement__c ca : caWithFundMap.values()) {
            String identifier = fundIdToIdentifierMap.get(ca.Fund_Onboarded_on__c);
            if (identifier != null) {
                if (!identifierToCAsMap.containsKey(identifier)) {
                    identifierToCAsMap.put(identifier, new List<Contribution_Agreement__c>());
                }
                identifierToCAsMap.get(identifier).add(ca);
            }
        }
        
        // Step 3: For each identifier, calculate next available number
        // Collect all existing Fund_Specific_CA_Number__c for related identifiers
        Set<String> allIdentifiers = identifierToCAsMap.keySet();
        Map<String, Integer> identifierToMaxNumberMap = new Map<String, Integer>();
        
        // Extract numeric suffix using SUBSTRING/REGEX logic
        List<AggregateResult> results = [
            SELECT Fund_Onboarded_on__r.Fund_Identifier__c identifier,
            MAX(Fund_Specific_CA_Number__c) maxCA
            FROM Contribution_Agreement__c
            WHERE Fund_Onboarded_on__r.Fund_Identifier__c IN :allIdentifiers
            AND Fund_Specific_CA_Number__c != null
            GROUP BY Fund_Onboarded_on__r.Fund_Identifier__c
        ];
        
        for (AggregateResult ar : results) {
            String identifier = (String) ar.get('identifier');
            String maxCANum = (String) ar.get('maxCA');
            
            if (String.isNotBlank(maxCANum)) {
                // Extract numeric part
                String numPart = maxCANum.replaceAll('[^0-9]', '');
                Integer maxNum = Integer.valueOf(numPart);
                identifierToMaxNumberMap.put(identifier, maxNum);
            }
        }
        
        // Step 4: Assign next sequential number for each CA per identifier
        for (String identifier : identifierToCAsMap.keySet()) {
            List<Contribution_Agreement__c> cas = identifierToCAsMap.get(identifier);
            Integer nextNumber = identifierToMaxNumberMap.get(identifier) != null ?
                identifierToMaxNumberMap.get(identifier) + 1 : 1;
            
            for (Contribution_Agreement__c ca : cas) {
    			String paddedNumber = getPaddedNumber(nextNumber, 4);
				ca.Fund_Specific_CA_Number__c = identifier + paddedNumber;
				String valuee = identifier + paddedNumber;
				System.debug('Value ::: ' + valuee);
				nextNumber++;
			}
		}
    }


	private static String getPaddedNumber(Integer num, Integer totalLength) {
        String numStr = String.valueOf(num);
        Integer padCount = totalLength - numStr.length();
        if (padCount > 0) {
            return String.join(new List<String>{''.padLeft(padCount, '0'), numStr});
        }
        return numStr;
    }
*/