<apex:page tabStyle="Lead__c" standardController="Lead__c" recordSetVar="accs" extensions="ConvertLeadsToAccountsCtrl" action="{!convert}" lightningStylesheets="true">
    <apex:form >
        <apex:pageMessages id="showmsg"></apex:pageMessages>
        
        <div style="text-align: center">
            <apex:commandButton value="Cancel" action="{!cancelBtn}" immediate="true" html-formnovalidate="formnovalidate"/>
        </div>
        
        <!-- <apex:outputLabel >Converted Account List</apex:outputLabel> -->
        <div style="{!IF(targetObjectList.size>0, 'overflow: scroll;height: 200px;display:block;', 'display:none')}">
            <apex:pageBlock title="Converted Account List" rendered="{!targetObjectList.size>0}" >
                <apex:pageBlockTable value="{!targetObjectList}" var="item">
                    <apex:column headerValue="Account Name" title="Account Name">
                        <apex:outputLink value="/{!item['Id']}" target="contactDisplay" >
                            {!item['Name']}
                        </apex:outputLink>
                    </apex:column>
                    <!--<apex:column value="{!item['name']}"/> -->
                    <apex:column value="{!item['Membership_Status__c']}"/>
                </apex:pageBlockTable>
            </apex:pageBlock>
        </div>
        
    </apex:form>
</apex:page>