public class AccountTriggerHandler
{
 public void beforeInsert(List<Account> accList)
    {
        Map<String,set<Id>> emailToIdsMap = new Map<String,set<Id>>();
        id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        system.debug('recordTypeId>>>>'+recordTypeId);
        
        Map<ID, Schema.RecordTypeInfo> recordTypeMap = Schema.SObjectType.Account.getRecordTypeInfosById();
        
        // added by bharat for referral code generation on 06-05-2024
        integer count = 1;
        string Unique_Referral_Code = '';
        string firstpart = '' ;
        string lastPart = '';
        
        for(Account acc: accList)
        {
            if(acc.RecordTypeId!=null)
            {
                acc.Record_Type_Primary_Contact_Unique_Key__c = ''+recordTypeMap.get(acc.RecordTypeId).getDeveloperName() +'_'+acc.Primary_Contact__c;
            }
            
            //Added by <PERSON><PERSON><PERSON> As disscussed with Hemant 13-10-22.
            if(acc.RecordTypeId == recordTypeId)
            {
                //Update Gender Based on Title
                if(acc.Title__c == 'Mr')
                    acc.Gender__c = 'Male';
                else if(acc.Title__c == 'Ms' || acc.Title__c == 'Mrs')
                    acc.Gender__c = 'Female';
                else
                    acc.Gender__c = '';
                
                if(acc.Bot_Communication_Country_Code__c==null)
                    acc.Bot_Communication_Country_Code__c = acc.Primary_Country_Code__c;
                
                if(acc.Bot_Communication_Number__c==null || acc.Bot_Communication_Number__c=='')    
                    acc.Bot_Communication_Number__c = acc.Primary_Contact__c ;
                
                if(acc.Investor_WA_group_Country_Code__c==null)
                    acc.Investor_WA_group_Country_Code__c=  acc.Primary_Country_Code__c;
                
                if(acc.Investor_WA_group_Number__c==null || acc.Investor_WA_group_Number__c=='')    
                    acc.Investor_WA_group_Number__c = acc.Primary_Contact__c ;
                
                //Added By Sahil on 01/12/2023
                if(acc.App_City__c != null && acc.App_City__c !='')
                {
                    acc.App_City__c = acc.App_City__c.toLowerCase();    
                    if(isValidCity(acc.App_City__c))    
                    {
                        acc.City__c = acc.App_City__c;
                        // System.debug('I am Inside City >>>' + acc.City__c);
                    }
                }
                
                /* Added By Sahil on 26/03/2024 */
                if(acc.Facebook_Campaign_Name__c != null && acc.Facebook_Campaign_Name__c !='')
                {
                    if(isValidCampaign(acc.Facebook_Campaign_Name__c))
                    {
                        acc.Campaign__c = acc.Facebook_Campaign_Name__c;
                    }                    
                }
                
                system.debug('acc.Membership_Validity__c>>>'+acc.Membership_Validity__c);
                
                /* NOT NEEDED FOR NOW BECAUSE CHAITANYA HAS ASKED TO REMOVE
//Update Membership Validity based on membership status field
if((acc.Membership_Status__c=='On Trial' || acc.Membership_Status__c=='On Trial Community') && acc.Date_of_Addition__c!=null)
acc.Membership_Validity__c = acc.Date_of_Addition__c.AddDays(30);

if((acc.Membership_Status__c =='Paid IPV Fee' || acc.Membership_Status__c =='Paid Community') && acc.Membership_Validity__c!=null)
acc.Membership_Validity__c = acc.Membership_Validity__c.AddDays(365);
*/
                
                /* 
if(String.IsNotBlank(acc.Relationship_Manager_Contact__c) && acc.Relationship_Manager__c==null)  
{
relManagerNoMap.put(acc.Relationship_Manager_Contact__c,null); 
}
if(String.IsNotBlank(acc.Refer_By_Contact_No__c) && acc.ParentId==null)  
{
referByNoMap.put(acc.Refer_By_Contact_No__c,null); 
}
accountLeadMap.put(acc.Primary_Contact__c,False);

*/
                system.debug('acc.Personal_Email__c>>>'+acc.Personal_Email__c);
                //Map for duplicate notification. Make is duplicate flag true or false to get the notification
                if(acc.Personal_Email__c !=null && acc.Personal_Email__c !='')
                {
                    emailToIdsMap.put(acc.Personal_Email__c,new set<Id>());
                }
                
                // added by bharat for referral code generation on 06-05-2024
                
                if(String.isNotBlank(acc.Name) && acc.Name.length()>=3 ){
                    String memberName = acc.Name.replaceAll('[^a-zA-Z0-9\\s]', '');
                    Integer lastSpaceIndex = memberName.lastIndexOf(' ');
                    Integer firstSpaceIndex = memberName.IndexOf(' ');
                    System.debug('memberName.IndexOf>>>'+ memberName.IndexOf(' '));
                    
                    if(memberName.length() >= lastSpaceIndex + 4){
                        firstPart = memberName.subString(lastSpaceIndex+1 , lastSpaceIndex+4).toUpperCase() + '-';
                        System.debug('First>>>'+ firstPart);
                    }
                    else if((memberName.length() >= lastSpaceIndex + 4) && (memberName.subString(firstSpaceIndex+3 , firstSpaceIndex+4) != ' ') && (memberName.subString(firstSpaceIndex+2 , firstSpaceIndex+3) != ' ')){
                        firstPart = memberName.subString(firstSpaceIndex+1 , firstSpaceIndex+4).toUpperCase() + '-';
                        System.debug('NEW>>>'+ firstPart);
                    }
                    else if(memberName.IndexOf(' ') == 2 && memberName.length()==5){
                        firstPart = 'IPV-';
                        System.debug('Second>>>'+ firstPart);
                    }
                    else if(memberName.IndexOf('.') == 2 && memberName.length()<= 6){
                        firstPart = 'IPV-';
                        System.debug('Third>>>'+ firstPart);
                    }
                    else{
                        firstPart = memberName.subString(0 , 3).toUpperCase() + '-';
                        System.debug('Final>>>'+ firstPart);
                    }
                }
                else if(String.isNotBlank(acc.Name) && acc.Name.length()<3){
                    String memberName = acc.Name;
                    firstPart = 'IPV-';
                }
                
                if(acc.Primary_Contact__c != null && acc.Primary_Contact__c.length()>0) {
                    String primaryContact = String.valueOf(acc.Primary_Contact__c);
                    if(primaryContact.length() >= 5) {
                        lastPart = '-' +  primaryContact.substring(primaryContact.length()-5 , primaryContact.length()) ;
                        System.debug('primaryContact>>>'+lastPart);
                    }
                }
                
                Unique_Referral_Code = firstPart + '0' + String.valueOf(count) + lastPart ;
                String query = 'SELECT Id, Name, Unique_Referral_Code__c FROM Account WHERE Unique_Referral_Code__c LIKE \'' + firstPart + '%\' AND Unique_Referral_Code__c LIKE \'%' + lastPart + '\' ORDER BY Unique_Referral_Code__c DESC';
                List<Account> existAcc = Database.query(query);
                System.debug('asdf>>>>' + existAcc);
                
                if(existAcc != null && existAcc.size() > 0){
                    Integer index = 4; // Index of the character we want to extract, counting from 0
                    string latestReferCode = existAcc[0].Unique_Referral_Code__c ;
                    Integer oldCount = Integer.Valueof(latestReferCode.substring(index, index + 2));
                    Integer newCount = oldCount + 1 ;
                    
                    if(newCount <10 ){
                        Unique_Referral_Code = firstPart + '0' + String.valueOf(newCount) + lastPart ;
                    }else{
                        Unique_Referral_Code = firstPart + String.valueOf(newCount) + lastPart ;
                    }
                    System.debug('oldCount>>>>>>>>>>>>>>>.' + oldCount);
                    System.debug('newCount>>>>>>>>>>>>>.>' + newCount);
                }
                if(Unique_Referral_Code != null){
                    acc.Unique_Referral_Code__c = Unique_Referral_Code ;
                }
                system.debug('Unique_Referral_Code__c>>>>>>>>>>>>>>>>>>>>..' + Unique_Referral_Code);
            } 
        }
        system.debug('emailToIdsMap>>>'+emailToIdsMap);
        if(emailToIdsMap!=null && emailToIdsMap.size()>0)
            emailToIdsMap = checkForEmailDuplicate(emailToIdsMap);   
        
        updateReferenceFieldsCommon(accList,emailToIdsMap );
        
        if(accList.size() > 0)
        {
            updateSpoc(accList);
            validateRmCode(accList);
        }
    }
    public void afterInsert(List<Account> accList)
    {
        Id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        //Added by Ankush to send Acc info to ORAI
        Set<id> AccIdSet = New Set<id>();
        
        //Added by Bharat on 12-04-2024 For B2BuserOnboardingAPI 
        Set<Id> B2BaccIdSet = New Set<Id>();
        
      
        for(Account Acc: accList){
            if(Acc.Is_Converted_From_Lead__c == true && Acc.Lead_Source__c =='ORAI')
                AccIdSet.add(Acc.id);
            
            //  Added By Sahil on 01.04.2024 For Lead Quality Score & Lead Quality Score % Updation.
            if(Acc.Id != null && Acc.RecordTypeId == recordTypeId)
            {
                LeadQualityScoreAccountObjectController.getLeadQualityScoreAccountObject(Acc.Id);
            }
            
            // Added by Bharat on 12-04-2024 For B2BuserOnboardingAPI 
            if(Acc.Id != null && Acc.RecordTypeId == recordTypeId)
            {
                if(Acc.Partner_parent_account__c != null || Acc.Org_RM__c != null || Acc.B2B_User_Type__c != null || Acc.L1_manager__c != null || Acc.Tenant_SPOC_Account__c != null || Acc.ParentId != null )
                {
                    B2BaccIdSet.add(Acc.Id);
                }  if(Acc.ParentId != null)
                    B2BaccIdSet.add(Acc.Org_RM__c);
            }
           
            
            //added by vidhi for updating b2b fields
            if(accList.size() > 0)
            {
                updateFileds(accList);
            }
            
        }
        
        if(AccIdSet != null && AccIdSet.size() > 0)
        {
            System.debug('AccIDtosend>>>' +AccIdSet);
            AccountRestAPIController.UpdateAccountDetails(AccIdSet, False);
        }
        
        // Added by Bharat on 12-04-2024 For B2BuserOnboardingAPI
        if(B2BaccIdSet != null && B2BaccIdSet.size() > 0)
        {
            System.debug('B2BaccIdSet>>>>>>>>>>>>>>>>>' + B2BaccIdSet);
            B2BuserOnboardingAPI.sendAccountDetails(B2BaccIdSet , false);
        }
        createPointTransaction(accList , null);
        //added by Jay Dabhi for Referral Task Creation on 23/07/2025
        if(accList!=Null && accList.Size() > 0){
        ReferralTaskCreation.taskCreationOnAccountInsert(accList);
        }
        
    }
    public void beforeUpdate(List<Account> accList,Map<Id,Account> oldMap)
    {
        Map<String,set<Id>> emailToIdsMap = new Map<String,set<Id>>();
        Boolean updateReferenceField = false;
        Id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        system.debug('recordTypeId>>>>'+recordTypeId);
        Map<ID, Schema.RecordTypeInfo> recordTypeMap = Schema.SObjectType.Account.getRecordTypeInfosById();
        
        
        for(Account acc: accList)
        {  
            if(acc.RecordTypeId!=null)
            {
                acc.Record_Type_Primary_Contact_Unique_Key__c = ''+recordTypeMap.get(acc.RecordTypeId).getDeveloperName() +'_'+acc.Primary_Contact__c;
            }
            
            if(Acc.RecordTypeId ==recordTypeId)
            {
                System.debug('IPVRecordTypes >>>>' +recordTypeId );
                /*
if(String.IsNotBlank(acc.Relationship_Manager_Contact__c) && acc.Relationship_Manager__c==null)  
{
relManagerNoMap.put(acc.Relationship_Manager_Contact__c,null); 
}
if(String.IsNotBlank(acc.Refer_By_Contact_No__c) && acc.ParentId==null)  
{
referByNoMap.put(acc.Refer_By_Contact_No__c,null); 
}
accountLeadMap.put(acc.Primary_Contact__c,False);
*/
                
                /* Added By Sahil on 01/12/2023 */
                if(acc.App_City__c != null && acc.App_City__c !='')
                {
                    acc.App_City__c = acc.App_City__c.toLowerCase();
                    if(isValidCity(acc.App_City__c))
                    {
                        acc.City__c = acc.App_City__c;
                        System.debug('I Am Inside City >>>');
                    }                    
                }
                
                /* Added By Sahil on 26/03/2024 */
                if(acc.Facebook_Campaign_Name__c != null && acc.Facebook_Campaign_Name__c !='')
                {
                    if(isValidCampaign(acc.Facebook_Campaign_Name__c))
                    {
                        acc.Campaign__c = acc.Facebook_Campaign_Name__c;
                    }                    
                }
                
                if(acc.Bot_Communication_Country_Code__c==null)
                    acc.Bot_Communication_Country_Code__c = acc.Primary_Country_Code__c;
                
                if(acc.Bot_Communication_Number__c==null || acc.Bot_Communication_Number__c=='')    
                    acc.Bot_Communication_Number__c = acc.Primary_Contact__c ;
                
                if(acc.Investor_WA_group_Country_Code__c==null)
                    acc.Investor_WA_group_Country_Code__c=  acc.Primary_Country_Code__c;
                
                if(acc.Investor_WA_group_Number__c==null || acc.Investor_WA_group_Number__c=='')    
                    acc.Investor_WA_group_Number__c = acc.Primary_Contact__c ;
                
                if(acc.Personal_Email__c !=null && acc.Personal_Email__c !='' && acc.Personal_Email__c!=oldMap.get(acc.Id).Personal_Email__c)
                {
                    emailToIdsMap.put(acc.Personal_Email__c,new set<Id>());
                }
                
                if(acc.Primary_Contact__c!=oldMap.get(acc.Id).Primary_Contact__c)
                {
                    updateReferenceField = true;
                }
                
                if(acc.Refer_By_Contact_No__c!=oldMap.get(acc.Id).Refer_By_Contact_No__c)
                {
                    updateReferenceField = true;
                }
                
                if(acc.Relationship_Manager_Contact__c!=oldMap.get(acc.Id).Relationship_Manager_Contact__c)
                {
                    updateReferenceField = true;
                }
                
                if(acc.Relationship_Manager__c!=oldMap.get(acc.Id).Relationship_Manager__c)
                {
                    acc.OwnerId = acc.Relationship_Manager__c;
                }
                
            }
            
            if(emailToIdsMap!=null && emailToIdsMap.size()>0)
                emailToIdsMap = checkForEmailDuplicate(emailToIdsMap);
            
            system.debug('emailToIdsMap>>>'+emailToIdsMap);
            
            if(updateReferenceField || (emailToIdsMap!=null && emailToIdsMap.size()>0))
                updateReferenceFieldsCommon(accList,emailToIdsMap);
            //added by jay dabhi for Quarterky mail requirment
            if (Acc.Total_Referred__c > 0) {
               Acc.Quaterly_Mail_Catagory__c = 'Referral';
            }
            else {
                Acc.Quaterly_Mail_Catagory__c = 'Motivational';
            }
        }
        
        if(accList.size() > 0)
        {
            updateSlabHistoryField(accList , oldMap);
            updateSpoc(accList);
            validateRmCode(accList);
        }
    }
    public void afterUpdate(List<Account> accList,map<Id,Account>accMap)
    {
        //Added by Bharat on 12-04-2024 For B2BuserOnboardingAPI 
        Set<Id> B2BaccIdSet = New Set<Id>();
        //Added by Ankush to send Account info to ORAI
        Set<id> AccIdSet = New Set<id>();
        Id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        //  Added By Sahil on 20.05.2024 For Task Creation When Membership Status Get Changed  
        Map<Id, Account> oldAccountMap = (Map<Id, Account>) Trigger.oldMap;
        List<Task> tasksToCreate = new List<Task>();
        
        //added by bharat for secondary module requirement on 29-05-2025
        Set<Id> newRMaccountIds = new Set<Id>();
        Map<Id , Id> accountRMmap = new Map<Id,Id>();
        
        for(Account Acc: accList)
        {
            Account oldAcc = oldAccountMap.get(Acc.Id);
            if(Acc.Is_Converted_From_Lead__c == true && Acc.Lead_Source__c =='ORAI')
                AccIdSet.add(Acc.id);
            
            // Check if "Membership Status" field value has changed
            if (acc.Membership_Status__c != oldAcc.Membership_Status__c) {
                
                Set<String> targetStatuses = new Set<String>{'No Response 1', 'No Response 2', 'No Response 3', 'Call Scheduled'};
                    if (targetStatuses.contains(Acc.Membership_Status__c)) {
                        Task newTask = new Task();
                        newTask.WhatId = Acc.Id; 
                        newTask.OwnerId = UserInfo.getUserId(); 
                        newTask.Subject = 'Call';
                        newTask.Status = 'Completed';
                        newTask.Outcome__c = 'Call Done';
                        newTask.Description = 'Membership Status changed from ' + oldAcc.Membership_Status__c + ' to ' + Acc.Membership_Status__c;
                        newTask.ActivityDate = Date.today();
                        tasksToCreate.add(newTask);
                    }
            }
            
            // Check if "Payment Follow Up Option" field value has changed.
         /*   if(oldAcc.P__c == null && acc.P__c != null)
            {
                Task newTask = new Task();
                newTask.WhatId = Acc.Id; 
                newTask.OwnerId = UserInfo.getUserId(); 
                newTask.Subject = 'Follow up Call';
                newTask.Status = 'Completed';
                newTask.Outcome__c = 'Call Done';
                newTask.Description = 'New Payment Follow Up Option : ' + Acc.P__c;
                newTask.ActivityDate = Date.today();
                tasksToCreate.add(newTask);
            }
            else if (acc.P__c != oldAcc.P__c) {
                Task newTask = new Task();
                newTask.WhatId = Acc.Id; 
                newTask.OwnerId = UserInfo.getUserId(); 
                newTask.Subject = 'Follow up Call';
                newTask.Status = 'Completed';
                newTask.Outcome__c = 'Call Done';
                newTask.Description = 'Payment Follow Up Option Changed From ' + oldAcc.P__c + ' to ' + Acc.P__c;
                newTask.ActivityDate = Date.today();
                tasksToCreate.add(newTask);
            }
            */
            // Added by Bharat on 12-04-2024 For B2BuserOnboardingAPI 
            if(Acc.Id != null && Acc.RecordTypeId == recordTypeId)
            {
                if(Acc.Partner_parent_account__c != null || Acc.Org_RM__c != null || Acc.B2B_User_Type__c != null || Acc.L1_manager__c != null || Acc.ParentId != null)
                {
                    B2BaccIdSet.add(Acc.Id);
                }
                if(Acc.ParentId != null)
                    B2BaccIdSet.add(Acc.Org_RM__c);
            }
            
            //added by bharat for secondary module requirement on 29-05-2025
            if(Acc.Relationship_Manager__c != oldAcc.Relationship_Manager__c){
                newRMaccountIds.add(Acc.Id);
                accountRMmap.put(Acc.Id  , Acc.Relationship_Manager__c);
            }
            
            //added by vidhi for updating b2b fields
            if(accList.size() > 0)
            {
                updateFileds(accList);
            }
            
        }
        System.debug('newRMaccountIds>>>>>>' + newRMaccountIds);
        System.debug('accountRMmap>>>>>>>>>' + accountRMmap);
        
        //  Creating Tasks 
        if (!tasksToCreate.isEmpty()) 
        {
            try 
            {
                insert tasksToCreate;
            }catch (DmlException e) 
            {
                System.debug('Error While Creating Tasks: ' + e.getMessage());
            }
        }
        
        if(AccIdSet != null && AccIdSet.size() > 0)
        {
            
            if(system.isBatch()){
                
                System.debug('Returning from Account AfterUpdate method becasue its updating from batch>>>>>>>' + system.isBatch());
                return;  
            }    
            else
                System.debug('AccIDtosend>>>' +AccIdSet);
            AccountRestAPIController.UpdateAccountDetails(AccIdSet, False);
        }
        
        // Added by Bharat on 12-04-2024 For B2BuserOnboardingAPI
        if(B2BaccIdSet != null && B2BaccIdSet.size() > 0)
        {
            if(System.isBatch())
            {
                System.debug('Returning from Account AfterUpdate method becasue its updating from batch>>>>>>>' + system.isBatch());
                return;  
            }    
            else
            {
                System.debug('B2BaccIdSet>>>>>>>>>>>>>>>>>' + B2BaccIdSet);
                B2BuserOnboardingAPI.sendAccountDetails(B2BaccIdSet , false);
            }
        }
        
        //added by bharat for secondary module requirement on 29-05-2025
        if(newRMaccountIds != null && newRMaccountIds.size()>0){
            
            List<Secondary_Module__c> secReqToUpdate = [SELECT Id, Member_Name__c, Relationship_Manager__c FROM Secondary_Module__c
                                                        WHERE Member_Name__c IN :newRMaccountIds];
            System.debug('secReqToUpdate>>>>>>>>>' + secReqToUpdate);
            
            for (Secondary_Module__c req : secReqToUpdate) {	
                if (accountRMmap.containsKey(req.Member_Name__c)) {
                    req.Relationship_Manager__c = accountRMmap.get(req.Member_Name__c);
                }
            }
       
            
            if (!secReqToUpdate.isEmpty()) {
                update secReqToUpdate;
            }
            System.debug('Sfter Update secReqToUpdate>>>>>>>>>' + secReqToUpdate);
        }
        if(accList!=Null && accList.Size() > 0){
        ReferralTaskCreation.taskCreationOnAccountUpdate(accList,accMap);
            system.debug('Task creation on account update>>>>>>');
        }
    }
    
    public Map<String,set<Id>> checkForEmailDuplicate(Map<String,set<Id>> emailIdsMap)
    {
        
        for(Account acc : [select id,Official_Email__c,Personal_Email__c from account where Personal_Email__c in : emailIdsMap.keyset()])
        {
            
            /* if(acc.Official_Email__c!=null && acc.Official_Email__c!='')
{
if(!emailIdsMap.containsKey(acc.Official_Email__c)){
emailIdsMap.put(acc.Official_Email__c, new Set<Id>());
}
emailIdsMap.get(acc.Official_Email__c).add(acc.Id);
}*/
            
            if(acc.Personal_Email__c !=null && acc.Personal_Email__c !='')
            {
                if(!emailIdsMap.containsKey(acc.Personal_Email__c )){
                    emailIdsMap.put(acc.Personal_Email__c , new Set<Id>());
                }
                emailIdsMap.get(acc.Personal_Email__c).add(acc.Id);
            } 
        }
        
        for(lead__c ld : [select id,Personal_Email__c, Primary_Contact__c from lead__c where Personal_Email__c in : emailIdsMap.keyset()])
        {
            if(ld.Personal_Email__c !=null && ld.Personal_Email__c !='')
            {
                if(!emailIdsMap.containsKey(ld.Personal_Email__c )){
                    emailIdsMap.put(ld.Personal_Email__c , new Set<Id>());
                }
                emailIdsMap.get(ld.Personal_Email__c).add(ld.Id);
            } 
        }
        
        return emailIdsMap;
    }
    
    public void updateReferenceFieldsCommon(List<Account> accList,Map<String,set<Id>> emailToIdsMap)
    {
        Map<String,Id> relManagerNoMap = new Map<String,Id>();
        Map<String,Id> referByNoMap = new Map<String,Id>();
        Map<String,Boolean> accountLeadMap = new Map<String,Boolean>();
        Map<String,String> recordTypeAccMap = new Map<String,String>();
        Map<Id,String> recordTypeMap = new Map<Id,String>();
        recordTypeMap = getAccountLeadRecordTypeInfo();
        
        for(Account acc: accList)
        {
            if(String.IsNotBlank(acc.Relationship_Manager_Contact__c) && acc.Relationship_Manager__c==null)  
            {
                relManagerNoMap.put(acc.Relationship_Manager_Contact__c,null); 
            }
            if(String.IsNotBlank(acc.Refer_By_Contact_No__c) && acc.ParentId==null)  
            {
                referByNoMap.put(acc.Refer_By_Contact_No__c,null); 
            }
            if(!acc.Is_Converted_From_Lead__c)
            {
                accountLeadMap.put(acc.Primary_Contact__c,False);
                if(recordTypeMap.containsKey(acc.recordtypeId))
                    recordTypeAccMap.put(acc.Primary_Contact__c,recordTypeMap.get(acc.recordtypeId));
                else
                    recordTypeAccMap.put(acc.Primary_Contact__c,null);
            }
        }
        
        system.debug('recordTypeAccMap>>>>'+recordTypeAccMap);
        system.debug('relManagerNoMap>>>>'+relManagerNoMap);
        system.debug('referByNoMap>>>>'+referByNoMap);
        system.debug('accountLeadMap>>>>'+accountLeadMap);
        system.debug('emailToIdsMap>>>>'+emailToIdsMap);
        
        if(accountLeadMap!=null && accountLeadMap.size()>0)
        {
            for(Lead__c ld : [select id,Primary_Contact__c,recordtypeId from Lead__c where Primary_Contact__c in : accountLeadMap.keyset()])
            {
                if(ld.Primary_Contact__c!=null && ld.Primary_Contact__c !='' && recordTypeAccMap.get(ld.Primary_Contact__c)==recordTypeMap.get(ld.recordtypeId))
                    accountLeadMap.put(ld.Primary_Contact__c,True);
            }        
        }
        
        if(relManagerNoMap!=null && relManagerNoMap.size()>0)
        {
            for(user u : [select id,Contact_No__c from user where Contact_No__c in : relManagerNoMap.Keyset()])
            {   
                if(u.Contact_No__c !=null)
                    relManagerNoMap.put(u.Contact_No__c,u.Id);
            }
        }
        
        system.debug('relManagerNoMap11>>>>'+relManagerNoMap);
        if(referByNoMap!=null && referByNoMap.size()>0)
        {
            for(Account existAcc : [select id,Primary_Contact__c from account where Primary_Contact__c in : referByNoMap.Keyset()])
                referByNoMap.put(existAcc.Primary_Contact__c,existAcc.Id);
        }
        system.debug('referByNoMap111>>>>'+referByNoMap);
        
        for(Account acc: accList)
        {
            if(String.IsNotBlank(acc.Relationship_Manager_Contact__c) && acc.Relationship_Manager__c==null && relManagerNoMap.size()>0 && relManagerNoMap.get(acc.Relationship_Manager_Contact__c)!=null) 
            {
                acc.Relationship_Manager__c = relManagerNoMap.get(acc.Relationship_Manager_Contact__c);
            }
            if(String.IsNotBlank(acc.Refer_By_Contact_No__c) && acc.ParentId==null && referByNoMap.size()>0 && referByNoMap.get(acc.Refer_By_Contact_No__c)!=null)  
            {
                acc.ParentId = referByNoMap.get(acc.Refer_By_Contact_No__c);
            }
            
            if(acc.Relationship_Manager__c!=null)
                acc.OwnerId = acc.Relationship_Manager__c;
            
            if(accountLeadMap.size()>0 && accountLeadMap.ContainsKey(acc.Primary_Contact__c) && accountLeadMap.get(acc.Primary_Contact__c))
                acc.addError('Lead is already exist for the same primary contact : '+acc.Primary_Contact__c);
            
            if(emailToIdsMap!=null && emailToIdsMap.size()>0 && acc.Personal_Email__c !=null && acc.Personal_Email__c !='' && emailToIdsMap.containsKey(acc.Personal_Email__c))
            {
                if(emailToIdsMap.get(acc.Personal_Email__c).size()>0)
                    acc.Is_Duplicate_Email_Found__c = true;
                else
                    acc.Is_Duplicate_Email_Found__c = false;            
            }
            else
            {
                acc.Is_Duplicate_Email_Found__c = false;            
            }
            
        }
    }
    
    Public Map<Id,String> getAccountLeadRecordTypeInfo(){
        Map<Id,String> recTypeNameMap = new Map<Id,String>(); 
        
        Schema.DescribeSObjectResult acc = Schema.SObjectType.Account; 
        Schema.DescribeSObjectResult ld = Schema.SObjectType.Lead__c; 
        
        Map<Id,Schema.RecordTypeInfo> rtMapById = acc.getRecordTypeInfosById();
        Map<Id,Schema.RecordTypeInfo> rtMapByldId = ld.getRecordTypeInfosById();
        
        for(Id rtAcc : acc.getRecordTypeInfosById().Keyset())
        {
            recTypeNameMap.put(rtAcc,''+acc.getRecordTypeInfosById().get(rtAcc).getDeveloperName());
        }
        
        for(Id rtld : ld.getRecordTypeInfosById().Keyset())
        {
            recTypeNameMap.put(rtld,''+ld.getRecordTypeInfosById().get(rtld).getDeveloperName());
        }
        
        for(Id rttId : recTypeNameMap.keyset())
        {
            system.debug('rttId >>>>>'+rttId );
            system.debug('rtMapById>>>>>'+recTypeNameMap.get(rttId));
        }
        
        return recTypeNameMap;
    }
    // Added by ankush for Activity daily Update.16.8.23
    public void updateReferredCounts(List<Account> accList) {
        Map<Id, Integer> referralCountMap = new Map<Id, Integer>();
        
        for (Account acc : accList) {
            if (acc.ParentId != null) {
                referralCountMap.put(acc.ParentId, 0);
                System.debug('ACC.PARENTID>>>>>>>>' + acc.ParentId );
            }
        }
        
        
        for (AggregateResult result : [SELECT ParentId, COUNT(Id) counts FROM Account WHERE ParentId IN :referralCountMap.keySet() GROUP BY ParentId]) {
            Id referredById = (Id)result.get('ParentId');
            Integer referralCount = (Integer)result.get('counts');
            referralCountMap.put(referredById, referralCount);
        }
        
        List<Account> accountsToUpdate = new List<Account>();
        for (Id accountId : referralCountMap.keySet()) {
            accountsToUpdate.add(new Account(Id = accountId, Referred_Accounts__c = referralCountMap.get(accountId)));
        }
        
        update accountsToUpdate;
    }
    
    //  need to check this for renewal membership point creation
    public void createPointTransaction(List<Account> accList,Map<Id,Account> oldMap){
        List<Points_Transaction__c> points = new List<Points_Transaction__c>();
        for(Account acc : accList){  
            system.debug('scc '+acc.date_of_payment__c);
            system.debug('scc '+acc.Membership_Status__c);
            // if(acc.date_of_payment__c != null && acc.Membership_Status__c != null && (oldMap.get(acc.Id).date_of_payment__c != acc.date_of_payment__c) && (acc.Membership_Status__c == 'Paid IPV Fee' || acc.Membership_Status__c == 'Paid Community'
            //    || acc.Membership_Status__c == 'Paid by CXO Points' || acc.Membership_Status__c == 'Paid by IPV Points' || acc.Membership_Status__c == 'Platinum' || acc.Membership_Status__c == 'Complimentary'))
            if((acc.payment_type__c == 'Renewal' || acc.payment_type__c == 'Renewal - Due date expired' || acc.payment_type__c == 'Early renewal' ) 
               && ((acc.date_of_payment__c != null && (oldMap.get(acc.Id).date_of_payment__c != acc.date_of_payment__c)) || 
                   (acc.Membership_Slab__c != oldMap.get(acc.Id).Membership_Slab__c)) &&    
               (oldMap != null && !((acc.Membership_Slab__c == 'Bronze' && oldMap.get(acc.Id).Membership_Slab__c == 'Silver') || (acc.Membership_Slab__c == 'Silver' && oldMap.get(acc.Id).Membership_Slab__c == 'Gold') || (acc.Membership_Slab__c == 'Bronze' && oldMap.get(acc.Id).Membership_Slab__c == 'Gold'))) &&    
               (acc.Membership_Status__c == 'Paid IPV Fee' || acc.Membership_Status__c == 'Paid Community'
                || acc.Membership_Status__c == 'Paid by CXO Points' || acc.Membership_Status__c == 'Paid by IPV Points' || acc.Membership_Status__c == 'Platinum' || acc.Membership_Status__c == 'Complimentary'))
            {
                system.debug('in '+acc.Membership_Slab__c);
                system.debug('in old '+oldMap.get(acc.Id).date_of_payment__c);
                Points_Transaction__c point =  new Points_Transaction__c();
                point.Credit_To__c = acc.Id;
                point.Debit_From__c = Label.PT_Debit_Account;
                if(acc.Membership_Slab__c == 'Gold'){
                    point.Points_Alloted__c = 100;
                } 
                if(acc.Membership_Slab__c == 'Silver'){
                    point.Points_Alloted__c = 75;
                } 
                if(acc.Membership_Slab__c == 'Bronze'){
                    point.Points_Alloted__c = 50;
                } 
                point.Point_Type__c = 'Renew Membership';
                point.Payment_Date__c = acc.date_of_Payment__c;
                point.date__c = acc.date_of_Payment__c;               
                point.Member_Name__c = acc.Id;
                point.Membership_Slab__c = acc.Membership_Slab__c;
                point.Event_Date__c = system.today();
                //point.Date_of_IC__c = system.today(); commented by sahil on 19.09.2024
                points.add(point);
            }
            //if(oldmap == null){
            if(acc.date_of_payment__c != null && (oldMap != null && oldMap.get(acc.Id).Payment_Type__c != 'New Payment') &&  acc.Payment_Type__c == 'New Payment' && acc.ParentId != null){
                system.debug('in Payment_Type__c'+acc.Payment_Type__c);
                Points_Transaction__c point1 =  new Points_Transaction__c();
                point1.Credit_To__c = acc.ParentId;
                point1.Event_Date__c = system.today();
                point1.Debit_From__c = Label.PT_Debit_Account; 
                point1.Points_Alloted__c = 20;
                //point1.Point_Type__c = 'Successful conversion of referrals in addition to Amazon vouchers';
                point1.Point_Type__c = 'Referal Conversions';
                point1.Payment_Date__c = acc.date_of_Payment__c;
                //point1.Date_of_Receiving_Leads__c = acc.Date_of_receiving_lead__c;
                point1.date__c = acc.date_of_Payment__c;
                point1.Date_of_Receiving_Lead__c = acc.Date_of_receiving_lead__c;
                point1.Refer_Date__c = acc.Date_of_receiving_lead__c;
                point1.Member_Name__c = acc.ParentId;
                point1.Referee_Name__c = acc.Name;
                points.add(point1);
            }
            //}
            
        }
        if(!points.isEmpty()){
            system.debug('in point insertion..');
            if(PointsTransactionTriggerHandler.isFirstTime)    
                insert points; 
            PointsTransactionTriggerHandler.isFirstTime = false;
        }
    }
    
    // Added by Sahil on 29.01.2024 Method to check if the City value is valid in the picklist
    private boolean isValidCity(String lowercaseCity)
    {
        List<String> validCities = new List<String>();
        
        // Describe the Account object to get the picklist values for the City field
        Schema.DescribeFieldResult fieldResult = Account.City__c.getDescribe();
        
        List<Schema.PicklistEntry> picklistValues = fieldResult.getPicklistValues();
        
        for(Schema.PicklistEntry picklistEntry : picklistValues) {
            validCities.add(picklistEntry.getValue());
        }
        
        System.debug('City >>>>> ' + validCities);
        
        return validCities.contains(lowercaseCity);
    }
    
    // Added by Sahil on 26.03.2024 Method to check if the Campaign value is valid in the picklist
    private boolean isValidCampaign(String campagin)
    {
        List<String> validCampaigns = new List<String>();
        
        // Describe the Lead object to get the picklist values for the Campaign field
        Schema.DescribeFieldResult fieldResult = Lead__c.Campaign__c.getDescribe();
        
        List<Schema.PicklistEntry> picklistValues = fieldResult.getPicklistValues();
        
        for(Schema.PicklistEntry picklistEntry : picklistValues) {
            validCampaigns.add(picklistEntry.getValue());
        }
        
        System.debug('Camapign >>>>> ' + validCampaigns);
        
        return validCampaigns.contains(campagin);
    }
    
    
    //added by Vidhi for membership slab history
    public void updateSlabHistoryField(List<Account> accList, Map<Id, Account> oldMap) {
        for (Account acc : accList) {
            if (String.isBlank(acc.Membership_Slab_History__c)) {
                acc.Membership_Slab_History__c = '';
            }
            Account oldValue = oldMap.get(acc.Id);
            String oldSlab = oldValue.Membership_Slab__c != null ? oldValue.Membership_Slab__c : '';
            String oldValidity = oldValue.Membership_Slab_Validity_Upto__c != null ? oldValue.Membership_Slab_Validity_Upto__c.format() : '';
            String oldUpdateDate = oldValue.Date_of_Slab_Updation__c != null ? oldValue.Date_of_Slab_Updation__c.format() : '';
            
            String newSlab = acc.Membership_Slab__c != null ? acc.Membership_Slab__c : '';
            String newValidity = acc.Membership_Slab_Validity_Upto__c != null ? acc.Membership_Slab_Validity_Upto__c.format() : '';
            String newUpdateDate = acc.Date_of_Slab_Updation__c != null ? acc.Date_of_Slab_Updation__c.format() : '';
            
            if (oldSlab != newSlab || oldValidity != newValidity || oldUpdateDate != newUpdateDate) {
                String logEntry = '{"oldSlab": "' + oldSlab + '", "newSlab": "' + newSlab + '", ' +
                    '"oldValidity": "' + oldValidity + '", "newValidity": "' + newValidity + '", ' +
                    '"oldUpdateDate": "' + oldUpdateDate + '", "newUpdateDate": "' + newUpdateDate + '"}';
                acc.Membership_Slab_History__c += logEntry + '\n';
            }
        }
    }
    
    //added by vidhi for Update Parent Account and B2B user type field while creating tenant 
    public void updateFileds(List<Account> accList){
        
        Set<Id> spocIds = new Set<Id>();
        Map<Id, Account> tenantToSpocMap = new Map<Id, Account>();
        for(Account acc : accList){
            if(acc.Tenant_SPOC_Account__c != null && acc.Is_Tenant__c && acc.Partner_Type__c != null){
                spocIds.add(acc.Tenant_SPOC_Account__c);
                tenantToSpocMap.put(acc.Tenant_SPOC_Account__c, acc);
                System.debug('spocIds==>'+ spocIds);
                System.debug('tenantToSpocMap==>'+tenantToSpocMap);
            }
        }
        
        if(!spocIds.isEmpty()){
            List<Account> accountsToUpdate = [SELECT Id,Is_Tenant__c, Partner_parent_account__c, Partner_Type__c from Account where Id IN :spocIds];
            System.debug('accountsToUpdate==>'+accountsToUpdate);
            
            for (Account spocAccount : accountsToUpdate) {
                
                if (tenantToSpocMap.containsKey(spocAccount.Id)) {
                    Account tenantAccount = tenantToSpocMap.get(spocAccount.Id);
                    System.debug(tenantAccount);
                    spocAccount.Partner_parent_account__c = tenantAccount.Id;
                    spocAccount.B2B_User_Type__c = 'L1';
                    spocAccount.Partner_Type__c = tenantAccount.Partner_Type__c;
                    
                }
            }
            
            if (!accountsToUpdate.isEmpty()) {
                update accountsToUpdate;
                System.debug(accountsToUpdate);
            }     
        }   
    }   
    
    //added by vidhi for update spoc account based on partner parent account
    public void updateSpoc(List<Account> accounts) {
        System.debug(accounts);
        if (accounts == null || accounts.isEmpty()) {
            return;
        }
        
        Set<Id> parentAccountIds = new Set<Id>();
        
        for (Account acc : accounts) {
            if (acc != null && acc.Partner_parent_account__c != null && acc.Is_Tenant__c == false) {
                parentAccountIds.add(acc.Partner_parent_account__c);
            }
        }
        
        if (!parentAccountIds.isEmpty()) {
            Map<Id, Account> parentAccountsMap = new Map<Id, Account>(
                [SELECT Id, Tenant_SPOC_Account__c, Partner_Type__c
                 FROM Account
                 WHERE Id IN :parentAccountIds]
            );
            
            List<Account> accountsToUpdate = new List<Account>();
            
            for (Account acc : accounts) {
                if (acc != null && acc.Is_Tenant__c == false) {
                    Account parentAccount = parentAccountsMap.get(acc.Partner_parent_account__c);
                    if (parentAccount != null) {
                        if (parentAccount.Tenant_SPOC_Account__c != null && 
                            acc.Id != parentAccount.Tenant_SPOC_Account__c) {
                                acc.Tenant_SPOC_Account__c = String.valueOf(parentAccount.Tenant_SPOC_Account__c);
                            }
                        acc.Partner_Type__c = parentAccount.Partner_Type__c;
                    }
                }
            }
        }
    }

    
    //added by vidhi for validate RM Code.
    public static void validateRmCode(List<Account> accounts){
        Set<Id> parentAccountIds = new Set<Id>();
        Set<Id> currentAccountIds = new Set<Id>();
        
        for(Account acc : accounts){
            if(acc.Partner_parent_account__c != null && !acc.Is_Tenant__c){
                parentAccountIds.add(acc.Partner_parent_account__c);
            }
            currentAccountIds.add(acc.Id); 
        }
        
        if (!parentAccountIds.isEmpty()) {
            List<Account> accList = [SELECT Id, RM_Code__c FROM Account WHERE Partner_parent_account__c IN :parentAccountIds AND Id NOT IN :currentAccountIds AND B2B_User_Type__c IN ('L1', 'L2')];
            
            Set<String> existingRMCodeSet = new Set<String>();   //set to store the existing RM_Code__c values
            for (Account existingAcc : accList) {
                if (existingAcc.RM_Code__c != null) {
                    existingRMCodeSet.add(existingAcc.RM_Code__c);
                }
            }
            
            for (Account acc : accounts) {
                if (acc.RM_Code__c != null && existingRMCodeSet.contains(acc.RM_Code__c)) {      
                    acc.addError('The RM code you entered already exists in the system. Please enter a unique RM code.');   // Check for duplicates and add errors
                }
            }
        }
    }
    // added by jay dabhi on 5th september 2024 for handling user activity based on Account Field value changes 
  public void handleAccountActivitiesUpdate(List<Account> newAccounts, Map<Id, Account> oldAccountsMap) {
    List<User_Activity__c> activitiesToInsert = new List<User_Activity__c>();
    id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();

  
    Set<Id> accountIds = new Set<Id>();
    for (Account acc : newAccounts) {
        accountIds.add(acc.Id);
    }

    List<Account> accountsWithDetails = [SELECT Id, Relationship_Manager__r.Name, Premier_Premier_Deboarding_Date__c, 
                                              Date_of_1st_Payment__c, AIM_Onboarding_Date__c, Premier_Premier_Onboarding_Date__c, 
                                             Account_Premier_Status__c, Date_of_Slab_Updation__c, Membership_Status__c, 
                                             Payment_Type__c, Date_of_Payment__c, AIM__c, Membership_Slab__c, ParentId, 
                                             Name, Date_of_Addition__c
                                           FROM Account
                                          WHERE Id IN :accountIds];

    Map<Id, Account> accountMap = new Map<Id, Account>();
    for (Account acc : newAccounts) {
        accountMap.put(acc.Id, acc); 
    }

    SET<id> userId = new SET<id>();
    for (Account acc : oldAccountsMap.values()) {
        if (acc.Relationship_Manager__c != NULL) {
            userId.add(acc.Relationship_Manager__c);  
        }
    }
    Map<Id, User> userMap = new Map<Id, User>([SELECT Id, Name FROM User WHERE Id IN :userId]);

    for (Account acc : newAccounts) {
        Account oldAcc = oldAccountsMap.get(acc.Id);
   
        if (!accountMap.containsKey(acc.Id)) {
            system.debug('Skipping Account update due to missing data for record: ' + acc.Id);
            continue;
        }

        acc = accountMap.get(acc.Id);

        Id oldRmId = oldAcc != null ? oldAcc.Relationship_Manager__c : null;
        Id newRmId = acc.Relationship_Manager__c;

        if (oldAcc != null && acc.Membership_Status__c != oldAcc.Membership_Status__c) {
            activitiesToInsert.add(new User_Activity__c(
                Related_Account__c = acc.Id,
                Activity_Type__c = 'Membership Status Change',
                Activity_Detail_RICH__c = 'Membership status changed to ' + acc.Membership_Status__c + ' from ' + oldAcc.Membership_Status__c,
                Time_Stamp__c = SYSTEM.NOW()
            ));
        }
            if (oldAcc != null && acc.Payment_Type__c != oldAcc.Payment_Type__c && acc.Payment_Type__c == 'New Payment') {
                activitiesToInsert.add(new User_Activity__c(
                    Related_Account__c = acc.Id,
                    Activity_Type__c = 'New Payment',
                    Activity_Detail_RICH__c = 'Payment status "' + acc.Payment_Type__c + '"',
                    Time_Stamp__c = acc.Date_of_1st_Payment__c
                ));
            }
            
            
            Set<String> validPaymentTypes = new Set<String>{'Renewal', 'Renewal - Due date expired','Early Renewal'};
                if (oldAcc != null && acc.Payment_Type__c != oldAcc.Payment_Type__c && validPaymentTypes.contains(acc.Payment_Type__c)) {
                    activitiesToInsert.add(new User_Activity__c(
                        Related_Account__c = acc.Id,
                        Activity_Type__c = 'Renewal Payment',
                        Activity_Detail_RICH__c = 'Payment status "' + acc.Payment_Type__c + '"',
                        Time_Stamp__c =  SYSTEM.NOW()
                    ));
                }
            
             Set<String> validStatus = new Set<String>{'Interested in AIM+', 'Refused AIM+','Addendum shared - sign pending'};
            if (oldAcc != null && acc.AIM__c != oldAcc.AIM__c && validStatus.contains(acc.AIM__c) ) {
                activitiesToInsert.add(new User_Activity__c(
                    Related_Account__c = acc.Id,
                    Activity_Type__c = 'Onboarded on AIM+',
                    Activity_Detail_RICH__c = 'AIM+ Status " ' + acc.AIM__c + '"',
                    Time_Stamp__c =SYSTEM.NOW()
                ));
                system.debug('activitiesToInsert>>>' + activitiesToInsert);
            }
            if (oldAcc != null && acc.AIM__c != oldAcc.AIM__c && acc.AIM__c =='Onboarded on AIM+' ) {
                activitiesToInsert.add(new User_Activity__c(
                    Related_Account__c = acc.Id,
                    Activity_Type__c = 'Onboarded on AIM+',
                    Activity_Detail_RICH__c = 'AIM+ Status " ' + acc.AIM__c + '"',
                    Time_Stamp__c =acc.AIM_Onboarding_Date__c
                ));
                system.debug('activitiesToInsert>>>' + activitiesToInsert);
            }
            
            if (oldAcc != null && acc.Relationship_Manager__c != oldAcc.Relationship_Manager__c) {
                String oldRMUrl, newRMUrl;
                oldRMUrl = oldRmId != null ? URL.getOrgDomainURL().toExternalForm() + '/' + oldRmId : '';
                newRMUrl = acc.Relationship_Manager__c != null ? URL.getOrgDomainURL().toExternalForm() + '/' + acc.Relationship_Manager__c : '';  
                
                String oldRMName = oldRmId != null && userMap.containsKey(oldRmId) ? userMap.get(oldRmId).Name : 'null';
                String newRMName = acc.Relationship_Manager__c != null ? acc.Relationship_Manager__r.Name : 'null';
                
                String activityDetailRich = 'Relationship Manager changed from ' + 
                    (oldRMUrl != '' ? '<a href="' + oldRMUrl + '" target="_blank">' + oldRMName + '</a>' : oldRMName) + 
                    ' to ' + 
                    (newRMUrl != '' ? '<a href="' + newRMUrl + '" target="_blank">' + newRMName + '</a>' : newRMName);
                
                activitiesToInsert.add(new User_Activity__c(
                    Related_Account__c = acc.Id,
                    Activity_Type__c = 'RM Change',
                    Activity_Detail_RICH__c = activityDetailRich,
                    Time_Stamp__c = System.now()
                ));
            }
            
            Map<String, Integer> membershipHierarchy = new Map<String, Integer>{
                'Gold' => 3,
                    'Silver' => 2,
                    'Bronze' => 1
                    };  
                        
                        if (oldAcc != null && acc.Membership_Slab__c != oldAcc.Membership_Slab__c) {
                            String action;
                            
                            if (oldAcc.Membership_Slab__c == null) {
                                action = 'upgraded';
                            } else if (acc.Membership_Slab__c == null) {
                                action = 'downgraded';
                            } else {
                                if (membershipHierarchy.get(acc.Membership_Slab__c) > membershipHierarchy.get(oldAcc.Membership_Slab__c)) {
                                    action = 'upgraded';
                                } else {
                                    action = 'downgraded';
                                }
                            }
                            activitiesToInsert.add(new User_Activity__c(
                                Related_Account__c = acc.Id,
                                Activity_Type__c = 'Membership Slab Change (Gold/Silver/Bronze)',
                                Activity_Detail_RICH__c = 'Membership slab ' + action + ' from ' + (oldAcc.Membership_Slab__c != null ? oldAcc.Membership_Slab__c : 'none') + ' to ' + (acc.Membership_Slab__c != null ? acc.Membership_Slab__c : 'none'),
                                Time_Stamp__c = acc.Date_of_Slab_Updation__c
                            ));
                        }
            if (oldAcc != null && acc.Membership_Status__c != oldAcc.Membership_Status__c && acc.Membership_Status__c == 'On Trial') {
                String ConvertedUrl;
                ConvertedUrl = URL.getOrgDomainURL().toExternalForm() + '/' + acc.Id ;
                activitiesToInsert.add(new User_Activity__c(
                    Related_Account__c = acc.ParentId,
                    Activity_Type__c = 'Member Converted to Trial from his/her referral',
                    Activity_Detail_RICH__c = (ConvertedUrl != '' ? '<a href="' + ConvertedUrl + '" target="_blank">' + acc.Name + '</a>' : acc.Name) + ' converted to Trial',
                    Time_Stamp__c = SYSTEM.NOW()
                ));
            }
            if (oldAcc != null && acc.Membership_Status__c != oldAcc.Membership_Status__c && (acc.Membership_Status__c == 'Paid IPV Fee' || acc.Membership_Status__c == 'Paid Community')) {
                string  ConvertedUrl;
                ConvertedUrl =URL.getOrgDomainURL().toExternalForm() + '/' + acc.Id ;
                activitiesToInsert.add(new User_Activity__c(
                    Related_Account__c = acc.ParentId,
                    Activity_Type__c = 'Member Converted to Paid from his/her referral',
                    Activity_Detail_RICH__c = (ConvertedUrl != '' ? '<a href="' + ConvertedUrl + '" target="_blank">' + acc.Name + '</a>' : acc.Name)  + ' converted to paid',
                    Time_Stamp__c = acc.Date_of_1st_Payment__c
                ));
            }
            
            if (oldAcc != null && acc.Membership_Status__c != oldAcc.Membership_Status__c && acc.Membership_Status__c == 'Complimentary') {
                string  ConvertedUrl;
                ConvertedUrl = URL.getOrgDomainURL().toExternalForm() + '/' + acc.Id  ;
                activitiesToInsert.add(new User_Activity__c(
                    Related_Account__c = acc.ParentId,
                    Activity_Type__c = 'Member Converted to Complimentary from his/her referral',
                    Activity_Detail_RICH__c =  (ConvertedUrl != '' ? '<a href="' + ConvertedUrl + '" target="_blank">' + acc.Name + '</a>' : acc.Name)  + ' converted to Complimentary',
                    Time_Stamp__c =acc.Date_of_Addition__c
                ));
            }
            Set<String> validOnboardingTypes = new Set<String>{
                'Onboarded on Premier', 'Onboarded on Premier +'
                    };
                        if (acc != null && validOnboardingTypes.contains(acc.Account_Premier_Status__c) && acc.Account_Premier_Status__c != oldAcc.Account_Premier_Status__c  ) {
                            String statusUrl = acc.Name != null ? URL.getOrgDomainURL().toExternalForm() + '/' + acc.Id : '';
                            
                            activitiesToInsert.add(new User_Activity__c(
                                Related_Account__c = acc.Id,
                                Activity_Type__c = 'Onboarded on Premier Status',
                                Activity_Detail_RICH__c = (statusUrl != '' ? '<a href="' + statusUrl + '" target="_blank">' + acc.Name + '</a>' : acc.Name) + ' Premier status "' + acc.Account_Premier_Status__c + '"',
                                Time_Stamp__c = acc.Premier_Premier_Onboarding_Date__c 
                            ));
                        }
             Set<String> valideboardingTypes = new Set<String>{
                 'Deboarded to Class D from Premier', 'Deboarded to Class D from Premier +'
                    };
                        if (acc != null && valideboardingTypes.contains(acc.Account_Premier_Status__c) && acc.Account_Premier_Status__c != oldAcc.Account_Premier_Status__c  ) {
                            String statusUrl = acc.Name != null ? URL.getOrgDomainURL().toExternalForm() + '/' + acc.Id : '';
                            
                            activitiesToInsert.add(new User_Activity__c(
                                Related_Account__c = acc.Id,
                                Activity_Type__c = 'Onboarded on Premier Status',
                                Activity_Detail_RICH__c = (statusUrl != '' ? '<a href="' + statusUrl + '" target="_blank">' + acc.Name + '</a>' : acc.Name) + ' Premier status "' + acc.Account_Premier_Status__c + '"',
                                Time_Stamp__c = acc.Premier_Premier_Deboarding_Date__c 
                            ));
                        }
            Set<String> validPremierTypes = new Set<String>{
                'Premier+ Exception (check comments)', 'Premier Exception (check comments)', 'Interested in Premier Select', 'Onboarded on Premier Select', 'Addendum Shared for Premier Select - Sign Pending'
                    };
                        
                        if (acc != null && validPremierTypes.contains(acc.Account_Premier_Status__c) && acc.Account_Premier_Status__c != oldAcc.Account_Premier_Status__c ) {
                            String statusUrl = acc.Name != null ? URL.getOrgDomainURL().toExternalForm() + '/' + acc.Id : '';
                            
                            activitiesToInsert.add(new User_Activity__c(
                                Related_Account__c = acc.Id,
                                Activity_Type__c = 'Onboarded on Premier Status',
                                Activity_Detail_RICH__c = (statusUrl != '' ? '<a href="' + statusUrl + '" target="_blank">' + acc.Name + '</a>' : acc.Name) + ' Premier status "' + acc.Account_Premier_Status__c + '"',
                                Time_Stamp__c =system.now()
                            ));
                        }
                  }
            
            if (!activitiesToInsert.isEmpty()) {
                try {
                    insert activitiesToInsert;
                } catch (DmlException e) {
                    System.debug('Error inserting activities: ' + e.getMessage());
                }
            }
        
    }
    public void handleAccountActivitiesInsert(List<Account> newAccounts){
        List<User_Activity__c> activitiesToInsert = new List<User_Activity__c>();
        for (Account acc : newAccounts) {
            if (acc.Membership_Status__c == 'On Trial') {
                String ConvertedUrl;
                ConvertedUrl = URL.getOrgDomainURL().toExternalForm() + '/' + acc.Id ;
                activitiesToInsert.add(new User_Activity__c(
                    Related_Account__c = acc.ParentId,
                    Activity_Type__c = 'Member Converted to Trial from his/her referral',
                    Activity_Detail_RICH__c = (ConvertedUrl != '' ? '<a href="' + ConvertedUrl + '" target="_blank">' + acc.Name + '</a>' : acc.Name) + ' converted to Trial',
                    Time_Stamp__c = SYSTEM.NOW()
                ));
            }
            if  (acc.Membership_Status__c == 'Paid IPV Fee' || acc.Membership_Status__c == 'Paid Community') {
                string  ConvertedUrl;
                ConvertedUrl =URL.getOrgDomainURL().toExternalForm() + '/' + acc.Id ;
                activitiesToInsert.add(new User_Activity__c(
                    Related_Account__c = acc.ParentId,
                    Activity_Type__c = 'Member Converted to Paid from his/her referral',
                    Activity_Detail_RICH__c = (ConvertedUrl != '' ? '<a href="' + ConvertedUrl + '" target="_blank">' + acc.Name + '</a>' : acc.Name)  + ' converted to paid',
                    Time_Stamp__c = acc.Date_of_1st_Payment__c
                ));
            }
            
            if ( acc.Membership_Status__c == 'Complimentary') {
                string  ConvertedUrl;
                ConvertedUrl = URL.getOrgDomainURL().toExternalForm() + '/' + acc.Id  ;
                activitiesToInsert.add(new User_Activity__c(
                    Related_Account__c = acc.ParentId,
                    Activity_Type__c = 'Member Converted to Complimentary from his/her referral',
                    Activity_Detail_RICH__c =  (ConvertedUrl != '' ? '<a href="' + ConvertedUrl + '" target="_blank">' + acc.Name + '</a>' : acc.Name)  + ' converted to Complimentary',
                    Time_Stamp__c =acc.Date_of_Addition__c
                ));
            }
            if ( acc.Membership_Slab__c != NULL){
                activitiesToInsert.add(new User_Activity__c(
                    Related_Account__c = acc.Id,
                    Activity_Type__c = 'Membership Slab Change (Gold/Silver/Bronze)',
                    Activity_Detail_RICH__c = 'Membership slab ' + 'Upgraded' + ' from null' + ' to ' + acc.Membership_Slab__c,
                    Time_Stamp__c = acc.Date_of_Slab_Updation__c
                ));
            }
            
        }
    
        if (!activitiesToInsert.isEmpty()) {
            try {
                insert activitiesToInsert;
            } catch (DmlException e) {
                System.debug('Error inserting activities: ' + e.getMessage());
            }
        }
    }
}