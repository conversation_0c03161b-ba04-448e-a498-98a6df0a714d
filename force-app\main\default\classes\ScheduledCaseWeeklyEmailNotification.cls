global class ScheduledCaseWeeklyEmailNotification implements Schedulable {
    
    global void execute(SchedulableContext sc) {
        sendEmailNotifications();
    }
    
    public void sendEmailNotifications() {
        List<Case> casesToSendEmailList = [SELECT Id,Case_Age__c,CaseNumber, Subject, Status, CreatedDate,Responsibility_to_solve_Internal__r.email,Owner.Name,Turnaround_Time__c,Date_of_issue_assigned_Internal__c,Relevant_Team__c FROM Case WHERE Date_of_issue_assigned_Internal__c= THIS_WEEK and Status in ('WIP','Closed') order by Relevant_Team__c];
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        Map<String, Case_escalation_setting__mdt> metadataMap = new Map<String, Case_escalation_setting__mdt>();
        Map<String,List<Case>> statusCaseMap = new Map<String,List<Case>>();
        
        for (Case_escalation_setting__mdt record : Case_escalation_setting__mdt.getAll().values()) 
        {
            System.debug('Developer Name: ' + record.DeveloperName);
            System.debug('Case_Relevant_Team__c: ' + record.Case_Relevant_Team__c);
            System.debug('record: ' + record);
            metadataMap.put(record.Case_Relevant_Team__c,record);
        }
        
        Map<String,List<Case>> teamCaseMap = new Map<String,List<Case>>();
        for (Case c : casesToSendEmailList) 
        {
            if(!teamCaseMap.containsKey(c.Relevant_Team__c))
            {
                teamCaseMap.put(c.Relevant_Team__c, new List<case>());
            }
            teamCaseMap.get(c.Relevant_Team__c).add(c);
            
            if(!statusCaseMap.ContainsKey(c.Status))
            {
                statusCaseMap.put(c.status,new List<Case>());
            }
            
            statusCaseMap.get(c.status).add(c);
        }
        
        for(String team : teamCaseMap.keyset())
        {
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            // Build email body
            String htmlBody = buildEmailBody(statusCaseMap);
            List<String> toAddresses = new List<String>();
            List<String> ccAddresses = new List<String>();
            
            // Create email
            email.setSubject('Weekly Cases Summary');
            email.setHtmlBody(htmlBody);
            email.setSaveAsActivity(false);    
            
            if(metadataMap!=null && metadataMap.containsKey(team) && metadataMap.get(team).Customer_success_SPOC_email__c!=null){            
                toAddresses.add(metadataMap.get(team).Customer_success_SPOC_email__c);
            }
            
            //Commented by Bharat as per discussion with mauli on 11-03-2025
            /*
            if(metadataMap!=null && metadataMap.containsKey(team) && metadataMap.get(team).Case_functional_head_email__c!=null){            
                ccAddresses.add(metadataMap.get(team).Case_functional_head_email__c);
            }
            */
            for(Case c : teamCaseMap.get(team))
            {
                if(c.Responsibility_to_solve_Internal__r.email!=null)
                {
                    ccAddresses.add(c.Responsibility_to_solve_Internal__r.email);
                }
            }                    
            if(ccAddresses.size()>0)
            {
                email.setCcAddresses(ccAddresses);
            }
            toAddresses.add('<EMAIL>');
            email.setToAddresses(toAddresses);
                
            emails.add(email);
        }
        system.debug('emails>>>>>>>>>>>>>>>'+emails);
        if (!emails.isEmpty()) {
            Messaging.SendEmailResult[] results = Messaging.sendEmail(emails);
            
            for (Messaging.SendEmailResult result : results) {
                if (!result.isSuccess()) {
                    for (Messaging.SendEmailError error : result.getErrors()) {
                        System.debug('Error sending email: ' + error.getMessage());
                    }
                }
            }
        }
    }
    
    private static String buildEmailBody(Map<String,List<Case>> caseMap)
    {
        //Map<String,Decimal> averageMap = new Map<String,Decimal>();
        //Date today = Date.today();
        //Date startDate = today.toStartOfWeek();
        //Date endDate = startDate.addDays(6);
        
        String htmlBody = '<html><body>';
        htmlBody += 'Hi Team,<br/><br/> I trust this email finds you well.<br/><br/>';
        htmlBody += 'Please find attached the link to the report summarizing the closed cases for this quarter along with all-time WIP cases.:<br/><br/>';
        htmlBody += '<p><a href="https://ipventures.lightning.force.com/lightning/r/Report/00OOX00000BMfz42AD/view?queryScope=userFolders">Report link</a></p>';
        htmlBody += '<br/> Thank you for your efforts and we kindly request you to help us in the resolving the WIP cases within the defined TAT. Please do not hesitate to contact us if you need assistance in getting the matter resolved. <br/><br/> Best regards,<br/>Customer Success Team';
        htmlBody += '</body></html>';

        return htmlBody;
    }
    
/*
    String jobName = 'Scheduled Case Weekly Email Notification';
String cronExpression = '0 59 23 ? * SUN *'; // Runs every Sunday at 11:59 PM
System.schedule(jobName, cronExpression, new ScheduledCaseWeeklyEmailNotification());
    
*/    
}