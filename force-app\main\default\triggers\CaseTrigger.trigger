trigger CaseTrigger on Case (before insert,before update,after insert,after update,after delete) {
    
    List<System_Setting__c> settingList = System_Setting__c.getall().values();
    if(settingList!=null && settingList.size()>0 && !settingList[0].CaseTriggerActivated__c)
    {
        system.debug('Returning from CaseTrigger because of custom setting>>>'+settingList);
        return; 
    }
    
    if(trigger.isBefore)
    {
        set<id> accountIds = new set<id>();
        Map<id,Account> accountMap = new map<Id,Account>();
        Map<id,Startup_Round__c> roundMap = new map<Id,Startup_Round__c>();
        
        for(case cs : Trigger.New)
        {
            String descrStr = cs.Description;
            if(String.IsBlank(cs.subject) && String.IsNOTBlank(descrStr))
            {
                if(descrStr.length()>255)
                    cs.subject = descrStr.substring(0, 255);
                else
                    cs.subject = descrStr;
            }
            
            if(cs.Responsibility_to_Solve__c!=null)
                cs.OwnerId = cs.Responsibility_to_Solve__c;
            
            if(cs.Issue_raised_By__c!=null)
                accountMap.put(cs.Issue_raised_By__c,new account());
            
            if(cs.Startup_Round__c!=null)
                roundMap.put(cs.Startup_Round__c,new Startup_Round__c());
            
            
            //    Added By Karan to update By default to be same as Date (issue raised)
            system.debug('Date Value'+  cs.Date_Issue_Raised__c);
            if(cs.Date_of_issue_assigned_Internal__c == null && cs.Relevant_Team__c!=null){  
                //cs.Date_of_issue_assigned_Internal__c = cs.Date_Issue_Raised__c;
                //Updated by Hemant as we need to capture the actual case assignment date || Date: 28-05-2024
                cs.Date_of_issue_assigned_Internal__c = System.today();
                
            }
            if(trigger.oldMap!=null)
            {
                cs.Old_Due_date_for_closure__c = trigger.oldMap.get(cs.Id).Due_date_for_closure__c;
            
            }
        }
        system.debug('accountMap>>>>'+accountMap);
        system.debug('roundMap>>>>'+roundMap);
        if((accountMap!=null && accountMap.size()>0) || (roundMap!=null && roundMap.size()>0))
        {
            for(Account acc : [select id,Personal_Email__c,Relationship_Manager__r.email from account where id in : accountMap.keyset()])
            {
                accountMap.put(acc.Id,acc);
            }
            for(Startup_Round__c round : [select id, name, IR_Owner__c from Startup_Round__c  where id in : roundMap.keyset()])
            {
                roundMap.put(round.Id,round);
            }
            system.debug('roundMap111>>>>'+roundMap);
            system.debug('accountMap111>>>>'+accountMap);
            for(case cs : Trigger.New)
            {
                if(cs.Issue_raised_By__c!=null && accountMap.get(cs.Issue_raised_By__c)!=null)
                {
                    cs.Account_Email__c= accountMap.get(cs.Issue_raised_By__c).Personal_Email__c;
                    cs.Relationship_Manager_Email__c = accountMap.get(cs.Issue_raised_By__c).Relationship_Manager__r.email;
                    
                    if(cs.Responsibility_to_Solve__c == null){
                        cs.Responsibility_to_Solve__c = accountMap.get(cs.Issue_raised_By__c).Relationship_Manager__r.Id;
                        //system.debug('accountMap00000>>>>'+roundMap.get(cs.Startup_Round__c).IR_Owner__c);
                    }
                    else{
                        cs.Responsibility_to_Solve__c = accountMap.get(cs.Issue_raised_By__c).Relationship_Manager__r.Id;
                    }
                    
                    /*if(cs.Relevant_Team__c == 'Startup Related' && cs.Startup_Round__c != null && roundMap.get(cs.Startup_Round__c).IR_Owner__c !=null){
                        cs.Responsibility_to_solve_Internal__c = roundMap.get(cs.Startup_Round__c).IR_Owner__c;
                    } else if(cs.Relevant_Team__c == 'Startup Related' && cs.Startup_Round__c == null){
                       cs.Responsibility_to_solve_Internal__c = null;
                    }
                    if(cs.Relevant_Team__c == 'Legal Related'){
                       cs.Responsibility_to_solve_Internal_1__c = System.Label.Case_Legal_Related_RM_Owner;
                    }
                    if(cs.Relevant_Team__c == 'Accounts Related'){
                        cs.Responsibility_to_solve_Internal_1__c = System.Label.Case_Accounts_Related_RM_Owner;
                    }
                    if(cs.Relevant_Team__c == 'Tech Related'){
                        cs.Responsibility_to_solve_Internal_1__c = System.Label.Case_Tech_Related_RM_Owner;
                    }*/
                }    
            }
        }
    }
    
    
        if(Trigger.isAfter && (Trigger.isInsert || Trigger.isUpdate)) {
    Set<Id> caseIdSet = new Set<Id>();
    Set<Id> Accid = new Set<Id>();

    for (Case c : Trigger.new) {
        System.debug('Case Id: ' + c.Id + ', Not_Send_to_External__c: ' + c.Not_Send_to_External__c);
        
        // Only add to caseIdSet if the checkbox is NOT checked 
        if (c.Not_Send_to_External__c == false) {
            caseIdSet.add(c.Id);
        }
        Accid.add(c.Issue_raised_By__c);
    

    System.debug('CaseId Set: ' + caseIdSet);
    System.debug('Accid Set: ' + Accid);

    // Call the future method only if there are cases that should be sent
    if (!caseIdSet.isEmpty()) {
        AppFutureCreateCaseAPI.sendCaseInfo(caseIdSet);
     System.debug('AppFutureCreateCaseAPI>>>>>' +caseIdSet);
    }
    }


        List<Account> accountsToUpdate = new List<Account>();

        for (Id Ids : Accid) {
        Integer caseCount = [SELECT COUNT() FROM Case WHERE Issue_raised_By__c IN:Accid];
        accountsToUpdate.add(new Account(Id = Ids, Query_Raised__c = caseCount));
       // accountsToUpdate.add(new Account(Id = accountId, Total_Case_Count__c = caseCount));
        }

        update accountsToUpdate;
    }
    if(Trigger.isafter && trigger.isDelete){
         set<id> Accid = New Set<id>();
         for(Case c: Trigger.old) {
            Accid.add(c.Issue_raised_By__c);
        }
          List<Account> accountsToUpdate = new List<Account>();

        for (Id Ids : Accid) {
        Integer caseCount = [SELECT COUNT() FROM Case WHERE Issue_raised_By__c IN:Accid];
        accountsToUpdate.add(new Account(Id = Ids, Query_Raised__c = caseCount));
       // accountsToUpdate.add(new Account(Id = accountId, Total_Case_Count__c = caseCount));
      
        }

        update accountsToUpdate;
    }
    
    if(Trigger.isAfter && (Trigger.isInsert || Trigger.isUpdate)){
        Boolean isInsert = true;
        
        If (Trigger.isUpdate)
            isInsert = false;
            
        system.debug('Case trigger calls');
        CaseTriggerHandler.handleCaseAssignmentClosure(isInsert ,Trigger.New,Trigger.OldMap);
        // CaseTriggerHandler.handleCaseActivity(Trigger.New,Trigger.OldMap);
         if (Trigger.isInsert) {
        CaseUserTimeLineActivityHandler.handleinsertCaseActivity(Trigger.New);
    }
         if (Trigger.isUpdate) {
        CaseUserTimeLineActivityHandler.handleUpdateCaseActivity(Trigger.New,Trigger.OldMap);
    }
        system.debug('handleCaseActivity ');
        if(Trigger.isUpdate && Trigger.isAfter){
        ReferralTaskCreation.taskCreationOnCaseResolve(Trigger.New,Trigger.OldMap);
    }
    }
    
}