public class TaskTriggerHandler 
{
	public static void beforeInsertUpdate(List<Task> taskList)
    {
        for(Task tsk : taskList)
        {
            if(tsk.ActivityDate == null && tsk.Task_Due_Date_and_Time__c != null)
            {
                tsk.ActivityDate = tsk.Task_Due_Date_and_Time__c.Date();
            }
        }
    }
    
    public static void updateCallStatus(Set<Id> taskIds) 
    {
        //Set<Id> uniqueAccountIds = new Set<Id>();
        Map<Id, Date> accDateOfPaymentMap = new Map<Id, Date>();
        Set<id> accountIds = new set<id>();
        Map<id,string> SetOverMap = new Map<id,String>();
        Map<id,integer> SetDelayDaysMap = new Map<id,Integer>();
        Map<Id, Account> uniqueAccountsMap = new Map<Id, Account>();

        Date todayDate = Date.today();
        List<Task> relatedTasks = [SELECT Id, WhatId, Subject, Type, Outcome__c, Status
                                   FROM Task
                                   WHERE Id IN :taskIds AND What.Type = 'Account' AND Type IN ('L1', 'L2', 'L3', 'L4', 'L5', 'L6')];

        Map<String, Integer> typeToDaysMap = new Map <String, Integer>{'L1' => 15, 'L2' => 60, 'L3' => 120, 'L4' => 180, 'L5' => 270, 'L6' => 335 };
         
        for(Task tsk : relatedTasks){
            accountIds.add(tsk.WhatId);
        }
        
        List<Account> accountsWithPaymentDates = [SELECT Id, Date_of_Payment__C FROM Account WHERE Recordtype.name ='IPV' AND Id IN :accountIds AND Date_of_Payment__C != null];
                    
        for (Account acc : accountsWithPaymentDates) {
            accDateOfPaymentMap.put(acc.Id, acc.Date_of_Payment__c);
        }

        for (Task task : relatedTasks) 
        {
            Account relatedAccount = new Account();
            relatedAccount.Id =task.WhatId;
            Id accountId = task.WhatId;
            
            Date paymentDate = accDateOfPaymentMap.get(relatedAccount.Id);
            Date dueDate = getDueDate(task.Type, paymentDate);
            
            if (task.Subject =='Call' && task.Outcome__c =='Call Done' && task.Status =='Completed') 
               {
                    String fieldToUpdate = task.Type +'_Call_status__c';
                    relatedAccount.put(fieldToUpdate, 'Completed');
                }
            
            if (task.Subject == 'Call' && task.Outcome__c =='Call Done' && task.Status !='Completed') {
                if (dueDate != null && dueDate < todayDate) {
                    setOverdueStatus(relatedAccount, task.Type);
                }
                setDelayedDays(relatedAccount, task.Type, dueDate, todayDate);
            }
            
            if (!uniqueAccountsMap.containsKey(accountId)) {
                uniqueAccountsMap.put(accountId, relatedAccount);
            }   
        }
        
        List<Account> accountsToUpdate = new List<Account>(uniqueAccountsMap.values());

        if (!accountsToUpdate.isEmpty() && accountsToUpdate.size() > 0) {
            update accountsToUpdate;
        }
    }
    
    private static Date getDueDate(String taskType, Date paymentDate) {
        
        Map<String, Integer> typeToDaysMap = new Map<String, Integer>{
            'L1' => 15, 'L2' => 60, 'L3' => 120, 'L4' => 180, 'L5' => 270, 'L6' => 335
        };
        return paymentDate != null && typeToDaysMap.containsKey(taskType) ? paymentDate.addDays(typeToDaysMap.get(taskType)) : null;
    }

    private static void setOverdueStatus(Account account, String taskType) {
        String statusField = taskType +'_Call_status__c';
        account.put(statusField,'Overdue');
 
    }

    private static void setDelayedDays(Account account, String taskType, Date dueDate, Date today) {
        if (dueDate != null) {
            String delayedDaysField = taskType +'_Call_delayed_days__c';
            Integer delayedDays = today > dueDate ? dueDate.daysBetween(today) : null;
            Account.put(delayedDaysField, delayedDays);
        }
    }
}