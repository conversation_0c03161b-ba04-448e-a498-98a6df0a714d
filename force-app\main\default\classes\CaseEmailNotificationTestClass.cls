//----------------------------------Test Class for ScheduledCaseUnresolvedEmailNotification AND ScheduledCaseUnassignedEmailNotification Class--------------------

@isTest
public class CaseEmailNotificationTestClass {

    @testSetup
    static void setup() {
        
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseController());
        
        
        API_Setting__c setting = new API_Setting__c();
        setting.Name = 'Test Setting';
        setting.Enable_Investment_API_Call__c = false;
        setting.Enable_Investor_API_Call__c = true;
        setting.Enable_Startup_API_Call__c = false;
        setting.Enable_Whatsapp_API_Call__c = true;
        setting.End_URL__c = 'test.com';
        insert setting;

        EmailTemplate emailTemp = [select id,name from EmailTemplate order by LastModifiedDate desc limit 1];
        EmailTemplate emailTempAssign = [select id,name from EmailTemplate where DeveloperName = 'New_Case_Assigned' limit 1];
            List<Email_Notification_Setting__c> emailSettingList = new List<Email_Notification_Setting__c>();
            Email_Notification_Setting__c emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Back_out_approved';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Back_out_Unapproved';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Commitment_Released_Confirmation';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Round_closed_deal_dropped';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'IPV_Fees_cheque';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'IPV_Membership_Fee_Commitment_for';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Renewal_IPV_Membership_Fee';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Waitlist_members_getting_Confirmed';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Residential_status_Committed';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Fnf_T1_Confirmation_members';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Fnf_T2_Confirmation_members';
            emailSettingList.add(emailSetting);
                        
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Status_Waitlist_Over_Subscription';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Status_Waitlist_Unapproved_Backout';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Reason_for_Waitlist_Pending_Documents';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Reason_for_Waitlist_IPV_Fee_Pending';
            emailSettingList.add(emailSetting);
            
            
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Inv_amount_received_confirmation';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'AIF_Investment_Amount_received';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Fnf_T2_Investment_Amount_Received';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTemp.ID;
            emailSetting.name = 'Fnf_T1_Investment_amount_Received';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'CASEASSIGNMENT';
            emailSettingList.add(emailSetting); 
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'CASERESOLVED';
            emailSettingList.add(emailSetting); 
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'CASEDUEDATEREVISED';
            emailSettingList.add(emailSetting); 
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'CASEUNASSIGNMENT';
            emailSettingList.add(emailSetting); 
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'CASEUNRESOLVEDSECONDDAY';
            emailSettingList.add(emailSetting); 
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'CASEUNRESOLVEDAFTER2DAYS';
            emailSettingList.add(emailSetting);
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'DUEDATEMEET48HOURS';
            emailSettingList.add(emailSetting);
            
            
            emailSetting = new Email_Notification_Setting__c();
            emailSetting.Email_Template__c = emailTempAssign.ID;
            emailSetting.name = 'DUEDATEMEET24HOURS';
            emailSettingList.add(emailSetting);
            
            
            
            
            
            insert emailSettingList;
        
        Account acc = TestFactory.createAccount();
        insert acc;
        System.debug('Account iD>>>>>>>>>> ' + Account.Id);
        
        User systemAdminUser = [SELECT Id, ProfileId, Profile.Name , Name FROM User WHERE Profile.Name = 'System Administrator' LIMIT 1];
        
        Contact cont = new Contact();
        cont.FirstName='Test';
        cont.LastName='Test';
        cont.Accountid= acc.id;
        cont.Investor_s_PAN__c = '**********';
        cont.AIF_Contributor__c = true;
        cont.Email = '<EMAIL>';
        cont.Account_Email_ID__c = '<EMAIL>';
        insert cont;
        System.debug('Contact iD>>>>>>>>>> ' + cont.Id);
        
        //for code coverage of ScheduledCaseUnresolvedEmailNotification class scenario
        Case cs1 = new Case();
        cs1.Issue_raised_By__c = acc.Id;
        cs1.Priority = 'High';
        cs1.Status ='WIP';
        cs1.Date_Issue_Raised__c = date.today();
        cs1.Description = 'test Case1';
        cs1.Relevant_Team__c = 'Tech Related';
        cs1.Date_of_issue_assigned_Internal__c = date.today()-2 ;
        cs1.Responsibility_to_solve_Internal__c = systemAdminUser.Id;
        cs1.Due_date_for_closure__c = date.today()+1;
        insert cs1;
       
        Case cs2 = new Case();
        cs2.Issue_raised_By__c = acc.Id;
        cs2.Priority = 'High';
        cs2.Status ='WIP';
        cs2.Date_Issue_Raised__c = date.today();
        cs2.Description = 'test Case2';
        cs2.Relevant_Team__c = 'Tech Related';
        cs2.Date_of_issue_assigned_Internal__c = date.today()-4 ;
        cs2.Responsibility_to_solve_Internal__c = systemAdminUser.Id; 
        insert cs2;
        
        //for code coverage of ScheduledCaseUnassignedEmailNotification class And ScheduledCaseWeeklyEmailNotification Class scenario
        Case cs3 = new Case();
        cs3.Issue_raised_By__c = acc.Id;
        cs3.Priority = 'High';
        cs3.Status ='Closed';
        cs3.Date_Issue_Raised__c = date.today();
        cs3.Description = 'test Case3';
        cs3.Date_of_issue_assigned_Internal__c = date.today()-1 ;
        cs3.Responsibility_to_solve_Internal__c = systemAdminUser.Id; 
        insert cs3;
    }
    
    @isTest
    static void CaseUnresolvedAndUnassignedEmailNotificationTest(){
        
        List<Case> caseToSendEmailList = [SELECT Id,Relevant_Team__c,Date_of_issue_assigned_Internal__c,Due_date_for_closure__c,OwnerId, Subject, Description, Owner.Email, Responsibility_to_Solve__r.email,Responsibility_to_solve_Internal__r.email FROM Case WHERE Status ='WIP' AND Relevant_Team__c !=null];
        
        Test.startTest();
        ScheduledCaseUnresolvedEmailNotification ces = new ScheduledCaseUnresolvedEmailNotification();
        ces.execute(null);
        Test.stopTest();
    }
    
    @isTest
    static void CaseUnassignedEmailNotificationTest(){
        
        List<Case> caseToSendEmailList =[SELECT Id, OwnerId, Subject, Description, Owner.Email, Responsibility_to_Solve__r.email FROM Case WHERE Complaint_Type__c = null AND Relevant_Team__c =null];
        
        Test.startTest();
        ScheduledCaseUnassignedEmailNotification ces = new ScheduledCaseUnassignedEmailNotification();
        ces.execute(null);
        Test.stopTest();
    }
    
    @isTest
    static void CaseWeeklyEmailNotificationTest(){
        
        List<Case> caseToSendEmailList = [SELECT Id,Case_Age__c,CaseNumber, Subject, Status, CreatedDate,Responsibility_to_solve_Internal__r.email,Owner.Name,Turnaround_Time__c,Date_of_issue_assigned_Internal__c,Relevant_Team__c FROM Case WHERE Date_of_issue_assigned_Internal__c= THIS_WEEK and Status in ('WIP','Closed') order by Relevant_Team__c];
        
        Test.startTest();
        ScheduledCaseWeeklyEmailNotification ces = new ScheduledCaseWeeklyEmailNotification();
        ces.execute(null);
        Test.stopTest();
    }
}