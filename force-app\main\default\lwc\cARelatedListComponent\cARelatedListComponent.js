import { LightningElement, wire, api } from 'lwc';
import getDocuments from '@salesforce/apex/CARelatedListController.getDocuments';
import getInvestments from '@salesforce/apex/CARelatedListController.getInvestments';
import getInvestmentsRedemption from '@salesforce/apex/CARelatedListController.getInvestmentsRedemption';
import getTransactions from '@salesforce/apex/CARelatedListController.getTransactions';
import getInvestors from '@salesforce/apex/CARelatedListController.getInvestors';
import getBankAccount from '@salesforce/apex/CARelatedListController.getBankAccount';
import getOffMarketInv from '@salesforce/apex/CARelatedListController.getOffMarketInv';
import getExit from '@salesforce/apex/CARelatedListController.getExit';
import getRePayments from '@salesforce/apex/CARelatedListController.getRePayments';

const contactColumns = [
    { label: 'Investor Name', fieldName: 'contactName', type: 'url',
    typeAttributes: { label: { fieldName: 'Name' }, target: '_blank'} },
    { label: 'Member Name', fieldName: 'accId', type: 'url',
    typeAttributes: { label: { fieldName: 'AccountName' }, target: '_blank'} },
    { label: 'Phone', fieldName: 'Phone',hideDefaultActions: true },
    { label: 'Email', fieldName: 'Email',type:'email',hideDefaultActions: true }, 
    { label: 'PAN', fieldName: 'Investor_s_PAN__c',hideDefaultActions: true }, 
    { label: 'Residential Status', fieldName: 'Residential_Status__c',hideDefaultActions: true }, 
];

const columns = [
    { label: 'Sr No.', fieldName: 'docName', type: 'url',
    typeAttributes: { label: { fieldName: 'Name' }, target: '_blank'} },
    { label: 'AIF Document Name', fieldName: 'Document_Name__c',hideDefaultActions: true },
    { label: 'AIF Document Initiation Date', fieldName: 'AIF_Document_Initiation_Date__c',type:'date',hideDefaultActions: true,
    typeAttributes: {
        day: "numeric",
        month: "numeric",
        year: "numeric"
    }  },
    { label: 'AIF Document Number', fieldName: 'AIF_Document_Number__c',hideDefaultActions: true },
    { label: 'Contributor Signing Date', fieldName: 'Contributor_Signing_Date__c',type:'date',hideDefaultActions: true,
    typeAttributes: {
        day: "numeric",
        month: "numeric",
        year: "numeric"
    }  },
    { label: 'Trustee Signing Date', fieldName: 'Trustee_Signing_Date__c',type:'date',hideDefaultActions: true,
    typeAttributes: {
        day: "numeric",
        month: "numeric",
        year: "numeric"
    }  },
    { label: 'FPC Signing Date', fieldName: 'FPC_Signing_Date__c',type:'date',hideDefaultActions: true,
    typeAttributes: {
        day: "numeric",
        month: "numeric",
        year: "numeric"
    }  },
    { label: 'Stamp Paper', fieldName: 'Stamp_Paper__c',hideDefaultActions: true },
    { label: 'AIF Document Amount', fieldName: 'AIF_Document_Amount__c',hideDefaultActions: true },
];

const investmentColumns = [
    { label: 'Investmnet Id', fieldName: 'investmentName', type: 'url',
    typeAttributes: { label: { fieldName: 'Name' }, target: '_blank'} },
    { label: 'Scheme Name', fieldName: 'roundName', type: 'url',
    typeAttributes: { label: { fieldName: 'Startup_Round__c' }, target: '_blank'} },
    //{ label: 'Scheme Name', fieldName: 'Startup_Round__c',hideDefaultActions: true },
    { label: 'Date of Allotment', fieldName: 'Date_of_Allotment__c',type:'date',hideDefaultActions: true,
    typeAttributes: {
        day: "numeric",
        month: "numeric",
        year: "numeric"
    } },
    { label: 'Units', fieldName: 'Number_of_Units_Held__c',hideDefaultActions: true },
    { label: 'Class', fieldName: 'Investment_AIF_Class__c',hideDefaultActions: true },
    { label: 'Type', fieldName: 'Type__c',hideDefaultActions: true },
    { label: 'Face Value', fieldName: 'FaceValue',hideDefaultActions: true },
    { label: 'Investment Amount', fieldName: 'Investment_Amount__c',hideDefaultActions: true },
];

const committedColumns = [
    { label: 'Investmnet Id', fieldName: 'investmentName', type: 'url',
    typeAttributes: { label: { fieldName: 'Name' }, target: '_blank'} },
    { label: 'Scheme Name', fieldName: 'roundName', type: 'url',
    typeAttributes: { label: { fieldName: 'Startup_Round__c' }, target: '_blank'} },
    //{ label: 'Scheme Name', fieldName: 'Startup_Round__c',hideDefaultActions: true },
    { label: 'Class', fieldName: 'Investment_AIF_Class__c',hideDefaultActions: true },
    { label: 'Type', fieldName: 'Type__c',hideDefaultActions: true },
    { label: 'Investment Amount', fieldName: 'Investment_Amount__c',hideDefaultActions: true },
];

const offMarketColumns = [
    { label: 'Investmnet Id', fieldName: 'investmentName', type: 'url',
    typeAttributes: { label: { fieldName: 'Name' }, target: '_blank'} },
    { label: 'Scheme Name', fieldName: 'roundName', type: 'url',
    typeAttributes: { label: { fieldName: 'Startup_Round__c' }, target: '_blank'} },
    //{ label: 'Scheme Name', fieldName: 'Startup_Round__c',hideDefaultActions: true },
    { label: 'Date of Transfer Deed', fieldName: 'Date_of_Allotment__c',type:'date',hideDefaultActions: true,
    typeAttributes: {
        day: "numeric",
        month: "numeric",
        year: "numeric"
    } },
    { label: 'Units', fieldName: 'Number_of_Units_Held__c',hideDefaultActions: true },
    { label: 'Class', fieldName: 'Investment_AIF_Class__c',hideDefaultActions: true },
    { label: 'Type', fieldName: 'Type__c',hideDefaultActions: true },
    { label: 'Face Value', fieldName: 'FaceValue',hideDefaultActions: true },
    { label: 'Investment Amount', fieldName: 'Investment_Amount__c',hideDefaultActions: true },
];

const redemptionColumns = [
    { label: 'Redemption Id', fieldName: 'investmentName', type: 'url',
    typeAttributes: { label: { fieldName: 'Name' }, target: '_blank'} },
    { label: 'Scheme Name', fieldName: 'roundName', type: 'url',
    typeAttributes: { label: { fieldName: 'Startup_Round__c' }, target: '_blank'} },
    //{ label: 'Scheme Name', fieldName: 'Startup_Round__c',hideDefaultActions: true },
    { label: 'Date of Redemption', fieldName: 'Date_of_SH_4__c',type:'date',hideDefaultActions: true,
    typeAttributes: {
        day: "numeric",
        month: "numeric",
        year: "numeric"
    }  },
    { label: 'Units', fieldName: 'Number_of_Units_Held__c',hideDefaultActions: true },
    { label: 'Class', fieldName: 'Investment_AIF_Class__c',hideDefaultActions: true },
    { label: 'Type', fieldName: 'Type__c',hideDefaultActions: true },
    { label: 'Face Value', fieldName: 'FaceValue',hideDefaultActions: true },
    { label: 'Investment Amount', fieldName: 'Investment_Amount__c',hideDefaultActions: true },
];

const rePaymentColumns = [
    { label: 'Investment Id', fieldName: 'investmentName', type: 'url',
    typeAttributes: { label: { fieldName: 'Name' }, target: '_blank'} },
    { label: 'Scheme Name', fieldName: 'roundName', type: 'url',
    typeAttributes: { label: { fieldName: 'Startup_Round__c' }, target: '_blank'} },
    //{ label: 'Scheme Name', fieldName: 'Startup_Round__c',hideDefaultActions: true },
    { label: 'Date of SH-4', fieldName: 'Date_of_SH_4__c',type:'date',hideDefaultActions: true,
    typeAttributes: {
        day: "numeric",
        month: "numeric",
        year: "numeric"
    }  },
    { label: 'Gross Amount Payable', fieldName: 'Exit_amount_to_be_transferred__c',hideDefaultActions: true },
    { label: 'Exit Fee', fieldName: 'Exit_Fee_received__c',hideDefaultActions: true },
    { label: 'Carry Fee', fieldName: 'Carry_fee_received__c',hideDefaultActions: true },
    { label: 'Total Amount Paid including TDS', fieldName: 'Total_Amount_Paid_Including_TDS__c',hideDefaultActions: true },
    { label: 'TDS Deducted', fieldName: 'TDS_Deducted__c',hideDefaultActions: true },
    { label: 'Amount Paid', fieldName: 'Net_Amount_Paid__c',hideDefaultActions: true },
];

const transactionColumns = [
    { label: 'Transaction Name', fieldName: 'transName', type: 'url',
    typeAttributes: { label: { fieldName: 'Name' }, target: '_blank'} },    
    { label: 'Transaction Date', fieldName: 'Transaction_Date__c',type:'date',hideDefaultActions: true,
        typeAttributes: {
            day: "numeric",
            month: "numeric",
            year: "numeric"
    } },
    { label: 'Virtual Account Number', fieldName: 'Virtual_Account_Number__c',hideDefaultActions: true },
    { label: 'Remitters Name', fieldName: 'Remitters_Name__c',hideDefaultActions: true },
    { label: 'Remitters Bank A/C', fieldName: 'Remitters_Bank_A_C__c',hideDefaultActions: true },
    { label: 'Remitters IFSC', fieldName: 'Remitters_IFSC__c',hideDefaultActions: true },
    { label: 'Mode of Payment', fieldName: 'Mode_of_Payment__c',hideDefaultActions: true },
    { label: 'Remitters Remark', fieldName: 'Remarks__c',hideDefaultActions: true },
    { label: 'UTR Number', fieldName: 'UTR_Number__c',hideDefaultActions: true },
    { label: 'Transaction Amount', fieldName: 'Amount__c',hideDefaultActions: true },
];

const feeColumns = [
    { label: 'Fee Id', fieldName: 'feeName', type: 'url',
    typeAttributes: { label: { fieldName: 'Name' }, target: '_blank'} },    
    { label: 'Scheme Name', fieldName: 'roundName', type: 'url',
    typeAttributes: { label: { fieldName: 'Startup_Round__c' }, target: '_blank'} },
    //{ label: 'Scheme Name', fieldName: 'Startup_Round__c', hideDefaultActions: true },
    { label: 'Date of Deduction', fieldName: 'Date_of_transaction__c', type:'date',hideDefaultActions: true,
    typeAttributes: {
        day: "numeric",
        month: "numeric",
        year: "numeric"
    } },   
    { label: 'Type', fieldName: 'Type__c',hideDefaultActions: true },
    { label: 'Fee Amount', fieldName: 'Exit_Fee_received__c', hideDefaultActions: true },
];

const bankColumns = [
    { label: 'Bank Id', fieldName: 'bankName', type: 'url',
    typeAttributes: { label: { fieldName: 'Name' }, target: '_blank'} }, 
    { label: 'Account Holder Name', fieldName: 'Account_Holder_Name__c', hideDefaultActions: true },    
    { label: 'Date of PD', fieldName: 'Date_of_PD__c', type:'date',hideDefaultActions: true,
    typeAttributes: {
        day: "numeric",
        month: "numeric",
        year: "numeric"
    } },    
    { label: 'Bank Name', fieldName: 'Bank_Name__c', hideDefaultActions: true },
    { label: 'Account Number', fieldName: 'Account_Number__c', hideDefaultActions: true },
    { label: 'Bank Account Type', fieldName: 'Bank_Account_Type__c', hideDefaultActions: true },
    { label: 'IFSC', fieldName: 'IFSC__c', hideDefaultActions: true },
    { label: 'Penny Drop', fieldName: 'Penny_Drop__c', hideDefaultActions: true },
    { label: 'Mapped with Demat', fieldName: 'Mapped_with_Demat__c', hideDefaultActions: true },
];

export default class cARelatedListComponent extends LightningElement {
@api recordId;
columns = columns; 
contactColumns = contactColumns;
investmentColumns = investmentColumns; 
offMarketColumns = offMarketColumns;
redemptionColumns = redemptionColumns; 
transactionColumns = transactionColumns;  
feeColumns = feeColumns;  
bankColumns = bankColumns;
rePaymentColumns = rePaymentColumns;
committedColumns = committedColumns;
bankData = [];
data = [];
contactData = [];
invData = [];
committedData = [];
offMarketData = [];
offMarketSales = [];
offMarketPurchase = [];
redData = [];
rePaymentData = [];
transactionData = [];
feeData = [];
transTotal = 0;
docTotal = 0;
invTotal = 0;
commitTotal = 0;
redTotal = 0;
offmTotal = 0;
feeTotal = 0;
offmsTotal = 0;
exitAmountTotal = 0;
exitFeeAmountTotal = 0;
carryFeeTotal = 0;
tdsAmountTotal = 0;
tdsDeductedTotal = 0;
netAmountTotal = 0;

@wire(getDocuments, { recordId : '$recordId'})
wiredData({ error, data }) {
if (data) {
   // this.data = data;
    let currentData = [];
    data.forEach((row) => {

    let rowData = {};
    console.log(row);
        console.log('inn');
        rowData.docName = '/' + row.Id;
        rowData.Name = row.Name;
        rowData.Document_Name__c = row.Document_Name__c; 
        rowData.Stamp_Paper__c = row.Stamp_Paper__c;
        rowData.AIF_Document_Initiation_Date__c = row.AIF_Document_Initiation_Date__c;
        rowData.AIF_Document_Number__c = row.AIF_Document_Number__c; 
        rowData.AIF_Document_Amount__c = row.AIF_Document_Amount__c;
        rowData.Contributor_Signing_Date__c = row.Contributor_Signing_Date__c;
        rowData.Trustee_Signing_Date__c = row.Trustee_Signing_Date__c;
        rowData.FPC_Signing_Date__c = row.FPC_Signing_Date__c;
        if(row.AIF_Document_Amount__c !== '' && row.AIF_Document_Amount__c !== null && row.AIF_Document_Amount__c !== undefined){
            this.docTotal = this.docTotal + row.AIF_Document_Amount__c;
        }
       
        currentData.push(rowData);
    });

    this.data = currentData;
    // Handle retrieved record
} else if (error) {
    console.log('error ::'+error);
    // Handle error
}
}

connectedCallback(){
this.getInvestorsData();
this.getInvestmentData();
this.getOffMarketData();
this.getInvestmentsRedemptionData();
this.getTransactionsData();
this.getBankData();
this.getExitData();
this.getRePaymentData();
}
getInvestorsData(){
getInvestors({ recordId : this.recordId })
.then(result => {
    // this.contactData = result;           
    let currentData = [];
    result.forEach((row) => {

        let rowData = {};
        console.log(row);
            console.log('inn');
            rowData.contactName = '/' + row.Id;
            rowData.Name = row.Name;
            rowData.accId = '/' + row.AccountId;
            rowData.AccountName = row.Account.Name; 
            rowData.Phone = row.Phone;
            rowData.Email = row.Email;
            rowData.Investor_s_PAN__c = row.Investor_s_PAN__c;
            rowData.Residential_Status__c = row.Residential_Status__c;
            //rowData.First_Holder__c = row.First_Holder__c;
        
        currentData.push(rowData);
    });

    this.contactData = currentData;
    console.log(this.contactData);
})
.catch(error => {
    // handle any errors
});
}
getInvestmentData(){
getInvestments({ recordId : this.recordId })
.then(result => {
    // this.invData = result;
    let currentData = [];
    let commitTable = [];
    result.forEach((row) => {

        let rowData = {};
        let comtdData = {};
        console.log(row);
        
        
        if (row.Startup_Round__r) {
            console.log('investment call');
            if(row.Type__c === 'Invested'){
                rowData.Name = row.Name;
                rowData.investmentName = '/' + row.Id;
                rowData.roundName = '/' +row.Startup_Round__c;
                rowData.Startup_Round__c = row.Startup_Round__r.Startup_Name__c;
                rowData.Date_of_Allotment__c = row.Date_of_Allotment__c;
                rowData.Number_of_Units_Held__c = row.Number_of_Units_Held__c;
                rowData.Investment_AIF_Class__c = row.Investment_AIF_Class__c;
                rowData.Type__c = row.Type__c;            
                rowData.Investment_Amount__c = row.Investment_Amount__c;    
                rowData.FaceValue = row.Startup_Round__r.Unit_Price__c;  
                currentData.push(rowData); 
                if(row.Investment_Amount__c !== undefined){                
                    this.invTotal = this.invTotal + row.Investment_Amount__c;  
                }  
            }
            else if(row.Type__c === 'Committed'){
                comtdData.Name = row.Name;
                comtdData.investmentName = '/' + row.Id;
                comtdData.roundName = '/' +row.Startup_Round__c;
                comtdData.Startup_Round__c = row.Startup_Round__r.Startup_Name__c;
                comtdData.Investment_AIF_Class__c = row.Investment_AIF_Class__c;
                comtdData.Type__c = row.Type__c;            
                comtdData.Investment_Amount__c = row.Final_Commitment_Amount__c;    
                commitTable.push(comtdData);
                if(row.Final_Commitment_Amount__c !== undefined){ 
                this.commitTotal = this.commitTotal + row.Final_Commitment_Amount__c;   
                } 
            }                     
        }
        
       
    });

    this.invData = currentData;
    this.committedData = commitTable;
    // this.redData = result;            
    console.log(this.invData); 
    // do something with the result
})
.catch(error => {
    // handle any errors
});
}

getExitData(){
    getExit({ recordId : this.recordId })
    .then(result => {        
        let feeTable = [];
        result.forEach((row) => {    
            
            let feeRow = {};
            console.log(row);
            
                console.log('in exit');                 
                feeRow.Name = row.Name; 
                feeRow.feeName = '/'+ row.Id;
                feeRow.roundName = '/' +row.Startup_Round__c;
                feeRow.Type__c = row.Type__c;
                feeRow.Startup_Round__c = row.Startup_Round__r.Startup_Name__c;
                feeRow.Date_of_transaction__c = row.Date_on_transfer_deed__c;                
                feeRow.Exit_Fee_received__c = row.Investment_Fee_Received__c;         
            
            feeTable.push(feeRow);
            if(row.Investment_Fee_Received__c !== '' && row.Investment_Fee_Received__c !== null && row.Investment_Fee_Received__c !== undefined){
                this.feeTotal = this.feeTotal + row.Investment_Fee_Received__c;
            }
                 
        });
    
        this.feeData = feeTable;           
        console.log(this.feeData);
    })
    .catch(error => {
        // handle any errors
    });
    }

getOffMarketData(){
    getOffMarketInv({ recordId : this.recordId })
    .then(result => {
        // this.invData = result;
        let currentData = [];
        let salesData = [];
        result.forEach((row) => {
    
            let rowData = {};
            let saleData = {};
            console.log(row);
            
            
            if (row.Startup_Round__r) {
                if(row.Type__c === 'Exit'){
                    console.log('in sales');
                    saleData.Name = row.Name;
                    saleData.investmentName = '/' + row.Id;
                    saleData.roundName = '/' +row.Startup_Round__c;
                    saleData.Startup_Round__c = row.Startup_Round__r.Startup_Name__c;
                    saleData.Date_of_Allotment__c = row.Date_on_Transfer_Deed__c;
                    saleData.Number_of_Units_Held__c = row.Number_of_Units_Held__c;
                    saleData.Investment_AIF_Class__c = row.Investment_AIF_Class__c;
                    saleData.Type__c = row.Type__c; 
                    saleData.FaceValue = row.Startup_Round__r.Unit_Price__c;
                    saleData.Investment_Amount__c = row.Investment_Amount__c; 
                    if(saleData.Investment_Amount__c !== undefined){
                        this.offmsTotal = this.offmsTotal + saleData.Investment_Amount__c;    
                    }   
                    salesData.push(saleData);             
                }else{
                    console.log('inn purchase');
                    rowData.Name = row.Name;
                    rowData.investmentName = '/' + row.Id;
                    rowData.roundName = '/' +row.Startup_Round__c;
                    rowData.Startup_Round__c = row.Startup_Round__r.Startup_Name__c;
                    rowData.Date_of_Allotment__c = row.Date_on_Transfer_Deed__c;
                    rowData.Number_of_Units_Held__c = row.Number_of_Units_Held__c;
                    rowData.Investment_AIF_Class__c = row.Investment_AIF_Class__c;
                    rowData.Type__c = row.Type__c; 
                    rowData.Investment_Amount__c = row.Considered_Investment_Amount__c;
                    rowData.FaceValue = row.Startup_Round__r.Unit_Price__c;                    
                    if(rowData.Investment_Amount__c !== undefined){
                        this.offmTotal = this.offmTotal + rowData.Investment_Amount__c;    
                    }  
                    currentData.push(rowData);  
                }              
            }
                 
        });
    
        this.offMarketData = currentData; 
        this.offMarketSales =  salesData;
        
        // this.redData = result;            
        console.log(this.offMarketData); 
        // do something with the result
    })
    .catch(error => {
        // handle any errors
    });
    }

getInvestmentsRedemptionData(){
getInvestmentsRedemption({ recordId : this.recordId })
.then(result => {
    let currentData = [];

    result.forEach((row) => {

        let rowData = {};
        console.log(row);
        // rowData.Name = row.Name;
        rowData.investmentName = '/' + row.Id;
        
        if (row.Startup_Round__r) {
            console.log('inn');
            rowData.Name = row.Name;
            rowData.roundName = '/' +row.Startup_Round__c; 
            rowData.Startup_Round__c = row.Startup_Round__r.Startup_Name__c;
            rowData.Date_of_SH_4__c = row.Date_of_SH_4__c;
            rowData.Type__c = row.Type__c;
            rowData.Number_of_Units_Held__c = row.Number_of_Units_Held__c;
            rowData.Investment_AIF_Class__c = row.Investment_AIF_Class__c;
            rowData.FaceValue = row.Startup_Round__r.Unit_Price__c;
            rowData.Investment_Amount__c = row.Investment_Amount__c;
            if(row.Investment_Amount__c !== undefined){
            this.redTotal = this.redTotal + row.Investment_Amount__c;
            }
        }
        currentData.push(rowData);
    });

    this.redData = currentData;

    // this.redData = result;            
    console.log(this.redData);            
    // do something with the result
})
.catch(error => {
    // handle any errors
});
}

getRePaymentData(){
    getRePayments({ recordId : this.recordId })
    .then(result => {
        let currentData = [];
         
        result.forEach((row) => {

            let rowData = {};
            console.log(row);
            // rowData.Name = row.Name;
            rowData.investmentName = '/' + row.Id;
            
            if (row.Startup_Round__r) {
                console.log('inn');
                rowData.Name = row.Name;
                rowData.roundName = '/' +row.Startup_Round__c; 
                rowData.Startup_Round__c = row.Startup_Round__r.Startup_Name__c;
                rowData.Date_of_SH_4__c = row.Date_of_SH_4__c;
                rowData.Type__c = row.Type__c;
                rowData.Exit_Fee_received__c = row.Exit_Fee_received__c;
                rowData.Exit_amount_to_be_transferred__c = row.Exit_amount_to_be_transferred__c;
                rowData.Carry_fee_received__c = row.Carry_fee_received__c;
                rowData.Total_Amount_Paid_Including_TDS__c = row.Total_Amount_Paid_Including_TDS__c;
                rowData.TDS_Deducted__c = row.TDS_Deducted__c;
                rowData.Net_Amount_Paid__c = row.Net_Amount_Paid__c;
                if(row.Exit_Fee_received__c !== undefined){
                    this.exitFeeAmountTotal = this.exitFeeAmountTotal + row.Exit_Fee_received__c;
                }
                if(row.Exit_amount_to_be_transferred__c !== undefined){
                    this.exitAmountTotal = this.exitAmountTotal + row.Exit_amount_to_be_transferred__c;
                }
                if(row.Carry_fee_received__c !== undefined){
                    this.carryFeeTotal = this.carryFeeTotal + row.Carry_fee_received__c;
                }
                if(row.Total_Amount_Paid_Including_TDS__c !== undefined){
                    this.tdsAmountTotal = this.tdsAmountTotal + row.Total_Amount_Paid_Including_TDS__c;
                }
                if(row.TDS_Deducted__c !== undefined){
                    this.tdsDeductedTotal = this.tdsDeductedTotal + row.TDS_Deducted__c;
                }
                if(row.Net_Amount_Paid__c !== undefined){
                    this.netAmountTotal = this.netAmountTotal + row.Net_Amount_Paid__c;
                }
            }
            currentData.push(rowData);
        });
        console.log('currentData :'+currentData);
        console.log('netAmountTotal :'+this.netAmountTotal);
        let totalAmtData = {};
        totalAmtData.investmentName = '';
        totalAmtData.roundName = ''; 
        totalAmtData.Startup_Round__c = '';
        totalAmtData.Date_of_SH_4__c = '';
        totalAmtData.Type__c = '';
        totalAmtData.Exit_Fee_received__c =  this.exitFeeAmountTotal;
        totalAmtData.Exit_amount_to_be_transferred__c = 'Total : ' + this.exitAmountTotal;
        totalAmtData.Carry_fee_received__c = this.carryFeeTotal;
        totalAmtData.Total_Amount_Paid_Including_TDS__c = this.tdsAmountTotal;
        totalAmtData.TDS_Deducted__c = this.tdsDeductedTotal;
        totalAmtData.Net_Amount_Paid__c = this.netAmountTotal;
        currentData.push(totalAmtData);
        this.rePaymentData = currentData;

        // this.redData = result;            
        console.log(this.rePaymentData);            
        // do something with the result
    })
    .catch(error => {
        // handle any errors
    });
}

getBankData(){
    getBankAccount({ recordId : this.recordId })
    .then(result => {
        //this.transactionData = result;
            
        let currentData = [];
        result.forEach((row) => {

        let rowData = {};
        console.log(row);
            console.log('inn bankcall');
            rowData.bankName = '/' + row.Id;
            rowData.Name = row.Name;
            rowData.Account_Holder_Name__c = row.Account_Holder_Name__c; 
            rowData.Date_of_PD__c = row.Date_of_PD__c;
            rowData.Bank_Name__c = row.Bank_Name__c;
            rowData.Account_Number__c = row.Account_Number__c; 
            rowData.Bank_Account_Type__c = row.Bank_Account_Type__c; 
            rowData.IFSC__c = row.IFSC__c;
            rowData.Penny_Drop__c = row.Penny_Drop__c;
            rowData.Mapped_with_Demat__c = row.Mapped_with_Demat__c;            
            currentData.push(rowData);
        });

        this.bankData = currentData;
        console.log(this.bankData);
    })
    .catch(error => {
        // handle any errors
    });
}

getTransactionsData(){
getTransactions({ recordId : this.recordId })
.then(result => {
    //this.transactionData = result;
    let total = 0;
    let currentData = [];
    result.forEach((row) => {

    let rowData = {};
    console.log(row);
        console.log('inn');
        rowData.transName = '/' + row.Id;
        rowData.Name = row.Name;
        rowData.Transaction_Date__c = row.Transaction_Date__c; 
        rowData.Virtual_Account_Number__c = row.Virtual_Account_Number__c; 
        rowData.Remitters_Name__c = row.Remitters_Name__c;
        rowData.Remitters_Bank_A_C__c = row.Remitters_Bank_A_C__c;
        rowData.Remitters_IFSC__c = row.Remitters_IFSC__c; 
        rowData.Amount__c = row.Amount__c;
        rowData.Mode_of_Payment__c = row.Mode_of_Payment__c;
        rowData.Remarks__c = row.Remarks__c;
        rowData.UTR_Number__c = row.UTR_Number__c;
        if(row.Amount__c !== undefined){
            this.transTotal = this.transTotal + row.Amount__c;
        }
        currentData.push(rowData);
    });
    
    this.transactionData = currentData;
    console.log(this.transactionData);
})
.catch(error => {
    // handle any errors
});
}

}