public class InvestorCAMappingHandler {
    public void bulkBefore(List<Investor_CA_Mapping__c> mappings)
    {
        Map<Id,integer> accCountMap = new map<id,integer>();
        set<Id> CAId = new set<Id>();
        set<Id> mappingId = new set<Id>();
        system.debug('called');
        for(Investor_CA_Mapping__c mapping : mappings){ 
            mappingId.add(mapping.Contribution_Agreement__c);
            list<Investor_CA_Mapping__c> listCA = [SELECT Id,Contribution_Agreement__c,Contribution_Agreement__r.Type__c
                                                  FROM Investor_CA_Mapping__c WHERE Contribution_Agreement__c = :mapping.Contribution_Agreement__c and
                                                  Contribution_Agreement__r.Type__c = 'Individual'];
            system.debug('called'+listCA);
            if(listCA.size() >= 1){
                mapping.addError('Only one investor can be attached to Individual type CA');
            }
            list<Investor_CA_Mapping__c> listCAJoint = [SELECT Id,Contribution_Agreement__c,Contribution_Agreement__r.Type__c
                                                  FROM Investor_CA_Mapping__c WHERE Contribution_Agreement__c = :mapping.Contribution_Agreement__c and
                                                  Contribution_Agreement__r.Type__c = 'Joint'];
            system.debug('called'+listCAJoint);
            if(listCAJoint.size() >= 2){
                mapping.addError('Only two investor can be attached to Joint type CA');
            }
        }    
        
    }
}