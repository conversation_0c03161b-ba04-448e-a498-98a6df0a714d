global class TransactionsOptAPIMock implements HttpCalloutMock {
    
    global HttpResponse respond(HttpRequest request){

        HttpResponse response = new HttpResponse();
        JSONGenerator gen = JSON.createGenerator(true);    
            gen.writeStartObject();      
            gen.writeString<PERSON>ield('TransactionAmount', '111');
            gen.writeString<PERSON>ield('Mode','test');
            gen.writeString<PERSON>ield('SendertoReceiverInformation','test');
            gen.writeStringField('RemitterAccountNumber','123');
            gen.writeStringField('RemitterIFSC_Code','123');
            gen.writeString<PERSON>ield('RemitterName','123');
            gen.writeString<PERSON>ield('UTRnumber','123');
            gen.writeStringField('VirtualAccountNumber','1233456');
            
            
            gen.writeEndObject();  
        String jsonData = gen.getAsString();
        response.setBody(jsonData);
        
        response.setHeader('Content-Type', 'application/json');
        response.setStatusCode(200);
        
        return response;
    }
}