/************************************************************************
    Test Class for ContactTrigger AND ContactTriggerHandler
************************************************************************/
@isTest(SeeAllData=false)
private class ContactTriggerTest{
    
    @testSetup static void setup() {
         API_Setting__c setting = new API_Setting__c();
        setting.Name = 'Test Setting';
        setting.Enable_Investment_API_Call__c = true;
        setting.Enable_Investor_API_Call__c = true;
        setting.Enable_Startup_API_Call__c = true;
        setting.End_URL__c = 'test.com';
        insert setting;
        
        Account testAccount = TestFactory.createAccount();
        insert testAccount;
        
        List<contact> contList = new List<Contact>();
        Contact cont = new Contact();
        cont.FirstName='Test';
        cont.LastName='Test';
        cont.Accountid= testAccount.id;
        cont.Investor_s_PAN__c = '**********';
        cont.AIF_Contributor__c = true;
        contList.add(Cont);
        insert contList; 
        
        //Added for Investor to Investment flow
        Startup__c StrObj = TestFactory.createStartUp();
        insert StrObj;
        
        Startup_Round__c str = new Startup_round__c ();
        str.Startup__c = StrObj.id;
        str.Lead_Analyst__c =testAccount.id;
        str.Lead_Member__c =testAccount.id;
        str.Date_Of_Founders_Call__c = date.newInstance(2020, 9, 15);
        str.Pre_Money_Valuation__c = 10;
        str.Doi_Percent_Fee__c = 11;
        str.Doi_Percent_Equity__c = 12;
        str.Investors_Call__c =date.newInstance(2022, 10, 12);
        str.Date_of_Investor_Call__c =date.newInstance(2022, 10, 10);
        insert str;

     }
    
    static testMethod void insertCase()
    {
        test.startTest();
            List<Contact> contList = new List<Contact>();
            Contact cont = new Contact();
            cont.FirstName='Test';
            cont.LastName='Test';
            cont.Accountid= [select id from account limit 1].id;
            cont.Investor_s_PAN__c = '**********';
            cont.AIF_Contributor__c = true;
            contList.add(cont);
          
            //Try catch is writtern because we will get "FIELD_CUSTOM_VALIDATION_EXCEPTION, You can not add more than 1 AIF Contributor for Account: []" error which is expected.
            try
            {
                insert contList;
                
            }
            catch(Exception ee)
            {
                system.debug('ee>>>>'+ee);
            }

        test.stopTest();
    }   
    
    static testMethod void updateCase()
    {
        test.startTest();
            List<Contact> contList = new List<Contact>();
          Contact cont = new Contact();
            cont.FirstName='Test';
            cont.LastName='Test';
            cont.Accountid= [select id from account limit 1].id;
            cont.Investor_s_PAN__c = '**********';
            cont.AIF_Contributor__c = false;
          Cont.Investor_Father_Husband_s_name__c = 'Test';
          Cont.Postal_Address__c ='Adress1';
          Cont.Phone = '**********';
            contList.add(cont);
        
          //for Investor to Investment flow.
          List<Investment__C> InvList = new list<Investment__c>();
       Investment__c Invt = New investment__c ();
          Invt.Investor__c = cont.id;
          invt.Startup_Round__c =[Select id from startup_round__c limit 1].id;
           invt.Type__c ='Invested';
          Invt.Investor_Type__c ='Via platform';
          Invt.Investor_s_PAN__c = Cont.Investor_s_PAN__c;
          Invt.Account__c = Cont.AccountId;
          Invt.Investor_Name__c = 'test2';
          Invlist.add(Invt);
          
          
            //Try catch is writtern because we will get "FIELD_CUSTOM_VALIDATION_EXCEPTION, You can not add more than 1 AIF Contributor for Account: []" error which is expected.
            try
            {
                insert contList;
                Insert InvList;
                contList[0].AIF_Contributor__c = true;
                ContList[0].Postal_Address__c = 'Addresss';
                update contList;
                Invlist[0].Postal_Address__c = ContList[0].Postal_Address__c;
                update InvList;
                Delete contList;

            }
            catch(Exception ee)
            {
                system.debug('ee>>>>'+ee);
            }
        test.stopTest();
    }  
    
    
}