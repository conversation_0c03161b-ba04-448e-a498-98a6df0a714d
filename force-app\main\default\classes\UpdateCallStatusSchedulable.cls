public class UpdateCallStatusSchedulable implements Schedulable {

    private static final Integer L1_DAYS = 15;
    private static final Integer L2_DAYS = 60;
    private static final Integer L3_DAYS = 120;
    private static final Integer L4_DAYS = 180;
    private static final Integer L5_DAYS = 270;
    private static final Integer L6_DAYS = 335;

    public void execute(SchedulableContext context) {
        Set<Id> accountIds = new Set<Id>();
        Map<Id, Date> accountIdToPaymentDateMap = new Map<Id, Date>();
		
        for (Task Rtask : [SELECT Id, WhatId FROM Task WHERE WhatId != null AND Type != null AND What.Type = 'Account' AND (Type ='L1' OR Type ='L2' OR Type ='L3' OR Type ='L4' OR Type ='L5' OR Type ='L6')]) {
            accountIds.add(Rtask.WhatId);
        }

        for (Account acc : [SELECT Id, Date_of_Payment__c FROM Account WHERE Date_of_Payment__c != null AND Id IN :accountIds]) {
            accountIdToPaymentDateMap.put(acc.Id, acc.Date_of_Payment__c);
        }

        List<Account> accountsToUpdate = new List<Account>();
        Set<id> uniquAccIds =  new set<id>();
        
        for (Task tasks : [SELECT Id,Type, WhatId FROM Task WHERE Type !=null AND Type !='' AND (Type ='L1' OR Type ='L2' OR Type ='L3' OR Type ='L4' OR Type ='L5' OR Type ='L6') AND WhatId IN :accountIds]) 
        {
            
            Id accountId = tasks.WhatId;
            Date paymentDate = accountIdToPaymentDateMap.get(accountId);
        
            system.debug('tasks.Type::' + tasks.Type);

            if (paymentDate != null && Date.today() > paymentDate) {
                Account relatedAccount = new Account(Id = accountId);
                
                if(tasks.Type != null && tasks.Type != '')
                {
                 Date dueDate;
                 String callStatusField;
         		
                if (tasks.Type =='L1') {
                    dueDate = paymentDate.addDays(L1_DAYS);
                    callStatusField = 'L1_Call_Status__c';
                } else if (tasks.Type =='L2') {
                    dueDate = paymentDate.addDays(L2_DAYS);
                    callStatusField = 'L2_Call_Status__c';
                } else if (tasks.Type == 'L3') {
                    dueDate = paymentDate.addDays(L3_DAYS);
                    callStatusField = 'L3_Call_Status__c';
                } else if (tasks.Type == 'L4') {
                    dueDate = paymentDate.addDays(L4_DAYS);
                    callStatusField = 'L4_Call_Status__c';
                } else if (tasks.Type == 'L5') {
                    dueDate = paymentDate.addDays(L5_DAYS);
                    callStatusField = 'L5_Call_Status__c';
                } else if (tasks.Type == 'L6') {
                    dueDate = paymentDate.addDays(L6_DAYS);
                    callStatusField = 'L6_Call_Status__c';
                } 
				system.debug('callStatusField::' + callStatusField);
                // Update Account if overdue
                if (//relatedAccount.get(callStatusField) !='Completed' && 
                    Date.today() > dueDate && callStatusField != null){
         
                    relatedAccount.put(callStatusField, 'Overdue');
                    relatedAccount.put(callStatusField.replace('Status__c', 'delayed_days__c'), calculateDayDifference(dueDate));
                    accountsToUpdate.add(relatedAccount);
                }
              }
            }
        }

        if (!accountsToUpdate.isEmpty()) {
            update accountsToUpdate;
        }
    }

    private Integer calculateDayDifference(Date dueDate) {
        return Date.today().daysBetween(dueDate);
    }
}