@isTest
public class StartupPipelineHandlerTest {
    
    @isTest
    Public static void testStartupPipelineHandler() {
        
        Blob evaluationFormBlob = Blob.valueOf('Dummy Excel Content');
        Test.startTest();
        
        Date datethirteenDaysAgo = Date.today().addDays(-13);
        Date datefifteenDaysAgo = Date.today().addDays(-15);
        
        Startup_Pipeline__c sp1 = new Startup_Pipeline__c(
            SP_Sector__c = 'D2C',
            SP_Startup_Name__c = 'test startup',
            SP_Founder_Email__c = '<EMAIL>',
            SP_Founder_Name__c = 'Test Founder',
            SP_Founder_Phone_Number__c = '9090908787',
            SP_Source__c = 'Website',
            SP_Startup_Received_on__c = datethirteenDaysAgo,
            Status_Post_Voting__c = 'Shortlisted',
            Status_of_1st_Meeting__c = 'Investor\'s Call Done',
            Rejection_Mail_Onboarding_Sent_on__c = datethirteenDaysAgo+12 
        );
        insert sp1;
        
        Startup_Pipeline__c sp2 = new Startup_Pipeline__c(
            SP_Sector__c = 'D2C',
            SP_Startup_Name__c = 'test startup',
            SP_Founder_Email__c = '<EMAIL>',
            SP_Founder_Name__c = 'Test Founder',
            SP_Founder_Phone_Number__c = '9090908781',
            SP_Source__c = 'Website',
            SP_Startup_Received_on__c = datefifteenDaysAgo,
            Status_Post_Voting__c = 'Rejected',
            Status_of_1st_Meeting__c = 'Moved to Physis',
            Status_of_Follow_up_Call__c = 'Tracking Progress',
            Rejection_Mail_Onboarding_Sent_on__c = datefifteenDaysAgo+20
            //SLA_Status__c = 'Breached'
        );
        insert sp2;
        
        sp1.Status_Post_Voting__c = 'Rejected';
        sp1.Status_of_1st_Meeting__c = 'Moved to Physis';
        sp1.Rejection_Mail_Onboarding_Sent_on__c = null;
        update sp1;
        
        sp2.Status_Post_Voting__c = 'Shortlisted';
        sp2.Status_of_Follow_up_Call__c = 'Dropped';
        sp2.Rejection_Mail_Onboarding_Sent_on__c = null;
        update sp2;
        
        StartupPipelineEmailScheduler ss = new StartupPipelineEmailScheduler();
        ss.execute(null);
        Test.stopTest();
    }
}