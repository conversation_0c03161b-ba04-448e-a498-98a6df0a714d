public class ContactTriggerHandler
{
    
    public void beforeInsert(List<contact> contList)
    {
        Map<Id,integer> accCountMap = new map<id,integer>();
        set<Id> CAId = new set<Id>();
        //Added wrapper class to get all possible rollup for contact here. 
        //RollUpContactWrapper rollUpContactWrapObj = getContactRollUPMap(contList);
        
        for(contact cc : contList){
            if(cc.Investor_s_PAN__c!=null && cc.Investor_s_PAN__c!='')
            {
                String panStr = ''+cc.Investor_s_PAN__c;
                panStr = panStr.trim();
                panStr = panStr.replaceAll( '\\s+', '');
                panStr = panStr.toUpperCase();
                System.debug('*********************' + panStr);
                cc.Investor_s_PAN__c = panStr;
                
                if(cc.AIF_Contributor__c && (cc.Primary_Contributor__c || cc.AIF_Contributor_Type__c=='Joint')){
                    cc.Virtual_A_C_Investor__c = 'FTPORT'+cc.Investor_s_PAN__c;
                }
            }
            //change for validation of CA 
            system.debug('in ::'+cc.AIF_Contributor_Type__c);
            system.debug('in ::'+cc.Contribution_Agreement__c);
            if(cc.Contribution_Agreement__c != null){
                
                list<Contribution_Agreement__c> listCA = [SELECT Id,Type__c FROM Contribution_Agreement__c WHERE Id = :cc.Contribution_Agreement__c];
                system.debug('in ::'+listCA);
                if(!listCA.isEmpty()){
                    if(listCA.size() > 1 && listCA[0].Type__c == 'Individual'){
                        cc.addError('Only one Contribution Agreement can be attached for Individual Investor');
                    }                    
                }
            }
            /* commented as per Chaitanya's request
if(rollUpContactWrapObj.accContactCountMap!=null && rollUpContactWrapObj.accContactCountMap.size()>0 && rollUpContactWrapObj.accContactCountMap.get(cc.AccountId) > 4)
{
cc.addERror('You can not add more than 5 contact for the account');
}
*/
            
            /* No need to show error because now we can have more than 1 AIF members we just need to show it on related list  
if(cc.AIF_Contributor__c && rollUpContactWrapObj.accAIFContributorMap!=null && rollUpContactWrapObj.accAIFContributorMap.size()>0 && rollUpContactWrapObj.accAIFContributorMap.get(cc.AccountId) > 0)
{
cc.addERror('You can not add more than 1 AIF Contributor for Account');
}
*/
            system.debug('Value of Postal Address' + cc.Postal_Address__c);
            if(cc.Contributor_Address__c == null){
                cc.Contributor_Address__c = cc.Postal_Address__c;
            }
            //Added by Karan to calculate Addendum amount calculation.
            if(cc.Initial_Amount_agreed__c == null){    
                cc.Initial_Amount_agreed__c = 0;
            if(cc.Addendum_Amount__c == null){
                cc.Addendum_Amount__c = 0;
             if(cc.Amount_Agreed_for_Contribution__c == null){
                cc.Amount_Agreed_for_Contribution__c = 0;
            
                cc.Amount_Agreed_for_Contribution__c = cc.Initial_Amount_agreed__c + cc.Addendum_Amount__c;
                system.debug('Value of Total'+ cc.Amount_Agreed_for_Contribution__c);
                system.debug('Value of A'+ cc.Initial_Amount_agreed__c);
                system.debug('Value of B'+ cc.Addendum_Amount__c);
             }
            }
            }
        }
        
        
    }
    
    
    public void beforeUpdate(List<contact> contList,Map<Id,Contact> oldMap)
    {
        
/* No need to show error because now we can have more than 1 AIF members we just need to show it on related list  
List<Contact> contactNewList = new List<Contact>();
for(Contact c : contList)
{
if(c.AIF_Contributor__c && c.AIF_Contributor__c!=oldMap.get(c.Id).AIF_Contributor__c)
{
contactNewList.add(c);
}
}

if(contactNewList!=null && contactNewList.size()>0)
{
RollUpContactWrapper rollUpContactWrapObj = getContactRollUPMap(contactNewList);

if(rollUpContactWrapObj!=null && rollUpContactWrapObj.accAIFContributorMap!=null && rollUpContactWrapObj.accAIFContributorMap.size()>0)
{
for(contact cc : contList){                
if(rollUpContactWrapObj.accAIFContributorMap!=null && rollUpContactWrapObj.accAIFContributorMap.size()>0 && rollUpContactWrapObj.accAIFContributorMap.get(cc.AccountId) > 0)
{
cc.addERror('You can not add more than 1 AIF Contributor for Account');
}
}
}
}
*/
        //Added by Karan to calculate Addendum amount calculation.
        for(contact c : contList){
            system.debug('in ::'+c.AIF_Contributor_Type__c);
            system.debug('in ::'+c.Contribution_Agreement__c);
            if(c.AIF_Contributor_Type__c == 'Individual' && c.Contribution_Agreement__c != null){
                
                list<Contribution_Agreement__c> listCA = [SELECT Id FROM Contribution_Agreement__c WHERE Id = :c.Contribution_Agreement__c];
                system.debug('in ::'+listCA);
                if(!listCA.isEmpty() && listCA.size() > 1){
                    c.addError('Only one Contribution Agreement can be attached for Individual Investor');
                }
            }
            system.debug('Value of Previous Addendum'+ oldMap.get(c.Id).Addendum_Amount__c);
            system.debug('Value of Addendum'+ c.Amount_Agreed_for_Contribution__c);
            if(c.Addendum_Amount__c != null && c.Addendum_Amount__c != oldMap.get(c.Id).Addendum_Amount__c)
            {
                if(c.Amount_Agreed_for_Contribution__c == null){
                    c.Amount_Agreed_for_Contribution__c = 0;
                }
                c.Amount_Agreed_for_Contribution__c = c.Amount_Agreed_for_Contribution__c + c.Addendum_Amount__c;            
            }
            if(c.Initial_Amount_agreed__c != null && c.Initial_Amount_agreed__c != oldMap.get(c.Id).Initial_Amount_agreed__c)
            {
                system.debug('Value of Initial Amount' + c.Initial_Amount_agreed__c);
                if(c.Initial_Amount_agreed__c == null){
                    c.Initial_Amount_agreed__c = 0;
                } else if(c.Addendum_Amount__c == null){
                    c.Addendum_Amount__c = 0;
                }
                system.debug('Value of Amount Agreed' + c.Amount_Agreed_for_Contribution__c);
                if(c.Amount_Agreed_for_Contribution__c == null){
                    c.Amount_Agreed_for_Contribution__c = 0;
                }
                c.Amount_Agreed_for_Contribution__c = c.Initial_Amount_agreed__c + c.Addendum_Amount__c;            
            }
            
        }
    }
    
    
    public void afterInsert(List<contact> contList)
    {
        system.debug('contact afterinsert:');
        set<Id> contactIDSet1 = new set<Id>();
        set<Id> contactIDSet2 = new set<Id>();
        
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('Contact trigger API_Setting__c>>>'+settingList);
        if(settingList!=null && settingList.size()>0 && settingList[0].Enable_Investor_API_Call__c)
        {
            Integer count = 0;
            for(contact con : contList){
                if(count < 100)
                    contactIDSet1.add(con.Id);
                else
                    contactIDSet2.add(con.Id);
                
                count ++;
            }
            
            //To send max 100 records at a time to API
            if(contactIDSet1.size()>0)
                ContactRestAPIController.sendInvestorDetails(contactIDSet1);
            if(contactIDSet2.size()>0)
                ContactRestAPIController.sendInvestorDetails(contactIDSet2);
        }    
        /* No need to updated Contributing Member field because it can be more than one showing checkbox in related list updateContributingMember(contList); Date:21-08-2021 */
    }
    public void afterUpdate(List<contact> contList,Map<id,contact> oldMap)
    {
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('Contact trigger API_Setting__c>>>'+settingList);
        
        if(settingList!=null && settingList.size()>0 && settingList[0].Enable_Investor_API_Call__c)
        {
            set<Id> contactIDSet1 = new set<Id>();
            set<Id> contactIDSet2 = new set<Id>();
            
            Integer count = 0;
            for(contact con : contList){
                if(con.Investor_SIP_Balance__c==oldMap.get(con.Id).Investor_SIP_Balance__c)
                {
                    if(count < 100)
                        contactIDSet1.add(con.Id);
                    else
                        contactIDSet2.add(con.Id);
                    
                    count ++;
                }
                
            }
            
            //To send max 100 records at a time to API
            if(contactIDSet1.size()>0 &&  !System.isFuture())
                ContactRestAPIController.sendInvestorDetails(contactIDSet1);
            if(contactIDSet2.size()>0 &&  !System.isFuture())
                ContactRestAPIController.sendInvestorDetails(contactIDSet2);
            
        }   
        
        // As discussed with Akash now we need to call the same APIs for both the scenario ContactRestAPIController.updateInvestorDetails(contactID);
        //system.debug('contact afterupdate:');        
        //updateContributingMember(contList);
        
        //Added by Ankush for Investor to Investment flow 21/12/2022
        List<id> ContIds = new List<id>();
        List<Investment__c> invToupdate = new List<Investment__c>();
        Map<id,Contact> ContMap = new Map<id,contact>();

        for(Contact cont : ContList)
        {
             if(cont.Investor_s_PAN__c != oldMap.get(cont.id).Investor_s_PAN__c 
              || cont.Investor_Father_Husband_s_name__c != oldMap.get(cont.id).Investor_Father_Husband_s_name__c
              || cont.Postal_Address__c != oldMap.get(cont.id).Postal_Address__c
              || cont.Email != oldMap.get(cont.id).Email
              || cont.Phone != oldMap.get(cont.id).Phone
              || cont.name != oldMap.get(cont.id).name)
           {
             ContIds.add(Cont.Id);  
             ContMap.put(cont.id,Cont);
             
           }
        }
        
        System.debug('InvPhone>>' +ContMap );
        if(ContIds != null && ContIds.Size() > 0)
        {
        
         List<investment__c> Invt=[Select id,Investor_Name__c,Email_ID__c,Investor__c,Investor_s_PAN__c,Investor_Father_Husband_s_name__c,Postal_Address__c,Phone_No__c from investment__c where Investor__C In: ContIds];
            //System.debug('InvestorName>>>'+ Invt.Investor_Name__c);
            
            For(Investment__C Inv :Invt)
            {
                try{
                Investment__c Invts = new Investment__c();
                Invts.id = Inv.id;
                //String num =ContMap.get(Inv.Investor__c).Phone;
                Invts.Investor_Name__c = ContMap.get(Inv.Investor__c).Investor_Full_Name__c;
               // Invts.Phone_No__c = String.ValueOf(ContMap.get(Inv.Investor__c).Account_Phone_No__c);
                Invts.Email_ID__c = ContMap.get(Inv.Investor__c).Email;
                Invts.Investor_s_PAN__c = ContMap.get(Inv.Investor__c).Investor_s_PAN__c;
                Invts.Investor_Father_Husband_s_name__c = ContMap.get(Inv.Investor__c).Investor_Father_Husband_s_name__c;
                Invts.Postal_Address__c = ContMap.get(Inv.Investor__c).Postal_Address__c;
                system.debug('Inv Phone to update>>'+Invts.Phone_No__c );
                invToupdate.add(Invts);
                }	Catch(Exception e)
                {
                    system.debug('Investor update Exception>>>>>>'+e.getMessage());
                }
            }
            if(InvToUpdate != null && InvToupdate.size()>0)
            {
                Update invToupdate;
                InvestmentTriggerHandler.isPointTransactionCreationAllowed = false;
                system.debug('Updated Inv>>>'+ invToupdate);
            }
         }
        
             
    }
    
    public void afterDelete(List<contact> conList)
    {
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('Contact trigger API_Setting__c>>>'+settingList);
        if(settingList!=null && settingList.size()>0 && settingList[0].Enable_Investor_API_Call__c)
        {
            set<Id> idsToDelete1 = new set<Id>();
            set<Id> idsToDelete2 = new set<Id>();
            
            Integer count = 0;
            for(contact con : conList)
            {
                if(count < 100)
                    idsToDelete1.add(con.Id);
                else
                    idsToDelete2.add(con.Id);
                
                count ++;
            }
            
            //To send max 100 records at a time to API
            if(idsToDelete1.size()>0)
                RestLoginController.genericBulkDeleteAPI(idsToDelete1,'investor_salesforce_id','userInvestorRoutes/deleteUserInvestorUsingSalesforceId');
            if(idsToDelete2.size()>0)
                RestLoginController.genericBulkDeleteAPI(idsToDelete2,'investor_salesforce_id','userInvestorRoutes/deleteUserInvestorUsingSalesforceId');            
        }
    }
    // Added by Jay Dabhi on 9th September 2024 for handling user activity based on Investor Aif changes
    public void hanleInvestorActivities(List<Contact> Invlist, Map<Id, Contact> OldInvmap) {
    List<User_Activity__c> activitiesToInsert = new List<User_Activity__c>();
    Id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
    
    for (Contact InvRecord : Invlist) {
      
        if (InvRecord.AIF_Contributor__c == true) {
       
            if (OldInvmap != null && OldInvmap.containsKey(InvRecord.Id)) {
                Contact oldInv = OldInvmap.get(InvRecord.Id);
                if (oldInv.AIF_Contributor__c == false) {
                    String TypeUrl = URL.getOrgDomainURL().toExternalForm() + '/' + InvRecord.Id;
                    activitiesToInsert.add(new User_Activity__c(
                        Related_Account__c = InvRecord.AccountId,
                        Activity_Type__c = 'Onboarded on AIF',
                        Activity_Detail_RICH__c = 
                            (TypeUrl != null && TypeUrl != '' 
                                ? '<a href="' + TypeUrl + '" target="_blank">' + InvRecord.FirstName + ' ' + InvRecord.LastName + '</a>' 
                                : InvRecord.FirstName + ' ' + InvRecord.LastName) 
                            + ' Onboarded on AIF',
                        Time_Stamp__c = System.now()
                    ));
                }
            }
        }
    
    }
    if (!activitiesToInsert.isEmpty()) {
        insert activitiesToInsert;
    }
    
}
     
    
    
    /* No need to updated Contributing Member field because it can be more than one showing checkbox in related list Date:21-08-2021 
public void updateContributingMember(List<contact> contList){
Map<Id,String> accountToUpdate = new Map<Id,String>();
for(contact con : contList){
system.debug('con :'+con.AIF_Contributor__c+'  '+con.LastName);
if(con.AIF_Contributor__c == true){
String Name = con.FirstName!=null ? con.FirstName+ ' ' + con.LastName : con.LastName;
accountToUpdate.put(con.AccountID,Name);
}  else{
if(Trigger.isUpdate) accountToUpdate.put(con.AccountID,'');
}       
}

if(accountToUpdate!=null && accountToUpdate.size()>0)
updateAccount(accountToUpdate);
}

Public void updateAccount(Map<Id,String> accountToUpdate)
{
List<Account> accounts = [SELECT Id,Contributing_Member__c FROM Account WHERE Id IN : accountToUpdate.keySet()];
for(Account acc : accounts){
system.debug('name :'+accountToUpdate.get(acc.Id));
acc.Contributing_Member__c = accountToUpdate.get(acc.Id);
}
if(!accounts.isEmpty()){
update accounts;
}
}


public RollUpContactWrapper getContactRollUPMap(List<contact> contList)
{
Set<Id> parentAccIdSet = new Set<Id>();

RollUpContactWrapper rollUpContactWrapObj = new RollUpContactWrapper();
//rollUpContactWrapObj.accAIFContributorMap = new map<id,integer>();
rollUpContactWrapObj.accContactCountMap = new map<id,integer>();

for(contact cc : contList){
if(cc.AccountId!=null)
{
parentAccIdSet.add(cc.AccountId);
}
}

if(parentAccIdSet!=null && parentAccIdSet.size()>0)
{
for(contact cc : [select id,AccountId,AIF_Contributor__c from contact where AccountId in : parentAccIdSet])
{


if(rollUpContactWrapObj.accContactCountMap.containsKey(cc.AccountId))
rollUpContactWrapObj.accContactCountMap.put(cc.AccountId,rollUpContactWrapObj.accContactCountMap.get(cc.AccountId)+1);
else
rollUpContactWrapObj.accContactCountMap.put(cc.AccountId,1);
}

}

system.debug('rollUpContactWrapObj>>>>'+rollUpContactWrapObj);
return rollUpContactWrapObj;
}

public class RollUpContactWrapper{
Map<Id,integer> accContactCountMap{get; set;}
public RollUpContactWrapper()
{
}
}
*/
}