<template>
    <lightning-card title="Account Activities">
        <!-- Top-right corner buttons -->
        <div class="slds-grid slds-grid_align-end slds-p-around_medium">
            <lightning-button-icon icon-name="utility:filterList" alternative-text="Filter" onclick={openModal} class="slds-m-right_small"></lightning-button-icon>
            <lightning-button-icon icon-name="utility:download" alternative-text="Download" onclick={handleDownload}></lightning-button-icon>
        </div>

        <!-- Modal for Filters -->
        <template if:true={isModalOpen}>
            <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container">
                    <header class="slds-modal__header">
                        <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={closeModal}>
                            <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                            <span class="slds-assistive-text">Close</span>
                        </button>
                        <h2 class="slds-text-heading_medium">Filter Activities</h2>
                    </header>
                    <div class="slds-modal__content slds-p-around_medium">
                        <lightning-tabset>
                            <lightning-tab label="CRM Activity" active={isTab1} data-tab="tab1" onclick={handleTabChange}>
                                <!-- Filters Tab -->
                                <lightning-input type="checkbox" label="Industry change" value="Industry change" onchange={handleFilterChange}></lightning-input>
                                <lightning-input type="checkbox" label="Ownership change" value="Ownership change" onchange={handleFilterChange}></lightning-input>
                                <lightning-input type="checkbox" label="Membership Status Change" value="Membership Status Change" onchange={handleFilterChange}></lightning-input>
                                <lightning-input type="checkbox" label="Refered By change" value="Refered By change" onchange={handleFilterChange}></lightning-input>
                                <lightning-input type="checkbox" label="RM change" value="RM change" onchange={handleFilterChange}></lightning-input>
                            </lightning-tab>
                            <lightning-tab label="Date Range" active={isTab2} data-tab="tab2" onclick={handleTabChange}>
                                <!-- Date Range Tab -->
                                <lightning-input type="date" label="Start Date" name="startDate" onchange={handleDateChange}></lightning-input>
                                <lightning-input type="date" label="End Date" name="endDate" onchange={handleDateChange}></lightning-input>
                            </lightning-tab>
                            <lightning-tab label="App Activity" active={isTab3} data-tab="tab3" onclick={handleTabChange}>
                                <!-- Placeholder for future functionality -->
                                <lightning-input type="checkbox" label="App Sign In" value="App Sign In"></lightning-input>
                                <lightning-input type="checkbox" label="Query Raised" value="Query Raised"></lightning-input>
                                <lightning-input type="checkbox" label="Discussion" value="Discussion"></lightning-input>
                                <lightning-input type="checkbox" label="Commitment" value="Commitment"></lightning-input>
                            </lightning-tab>
                        </lightning-tabset>
                    </div>
                    <footer class="slds-modal__footer">
                        <lightning-button class="slds-p-around_medium" variant="neutral" label="Clear all" onclick={clearCheckboxes}></lightning-button>
                        <lightning-button variant="brand" label="Apply" onclick={applyFilters}></lightning-button>
                    </footer>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </template>

        <!-- Display Activities -->
        <template if:true={groupedActivities}>
            <template for:each={groupedActivities} for:item="activityGroup">
                <lightning-accordion key={activityGroup.date}>
                    <lightning-accordion-section
                        name={activityGroup.date}
                        label={activityGroup.date}
                        key={activityGroup.date}>
                        <template for:each={activityGroup.activities} for:item="activity">
                            <div key={activity.activityTimestamp} class="slds-p-around_medium activity-item">
                                <lightning-icon icon-name={activity.iconName} alternative-text="activity icon" size="x-small"></lightning-icon>&nbsp;
                                <div class="activity-detail">{activity.activityDetail}</div>
                                <div class="activity-timestamp">{activity.activityTimestamp}</div>
                            </div>
                        </template>
                    </lightning-accordion-section>
                </lightning-accordion>
            </template>
           
        </template>
       
        <template if:false={groupedActivities}>
            <p>No activities found.</p>
        </template>
        <div class="slds-grid slds-grid_align-end slds-p-around_medium">
          
            <lightning-button label="Previous" icon-name="utility:chevronleft"  class=" slds-p-around_medium" data-type="previous" onclick={previousHandler} disabled={disablePrevious}></lightning-button>
            <lightning-button label="Next" icon-name="utility:chevronright" class=" slds-p-around_medium" data-type="next"  onclick={nextHandler} disabled={disableNext}></lightning-button>
           
        
        </div>
    </lightning-card>
</template>