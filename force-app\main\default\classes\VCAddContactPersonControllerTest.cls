/************************************************************************
    Test Class for VCAddContactPersonController
************************************************************************/
@isTest(SeeAllData=false)
private class VCAddContactPersonControllerTest{
    
    @testSetup static void setup() {
        Startup__c st = TestFactory.createStartUp();
        insert st;          
        Venture_Connect__c vc = TestFactory.createVentureConnect();
        insert vc;          
    }
         
    static testMethod void VCObjcase()
    {
        test.startTest();
            Venture_Connect__c stObj = [select id,name,Contact_Person_Details__c from Venture_Connect__c limit 1];
             
            ApexPages.StandardController sc = new ApexPages.StandardController(stObj);
            VCAddContactPersonController ctrlObj = new VCAddContactPersonController(sc); 
            ctrlObj.cPersonWrapList[0].contactPerson = 'testing';
            ctrlObj.cPersonWrapList[0].emailID = '<EMAIL>';
            ctrlObj.cPersonWrapList[0].phoneNo = '12345678';
            ctrlObj.cPersonWrapList[0].linkedINURL = 'testing.com';
            
            Test.setCurrentPageReference(new PageReference('Page.myPage')); 
            System.currentPageReference().getParameters().put('index', '0');
            
            boolean f = VCAddContactPersonController.validateEmail(''+ctrlObj.cPersonWrapList[0].emailID);
            ctrlObj.saveAction();
            ctrlObj.removeRow();
        	ctrlObj.getLocationOptions();
            ctrlObj.getSectorFocus();
        	ctrlObj.getMarketSegment();
        	ctrlObj.getLeadOptions();
       	    ctrlObj.getMentorOptions();
                        
        test.stopTest();
    }    
}