import { LightningElement, api, track, wire } from 'lwc';
import getActivities from '@salesforce/apex/TimelineActivityLogController.getActivities';

const PAGE_SIZE = 100; // Number of activities per page

export default class ActivityTimeline extends LightningElement {
    @api recordId;
    @track activities = [];
    @track groupedActivities = [];
    @track isModalOpen = false;


    @track tempStartDate = null;
    @track tempEndDate = null;

    @track selectedFilters = [];
    @track startDate = null;
    @track endDate = null;
    @track openAccordions = {};
    @track activeTab = '';
    @track currentPage = 1;
    @track totalPages = 1;

    @track selectedValues = [];
    @track selectedValuesApp = [];

    activityIcons = {
        'Referred a new lead': 'utility:org_chart',
        'Member Converted to Trial from his/her referral': 'utility:adduser',
        'Member Converted to Paid from his/her referral': 'utility:people',
        'Member Converted to Complimentary from his/her referral': 'utility:moneybag',
        'Attendance - FC, IC, Events, Feedback Form, and all attendance that gets uploaded': 'utility:event',
        'Commitment Done': 'utility:locker_service_api_viewer',
        'Investment Done': 'utility:checkout',
        'RM Change': 'utility:change_owner',
        'Membership Slab Change (Gold/Silver/Bronze)': 'utility:change_request',
        'Onboarded on Premier Status': 'utility:adduser',
        'Onboarded on AIF': 'utility:privately_shared',
        'Onboarded on AIM+': 'utility:privately_shared',
        'Onboarded on LetsGrow as Investor Mentor': 'utility:trailblazer_ext',
        'Raised a query via Raise a query or Case created on CRM manually': 'utility:record_create',
        'Query Resolved': 'utility:retail_execution',
        'New Payment': 'utility:money',
        'Renewal Payment': 'utility:money',
        'Membership Status Change': 'utility:change_record_type',
        'Startup Tab': 'utility:touch_action',
        'Agenda Tab': 'utility:touch_action',
        'Sign in': 'utility:touch_action',
        'Refer a Member': 'utility:touch_action',
        'Discussion Add': 'utility:touch_action',
        'Discussion Add Comment': 'utility:touch_action',
        'Commitment Form Link': 'utility:touch_action',
        'Contact Your RM': 'utility:touch_action',
        'FAQs': 'utility:touch_action',
        'Feedback Form Link': 'utility:touch_action',
        'Leaderboard': 'utility:touch_action',
        'Favourite Startup': 'utility:touch_action',
        'Portfolio Tab': 'utility:touch_action',
        'Refer a Startup': 'utility:touch_action',
        'Raise a Query': 'utility:touch_action',
        'Startup Call Recording': 'utility:touch_action',
        'Portfolio Excel Export': 'utility:touch_action',
        'Startup Detail': 'utility:touch_action',
        'Startup - Share/Download': 'utility:touch_action',
        'Agenda Event Details': 'utility:touch_action',
        'App User Duration': 'utility:touch_action',
        'Startup - download': 'utility:touch_action',
        'Startup - Share': 'utility:touch_action',
    };
    checkboxOptionsGroup = [
        { label: 'Referred a new lead', value: 'Referred a new lead' },
        { label: 'Investment Done', value: 'Investment Done' },
        { label: 'Onboarded on AIM+', value: 'Onboarded on AIM+' },
        { label: 'New Payment', value: 'New Payment' },
        { label: 'Renewal Payment', value: 'Renewal Payment' },
        { label: 'Relationship Manager change', value: 'RM change' },
        { label: 'Membership Status Change', value: 'Membership Status Change' },
        { label: 'Onboarded on AIF', value: 'Onboarded on AIF' },
        { label: 'Membership Slab Change', value: 'Membership Slab Change (Gold/Silver/Bronze)' },
        { label: 'Commitment Done', value: 'Commitment Done' },
        { label: 'Onboarded on LetsGrow', value: 'Onboarded on LetsGrow as Investor Mentor' },
        { label: 'Onboarded on Premier Status', value: 'Onboarded on Premier Status' },
        { label: 'Attendance Event', value: 'Attendance - FC, IC, Events, Feedback Form, and all attendance that gets uploaded' },
        { label: 'Raised a query', value: 'Raised a query via Raise a query or Case created on CRM manually' },
        { label: 'Query Resolved', value: 'Query Resolved' },
        { label: 'Member Converted to Trial', value: 'Member Converted to Trial from his/her referral' },
        { label: 'Member Converted to Paid', value: 'Member Converted to Paid from his/her referral' },
        { label: 'Member Converted to Complimentary', value: 'Member Converted to Complimentary from his/her referral' },
    ];
    checkboxOptionsappGroup = [
        { label: 'Startup Tab', value: 'Startup Tab' },
        { label: 'Agenda Tab', value: 'Agenda Tab' },
        { label: 'Sign in', value: 'Sign in' },
        { label: 'Refer a Member', value: 'Refer a Member' },
        { label: 'Discussion Add', value: 'Discussion Add' },
        { label: 'Discussion Add Comment', value: 'Discussion Add Comment' },
        { label: 'Commitment Form Link', value: 'Commitment Form Link' },
        { label: 'Contact Your RM', value: 'Contact Your RM' },
        { label: 'FAQs', value: 'FAQs' },
        { label: 'Feedback Form Link', value: 'Feedback Form Link' },
        { label: 'Leaderboard', value: 'Leaderboard' },
        { label: 'Favourite Startup', value: 'Favourite Startup' },
        { label: 'Portfolio Tab', value: 'Portfolio Tab' },
        { label: 'Refer a Startup', value: 'Refer a Startup' },
        { label: 'Raise a Query', value: 'Raise a Query' },
        { label: 'Startup Call Recording', value: 'Startup Call Recording' },
        { label: 'App User Duration', value: 'App User Duration' },
        { label: 'Agenda Event Details', value: 'Agenda Event Details' },
        { label: 'Startup - Share', value: 'Startup - Share' },
        { label: 'Startup - download', value: 'Startup - download' },
        { label: 'Startup Detail', value: 'Startup Detail' },
        { label: 'Portfolio Excel Export', value: 'Portfolio Excel Export' },
        
    ];
    // Handle checkbox changes for CRM Activity
    handleCheckboxChange(event) {
        this.selectedValues = event.detail.value;
        console.log('CRM selectedValues: ', this.selectedValues);
    }

    // Handle checkbox changes for App Activity
    handleCheckboxAppChange(event) {
        this.selectedValuesApp = event.detail.value;
        console.log('App selectedValues: ', this.selectedValuesApp);
    }

    // Clear selections for both tabs
    clearAllCRM() {
        this.selectedValues = [];
    }
    clearAllDate() {
        this.tempStartDate = null;
        this.tempEndDate = null;
        this.dateError = '';
    }
    clearAllApp() {
        this.selectedValuesApp = [];
    }
    // Select all for CRM Activity
    selectAllCRM() {
        this.selectedValues = this.checkboxOptionsGroup.map(option => option.value);
    }
    // Select all for App Activity
    selectAllApp() {
        this.selectedValuesApp = this.checkboxOptionsappGroup.map(option => option.value);
    }

    @wire(getActivities, {
        accountId: '$recordId',
        activityTypes: '$selectedFilters',
        startDate: '$startDate',
        endDate: '$endDate'
    })
    wiredActivities({ error, data }) {
        if (data) {
            this.activities = data.map(activity => ({
                ...activity,
                activityTimestamp: this.formatTimestamp(activity.activityTimestamp),
                iconName: this.activityIcons[activity.activityType]
            }));
            this.totalPages = Math.ceil(this.activities.length / PAGE_SIZE);
            this.groupActivitiesByDate();
        } else if (error) {
            console.error('Error fetching activities:', error);
        }
    }

    groupActivitiesByDate() {
        const grouped = {};
        const start = (this.currentPage - 1) * PAGE_SIZE;
        const end = this.currentPage * PAGE_SIZE;
        const paginatedActivities = this.activities.slice(start, end);

        paginatedActivities.forEach(activity => {
            const date = new Date(activity.activityTimestamp).toLocaleDateString(); // For accordion section header (date only)
            if (!grouped[date]) {
                grouped[date] = [];
            }
            grouped[date].push({
                ...activity,
                activityTime: this.formatTime(activity.activityTimestamp) // Format only the time for activities
            });
        });

        this.groupedActivities = Object.keys(grouped).map(date => ({
            date,
            activities: grouped[date]
        }));
    }
    handlePageChange(event) {
        const direction = event.target.dataset.type;
        if (direction === 'next' && this.currentPage < this.totalPages) {
            this.currentPage += 1;
        } else if (direction === 'previous' && this.currentPage > 1) {
            this.currentPage -= 1;
        }
        this.groupActivitiesByDate();
    }

    openModal() {

        this.isModalOpen = true;
    }
    handleDateChange(event) {
        const { name, value } = event.target;
        if (name === 'startDate') {
            this.tempStartDate = value ? new Date(value).toISOString() : null;
        } else if (name === 'endDate') {
            this.tempEndDate = value ? new Date(value).toISOString() : null;
        }
    }
    formatTime(timestamp) {
        if (!timestamp) {
            return 'Invalid Time';
        }

        const date = new Date(timestamp);
        if (isNaN(date.getTime())) {
            return 'Invalid Time';
        }
        // Format only the time (HH:MM AM/PM)
        const options = { hour: '2-digit', minute: '2-digit', hour12: true };
        return new Intl.DateTimeFormat('en-US', options).format(date);
    }

    applyFilters() {
        // Ensure the start and end dates are valid
        if (this.tempStartDate && this.tempEndDate && new Date(this.tempStartDate) > new Date(this.tempEndDate)) {
            alert('Start date cannot be greater than end date.');
            return;
        }

        // Save the selected date range
        if (this.tempStartDate) {
            this.startDate = this.tempStartDate;
        }
        if (this.tempEndDate) {
            this.endDate = this.tempEndDate;
        }

        // Combine CRM and App filters before applying them
        this.selectedFilters = [...this.selectedValues, ...this.selectedValuesApp];

        // Close the modal
        this.isModalOpen = false;

        // Clear the existing activities before applying new filters
        this.activities = [];
        this.groupedActivities = [];
        this.currentPage = 1;

        // Fetch new filtered activities
        this.fetchActivities();
    }

    fetchActivities() {
        this.wiredActivities({
            accountId: this.recordId,
            activityTypes: this.selectedFilters,
            startDate: this.startDate,
            endDate: this.endDate
        });
    }
    closeModal() {
        this.isModalOpen = false;
        console.log('Inside closeModal : ');
    }
    handleDownload() {
        const filters = this.selectedFilters.join(',');
        const startDate = this.startDate ? this.startDate.split('T')[0] : '';
        const endDate = this.endDate ? this.endDate.split('T')[0] : '';
        const url = `/apex/TimelineActivityPdfGenerator?accountId=${this.recordId}&startDate=${startDate}&endDate=${endDate}&filters=${filters}`;
        window.open(url, '_blank');
    }
    formatTimestamp(timestamp) {
        if (!timestamp) {
            return 'Invalid Date';
        }
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) {
            return 'Invalid Date';
        }
        const options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', hour12: true };
        return new Intl.DateTimeFormat('en-US', options).format(date);
    }
    clearModalState() {
        this.clearCheckboxes();
    }
    handleTabChange(event) {
        this.activeTab = event.target.dataset.tab;
    }
    get isTab1() {
        return this.activeTab === 'tab1';
    }
    get isTab2() {
        return this.activeTab === 'tab2';
    }
    get isTab3() {
        return this.activeTab === 'tab3';
    }
    get disablePrevious() {
        return this.currentPage === 1;
    }
    get disableNext() {
        return this.currentPage === this.totalPages;
    }
}