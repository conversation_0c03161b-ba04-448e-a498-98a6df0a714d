import {LightningElement, track,  api} from 'lwc';
import sendPdf from "@salesforce/apex/GeneratePDFController.sendPdf";
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { loadStyle, loadScript } from 'lightning/platformResourceLoader';
import jspdf from '@salesforce/resourceUrl/jsPDF';


export default class pDFComponent extends LightningElement {
    @api recordId;
    @track l_All_Types;
   
    renderedCallback() {
        Promise.all([
            loadScript(this, jspdf)// load script here
        ])
            
    }
    connectedCallback(){
        setTimeout(() => {
            console.log('called in ::'+this.recordId);
            sendPdf({recordId : this.recordId})
        .then(res=>{
            this.ShowToast('Success', res, 'success', 'dismissable');
        })
        .catch(error=>{
            this.ShowToast('Error', 'Error in send email!!', 'error', 'dismissable');
        })
        }, 5);
        
        console.log('called'+this.recordId);
        
    }
    generatePDF(){
        console.log('called');
        sendPdf({contactId : this.recordId})
        .then(res=>{
            this.ShowToast('Success', res, 'success', 'dismissable');
        })
        .catch(error=>{
            this.ShowToast('Error', 'Error in send email!!', 'error', 'dismissable');
        })
    }
     
 
    ShowToast(title, message, variant, mode){
        const evt = new ShowToastEvent({
            title: title,
            message:message,
            variant: variant,
            mode: mode
        });
        this.dispatchEvent(evt);
    }

    handleGeneratePDF() {
        // create a new instance of jsPDF
        const doc = new jsPDF();
      
        // add some text to the PDF
        doc.text('Hello, world!', 10, 10);
      
        // save the PDF
        doc.save('my-document.pdf');
      }
}