/*
    This class is created to calculate the IRR for old/past investment data
*/
global with sharing class CalculateIRRBulkBatch implements Database.Batchable<sObject>, Database.Stateful{
    public string csvBody;
    public string successBody;
    public string headerValue;
    global list<Database.SaveResult> updateResult = new list<Database.SaveResult>();
    public final String Query;
    public String errorString = '';
    
    global CalculateIRRBulkBatch(String q){
        Query=q;
        csvBody = '';
        headerValue = 'Investment Id, Error Message \n';
        system.debug('Query>>>>>'+Query);
    }
    
    global Database.QueryLocator start(Database.BatchableContext BC){
        system.debug('Query>>>>>'+Query);
        return Database.getQueryLocator(query);
    }
    
    global void execute(Database.BatchableContext BC, List<Investment__c> invlist){ 
        system.debug('invlist>>>>>'+invlist.size());
        List<Investment__c> invUpdateList = new List<Investment__c>();
       
       for(Investment__c s : invlist){
           invUpdateList.add(s);
       }
       
       InvestmentTriggerHandler hndl = new InvestmentTriggerHandler();
       invUpdateList = hndl.calculateIRR(invUpdateList,null);
       system.debug('invUpdateList>>>>'+invUpdateList);
       
       if(invUpdateList!=null && invUpdateList.size()>0)
       {
           updateResult = Database.update(invUpdateList, false);
           system.debug('updateResult>>>>'+updateResult);
                if( updateResult != null && !updateResult.isEmpty()){
                    for(Integer index = 0 ; index < updateResult.size() ; index ++){
                        if(!updateResult[index].isSuccess() ){
                            csvBody += invUpdateList.get( index ).Id + ',';
                            system.debug('csvBody>>>>'+csvBody);
                            string errorMsg = '';
                            for(Database.Error error : updateResult[index].getErrors()){
                                errorMsg += error.getMessage() + ',';
                            }
                            errorMsg = errorMsg.removeEnd(',');
                            csvBody += '"'+errorMsg + '"' + ','; 
                            csvBody += '\n';
                        }else{
                            successBody += invUpdateList.get( index ).Id + ',';
                            
                            
                            successBody += '"'+'Successfully Updated' + '"' + ','; 
                            successBody += '\n';
                        }
                    }
               }
        }   
    }
    
    global void finish(Database.BatchableContext BC){
            try{
                    system.debug('csvBody>>>>>>>>'+csvBody);
                    system.debug('headerValue>>>>>>>>'+headerValue);
                    system.debug('successBody>>>>>>>>'+successBody);

                    String finalCsv = '';
                    if(csvBody != null && csvBody != ''){
                        finalCsv = headerValue + csvBody;
                    }
                    if(successBody != null && successBody != ''){
                        successBody = 'Invesment Id, Status \n' + successBody;
                    }
                    
                    //if( finalCsv != null && finalCsv != ''){
                        String csvname= 'Invesment_Update_Errors.csv';
                        Messaging.EmailFileAttachment csvAttc = new Messaging.EmailFileAttachment();
                        system.debug('finalCsv>>>>>'+finalCsv);
                        
                        if(finalCsv!=null && finalCsv!='')
                        {
                            csvAttc.setFileName(csvname);
                            csvAttc.setBody(Blob.valueOf(finalCsv));                            
                        }
                        
                        Messaging.EmailFileAttachment successCsvAttc = new Messaging.EmailFileAttachment();
                        if(successBody!=null && successBody!='')
                        {
                            successCsvAttc.setFileName('Invesment_Update_Success.csv');
                            successCsvAttc.setBody(Blob.valueOf(successBody));
                        }
                        
                        list<string> toAddresses = new list<string>();
                        
                        //toAddresses.addAll(new List<String>{'<EMAIL>'});
                        String subject ='Investment Update Error:';    
                        Messaging.SingleEmailMessage email =new Messaging.SingleEmailMessage();
                        email.setSubject(subject);
                        email.setToAddresses(toAddresses);
                        email.setPlainTextBody('PFA success and error file attached.');
                        
                        List<Messaging.EmailFileAttachment> attachmentFiles = new List<Messaging.EmailFileAttachment>();
                        
                        //email.setFileAttachments(new Messaging.EmailFileAttachment[]{csvAttc,successCsvAttc});
                        //email.setFileAttachments(new Messaging.EmailFileAttachment[]{csvAttc});
                        
                        if(csvAttc.getBody()!=null)
                            attachmentFiles.add(csvAttc);
                        
                        if(successCsvAttc.getBody()!=null)
                            attachmentFiles.add(successCsvAttc);
                       
                        system.debug('attachmentFiles>>>>>>>'+attachmentFiles);
                        if(attachmentFiles!=null && attachmentFiles.size()>0)
                            email.setFileAttachments(attachmentFiles);
                            
                        try{
                            Messaging.SendEmailResult [] r = Messaging.sendEmail(new Messaging.SingleEmailMessage[] {email});
                        }catch(Exception e){
                            system.debug('error message>>>>>>>'+e.getMessage());
                        }
                        
                    //}                 
                
            }catch(Exception e){
                system.debug('**Error at line number---'+e.getLineNumber() + ' - '+ e.getMessage());
            }
            
    }
}

/*

String solqStr = 'select id,Type__c,Parent_Investment__c,Exit_Date__c,Exit_amount_to_be_transferred__c,Number_Of_Shares__c,Exit_Price__c,IRR_Value__c,Startup_Round__c ,Investment_Year__c from Investment__c where Type__c = \'Exit\' And Startup_Round__c=\'a0G0l00000662f0EAA\'';
CalculateIRRBulkBatch batchObj = new CalculateIRRBulkBatch(solqStr);
Database.executeBatch(batchObj);
*/