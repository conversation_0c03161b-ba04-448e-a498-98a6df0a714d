//-------------------------------------- This is the test class for TransactionTriggerHandler , TransactionTrigger , CreateMembershipPaymentRecordAPI

@isTest
public class TransactionTriggerTest {
    /*static testmethod void testCase1(){
        Account testAccount = TestFactory.createAccount();
        insert testAccount;
        Contact con = new Contact();
        con.FirstName='Test';
        con.LastName='Test';
        con.Accountid= testAccount.id;
        con.Investor_s_PAN__c = '**********';
        con.AIF_Contributor__c = true;
        //contList.add(Cont);
        insert con; 
        Contribution_Agreement__c ca = new Contribution_Agreement__c();
        CA.investor1__c =  con.id;
        ca.Virtual_Account_Number__c = '********';
        insert ca;
        Transaction__c trans = new Transaction__c();
        trans.virtual_account_number__c = '********';
        trans.Transaction_Type__c = 'Investment';
        trans.Contribution_Agreement__c = ca.id;
        insert trans;
    }*/
    
    //setup for CreateMembershipPaymentRecordAPI Added by bharat for coverage
    @TestSetup
    static void setup() {
        Account acc= TestFactory.createAccount();
        acc.Name ='Test Acc';
        insert acc;
        
        Account acc1= TestFactory.createAccount();
        acc1.Name ='Test Acc1';
        insert acc1;
        
        Account acc2= TestFactory.createAccount();
        acc2.Name ='Test Acc2';
        insert acc2;
        
        Account acc3= TestFactory.createAccount();
        acc3.Name ='Test Acc3';
        insert acc3;
        
        Account acc4= TestFactory.createAccount();
        acc4.Name ='Test Acc4';
        insert acc4;
        
        Lead__c ld1 = TestFactory.createLead();
        ld1.Name = 'Test Lead';
        insert ld1;
    }
    
    @IsTest
    static void testCreateTransaction() {
        
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        
        CreateMembershipPaymentRecordAPI.requestWrapper requestWrapper = new CreateMembershipPaymentRecordAPI.requestWrapper();
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper1 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        
        transactionWrapper1.payment_amount = 100.00;
        transactionWrapper1.payment_id = 'pay_test_123';
        transactionWrapper1.order_id = 'order_test_456';
        transactionWrapper1.transaction_at = DateTime.now();
        transactionWrapper1.is_renew = false;
        transactionWrapper1.is_promo_code = true;
        transactionWrapper1.promo_code = 'Test Promo';
        transactionWrapper1.salesforce_user_account_id = [SELECT Id FROM Lead__c LIMIT 1].Id;
        
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper2 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        transactionWrapper2.payment_amount = 200.00;
        transactionWrapper2.payment_id = 'pay_test_789';
        transactionWrapper2.order_id = 'order_test_101';
        transactionWrapper2.transaction_at = DateTime.now().addDays(-1);
        transactionWrapper2.is_renew = true;
        transactionWrapper2.total_used_points = 100;
        transactionWrapper2.total_year_of_payment = 1;
        transactionWrapper2.is_promo_code = false;
        transactionWrapper2.promo_code = '';
        transactionWrapper2.salesforce_user_account_id = [SELECT Id FROM Account where Name = 'Test Acc' LIMIT 1].Id;
        System.debug('transactionWrapper2.salesforce_user_account_id>>>>>>' + transactionWrapper2.salesforce_user_account_id);
        
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper3 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        transactionWrapper3.payment_amount = 200.00;
        transactionWrapper3.payment_id = 'pay_test_789';
        transactionWrapper3.order_id = 'order_test_101';
        transactionWrapper3.transaction_at = DateTime.now().addDays(-1);
        transactionWrapper3.is_renew = true;
        transactionWrapper3.total_used_points = 100;
        transactionWrapper3.total_year_of_payment = 2;
        transactionWrapper3.is_promo_code = false;
        transactionWrapper3.promo_code = '';
        transactionWrapper3.salesforce_user_account_id = [SELECT Id FROM Account where Name = 'Test Acc1' LIMIT 1].Id;
        System.debug('transactionWrapper2.salesforce_user_account_id>>>>>>' + transactionWrapper3.salesforce_user_account_id);
        
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper4 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        transactionWrapper4.payment_amount = 200.00;
        transactionWrapper4.payment_id = 'pay_test_789';
        transactionWrapper4.order_id = 'order_test_101';
        transactionWrapper4.transaction_at = DateTime.now().addDays(-1);
        transactionWrapper4.is_renew = true;
        transactionWrapper4.total_used_points = 100;
        transactionWrapper4.total_year_of_payment = 3;
        transactionWrapper4.is_promo_code = false;
        transactionWrapper4.promo_code = '';
        transactionWrapper4.salesforce_user_account_id = [SELECT Id FROM Account where Name = 'Test Acc2' LIMIT 1].Id;
        System.debug('transactionWrapper2.salesforce_user_account_id>>>>>>' + transactionWrapper4.salesforce_user_account_id);
        
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper5 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        transactionWrapper5.payment_amount = 200.00;
        transactionWrapper5.payment_id = 'pay_test_789';
        transactionWrapper5.order_id = 'order_test_101';
        transactionWrapper5.transaction_at = DateTime.now().addDays(-1);
        transactionWrapper5.is_renew = true;
        transactionWrapper5.total_used_points = 100;
        transactionWrapper5.total_year_of_payment = 4;
        transactionWrapper5.is_promo_code = false;
        transactionWrapper5.promo_code = '';
        transactionWrapper5.salesforce_user_account_id = [SELECT Id FROM Account where Name = 'Test Acc3' LIMIT 1].Id;
        System.debug('transactionWrapper2.salesforce_user_account_id>>>>>>' + transactionWrapper5.salesforce_user_account_id);
        
        CreateMembershipPaymentRecordAPI.transactionRequestWrapper transactionWrapper6 = new CreateMembershipPaymentRecordAPI.transactionRequestWrapper();
        transactionWrapper6.payment_amount = 200.00;
        transactionWrapper6.payment_id = 'pay_test_789';
        transactionWrapper6.order_id = 'order_test_101';
        transactionWrapper6.transaction_at = DateTime.now().addDays(-1);
        transactionWrapper6.is_renew = true;
        transactionWrapper6.total_used_points = 100;
        transactionWrapper6.total_year_of_payment = 5;
        transactionWrapper6.is_promo_code = false;
        transactionWrapper6.promo_code = '';
        transactionWrapper6.salesforce_user_account_id = [SELECT Id FROM Account where Name = 'Test Acc4' LIMIT 1].Id;
        System.debug('transactionWrapper2.salesforce_user_account_id>>>>>>' + transactionWrapper6.salesforce_user_account_id);
        
        requestWrapper.dataList = new List<CreateMembershipPaymentRecordAPI.transactionRequestWrapper>{ transactionWrapper1 , transactionWrapper2 , transactionWrapper3 , transactionWrapper4 , transactionWrapper5 , transactionWrapper6 };
        String jsonReq = JSON.serialize(requestWrapper);
        req.requestURI = '/services/apexrest/CreateMembershipPaymentRecordAPI/';
        req.httpMethod = 'POST';
        req.requestBody = Blob.valueOf(jsonReq);
        RestContext.request = req;
        RestContext.response = res;
        
        CreateMembershipPaymentRecordAPI.ResponseWrapper response = CreateMembershipPaymentRecordAPI.createTransaction();
    }
}