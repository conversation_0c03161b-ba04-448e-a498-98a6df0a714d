@isTest
public class CAInvestmentMappingBatchTest {
 @testSetup static void setup() {
        Account testAccount = TestFactory.createAccount();
        insert testAccount;
        Account testAccount2 = TestFactory.createAccount();
        insert testAccount2;
        
        Fund_Module__c fnc = new Fund_Module__c();
        fnc.Name = 'test fund module';
        fnc.Fund_Identifier__c = 'TST';
        insert fnc;

        //List<contact> contList = new List<Contact>();
        Contact con = new Contact();
        con.FirstName='Test';
        con.LastName='Test';
        con.Accountid= testAccount.id;
        con.Investor_s_PAN__c = '**********';
        con.AIF_Contributor__c = true;
        //contList.add(Cont);
        insert con; 
        Contribution_Agreement__c ca = new Contribution_Agreement__c();
        CA.investor1__c =  con.id;
        ca.Fund_Onboarded_on__c = fnc.id;
        ca.Virtual_Account_Number__c = '********';
        insert ca;
        Contribution_Agreement__c ca1 = new Contribution_Agreement__c();
        CA1.investor1__c =  con.id;
        ca1.Fund_Onboarded_on__c = fnc.id;
        insert ca1;
        con.Contribution_Agreement__c = ca1.id;
        //contList.add(Cont);
        update con; 
        List<document__c> docs =  new List<document__c>();
        Document__c doc = new Document__c();
        doc.Document_Name__c = 'Premier contribution Agreement';
        doc.stamp_paper__c = '12345';
        doc.Contribution_Agreement__c = ca.id;
        Document__c doc1 = new Document__c();
        doc1.stamp_paper__c = '12345';
        doc1.Document_Name__c = 'Regular Addendum';
        doc1.Contribution_Agreement__c = ca.id;
        Document__c doc2 = new Document__c();
        doc2.stamp_paper__c = '12345';
        doc2.Document_Name__c = 'Premier+ Shift of Class Addendum';
        doc2.Contribution_Agreement__c = ca.id;
        docs.add(doc);
        docs.add(doc1);
        docs.add(doc2);
        insert docs;
        doc.Document_Name__c = 'Premier Contribution Agreement';
        update doc;
        doc1.Document_Name__c = 'Premier+ Contribution Agreement';
        doc1.Stamp_Paper__c = '24555';
        update doc1;
        Transaction__c trans = new Transaction__c();
        trans.virtual_account_number__c = '********';
        trans.Transaction_Type__c = 'Investment';
        trans.Contribution_Agreement__c = ca1.id;
        insert trans;
        
        test.startTest();
            List<Account> accList = new List<Account>();
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            accList.add(TestFactory.createAccount());
            for(Account accAux: accList)
            {
               accAux.ShippingStreet       = '';
                accAux.ShippingState        = '';
                accAux.ShippingPostalCode   = '';
                accAux.ShippingCountry      = '';
                accAux.ShippingCity         = '';
                accAux.Description          = '';
                accAux.BillingStreet        = '';
                accAux.BillingState         = '';
                accAux.BillingPostalCode    = '';
                accAux.BillingCountry       = '';
                accAux.BillingCity          = '';
                accAux.Designation__c ='Test';
                accAux.Company__c = 'Codiot';
                accAux.Are_you_a_first_time_investor_c__c ='';
            
            }
            insert accList;
            System.debug('invTrigger Acct insert....13' + accList);    
        
            List<Contact> conList = new List<Contact>();
            integer i = 1;
            for(Account acc : accList)
            {
                Contact cont = new Contact();
                cont.FirstName='Test';
                cont.LastName='Test';
                cont.Accountid= acc.id;
                cont.Investor_s_PAN__c = 'ABCDE'+i+'173D';
                cont.Investment_in_Own_Name_Family_Member__c = 'Own Name';
                cont.Send_Auto_Com_Email__c = true;
                conList.add(cont);
                i++;
            }
            insert conList;
            System.debug('invTrigger Cont insert....14' + conList);
            Startup__c stObj = TestFactory.createStartUp();
            insert stObj;
            
            Startup_Round__c strObj = TestFactory.createStartUpRound(accList[0].Id,accList[1].Id,stObj.Id);
            strObj.Date_of_Investor_Call__c = Date.newInstance(2022, 12, 9);
            strObj.Send_Auto_Comms_Email__c = true; 
         strObj.Round_type__c = 'Raise';
        
            insert strObj;   
            System.debug('invTrigger startupRound insert....15' + strObj.Round_type__c);
            strObj = [select id,name from Startup_Round__c limit 1];
            
            List<Investment__c> invList = new list<Investment__c >();       
            Investment__c inv = TestFactory.createInvestment(accList[0].Id); 
            
            
           
            //Joints Scenario
            inv = TestFactory.createInvestment(accList[0].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
        
            inv.Investment_in_Own_Name_Family_Member__c = 'Joint A/c - Outside';
            inv.Primary_Holder_Contact__c= accList[0].Primary_Contact__c;
            inv.Is_Primary__c=true;
            inv.SAF_soft_copy__c = false;
            inv.Transfer_Deed_hard_copy__c = false;
            inv.Transfer_Deed_soft_copy__c = false;
            invList.add(inv);
            
            inv = TestFactory.createInvestment(accList[1].Id); 
            inv.Investor_s_PAN__c = '**********'; 
        inv.Investor__c = con.id;
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_in_Own_Name_Family_Member__c = 'Joint A/c - Outside';
            inv.Primary_Holder_Contact__c= accList[0].Primary_Contact__c;
            inv.Is_Primary__c=false;
            inv.SAF_soft_copy__c = false;
            inv.Transfer_Deed_hard_copy__c = false;
            inv.Transfer_Deed_soft_copy__c = false;
          //inv.Contribution_Agreement__c = ca.id;
            invList.add(inv);
            
            inv = TestFactory.createInvestment(accList[2].Id); 
            inv.Investor_s_PAN__c = '**********'; 
            inv.Startup_Round_Name__c = strObj.Name;
            inv.Investment_in_Own_Name_Family_Member__c = 'Joint A/c - Outside';
            inv.Primary_Holder_Contact__c= accList[0].Primary_Contact__c;
            inv.Is_Primary__c=false;
            inv.SAF_soft_copy__c = false;
            inv.Transfer_Deed_hard_copy__c = false;
            inv.Transfer_Deed_soft_copy__c = false;
          //inv.Contribution_Agreement__c = ca.id;
        
            //inv.Startup_Round__c = strObj.Id;
            invList.add(inv);
            insert invList;
            System.debug('invTrigger Investment insert....16' + invList);
            
            //Update scenario
            inv.Funds_Cheque__c = true;
            inv.Type__c= 'Committed';
            inv.SAF_soft_copy__c = true;
            inv.Transfer_Deed_hard_copy__c = true;
            inv.Transfer_Deed_soft_copy__c = true;
            inv.Investor_Type__c = 'Via AIF';
            inv.Call_For_Money_Sent__c = false;
          inv.Final_Commitment_Amount__c = 100;
            inv.Issue_Type__c ='Friends & Family T1';
          //inv.Contribution_Agreement__c = ca1.id;
            update inv;  
        
          //inv.Contribution_Agreement__c = ca.id;
          inv.Committed_Amount__c = 100;
            update inv;  
        
          
        test.stopTest();
    }
    
     static testmethod void testCase1(){
         List<investment__c> inv = [SELECT ID,Contribution_Agreement__c,Type__C,Startup_Round__r.Round_Type__c,Investor__r.Contribution_Agreement__c FROM investment__c];
        test.startTest();
         CAInvestmentMappingBatch b = new CAInvestmentMappingBatch();
         b.execute(null,inv);
     database.executeBatch(b);
         test.stopTest();
     }
}