// matchingStartupsForIB.js
import { LightningElement, wire, api, track } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import getMatchingStartups from '@salesforce/apex/MatchingStartupController.getMatchingInvestmentBanking';

const columns = [
    {
        label: 'Public Name',
        fieldName: 'NavigateToRecordUrl',
        type: 'url',
        typeAttributes: {
            label: { fieldName: 'Public_Name__c' },
            target: '_blank',
            tooltip: 'Open Record'
        }
    },
    { label: 'Industry', fieldName: 'Industry_Sector__c', type: 'text' },
    { label: 'Fundraise Amount (In $ Mn)', fieldName: 'Fundraise_Amount__c', type: 'number' },
    { label: 'Fundraise Type', fieldName: 'Fundraise_Type__c', type: 'text' }
];

export default class MatchingStartupsForIB extends NavigationMixin(LightningElement) {
    @api recordId;
    @track startupData;

    connectedCallback() {
        this.getStartups();
    }

    getStartups() {
        if (this.recordId) {
            getMatchingStartups({ capitalDevelopmentId: this.recordId })
                .then(result => {
                    console.log('Startups:', result); // Log the complete records to inspect the fields
                    this.startupData = { data: this.mapDataForNavigation(result), error: undefined };
                })
                .catch(error => {
                    console.error('Error fetching startups:', error);
                    this.startupData = { data: undefined, error: 'Error fetching startups' };
                });
        } else {
            console.error('Record ID is missing.');
        }
    }

    mapDataForNavigation(data) {
        return data.map(record => {
            return {
                ...record,
                NavigateToRecordUrl: `/${record.Id}`,
            };
        });
    }

    handlePublicNameClick(event) {
        const recordId = event.detail.row.Id;
        // Use NavigationMixin to navigate to the record page
        this[NavigationMixin.GenerateUrl]({
            type: 'standard__recordPage',
            attributes: {
                recordId: recordId,
                actionName: 'view'
            }
        }).then(url => {
            window.open(url, '_blank');
        }).catch(error => {
            console.error('Error navigating to the record:', error);
        });
    }

    get columns() {
        return columns;
    }
}