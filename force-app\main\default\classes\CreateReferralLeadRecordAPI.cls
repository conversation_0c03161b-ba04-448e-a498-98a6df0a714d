@RestResource(urlMapping='/CreateReferralLeadRecordAPI/*')
global with sharing class CreateReferralLeadRecordAPI
{
    @HttpPost
    global Static ResponseWrapper createLead()
    {
        ResponseWrapper wResponse = new ResponseWrapper();
        
        try
        {
            RestRequest req = RestContext.request;
            RestResponse res = Restcontext.response;
            String jsonReqString=req.requestBody.tostring();
            System.debug('jsonReqString>>>>>'+jsonReqString);

            requestWrapper wResp=(requestWrapper) JSON.deserialize(jsonReqString,requestWrapper.class);
            System.debug('wResp>>>>>'+wResp);
            
            If(wResp!=null && wResp.leadList!=null)
            {
                List<Lead__c> leadInsertList = new List<Lead__c>();
                Map<String,Id> primaryNumberMap = new Map<String,Id>();
                Set<Integer> countryCodeSet = new Set<Integer>();
                Set<String> primaryNumberSet = new Set<String>();
                Map<String,Boolean> duplicateLeadMap = new  Map<String,Boolean>();
                List<Account> referredByAccountList = new List<Account>();
                Id CXOAccountRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('CXO Genie').getRecordTypeId();
                
                for(leadRequestWrapper crw : wResp.leadList)
                {
                    system.debug('crw>>>>'+crw);
                    if(String.isNotBlank(crw.referral_mobile))
                        crw.referral_mobile = crw.referral_mobile.replaceAll('[^\\d]','');
                    
                    if(crw.referred_by_contact !=null)
                    {  
                        if(String.isNotBlank(crw.referred_by_countrycode))
                        {
                            countryCodeSet.add(Integer.valueof(crw.referred_by_countrycode.Trim()));
                        }
                        else
                        {
                            countryCodeSet.add(91);
                        }
                        
                        System.debug('CountryCodeSet>>>>>>>>>>'+ countryCodeSet);
                        
                        if(String.isNotBlank(crw.referred_by_contact))
                        {
                            primaryNumberSet.add(crw.referred_by_contact.Trim());
                        }
                        System.debug('primaryNumberSet>>>>>>>>>>'+ primaryNumberSet);
                    }
                }
                
                System.debug('primaryNumberSet>>>>>'+primaryNumberSet);
                System.debug('referredByAccountList>>>>>'+referredByAccountList);
                
                
                referredByAccountList = [select Id,Primary_Contact__c,Primary_Country_Code__c from account where Primary_Contact__c in :primaryNumberSet AND Primary_Country_Code__c in :countryCodeSet AND RecordTypeId =: CXOAccountRecordTypeId];
                for(Account acc : referredByAccountList)
                {
                    primaryNumberMap.put(acc.Primary_Country_Code__c +'-'+acc.Primary_Contact__c,acc.Id);
                    //primaryNumberMap.put(crw.referred_by_countrycode+'-'+crw.referred_by_contact , acc.Id);
                }
                
                System.debug('primaryNumberMap>>>>>'+primaryNumberMap);
                duplicateLeadMap = checkForDuplicate(wResp.leadList);
                System.debug('duplicateLeadMap>>>>>'+duplicateLeadMap);
                
                for(leadRequestWrapper crw : wResp.leadList)
                {
                    if(duplicateLeadMap!=null && duplicateLeadMap.containsKey(crw.referral_mobile) && duplicateLeadMap.get(crw.referral_mobile))
                    {
                        wResponse.status = false;
                        wResponse.message= 'Lead or Account is already exist for the same primary contact : '+crw.referral_mobile;
                        return wResponse;
                    }
                    else
                    {
                        Lead__c ld = new Lead__c();
                        String key = '';
                        
                        Id CXOLeadRecordTypeId = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('CXO Genie').getRecordTypeId();
                        ld.RecordTypeId = CXOLeadRecordTypeId;
                        ld.Lead_source__c = 'App Referral';
                        ld.Reach_out_Status_WA__c = 'RM To Connect';
                        
                        if(crw.referral_name != null)
                            ld.Name = crw.referral_name;
                        if(crw.referral_mobile != null)
                            ld.Primary_Contact__c = crw.referral_mobile;
                        if(crw.created_date != null)
                            ld.Date_of_receiving_lead__c = crw.created_date;
                        if(crw.referral_app_id != null)
                            ld.Lead_External_ID_App__c = crw.referral_app_id;
                        
                        Schema.DescribeFieldResult fieldDescribeResult = Schema.getGlobalDescribe().get('Lead__C').getDescribe().fields.getMap().get('CXO_Genie_Forum__c').getDescribe();
                        Boolean forumIsSelected = False;
                        if (fieldDescribeResult.getType() == Schema.DisplayType.MULTIPICKLIST) 
                        {
                            List<Schema.PicklistEntry> picklistValuesList = fieldDescribeResult.getPicklistValues();
                            System.debug('ALL VALUE >>>>>>>>>>>>>>.'+picklistValuesList);
                            
                            if(picklistValuesList != NULL)
                            {
                            	for (Schema.PicklistEntry picklistEntry : picklistValuesList)
                            	{
                                	String currentPicklistValue = picklistEntry.getValue();
                                	If(currentPicklistValue.contains(crw.forum))
                                	{
                                    	ld.CXO_Genie_Forum__c = currentPicklistValue;
                                        forumIsSelected = True;                                        
                                    	Break;
                                	}
                                	
                            	}
                            }
                            System.debug('Forum is Selected :>>>>>>>>>' + forumIsSelected);
                            
                            If(forumIsSelected != True)
                            {
                                wResponse.status = False;
                                wResponse.message='Forum value Does Not Exist';
                                return wResponse;
                            }
                            
                        }
                                                
                        if(ld.CXO_Genie_Forum__c == 'CFO Genie')
                        {
                            ld.Relationship_Manager__c = Label.Lead_CFO_Genie_RM;
                        }
                        if(ld.CXO_Genie_Forum__c == 'CHRO Genie')
                        {
                            ld.Relationship_Manager__c = Label.Lead_CHRO_Genie_RM;
                        }
                        if(ld.CXO_Genie_Forum__c == 'CSCO Genie')
                        {
                            ld.Relationship_Manager__c = Label.Lead_CSCO_Genie_RM;
                        }
                        if(ld.CXO_Genie_Forum__c == 'CEO Forum')
                        {
                            ld.Relationship_Manager__c = Label.Lead_CEO_Forum_RM;
                        }
                        /*
                        if(ld.CXO_Genie_Forum__c == 'Founders Genie')
                        {
                            ld.Relationship_Manager__c = Label.Lead_Founders_Genie_RM;
                        }
                        */
                        if(String.isNotBlank(crw.referral_mobile_countryCode)) 
                            ld.Primary_Country_Code__c = Integer.valueof(crw.referral_mobile_countryCode);
                        else
                            ld.Primary_Country_Code__c = 91;
                        
                        
                        if(crw.cxo_member_id != null)
                        {
                            for(Account acc : referredByAccountList)
                            {
                                acc.CXO_Member_Id__c = crw.cxo_member_id ;
                            }
                        }
                        
                        if(String.isNotBlank(crw.referred_by_countrycode)) 
                            key = ''+crw.referred_by_countrycode.Trim()+'-'+crw.referred_by_contact.Trim();
                        else
                            key = 91+'-'+crw.referred_by_contact.Trim();	
    
                        system.debug('Key>>>>'+key);
                        
                        if(crw.referred_by_name != null)
                        {
                            if(primaryNumberMap!=null && primaryNumberMap.size()>0 && primaryNumberMap.containsKey(key) && primaryNumberMap.get(key)!=null)
                            {
                                ld.Referred_By__c = primaryNumberMap.get(key);
                            }
                            else
                            {
                                wResponse.status = false;
                                wResponse.message='Can not Find Referred By Member Account';
                                System.debug('Can not Find Referred By Member Account');
                                return wResponse;
                            }
                        }

                        System.debug('ld >>>>>>>>>>'+ld);
                        System.debug('ld.name>>>>>>>>>>'+ld.name);
                        leadInsertList.add(ld);
                    }
                }
                Insert leadInsertList;
                update referredByAccountList;
              /* for(Lead__c ld: leadInsertList)
                {
                    System.debug('NEW LEAD CREATED ID>>>>>'+ld.Id);
                    System.debug('NEW LEAD CREATED Name>>>>>'+ld.Name);
                } */
                wResponse.status = true;
                wResponse.message= 'Lead records are created successfully';
            }
            else
            {
                wResponse.status = false;
                wResponse.message= 'Lead list is null';
            }
        }
        catch(exception e)
        {
            wResponse.status = false;
            wResponse.message= 'Exception:'+e.getMessage() + 'Cause of Error >>>>>' + e.getCause() + 'On the Line Number>>>>>' + e.getLineNumber() + 'stack trace is >>>>>' + e.getStackTraceString();
            System.debug('wResponse.message>>>>>'+wResponse.message);
        }
		return wResponse;
        
    }
    
    Public Static Map<String,Boolean> checkForDuplicate(List<leadRequestWrapper> leadWrapList)
    {
        Map<String,Boolean> duplicateMap = new Map<String,Boolean>();
        List<String> soqlLikeStr = new List<String>();
        Id CXOAccountRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('CXO Genie').getRecordTypeId();
        Id CXOLeadRecordTypeId = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('CXO Genie').getRecordTypeId();
 
        for(leadRequestWrapper crw : leadWrapList)
        {
            if(String.isBlank(crw.referral_mobile_countryCode) && String.isNotBlank(crw.referral_mobile))
            {
                duplicateMap.put(crw.referral_mobile,false);
                soqlLikeStr.add('%'+crw.referral_mobile+'%');
                System.debug(soqlLikeStr);
            }
        }
        
        List<Account> accList = [select id,Primary_Contact__c,Full_Primary_Contact__c from Account where Full_Primary_Contact__c LIKE :soqlLikeStr AND RecordTypeId =: CXOAccountRecordTypeId];
        List<Lead__c> leadList = [select id,Primary_Contact__c,Full_Primary_Contact__c from Lead__c where Full_Primary_Contact__c LIKE :soqlLikeStr AND RecordTypeId =: CXOLeadRecordTypeId];

        System.debug('duplicateMap>>>>>>'+duplicateMap);
        System.debug('accList>>>>>>>'+accList);
        System.debug('leadList >>>>>>'+leadList );
        
        if(leadList!=null && leadList.size()>0)
        {
            for(String key : duplicateMap.keyset())
            {
                for(Account acc: accList)
                {
                     if(acc.Full_Primary_Contact__c.contains(key))
                        duplicateMap.put(key,true);
                }
                for(Lead__c ld : leadList)
                {
                    System.debug('ld.Full_Primary_Contact__c>>>>>>'+ld.Full_Primary_Contact__c);
                    System.debug('ld.key>>>>>>'+key);
                    if(ld.Full_Primary_Contact__c.contains(key))
                        duplicateMap.put(key,true);
                }
            }
        }  
        System.debug('duplicateMap111>>>>>>'+duplicateMap);
        return duplicateMap;
    }	
	
    global class leadRequestWrapper
    {
       global string referral_name;
       global string leadCountryCode;
       global string referral_mobile;
       global string referred_by_contact;
       global string referred_by_countrycode;
       global string referral_app_id;
       global Date created_date;
       global string forum;
       global string referred_by_name;
       global string cxo_member_id;
       global String referral_mobile_countryCode;
    }
    global class requestWrapper
    {
        global List<leadRequestWrapper> leadList;
    }
    global class ResponseWrapper
    {
       global boolean status;
       global string message;
       //global Map<String,String> feedbackIdMap;
    }
}