/************************************************************************
    Test Class for VCPointsFilterCtrlTest
************************************************************************/
@isTest(SeeAllData=false)
private class VCPointsFilterCtrlTest{
    
    @testSetup static void setup() {
        Startup__c st = TestFactory.createStartUp();
        st.VC_Connect__c = true;
        st.Portfolio_map_Sector__c = 'Saas';
        st.Sector_Focus__c = 'B2B';
        st.Industry__c = 'Internet';
        st.Round__c = 'Series B';
        st.Fundraise__c = 10;
        st.VC_Connect__c = true;
        insert st;
        system.debug('Startup Object Id>>>>>>>>>>...' + st.Id);
         
        Venture_Connect__c vc = TestFactory.createVentureConnect();
        vc.Investment_Size_From__c = 5;
        vc.Investment_Size_To__c = 20;
        vc.Not_preferred_Sector__c = 'Services';
        vc.Not_preferred_sub_sector__c = 'IT/ ITES';	
        vc.Sub_sector__c = 'Banking/ Financial Services';
        vc.Industry__c = 'Banking/ Financial Services';
        vc.Series__c = 'Pre Series';
        vc.VC_Connect__c = true;

        insert vc;
        system.debug('Venture Object Id>>>>>>>>>>...' + vc.Id);
    }
    
    static testMethod void startupObjcase()
    {
        test.startTest();
            Startup__c stObj = [select id from Startup__c limit 1];
        	Venture_Connect__c stObj1 = [select id from Venture_Connect__c limit 1];
        
            ApexPages.StandardController sc = new ApexPages.StandardController(stObj);
            VCPointsFilterCtrl ctrlObj = new VCPointsFilterCtrl(sc); 
            
            stObj.Portfolio_map_Sector__c = 'Services';
            //stObj.Broad_Category__c = 'B2B';
            //stObj.Round__c = 'Pre Series';
            update stObj;
            
            sc = new ApexPages.StandardController(stObj);
            ctrlObj = new VCPointsFilterCtrl(sc); 
            
            
            
            //ctrlObj.getventureCList();
        test.stopTest();
    }   
    
    static testMethod void VCObjcase()
    {
        test.startTest();
            Venture_Connect__c stObj = [select id from Venture_Connect__c limit 1];
            Startup__c stObj1 = [select id from Startup__c limit 1];

        	system.debug('Startup object>>>>>>>>>>...' + stObj1);
        	system.debug('Venture Connect object>>>>>>>>>>...' + stObj);
            ApexPages.StandardController sc = new ApexPages.StandardController(stObj);
            VCPointsFilterCtrl ctrlObj = new VCPointsFilterCtrl(sc); 
            
            
            stObj.Not_preferred_Sector__c = 'Oil & Energy';
            stObj.Not_preferred_sub_sector__c = 'Consultancy';
            stObj.Series__c = 'Pre Series';
            update stObj;   

            stObj1.Fundraise__c = 11;
            update stObj1;               
            
            sc = new ApexPages.StandardController(stObj);
            ctrlObj = new VCPointsFilterCtrl(sc); 
            
            ctrlObj.getTotalPages();
            ctrlObj.getPageNumber();
            //ctrlObj.getTotal_size();
            ctrlObj.getDisableNext();
            ctrlObj.getDisablePrevious();
            ctrlObj.Next();
            ctrlObj.End();
            ctrlObj.Previous();
            ctrlObj.Beginning();
        	ctrlObj.toggleSort();
            //ctrlObj.getstartupList();
        test.stopTest();
    }    
}