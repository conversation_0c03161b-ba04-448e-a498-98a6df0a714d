@isTest
public class ContactRestAPIControllerTest {
       
    static testmethod void insertContact(){
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseController());
        test.startTest();
        Account testAccount = TestFactory.createAccount();
        insert testAccount;        
        Contact cont = new Contact();
        cont.FirstName='Test';
        cont.LastName='Test';
        cont.Accountid= testAccount.id;
        cont.Investor_s_PAN__c = '**********';
        cont.AIF_Contributor__c = true;
        cont.Amount_Agreed_for_Contribution__c = 5;
        cont.Investment_in_Own_Name_Family_Member__c = 'Own Name';
        insert cont;
        
        Set<Id> investorIdSet = new Set<Id>();
        investorIdSet.add(cont.Id);
        ContactRestAPIController.sendInvestorDetails(investorIdSet);
        
        cont.FirstName='Test12';
        cont.Investment_in_Own_Name_Family_Member__c = '';
        update cont;    
        test.stopTest();
    }   
    
    static testmethod void ContactErrorTest(){
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseController());
        test.startTest();
        Account testAccount = TestFactory.createAccount();
        insert testAccount;        
        Contact cont = new Contact();
        cont.FirstName='Test';
        cont.LastName='Test';
        cont.Accountid= testAccount.id;
        cont.Investor_s_PAN__c = '**********';
        cont.AIF_Contributor__c = true;
        cont.Investment_in_Own_Name_Family_Member__c = 'IPV';
        insert cont;  
        // test coverage for delete scenario
        delete cont;
        test.stopTest();
    }   
    
}