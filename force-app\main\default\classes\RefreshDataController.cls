public class RefreshDataController {
    @auraEnabled
    public static void refreshTransfers(){
        List<Contribution_Agreement__c> lstCA = [SELECT Id,Total_Transfer__c,
                                                 (SELECT Id,Amount__c from Transactions__r)
                                                 FROM Contribution_Agreement__c];
        system.debug('lstCA ::'+lstCA);
        for(Contribution_Agreement__c ca : lstCA){           
            decimal totalTrans = 0;
            
            for(Transaction__c trans : ca.Transactions__r){
                if(trans.amount__c != null)
                    totalTrans += trans.amount__c;
            }
            ca.Total_Transfer__c = totalTrans;
        }
        update lstCA; 
        system.debug('lstCA ::'+lstCA);
    }
    
    @auraEnabled
    public static String refreshInvestments(){
        Id batchId = database.executeBatch(new CAInvestmentMappingBatch());
        // Optionally query AsyncApexJob for status
        AsyncApexJob job = [SELECT Id, Status, NumberOfErrors FROM AsyncApexJob WHERE Id = :batchId];
        /*if (job.Status == 'Failed' || job.NumberOfErrors > 0) {
            system.debug('in if !!');
            throw new AuraHandledException('Batch process failed with errors. Status: ' + job.ExtendedStatus);
            //return 'Error Occured while updating records..';
        }else {
            system.debug('in else !!');
            return 'Batch completed successfully.';
        }  */
        return batchId;
    }
    
    @AuraEnabled
    public static String checkBatchStatus(String jobId) {
        AsyncApexJob job = [SELECT Status, NumberOfErrors, JobItemsProcessed, TotalJobItems, ExtendedStatus FROM AsyncApexJob WHERE Id = :jobId];
        if (job.Status == 'Failed' || job.NumberOfErrors > 0) {
            throw new AuraHandledException('Batch process failed with errors. Status: ' + job.ExtendedStatus);
        }
        return job.Status;
    }
}