<apex:page controller="FileUploadController">

    <apex:form enctype="multipart/form-data">

        <input type="file" id="fileInput" />

        <button onclick="uploadFile()">Upload</button>

    </apex:form>



    <script>

        function uploadFile() {

            var fileInput = document.getElementById('fileInput');

            var file = fileInput.files[0];

            

            var action = '{!$Page.testvfv}';

            action += '?fileName=' + file.name;

            
alert(action);
            var formData = new FormData();

            formData.append('file', file);

            

            var xhr = new XMLHttpRequest();

            xhr.open('POST', action, true);

            xhr.send(formData);

        }

    </script>

</apex:page>