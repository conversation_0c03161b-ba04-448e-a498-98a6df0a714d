public class LetsgrowTriggerHandler {
    
    public void beforeInsert(List<LetsGrow__c> Lglist) {
        Map<String, LetsGrow__c> StridMap = new Map<String, LetsGrow__c>();
        Set<String> StartupLgmap = new Set<String>();
        Map<String, Date> effectiveDateMap = new Map<String, Date>();
        //Map<String, Boolean> OngoingMap = new map <String,Boolean>();
        Boolean isWithinDateRange = false;
        
        for (LetsGrow__c lg : Lglist) {
            if (lg.Cooling_off_period__c == null) {
                lg.Cooling_off_period__c = 0;
            }
            
            if(lg.Startup_Name__c != null && lg.Member_Name__c != null){
                StartupLgmap.add(lg.Member_Name__c);
            }
            
            Date effectiveDate = lg.Start_Date__c.addDays(lg.Cooling_off_period__c.intValue());
            //Date endDate = lg.End_Date__c;
            
            if(lg.Startup_Name__c != null && lg.Member_Name__c != null){
                //if (System.today() >= effectiveDate) {
                //  if (System.today() <= endDate || endDate == null) {
                //      isWithinDateRange = true;
                // OngoingMap.put(lg.Member_Name__c,isWithinDateRange);
                StridMap.put(lg.Member_Name__c,lg);
                String key = lg.Startup_Name__c + '' + lg.Member_Name__c;
                effectiveDateMap.put(key, effectiveDate);
                System.debug('effectiveDateMap123>>' + effectiveDateMap);
            }   
            //   }
            //  }
        }
        
        if (!StartupLgmap.isEmpty()) {
            List<LetsGrow__c> existingLgList = [
                SELECT Id, Member_Name__c, Ongoing__c, Startup_Name__c, End_Date__c,IsEndDateBlank__c
                FROM LetsGrow__c
                WHERE Member_Name__c IN :StartupLgmap ];
            
            for (LetsGrow__c lg : Lglist) {
                String key = lg.Startup_Name__c + '' + lg.Member_Name__c;
                Date newEffectiveDate = effectiveDateMap.get(key);
                
                if (StridMap.containsKey(lg.Member_Name__c)) {
                    for (LetsGrow__c existingLg : existingLgList) {
                        if (existingLg.Startup_Name__c + '' + existingLg.Member_Name__c == key) {
                            
                            if (!existingLg.IsEndDateBlank__c && newEffectiveDate < existingLg.End_Date__c) {
                                lg.addError('Member has an active profile with this startup.');
                            }
                            else if (existingLg.IsEndDateBlank__c) {
                                lg.addError('Member has an active profile with this startup.');
                            }
                            else if (newEffectiveDate > existingLg.End_Date__c && !existingLg.IsEndDateBlank__c) {
                                // Allow creation if the effective date is greater than the end date of an existing ongoing record.
                                break;
                            }
                        }
                    }
                }
            }
        }
    }
    public void afterinsert(List<LetsGrow__c> Lglist){
        
        Set<id> Accid = new set<id>();
        Map<id,Account> AccountLgfieldMap = new Map<id,Account>(); 
        for(LetsGrow__c lg : Lglist)
        {
            if(lg.Member_Name__c != null)
            {
                if(lg.LetsGrow_Membership_Type__c != null && lg.LetsGrow_Membership_Type__c =='LetsGrow Lead Investor Mentor' 
                   || lg.LetsGrow_Membership_Type__c =='LetsGrow Investor Mentor')
                {
                    Accid.add(lg.Member_Name__c);
                } 
            }
        }
        
        for(Account Acc : [Select id,LetsGrow_Investor_Mentor__c,LetsGrow_Lead_Investor_Mentor__c from account where id In:Accid])
        {
            AccountLgfieldMap.put(Acc.id,Acc);
        }
        
        for(LetsGrow__c lg : Lglist)
        {
            if(lg.Member_Name__c != null && lg.LetsGrow_Membership_Type__c =='LetsGrow Lead Investor Mentorr' || lg.LetsGrow_Membership_Type__c =='LetsGrow Investor Mentor')
            {
                if(lg.LetsGrow_Membership_Type__c != null && lg.LetsGrow_Membership_Type__c =='LetsGrow Lead Investor Mentor' && lg.Ongoing__c == true){
                    if(AccountLgfieldMap.containskey(lg.Member_Name__c)){
                        
                        AccountLgfieldMap.get(lg.Member_Name__c).LetsGrow_Lead_Investor_Mentor__c =true;
                    }
                } 
                
                else {
                    AccountLgfieldMap.get(lg.Member_Name__c).LetsGrow_Lead_Investor_Mentor__c = false;
                } 
                
                if(lg.LetsGrow_Membership_Type__c != null && lg.LetsGrow_Membership_Type__c =='LetsGrow Investor Mentor' && lg.Ongoing__c == true){
                    if(AccountLgfieldMap.containskey(lg.Member_Name__c)){
                        
                        AccountLgfieldMap.get(lg.Member_Name__c).LetsGrow_Investor_Mentor__c =true;
                    } 
                }
                
                else {
                    AccountLgfieldMap.get(lg.Member_Name__c).LetsGrow_Investor_Mentor__c = false;
                } 
            }
        }
        
        update AccountLgfieldMap.values();
        
    }  
    
    public void afterUpdate(List<LetsGrow__c> lgList, Map<Id, LetsGrow__c> oldMap) {
        
        Set<Id> oldAccountSet = new Set<Id>();
        Set<Id> newAccountSet = new Set<Id>();
        Map<Id, LetsGrow__c> newLgMap = new Map<Id, LetsGrow__c>();
        Map<Id, LetsGrow__c> oldLgMap = new Map<Id, LetsGrow__c>();
        
        for (LetsGrow__c lg : lgList) {
            LetsGrow__c oldLg = oldMap.get(lg.Id);
            
            if (lg.Member_Name__c != null && oldLg != null && oldLg.Member_Name__c != null &&
                lg.Member_Name__c != oldLg.Member_Name__c && oldLg.Ongoing__c == true &&
                (oldLg.LetsGrow_Membership_Type__c =='LetsGrow Lead Investor Mentor' ||
                 oldLg.LetsGrow_Membership_Type__c =='LetsGrow Investor Mentor')) {
                     
                     oldAccountSet.add(oldLg.Member_Name__c);
                     oldLgMap.put(oldLg.Member_Name__c, oldLg);
                 } 
            if (lg.Member_Name__c != null && (lg.Ongoing__c == true || lg.Ongoing__c == false) && (lg.LetsGrow_Membership_Type__c =='LetsGrow Lead Investor Mentor' || lg.LetsGrow_Membership_Type__c =='LetsGrow Investor Mentor')) {
                newAccountSet.add(lg.Member_Name__c);
                newLgMap.put(lg.Member_Name__c, lg);
            }
        }
        
        if (!oldAccountSet.isEmpty()) {
            List<Account> oldAccountsToUpdate = [SELECT Id, LetsGrow_Lead_Investor_Mentor__c, LetsGrow_Investor_Mentor__c FROM Account WHERE Id IN :oldAccountSet];
            
            for (Account acc : oldAccountsToUpdate) {
                LetsGrow__c oldLg = oldLgMap.get(acc.Id);
                
                if (oldLg != null && oldLg.LetsGrow_Membership_Type__c != null &&
                    oldLg.LetsGrow_Membership_Type__c =='LetsGrow Lead Investor Mentor') {
                        acc.LetsGrow_Lead_Investor_Mentor__c = false;
                    } 
                
                else if (oldLg != null && oldLg.LetsGrow_Membership_Type__c != null &&
                         oldLg.LetsGrow_Membership_Type__c =='LetsGrow Investor Mentor') {
                             acc.LetsGrow_Investor_Mentor__c = false;
                         }
            }
            update oldAccountsToUpdate;
        }
        
        if (!newAccountSet.isEmpty()) {
            List<Account> newAccountsToUpdate = [SELECT Id, LetsGrow_Lead_Investor_Mentor__c, LetsGrow_Investor_Mentor__c FROM Account WHERE Id IN :newAccountSet];
            
            for (Account acc : newAccountsToUpdate) {
                LetsGrow__c newLg = newLgMap.get(acc.Id);
                
                if(newLg != null && newLg.LetsGrow_Membership_Type__c != null &&  newlg.Ongoing__c == true &&
                   newLg.LetsGrow_Membership_Type__c =='LetsGrow Lead Investor Mentor') {
                       acc.LetsGrow_Lead_Investor_Mentor__c = true;
                   } 
                
                else if(newLg != null && newLg.LetsGrow_Membership_Type__c != null && newlg.Ongoing__c == false && 
                        newLg.LetsGrow_Membership_Type__c =='LetsGrow Lead Investor Mentor'){
                            
                            acc.LetsGrow_Lead_Investor_Mentor__c = False; 
                        }
                
                if(newLg != null && newLg.LetsGrow_Membership_Type__c != null && newlg.Ongoing__c == true && newLg.LetsGrow_Membership_Type__c =='LetsGrow Investor Mentor'){
                    acc.LetsGrow_Investor_Mentor__c = true;
                }
                
                else if(newLg != null && newLg.LetsGrow_Membership_Type__c != null && newlg.Ongoing__c == false && newLg.LetsGrow_Membership_Type__c =='LetsGrow Investor Mentor')
                    acc.LetsGrow_Investor_Mentor__c = False;
            }
            update newAccountsToUpdate;
        }
    }
    
    public void handleLetsGrowActivities(List<LetsGrow__c> lglist,Map<id,LetsGrow__c>OldlgList) {
        List<User_Activity__c> activitiesToInsert = new List<User_Activity__c>();
        id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        
        Set<Id> lgIds = new Set<Id>();
        for (LetsGrow__c lgRecord : lglist) {
            lgIds.add(lgRecord.Id);
        }
        
        List<LetsGrow__c> lgRecordsWithDetails = [SELECT Id, Member_Name__r.Name, Startup_Name__r.Name, LetsGrow_Membership_Type__c, Start_Date__c
                                                  FROM LetsGrow__c
                                                  WHERE Id IN :lgIds];
        Map<Id, LetsGrow__c> lgMap = new Map<Id, LetsGrow__c>();
        for (LetsGrow__c lg : lgRecordsWithDetails) {
            lgMap.put(lg.Id, lg);
        }
        
        for (LetsGrow__c lgRecord : lglist) {
                    LetsGrow__c oldLg = OldlgList.get(lgRecord.Id);
            if (!lgMap.containsKey(lgRecord.Id)) {
                system.debug('Skipping LetsGrow activity due to missing data for record: ' + lgRecord.Id);
                continue;
            }
            
            lgRecord = lgMap.get(lgRecord.Id);
            
            if (lgRecord.LetsGrow_Membership_Type__c !=null && (lgRecord.LetsGrow_Membership_Type__c == 'LetsGrow Investor Mentor' ||
                lgRecord.LetsGrow_Membership_Type__c == 'LetsGrow Lead Investor Mentor' ||
                lgRecord.LetsGrow_Membership_Type__c == 'Advisor')) {
                    
                    String NameUrl, startupUrl;
                    NameUrl = URL.getOrgDomainURL().toExternalForm() + '/' + lgRecord.Member_Name__c;
                    startupUrl = URL.getOrgDomainURL().toExternalForm() + '/' + lgRecord.Startup_Name__c;
                    
                    activitiesToInsert.add(new User_Activity__c(
                        Related_Account__c = lgRecord.Member_Name__c,
                        Activity_Type__c = 'Onboarded on LetsGrow as Investor Mentor',
                        Activity_Detail_RICH__c = (NameUrl != '' ? '<a href="' + NameUrl + '" target="_blank">' + lgRecord.Member_Name__r.Name + '</a>' : lgRecord.Member_Name__r.Name) +
                        ' Onboarded on LetsGrow as " ' + lgRecord.LetsGrow_Membership_Type__c + '" in " ' +
                        (startupUrl != '' ? '<a href="' + startupUrl + '" target="_blank">' + lgRecord.Startup_Name__r.Name + '</a>' : lgRecord.Startup_Name__r.Name) + '"',
                        Time_Stamp__c = lgRecord.Start_Date__c
                    ));
                }
        }
        
        if (!activitiesToInsert.isEmpty()) {
            insert activitiesToInsert;
        }
    }
    
    public void afterDelete(List<LetsGrow__c> deletedLetsGrows) 
    {
        // Set<Id> accountIdsToUpdate = new Set<Id>();
        Map<id,LetsGrow__c> AccLgMap = New Map<id,LetsGrow__c>();
        for (LetsGrow__c lg : deletedLetsGrows) {
            
            if (lg.Member_Name__c != null) {
                // accountIdsToUpdate.add(lg.Member_Name__c);
                AccLgMap.put(lg.Member_Name__c,lg);
            }
        }
        
        if (!AccLgMap.isEmpty()) {
            List<Account> accountsToUpdate = [SELECT Id, LetsGrow_Lead_Investor_Mentor__c, LetsGrow_Investor_Mentor__c FROM Account WHERE Id IN :AccLgMap.keyset()];
            
            for (Account acc : accountsToUpdate) {
                
                if(AccLgMap.containskey(Acc.id) && AccLgMap.get(acc.id).LetsGrow_Membership_Type__c =='LetsGrow Lead Investor Mentor'){
                    acc.LetsGrow_Lead_Investor_Mentor__c = false;
                }
                
                else if(AccLgMap.containskey(Acc.id) && AccLgMap.get(acc.id).LetsGrow_Membership_Type__c =='LetsGrow Investor Mentor'){
                    acc.LetsGrow_Investor_Mentor__c = false; 
                }                
            }
            
            update accountsToUpdate;
        }
    }
}