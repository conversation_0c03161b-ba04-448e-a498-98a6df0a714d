global with sharing class B2BuserOnboardingAPI {
    private static String CLASS_NAME = 'B2BuserOnboardingAPI';
    global static string accessToken; 
    
    @future (Callout=True) 
    global static void sendAccountDetails( Set<Id> AccIdSet , Boolean isInsert) {
        
        try{
            String endURLSetting ;
            Map<String,String> credMAp = new Map<String,String>();
            List<API_Setting__c> settingList = API_Setting__c.getall().values();
           
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].End_URL__c))
            {
                endURLSetting = ''+settingList[0].B2B_End_URL__c;
            }
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Username__c))
            {
                credMAp.put('username',''+settingList[0].B2B_Username__c);
            }
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Password__c))
            {
                credMAp.put('password',''+settingList[0].B2B_Password__c);
            }
            
            List<Account> accList = [SELECT Id, Name,RM_Code__c, ParentId, Primary_Contact__c, Primary_Country_Code__C, Official_Email__c, Personal_Email__c, B2B_User_Type__c, Is_Tenant__c, Partner_parent_account__c,L1_manager__r.Official_Email__c,
                                     L1_manager__c, L1_manager__r.Personal_Email__c, L1_manager__r.Name, L1_manager__r.Primary_Contact__c, L1_manager__r.Primary_Country_Code__C, Tenant_SPOC_Account__c ,Partner_parent_account__r.Name, Partner_parent_account__r.Official_Email__c,
                                     Partner_parent_account__r.Personal_Email__c ,Partner_parent_account__r.Tenant_SPOC_Account__c, Partner_parent_account__r.Primary_Country_Code__C, Partner_parent_account__r.Primary_Contact__c,  Org_RM__c,  Org_RM__r.Name, Org_RM__r.Personal_Email__c,
                                     Org_RM__r.Primary_Country_Code__C, Org_RM__r.Primary_Contact__c ,Org_RM__r.Official_Email__c, Partner_Type__c FROM Account WHERE B2B_User_Type__c IN ('L1','L2') AND Id IN : AccIdSet];
       
            List<ResponseWrapper> accountDetailWrapperList = new List<ResponseWrapper>();
            
            // Collecting all parent account ids
            Set<Id> parentIdSet = new Set<Id>();
            for (Account acc : accList) {
                parentIdSet.add(acc.Partner_parent_account__c);
                System.debug('parentIdSet=>'+parentIdSet);
            }
            
            Integer totalReferredUsers = 0;
            
            totalReferredUsers += [SELECT Count() FROM Account 
                                   WHERE ParentId IN :parentIdSet 
                                   AND B2B_User_Type__c = 'Customer' 
                                   AND Partner_parent_account__c IN :parentIdSet AND Org_RM__c IN :AccIdSet];
    
            for (Account acc : accList) {
         
                ResponseWrapper accountDetailWrapper = new ResponseWrapper();
                ParentAccWrapper pa = null;
                ParentAccWrapper l1 = null;
                ParentAccWrapper org = null;
                
                // Parent Account details
                if (acc.Partner_parent_account__c != null) {
                    pa = new ParentAccWrapper();
                    pa.salesforceId = acc.Partner_parent_account__c;
                    pa.memberName = acc.Partner_parent_account__r.Name;
                    pa.pcontact = acc.Partner_parent_account__r.Primary_Contact__c;
                    pa.pcountryCode = String.valueOf(acc.Partner_parent_account__r.Primary_Country_Code__C);
                    pa.pemail = acc.Partner_parent_account__r.Personal_Email__c != null ? acc.Partner_parent_account__r.Personal_Email__c : acc.Partner_parent_account__r.Official_Email__c;
                }
                
                // l1Manager Account details
                if (acc.L1_manager__c != null) {
                    l1 = new ParentAccWrapper();
                    l1.salesforceId = acc.L1_manager__c;
                    l1.memberName = acc.L1_manager__r.Name;
                    l1.pcontact = acc.L1_manager__r.Primary_Contact__c;
                    l1.pcountryCode = String.valueOf(acc.L1_manager__r.Primary_Country_Code__C);
                    l1.pemail = acc.L1_manager__r.Personal_Email__c!= null ? acc.L1_manager__r.Personal_Email__c : acc.L1_manager__r.Official_Email__c;
                }
                
                // OrgRM Account details
                if (acc.Org_RM__c != null) {
                    org = new ParentAccWrapper();
                    org.salesforceId = acc.Org_RM__c;
                    org.memberName = acc.Org_RM__r.Name;
                    org.pcontact = acc.Org_RM__r.Primary_Contact__c;
                    org.pcountryCode = String.valueOf(acc.Org_RM__r.Primary_Country_Code__C);
                    org.pemail = acc.Org_RM__r.Personal_Email__c != null ? acc.Org_RM__r.Personal_Email__c : acc.Org_RM__r.Official_Email__c;
                }
                
                if (acc.B2B_User_Type__c == 'L1' && acc.Partner_parent_account__r != null && acc.Id == acc.Partner_parent_account__r.Tenant_SPOC_Account__c) {
                    accountDetailWrapper.isSpoc = true;
                    
                    if (!parentIdSet.isEmpty() && accountDetailWrapper.isSpoc) {
                        totalReferredUsers += [SELECT COUNT() FROM Lead__c 
                                               WHERE Referred_By__c IN :parentIdSet];
                    }
                    
                } else {
                    accountDetailWrapper.isSpoc = false;
                }
                accountDetailWrapper.salesforceId = acc.Id;
                accountDetailWrapper.memberName = acc.Name;
                accountDetailWrapper.pcontact = acc.Primary_Contact__c;
                accountDetailWrapper.pcountryCode = String.valueOf(acc.Primary_Country_Code__C);
                accountDetailWrapper.pemail = acc.Personal_Email__c != null ? acc.Personal_Email__c : acc.Official_Email__c;
                accountDetailWrapper.b2bUserType = acc.B2B_User_Type__c;
                accountDetailWrapper.parentAc = pa;
                accountDetailWrapper.l1Manager = l1;
                accountDetailWrapper.orgRM = org;
                accountDetailWrapper.rmCode = acc.RM_Code__c;
                accountDetailWrapper.partnerType = acc.Partner_Type__c;
                accountDetailWrapper.totalReferredUsers = totalReferredUsers;
                
                accountDetailWrapperList.add(accountDetailWrapper);         
            }
           
            String JsonData = JSON.serialize(accountDetailWrapperList);
            system.debug('JSON DATA =>' + JsonData );
            String authEndpoint = ''+endURLSetting + '/auth/v1/login';
            // credMAp.put('username','<EMAIL>');	
            //  credMAp.put('password','qwe@1234');
            // system.debug('--credMAp--'+credMAp);
            
            HttpRequest authRequest = new HttpRequest();
            authRequest.setEndpoint(authEndpoint);
            authRequest.setMethod('POST');
            authRequest.setHeader('Content-Type', 'application/json');
            authRequest.setTimeout(120000);
            authRequest.setBody(JSON.serialize(credMAp));
            Http http = new Http();
            HTTPResponse authResponse = http.send(authRequest);
        
            if (authResponse.getStatusCode() == 200) {
                
                String jsonstr = JSON.serialize(authRequest.getBody());
                JSONParser parser = JSON.createParser(jsonStr);
                Map<String, Object> results = (Map<String, Object>) JSON.deserializeUntyped(authResponse.getBody());
                String tkn = JSON.serialize(results.get('data'));
                Map<String, Object> token = (Map<String, Object>) JSON.deserializeUntyped(tkn);
                accessToken = String.valueOF(token.get('token'));
                
                String endURL = '' + endURLSetting + '/salesforce/v1/syncUser';
                HttpRequest request = new HttpRequest();   
                request.setEndpoint(endURL);
                request.setHeader('Content-Type','application/json');
                request.setHeader('Authorization','Bearer ' + accessToken);
                request.setMethod('POST');
                request.setTimeout(120000);
                request.setBody(JsonData);
                Http http1 = new Http();
                HTTPResponse res = http1.send(request);
                
            }  else {
                System.debug('Authentication failed: ' + authResponse.getBody());
                Error_Log__c log =  new Error_Log__c();
                log.Class_Name__c = CLASS_NAME;
                log.method__c = 'sendAccountDetails_AuthFail' ;
                log.Error_Message__c = authResponse.getBody();
                insert log;
            } 
        }
        catch(Exception ex) {
            system.debug('Error: '+ex.getMessage());
            Error_Log__c log =  new Error_Log__c();
            log.Class_Name__c = CLASS_NAME;
            log.method__c = 'sendAccountDetails' ;
            log.Error_Message__c = ex.getMessage();
            insert log;
            System.debug('Error sending request to B2B web App : ' + log.Error_Message__c);     
            
            //send error email
            String errorDetails = 
                'An error occurred in the class B2BuserOnboardingAPI. Below are the details:\n\n' +
                'Timestamp: ' + DateTime.now().format('yyyy-MM-dd HH:mm:ss') + '\n' +
                'Request Parameters:\n' +
                '  Account IDs: ' + JSON.serialize(AccIdSet) + '\n' +
                'Class Name: ' + CLASS_NAME + '\n' +
                'Error Log ID: ' + log.Id + '\n\n' +
                'Error Message: ' + ex.getMessage() + '\n' +
                'Error Stack Trace: ' + ex.getStackTraceString() + '\n';
            
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setToAddresses(new String[] { '<EMAIL>' });
            email.setSubject('Error while onboarding user');
            email.setPlainTextBody(errorDetails);
            Messaging.sendEmail(new Messaging.SingleEmailMessage[] { email });       
        }        
    }
    
    public class ResponseWrapper {
        public String salesforceId;
        public ParentAccWrapper parentAc;
        public ParentAccWrapper l1Manager;
        public ParentAccWrapper orgRM;
        public String b2bUserType;
        public Boolean isSpoc;
        public String memberName;
        public String pcontact;
        public String pcountryCode;
        public String pemail;
        public Integer totalReferredUsers;
        public String rmCode;
        public String partnerType;
    }
    
    global class ParentAccWrapper {
        public String salesforceId;
        public String memberName;
        public String pcontact;
        public String pcountryCode;
        public String pemail;
    }
}