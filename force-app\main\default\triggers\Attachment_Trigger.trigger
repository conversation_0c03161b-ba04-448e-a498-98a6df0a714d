trigger Attachment_Trigger on Attachment (after insert) {
    
    if(trigger.New.size()==1 && trigger.New[0].ContentType == 'application/vnd.ms-excel' && ''+trigger.New[0].parentID.getsobjecttype() == 'Events__c')
    {
       AttachmentTrg_Handler.createRecord(trigger.New[0]);
       /*
        Map < String, Integer > fieldNumberMap = new Map < String, Integer > ();
        List<String> lstFieldNames = new list<String>();
        Integer fieldNumber;
        String fieldValue;
        Blob csvFileBody;
        for(attachment att : trigger.New)
        {
            if(''+att.parentID.getsobjecttype() == 'Events__c'){
                String csvFullData;
                csvFileBody = att.Body;
                csvFullData = ''+csvFileBody.toString();
                system.debug('csvFullData>>>>'+csvFullData);
                
                String[] csvRowValues = csvFullData.split('\n');
                string[] headerNames = csvRowValues[0].split(',');
                system.debug('csvRowValues>>>>'+csvRowValues);
                system.debug('headerNames>>>>'+headerNames);
                system.debug('headerNames>>>>'+headerNames.size());

                for (Integer i = 0; i < headerNames.size(); i++) {
                    fieldNumberMap.put(headerNames[i], i);
                    lstFieldNames.add(headerNames[i].trim());
                }
                
                for (Integer i = 1; i < csvRowValues.size(); i++) {
                    Contact conObj = new Contact();
                    system.debug('csvRowValues>>>>'+csvRowValues[i]);
                    
                    string[] csvRecordData = csvRowValues[i].split(',');
                    for(String fieldName: headerNames) 
                    {
                        fieldNumber = fieldNumberMap.get(fieldName);
                        fieldValue = csvRecordData[fieldNumber];
                        system.debug('fieldName>>>>'+fieldName);
                        system.debug('fieldValue>>>>'+fieldValue);
                        //conObj.put(fieldName.trim(), fieldValue.trim());
                    }
                    
                    //lstContacts.add(conObj);                
                }
        
        
            }
        }
        */
    }
}