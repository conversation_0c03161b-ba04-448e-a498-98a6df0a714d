@RestResource(urlMapping='/LeadConnectAPI/*')
global with sharing class LeadOraiConnectAPI {
	@HttpGet
    global static LeadWrapper getLeadDetails() {
        RestRequest req = RestContext.request;
        String phone = req.requestURI.substring(req.requestURI.lastIndexOf('/') + 1);
        system.debug('req ::'+phone);
        Lead__c ld = [SELECT Id,Primary_Contact__c,Relationship_Owner__r.name,Relationship_Owner__r.Contact_NO__C,Relationship_Owner__r.RM_calendly_Link__c FROM Lead__c where Primary_Contact__c = :phone LIMIT 1];
        LeadWrapper wrap =  new LeadWrapper();
        wrap.Phone = ld.Primary_Contact__c;
        wrap.RM_Name = ld.Relationship_Owner__r.name;
        wrap.RM_Number = ld.Relationship_Owner__r.Contact_NO__C;
        wrap.link = ld.Relationship_Owner__r.RM_calendly_link__c;
        return wrap;
    }
    
    @HttpPost
    global static String updateLeadDetails(String phone,string option_selected,string status) {
        //RestRequest req = RestContext.request;
		system.debug('req ::'+phone);
        //Map<String, String> leadRequestParams =req.params;
        system.debug('req ::'+status);
        Lead__c ld = [SELECT Id,Bot_Input__c FROM Lead__c where Primary_Contact__c = :phone LIMIT 1];
        ld.Bot_Input__c = status;
        try {
            update ld;
        	return 'Lead details updated';	
        }
        catch(Exception ex){
            return 'Error Occured :'+ex.getMessage();
        }
    }
    
    global class LeadWrapper{
        global string Phone;
        global string RM_Name;       
        global string RM_Number;     
        global string link;
    }
    
    global class LeadUpdateWrapper{
        global string phone;
        global string option_selected;       
        global string status;       
    }
}