/**************************************************************************
 This test class is for NewUpdateCallStatusSchedulableTest created on 8th Nov 2023. 
 *************************************************************************/

@isTest
public class NewUpdateCallStatusSchedulableTest {
    @isTest
    static void testSchedulable() {
        RecordType rt = [SELECT Id FROM RecordType WHERE Name = 'IPV' AND SObjectType = 'Account' LIMIT 1];
        Account testAccount = TestFactory.createAccount();
        testAccount.Date_of_Payment__c = Date.today().addDays(-20);
        testAccount.Name = 'Test Account2';
        insert testAccount;

        Test.startTest();
        NewUpdateCallStatusSchedulable schedulableInstance = new NewUpdateCallStatusSchedulable();
        schedulableInstance.execute(null);
        Test.stopTest();

        Account updatedAccount = [SELECT Id, L1_Call_status__c, L1_Call_delayed_days__c FROM Account WHERE Id = :testAccount.Id];
        System.assertEquals('Overdue', updatedAccount.L1_Call_status__c);
    }
}