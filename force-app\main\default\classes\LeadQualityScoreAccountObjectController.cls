public class LeadQualityScoreAccountObjectController {
	
    @AuraEnabled(cacheable=true)
    public static Map<String , Integer> getLeadQualityScoreAccountObject(Id accountId)
    {
        Account account = [SELECT Id , Name , Lead_Quality_Score__c , Lead_Quality_Score_Percentage__c ,Lead_Source__c , College_Tier__c , 
                           Designation_Band__c , ParentId ,  Residential_Status__c , Total_Number_of_Investor_Calls__c , 
                           Total_Number_of_Founder_Calls__c , Total_Numbe_of_Offline_Online_event__c , Total_Referred__c ,
                           Bot_Input__c , App_Signup_Date__c , 	Are_They_Part_of_Other_Platform__c , Bot_Journey_Stage__c
                           FROM Account WHERE RecordType.Name ='IPV' AND Id =: accountId];
        
        System.debug('Account >>>>> ' + account);
        Map<String , Integer> fieldsWithScores = new Map<String , Integer>();
        
        Integer totalScore = 0;
        Integer eachScore;
        
        
        //   1. Score of "Are They Part of Other Platform"
        if(account.Are_They_Part_of_Other_Platform__c != null)
        {
        	if(account.Are_They_Part_of_Other_Platform__c == 'Yes')
            {
                eachScore = 2;
                fieldsWithScores.put('Are They Part of Other Platform' , eachScore);
            }
            else if(account.Are_They_Part_of_Other_Platform__c == 'No')
            {
                eachScore = 0;
                fieldsWithScores.put('Are They Part of Other Platform' , eachScore);
            }    
        }
        else
        {
            eachScore = 0;
                fieldsWithScores.put('Are They Part of Other Platform' , eachScore);
        }
        
        
        
		//   2. Score of "App Signup Date"       
        if(account.App_Signup_Date__c != null)
        {
            eachScore = 2;
            fieldsWithScores.put('App Signup Date' , eachScore);
        }
        else
        {
            eachScore = 1;
            fieldsWithScores.put('App Signup Date' , eachScore);
        }
             
        //   3. Score of "Total Referred (Account/Leads)"
        if(account.Total_Referred__c == 0)
        {
             eachScore = 0;
			 fieldsWithScores.put('Total Referred (Account/Leads)' , eachScore);
        }
        else if(account.Total_Referred__c == 1)
        {
             eachScore = 1;
             fieldsWithScores.put('Total Referred (Account/Leads)' , eachScore);
        }
        else if(account.Total_Referred__c >= 2 && account.Total_Referred__c <= 5)
        {
             eachScore = 3;
             fieldsWithScores.put('Total Referred (Account/Leads)' , eachScore);
        }
        else if(account.Total_Referred__c >= 6 && account.Total_Referred__c <= 10)
        {
             eachScore = 4;
             fieldsWithScores.put('Total Referred (Account/Leads)' , eachScore);
        }
        else if(account.Total_Referred__c > 10)
        {
             eachScore = 5;
             fieldsWithScores.put('Total Referred (Account/Leads)' , eachScore);
        }       
        else if(account.Total_Referred__c == null)
        {
             eachScore = 0;
			 fieldsWithScores.put('Total Referred (Account/Leads)' , eachScore);
        }
                
        
        
        //   4. Score of "Bot Input"
        if(account.Bot_Input__c != null)
        {
            eachScore = 2;
            fieldsWithScores.put('Bot Input' , eachScore);
        }
        else if(account.Bot_Input__c == null && account.Bot_Journey_Stage__c == null)
        {
            eachScore = 1;
            fieldsWithScores.put('Bot Input' , eachScore);
        }
        else if(account.Bot_Input__c == null && account.Bot_Journey_Stage__c != null)
        {
            eachScore = 0;
            fieldsWithScores.put('Bot Input' , eachScore);
        }
        
        
        //   5. Score of "Total Number of offline/ online events"
        if(account.Total_Numbe_of_Offline_Online_event__c != null)
        {            
            if(account.Total_Numbe_of_Offline_Online_event__c == 0)
            {
                eachScore = 0;
                fieldsWithScores.put('Total Number of offline/ online events' , eachScore);
            }
            else if(account.Total_Numbe_of_Offline_Online_event__c >= 1 && account.Total_Numbe_of_Offline_Online_event__c <= 4)
            {
                eachScore = 3;
                fieldsWithScores.put('Total Number of offline/ online events' , eachScore);
            }
            else if(account.Total_Numbe_of_Offline_Online_event__c >= 5)
            {
                eachScore = 5;
                fieldsWithScores.put('Total Number of offline/ online events' , eachScore);
            }
        }
        else
        {
			eachScore = 0;
            fieldsWithScores.put('Total Number of offline/ online events' , eachScore);
        }
        
        
        //   6. Score of "Total Number of Investor Calls"
        if(account.Total_Number_of_Investor_Calls__c != null)
        {
            if(account.Total_Number_of_Investor_Calls__c == 0)
            {
                eachScore = 0;
                fieldsWithScores.put('Total Number of Investor Calls' , eachScore);
            }
            else if(account.Total_Number_of_Investor_Calls__c >= 1 && account.Total_Number_of_Investor_Calls__c <= 4)
            {
                eachScore = 2;
                fieldsWithScores.put('Total Number of Investor Calls' , eachScore);
            }
            else if(account.Total_Number_of_Investor_Calls__c >= 5 && account.Total_Number_of_Investor_Calls__c <= 9)
            {
                eachScore = 4;
                fieldsWithScores.put('Total Number of Investor Calls' , eachScore);
            }
            else if(account.Total_Number_of_Investor_Calls__c >= 10)
            {
                eachScore = 5;
                fieldsWithScores.put('Total Number of Investor Calls' , eachScore);
            }
        }
        else
        {
             eachScore = 0;
             fieldsWithScores.put('Total Number of Investor Calls' , eachScore);
        }
        
        
        //   7. Score of "Total Number of Founder Calls"
        if(account.Total_Number_of_Founder_Calls__c != null)
        {
            if(account.Total_Number_of_Founder_Calls__c == 0)
            {
                eachScore = 0;
                fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
            }
            else if(account.Total_Number_of_Founder_Calls__c >= 1 && account.Total_Number_of_Founder_Calls__c <= 4)
            {
                eachScore = 1;
                fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
            }    
            else if(account.Total_Number_of_Founder_Calls__c >= 5 && account.Total_Number_of_Founder_Calls__c <= 9)
            {
                eachScore = 2;
                fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
            }    
            else if(account.Total_Number_of_Founder_Calls__c >= 10 && account.Total_Number_of_Founder_Calls__c <= 19)
            {
                eachScore = 3;
                fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
            }
            else if(account.Total_Number_of_Founder_Calls__c >= 20)
            {
                eachScore = 4;
                fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
            }
        }
        else
        {
             eachScore = 0;
             fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
        }

        
        //   8. Score of "Residential Status"
        if(account.Residential_Status__c != null)
        {
            if(account.Residential_Status__c == 'NRI')
            {
                eachScore = 2;
                fieldsWithScores.put('Residential Status' , eachScore);
            }
            else if(account.Residential_Status__c == 'INDIAN')
            {
                eachScore = 1;
                fieldsWithScores.put('Residential Status' , eachScore);
            }
        }
        else
        {
             eachScore = 0;
             fieldsWithScores.put('Residential Status' , eachScore);
        }
        
        
        //   9. Score of "Designation Band"
        if(account.Designation_Band__c == null)
        {
			eachScore = 3;
            fieldsWithScores.put('Designation Band' , eachScore);
        }
        else if(account.Designation_Band__c == 'Band 1')
        {
            eachScore = 5;
            fieldsWithScores.put('Designation Band' , eachScore);            
        }
        else if(account.Designation_Band__c == 'Band 2')
        {
            eachScore = 4;
            fieldsWithScores.put('Designation Band' , eachScore);
        }
        else if(account.Designation_Band__c == 'Band 3')
        {
            eachScore = 2;
            fieldsWithScores.put('Designation Band' , eachScore);
        }
        else if(account.Designation_Band__c == 'Band 4')
        {
            eachScore = 1;
            fieldsWithScores.put('Designation Band' , eachScore);
        }
        
        
        
        //   10. Score of "College Tier"
        if(account.College_Tier__c == null)
        {
         	eachScore = 1;
            fieldsWithScores.put('College Tier' , eachScore);
        }
        else if(account.College_Tier__c == 'Tier 1')
        {
            eachScore = 5;
            fieldsWithScores.put('College Tier' , eachScore);
        }
        else if(account.College_Tier__c == 'Tier 2')
        {
            eachScore = 3;
            fieldsWithScores.put('College Tier' , eachScore);
        }
        else if(account.College_Tier__c == 'Tier 3')
        {
            eachScore = 1;
            fieldsWithScores.put('College Tier' , eachScore);
        }
        
        if(account.ParentId != null)
        {
            Account referredAccount = [SELECT Total_Amount_Invested__c FROM Account WHERE Id = :account.ParentId LIMIT 1];
            System.debug('Account Total Investment >>>>> ' + referredAccount.Total_Amount_Invested__c);
            
            //   11. Score of "Total Investment by Referrer"
            if(referredAccount.Total_Amount_Invested__c == 0 || referredAccount.Total_Amount_Invested__c == null)
            {
                eachScore = 1;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);
            }        
            else if(referredAccount.Total_Amount_Invested__c >= 1 && referredAccount.Total_Amount_Invested__c <= 500000)
            {
                eachScore = 2;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);
            }
            else if(referredAccount.Total_Amount_Invested__c > 500000 && referredAccount.Total_Amount_Invested__c <= 1000000)
            {
                eachScore = 3;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);
            }
            else if(referredAccount.Total_Amount_Invested__c > 1000000 && referredAccount.Total_Amount_Invested__c <= 2000000)
            {
                eachScore = 4;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);
            }
            else if(referredAccount.Total_Amount_Invested__c > 2000000)
            {
                eachScore = 5;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);
            }
        }
        else
        {
                eachScore = 0;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);		
        }
        
        
        System.debug('Lead Source >>> ' + account.Lead_Source__c);
        
        
        Set<String> leadSources = new Set<String>{
            'Referral','Others','Organic-Website','Linkedin','Facebook','Online Events','Organic-App Sign Up','Startup Calls','Sales Navigator','Offline Events',
            'Google Ads','B2B-Wealth Manager','App Referral','External database','B2B-Partnerships','B2B Real Estate','App','Offline Events-Gated Community',
            'B2B-Credit Cards','B2B-Luxury Brands','B2B-Corporates','Organic-social media','Whatsapp','Member referral','Email','Google Form','Ozonetel','Website',
            'Linkedin Ads','Linkedin - ORAI','Facebook Ads','Facebook - ORAI','Masterclass','External Masterclass Sessions','Webinar leads','App Sign Up','Feedback Form',
            'Saturday Calls','Wednesday Calls','Linkedin - Sales Navigator','Sales QL','GoogleSEM','WealthTrust','Investor Harvesting','Status Match','Sweetbox DB',
            'MF Database','Partnerships','Partnerships - Vinners','ORAI','Real Estate Project','Instagram','FB Comment','FB DM','Social Media Organic','Instagram DM',
            'Twitter', 'Events' , 'Webinar' , 'VC Connect Person' , 'Member Referral'
        };

        //   12. Score of "Lead Source"
        if(account.Lead_Source__c != null)
        {
            if(account.Lead_Source__c == 'Referral'|| account.Lead_Source__c == 'Whatsapp' || account.Lead_Source__c == 'Member Referral')
            {
                eachScore = 5;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'Others'|| account.Lead_Source__c == 'Google Form' || account.Lead_Source__c == 'Email' || account.Lead_Source__c == 'VC Connect Person' || account.Lead_Source__c == 'Ozonetel')
            {
                eachScore = 2;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'Organic-Website'|| account.Lead_Source__c == 'Website')
            {
                eachScore = 4;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'Linkedin'|| account.Lead_Source__c == 'Linkedin Ads' || account.Lead_Source__c == 'Linkedin - ORAI')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'Facebook'|| account.Lead_Source__c == 'Facebook Ads' || account.Lead_Source__c == 'Facebook - ORAI')
            {
                fieldsWithScores.put('Lead Source' , 2);                
            }
            else if(account.Lead_Source__c == 'Online Events'|| account.Lead_Source__c == 'Events' ||account.Lead_Source__c == 'Masterclass' || account.Lead_Source__c == 'External Masterclass Sessions' || account.Lead_Source__c == 'Webinar leads' || account.Lead_Source__c == 'Webinar')
            {
                eachScore = 2;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'Organic-App Sign Up'|| account.Lead_Source__c == 'App Sign Up')
            {
                eachScore = 4;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'Startup Calls' || account.Lead_Source__c == 'Feedback Form' || account.Lead_Source__c == 'Saturday Calls' || account.Lead_Source__c == 'Wednesday Calls')
            {
                eachScore = 2;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'Sales Navigator'|| account.Lead_Source__c == 'Linkedin - Sales Navigator' || account.Lead_Source__c == 'Sales QL')
            {
                eachScore = 2;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'Offline Events')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'Google Ads'|| account.Lead_Source__c == 'GoogleSEM')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'B2B-Wealth Manager'|| account.Lead_Source__c == 'WealthTrust')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'App Referral')
            {
                eachScore = 5;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'External database' || account.Lead_Source__c == 'Investor Harvesting' || account.Lead_Source__c == 'Status Match' || account.Lead_Source__c == 'Sweetbox DB' || account.Lead_Source__c == 'MF Database')
            {
                eachScore = 2;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'B2B-Partnerships' || account.Lead_Source__c == 'Partnerships' || account.Lead_Source__c == 'Partnerships - Vinners' || account.Lead_Source__c == 'ORAI')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'B2B Real Estate')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'App')
            {
                eachScore = 4;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'Offline Events-Gated Community')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'B2B-Credit Cards'|| account.Lead_Source__c == 'Real Estate Project')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'B2B-Luxury Brands')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'B2B-Corporates')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(account.Lead_Source__c == 'Organic-social media'|| account.Lead_Source__c == 'Instagram' || account.Lead_Source__c == 'FB Comment' || account.Lead_Source__c == 'FB DM' || account.Lead_Source__c == 'Social Media Organic' || account.Lead_Source__c == 'Instagram DM' || account.Lead_Source__c == 'Twitter')
            {
                eachScore = 4;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(!leadSources.contains(account.Lead_Source__c))
            {
                eachScore = 1;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
        }
        else
        {
            eachScore = 1;
            fieldsWithScores.put('Lead Source' , eachScore);
        }
        
        // Calculate total score
        for (Integer score : fieldsWithScores.values()) {
            totalScore += score;
        }
                
		
        // Calculate percentage
        Integer maximumPossibleScore = maxScore();
        Decimal percentage = (Decimal.valueOf(totalScore) / Decimal.valueOf(maximumPossibleScore)) * 100;
        
		// Update the Lead Quality Score % & Lead Quality Score field
		account.Lead_Quality_Score__c = totalScore;
        account.Lead_Quality_Score_Percentage__c = percentage.setScale(2, System.RoundingMode.HALF_UP);        
        fieldsWithScores.put('Lead Quality Score', totalScore);
        
        System.enqueueJob(new UpdateQualityScoreJob(account));
       
        System.debug('The Map of Fields With Its Score >>>>>> ' + fieldsWithScores);
        
        return fieldsWithScores;
    }

	public static Integer maxScore()
    {
        Integer totalPossibleScore = 0;
        Integer leadSourceMAXScore = 5;
        Integer totalInvestmentMAXScore = 5;
        Integer collegerTierMAXScore = 5;
        Integer designationMAXScore = 5;
        Integer residentMAXScore = 2;
        Integer totalNumberOfInvestorMAXScore = 4;
        Integer totalNumberOfFounderMAXScore = 5;
        Integer totalNumberOfEventsMAXScore = 5;
        Integer botInputMAXScore = 2;
        Integer appSignUpMAXScore = 2;
        Integer totalRefferedMAXScore = 5;
        Integer areTheyPartOfAngelInvestmentMAXScore = 2;
        
        //  Counting The Total Possible/Maximum Score Of Quality Lead
        totalPossibleScore = leadSourceMAXScore + totalInvestmentMAXScore + collegerTierMAXScore + designationMAXScore + residentMAXScore + totalNumberOfInvestorMAXScore + totalNumberOfFounderMAXScore + totalNumberOfEventsMAXScore +  botInputMAXScore + appSignUpMAXScore + totalRefferedMAXScore +  areTheyPartOfAngelInvestmentMAXScore;
        
        return totalPossibleScore;
    }
}