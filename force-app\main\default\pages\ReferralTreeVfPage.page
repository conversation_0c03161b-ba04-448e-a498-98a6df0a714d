<apex:page controller="ReferralTreeVfPageController" action="{!initializeHierarchy}">
   <style>
        
    .tree {
        width: 100%;
        overflow-x: auto;
    }
    .table-container {
        max-height: 400px;
        overflow-y: auto;
    }
    .table {
        width: 100%;
        border-collapse: collapse;
    }
    .table th,
    .table td {
        padding: 5px;
        border-bottom: 1px solid #ccc;
    }
    .sticky-totals {
        background-color: white;
        position: sticky;
        top: 0;
        z-index: 2;
        font-size: 13px;
        color: #00A1E0;
    }
    .table thead {
        position: sticky;
        top: 0;
        z-index: 3;
        background-color: #f8f8f8;
    }
    .tree ul {
        padding-left: 20px;
        list-style-type: none;
    }
    .tree li {
        margin: 0;
        padding: 5px 10px;
        position: relative;
    }
    .tree li.parent_li > span {
        cursor: pointer;
        font-weight: bold;
        color: blue;
    }
    .tree li.parent_li > span:before,
    .tree li.parent_li > span:after {
        content: "";
        position: absolute;
        top: 7px;
        left: -9px;
        width: 1px;
        height: 12px;
        border-left: 1px solid #ccc;
        border-bottom: 1px solid #ccc;
        transform: rotate(-45deg);
    }
    .tree li.parent_li > span:after {
        left: -3px;
        transform: rotate(45deg);
    }
    .tree li > span {
        display: inline-block;
        min-width: 120px;
        padding: 5px 0;
        border-radius: 3px;
        font-size: 15px;
        color: blue;
    }
    .tree li > span:hover {
        background: #f4f4f4;
    }
    .tree li > ul > li {
        display: none;
    }
    .tree li.active > ul > li {
        display: block;
    }
    .tree li.active > span:after,
    .tree li.active > span:before {
        border-color: #ccc transparent transparent transparent;
        top: 13px;
    }
    .accordion-toggle {
        cursor: pointer;
        width: 15px;
        font-size: 15px;
        margin-left: 5px;
    }
    </style>
  <div class="tree well">
        <div class="table-container">
            <table class="table table-bordered">
                <thead>
                    <!-- Second Header Row: Totals -->
                    <tr class="sticky-totals">
                        <th></th>
                        <th>
                            <b style="color: green; font-size: 13px;">Active:</b> <b style= "color: #00A1E0; font-size: 13px;">{!totalActive}</b><br />
                            <b style="color: red; font-size: 13px;">Inactive:</b> <b style="color: #00A1E0; font-size: 13px;">{!totalInactive}</b>
                        </th>
                        <th></th>
                        <th><b style="color: #00A1E0; font-size: 13px;">{!TotalInvested}</b></th>
                        <th><b style="color: #00A1E0;font-size: 13px;">{!TotalComitted}</b></th>
                        <th><b style="color: #00A1E0; font-size: 13px;">{!TotalReferred}</b></th>
                        <th></th>
                    </tr >
                    <!-- First Header Row: Column Names -->
                    <tr class="table thead" >
                        <th>Name</th>
                        <th scope="col">Type</th>
                        <th scope="col">Membership Status</th>
                        <th scope="col">Total Invested</th>
                        <th scope="col">Total Committed</th>
                        <th scope="col">Total Referred</th>
                        <th scope="col">Relationship Manager</th>
                    </tr>
                </thead>
                <tbody >
                    <apex:outputPanel rendered="{!mainParentParent != null}">
                        <tr>
                            <td colspan="7"></td>
                        </tr>
                        <tr>
                            <td>
                                <span>
                                    <img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/user-6769.svg"  height="12" width="12"  alt="SVG Icon" />
                                </span>&nbsp;
                                <b><a href="/{!mainParentParent.Id}" target="_blank">
                                    {!mainParentParent.Name}
                                    </a></b>
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </apex:outputPanel>
                    <apex:repeat value="{!ParentHierarchy}" var="parent">
                        <tr>
                            <td style="padding-left: 20px;" >
                                
                                <span>
                                    <img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Referral+Level+Icon.svg" height="15" width="15" alt="SVG Icon" />
                                </span>&nbsp;
                                
                                <b><a href="/{!IF(parent.account != null, parent.account.Id, '')}" target="_blank"   style="color: #00A1E0;">
                                    {!IF(parent.account != null, parent.account.Name, parent.lead.Name)}
                                    </a></b>
                                <span class="accordion-toggle" onclick="toggleChildren(this)">
                                    {!IF(parent.Children.size > 0, 'ʌ', '')}
                                </span>
                            </td>
                            <td style="color:{!parent.MembershipStatus}">
                                {!IF(parent.account != null, 'Account', 'Lead')}
                            </td>
                            <td>
                                {!IF(parent.account != null, parent.account.Membership_Status__c, parent.lead.Membership_Status__c)}
                            </td>
                            <td>
                                {!IF(parent.account != null, parent.account.Total_Amount_Invested__c, '')}
                            </td>
                            <td>
                                {!IF(parent.account != null, parent.account.Total_Amount_Committed__c, '')}
                            </td>
                            <td>
                                {!IF(parent.account != null, parent.account.Total_Referred__c, '')}
                            </td>
                            <td>
                                {!IF(parent.account != null, parent.account.Relationship_Manager__r.Name, parent.lead.Relationship_Owner__r.Name)}
                            </td>
                        </tr>
                        <apex:repeat value="{!parent.Children}" var="child">
                            <tr>
                                <td style="padding-left: 40px;">
                                    <span>
                                        <img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/First+Level+Referral+Icon.svg" height="15" width="15" alt="SVG Icon" />
                                    </span>&nbsp;
                                    <a href="/{!IF(child.account != null, child.account.Id, '')}" target="_blank"  >
                                        {!IF(child.account != null, child.account.Name, child.lead.Name)}
                                    </a>
                                    <span class="accordion-toggle" onclick="toggleChildren(this)">
                                        {!IF(child.Children.size > 0, 'ʌ', '')}
                                    </span>
                                    
                                </td>
                                <td style="color:{!child.MembershipStatus}">
                                    {!IF(child.account != null, 'Account', 'Lead')}
                                </td>
                                <td>
                                    {!IF(child.account != null, child.account.Membership_Status__c, child.lead.Membership_Status__c)}
                                </td>
                                <td>
                                    {!IF(child.account != null, child.account.Total_Amount_Invested__c, '')}
                                </td>
                                <td>
                                    {!IF(child.account != null, child.account.Total_Amount_Committed__c, '')}
                                </td>
                                <td>
                                    {!IF(child.account != null, child.account.Total_Referred__c, '0')}
                                </td>
                                <td>
                                    {!IF(child.account != null, child.account.Relationship_Manager__r.Name, child.lead.Relationship_Owner__r.Name)}
                                </td>
                            </tr>
                            <apex:repeat value="{!child.Children}" var="grandchild">
                                <tr>
                                    <td style="padding-left: 60px;">
                                        <span>
                                            <img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Second+Level+Referral+Icon.svg" height="15" width="15" alt="SVG Icon" />
                                        </span>&nbsp;
                                        <a href="/{!IF(grandchild.account != null, grandchild.account.Id, '')}" target="_blank">
                                            {!IF(grandchild.account != null, grandchild.account.Name, grandchild.lead.Name)}
                                        </a>
                                        <span class="accordion-toggle" onclick="toggleChildren(this)">
                                            {!IF(grandchild.Children.size > 0, 'ʌ', '')}
                                        </span>
                                    </td>
                                    <td style="color:{!grandchild.MembershipStatus}">
                                        {!IF(grandchild.account != null, 'Account', 'Lead')}
                                    </td>
                                    <td>
                                        {!IF(grandchild.account != null, grandchild.account.Membership_Status__c, grandchild.lead.Membership_Status__c)}
                                    </td>
                                    <td>
                                        {!IF(grandchild.account != null, grandchild.account.Total_Amount_Invested__c, '')}
                                    </td>
                                    <td>
                                        {!IF(grandchild.account != null, grandchild.account.Total_Amount_Committed__c, '')}
                                    </td>
                                    <td>
                                        {!IF(grandchild.account != null, grandchild.account.Total_Referred__c, '0')}
                                    </td>
                                    <td>
                                        {!IF(grandchild.account != null, grandchild.account.Relationship_Manager__r.Name, grandchild.lead.Relationship_Owner__r.Name)}
                                    </td>
                                </tr>
                                
                                <apex:repeat value="{!grandChild.Children}" var="childLevel5">
                                    <tr>
                                        <td style="padding-left: 80px;">
                                            <span>
                                                <img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Second+Level+Referral+Icon.svg" height="15" width="15" alt="SVG Icon"/>
                                            </span>&nbsp;&nbsp;
                                            <a href="/{!IF(childLevel5.account != null, childLevel5.account.Id, '')}" target="_blank">
                                                {!IF(childLevel5.account != null, childLevel5.account.Name, childLevel5.lead.Name)}
                                            </a>
                                            <span class="accordion-toggle" onclick="toggleChildren(this)">
                                                {!IF(childLevel5.Children.size > 0, 'ʌ', '')}
                                            </span>
                                        </td>
                                        <td style="color:{!childLevel5.MembershipStatus}">
                                            {!IF(childLevel5.account != null, 'Account', 'Lead')}
                                        </td>
                                        <td>
                                            {!IF(childLevel5.account != null, childLevel5.account.Membership_Status__c, childLevel5.lead.Membership_Status__c)}
                                        </td>
                                        <td>
                                            {!IF(childLevel5.account != null, childLevel5.account.Total_Amount_Invested__c, '')}
                                        </td>
                                        <td>
                                            {!IF(childLevel5.account != null, childLevel5.account.Total_Amount_Committed__c, '')}
                                        </td>
                                        <td>
                                            {!IF(childLevel5.account != null, childLevel5.account.Total_Referred__c, '0')}
                                        </td>
                                        <td>
                                            {!IF(childLevel5.account != null, childLevel5.account.Relationship_Manager__r.Name, childLevel5.lead.Relationship_Owner__r.Name)}
                                        </td>
                                    </tr>
                                    <apex:repeat value="{!childLevel5.Children}" var="childLevel6">
                                        <tr>
                                            <td style="padding-left: 100px;">
                                                <span>
                                                    <img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Second+Level+Referral+Icon.svg" height="15" width="15" alt="SVG Icon"/>
                                                </span>&nbsp;&nbsp;
                                                <a href="/{!IF(childLevel6.account != null, childLevel6.account.Id, '')}" target="_blank">
                                                    {!IF(childLevel6.account != null, childLevel6.account.Name, childLevel6.lead.Name)}
                                                </a>
                                                <span class="accordion-toggle" onclick="toggleChildren(this)">
                                                    {!IF(childLevel6.Children.size > 0, 'ʌ', '')}
                                                </span>
                                            </td>
                                            <td style="color:{!childLevel6.MembershipStatus}">
                                                {!IF(childLevel6.account != null, 'Account', 'Lead')}
                                            </td>
                                            <td>
                                                {!IF(childLevel6.account != null, childLevel6.account.Membership_Status__c, childLevel6.lead.Membership_Status__c)}
                                            </td>
                                            <td>
                                                {!IF(childLevel6.account != null, childLevel6.account.Total_Amount_Invested__c, '')}
                                            </td>
                                            <td>
                                                {!IF(childLevel6.account != null, childLevel6.account.Total_Amount_Committed__c, '')}
                                            </td>
                                            <td>
                                                {!IF(childLevel6.account != null, childLevel6.account.Total_Referred__c, '')}
                                            </td>
                                            <td>
                                                {!IF(childLevel6.account != null, childLevel6.account.Relationship_Manager__r.Name, childLevel6.lead.Relationship_Owner__r.Name)}
                                            </td>
                                        </tr>
                                        <apex:repeat value="{!childLevel6.Children}" var="childLevel7">
                                            <tr>
                                                <td style="padding-left: 120px;">
                                                    <span>
                                                        <img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Second+Level+Referral+Icon.svg" height="15" width="15" alt="SVG Icon"/>
                                                    </span>&nbsp;&nbsp;
                                                    <a href="/{!IF(childLevel7.account != null, childLevel7.account.Id, '')}" target="_blank">
                                                        {!IF(childLevel7.account != null, childLevel7.account.Name, childLevel7.lead.Name)}
                                                    </a>
                                                    <span class="accordion-toggle" onclick="toggleChildren(this)">
                                                        {!IF(childLevel7.Children.size > 0, 'ʌ', '')}
                                                    </span>
                                                </td>
                                                <td style="color:{!childLevel7.MembershipStatus}">
                                                    {!IF(childLevel7.account != null, 'Account', 'Lead')}
                                                </td>
                                                <td>
                                                    {!IF(childLevel7.account != null, childLevel7.account.Membership_Status__c, childLevel7.lead.Membership_Status__c)}
                                                </td>
                                                <td>
                                                    {!IF(childLevel7.account != null, childLevel7.account.Total_Amount_Invested__c, '')}
                                                </td>
                                                <td>
                                                    {!IF(childLevel7.account != null, childLevel7.account.Total_Amount_Committed__c, '')}
                                                </td>
                                                <td>
                                                    {!IF(childLevel7.account != null, childLevel7.account.Total_Referred__c, '0')}
                                                </td>
                                                <td>
                                                    {!IF(childLevel7.account != null, childLevel7.account.Relationship_Manager__r.Name, childLevel7.lead.Relationship_Owner__r.Name)}
                                                </td>
                                            </tr>
                                            <apex:repeat value="{!childLevel7.Children}" var="childLevel8">
                                                <tr>
                                                    <td style="padding-left: 140px;">
                                                        <span>
                                                            <img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Second+Level+Referral+Icon.svg" height="15" width="15" alt="SVG Icon"/>
                                                        </span>&nbsp;&nbsp;
                                                        <a href="/{!IF(childLevel8.account != null, childLevel8.account.Id, '')}" target="_blank" >
                                                            {!IF(childLevel8.account != null, childLevel8.account.Name, childLevel8.lead.Name)}
                                                        </a>
                                                        <span class="accordion-toggle" onclick="toggleChildren(this)">
                                                            {!IF(childLevel8.Children.size > 0, 'ʌ', '')}
                                                        </span>
                                                    </td>
                                                    <td style="color:{!childLevel8.MembershipStatus}">
                                                        {!IF(childLevel8.account != null, 'Account', 'Lead')}
                                                    </td>
                                                    <td>
                                                        {!IF(childLevel8.account != null, childLevel8.account.Membership_Status__c, childLevel8.lead.Membership_Status__c)}
                                                    </td>
                                                    <td>
                                                        {!IF(childLevel8.account != null, childLevel8.account.Total_Amount_Invested__c, '')}
                                                    </td>
                                                    <td>
                                                        {!IF(childLevel8.account != null, childLevel8.account.Total_Amount_Committed__c, '')}
                                                    </td>
                                                    <td>
                                                        {!IF(childLevel8.account != null, childLevel8.account.Total_Referred__c, '0')}
                                                    </td>
                                                    <td>
                                                        {!IF(childLevel8.account != null, childLevel8.account.Relationship_Manager__r.Name, childLevel8.lead.Relationship_Owner__r.Name)}
                                                    </td>
                                                </tr>
                                                <apex:repeat value="{!childLevel8.Children}" var="childLevel9">
                                                    <tr>
                                                        <td style="padding-left: 160px;">
                                                            <span>
                                                                <img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Second+Level+Referral+Icon.svg" height="15" width="15" alt="SVG Icon"/>
                                                            </span>&nbsp;&nbsp;
                                                            <a href="/{!IF(childLevel9.account != null, childLevel9.account.Id, '')}" target="_blank">
                                                                {!IF(childLevel9.account != null, childLevel9.account.Name, childLevel9.lead.Name)}
                                                            </a>
                                                            <span class="accordion-toggle" onclick="toggleChildren(this)">
                                                                {!IF(childLevel9.Children.size > 0, 'ʌ', '')}
                                                            </span>
                                                        </td>
                                                        <td style="color:{!childLevel9.MembershipStatus}">
                                                            {!IF(childLevel9.account != null, 'Account', 'Lead')}
                                                        </td>
                                                        <td>
                                                            {!IF(childLevel9.account != null, childLevel9.account.Membership_Status__c, childLevel9.lead.Membership_Status__c)}
                                                        </td>
                                                        <td>
                                                            {!IF(childLevel9.account != null, childLevel9.account.Total_Amount_Invested__c, '')}
                                                        </td>
                                                        <td>
                                                            {!IF(childLevel9.account != null, childLevel9.account.Total_Amount_Committed__c, '')}
                                                        </td>
                                                        <td>
                                                            {!IF(childLevel9.account != null, childLevel9.account.Total_Referred__c, '0')}
                                                        </td>
                                                        <td>
                                                            {!IF(childLevel9.account != null, childLevel9.account.Relationship_Manager__r.Name, childLevel9.lead.Relationship_Owner__r.Name)}
                                                        </td>
                                                    </tr>
                                                    <apex:repeat value="{!childLevel9.Children}" var="childLevel10">
                                                        <tr>
                                                            <td style="padding-left: 160px;">
                                                                <span>
                                                                    <img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Second+Level+Referral+Icon.svg" height="15" width="15" alt="SVG Icon"/>
                                                                </span>&nbsp;&nbsp;
                                                                <a href="/{!IF(childLevel10.account != null, childLevel10.account.Id, '')}" target="_blank" >
                                                                    {!IF(childLevel10.account != null, childLevel10.account.Name, childLevel10.lead.Name)}
                                                                </a>
                                                                <span class="accordion-toggle" onclick="toggleChildren(this)">
                                                                    {!IF(childLevel10.Children.size > 0, 'ʌ', '')}
                                                                </span>
                                                            </td>
                                                            <td style="color:{!childLevel10.MembershipStatus}">
                                                                {!IF(childLevel10.account != null, 'Account', 'Lead')}
                                                            </td>
                                                            <td>
                                                                {!IF(childLevel10.account != null, childLevel10.account.Membership_Status__c, childLevel10.lead.Membership_Status__c)}
                                                            </td>
                                                            <td>
                                                                {!IF(childLevel10.account != null, childLevel10.account.Total_Amount_Invested__c, '')}
                                                            </td>
                                                            <td>
                                                                {!IF(childLevel10.account != null, childLevel10.account.Total_Amount_Committed__c, '')}
                                                            </td>
                                                            <td>
                                                                {!IF(childLevel10.account != null, childLevel10.account.Total_Referred__c, '0')}
                                                            </td>
                                                            <td>
                                                                {!IF(childLevel10.account != null, childLevel10.account.Relationship_Manager__r.Name, childLevel10.lead.Relationship_Owner__r.Name)}
                                                            </td>
                                                        </tr>
                                                    </apex:repeat>
                                                </apex:repeat>
                                            </apex:repeat>
                                        </apex:repeat>
                                    </apex:repeat>
                                </apex:repeat>
                            </apex:repeat>
                        </apex:repeat>
                    </apex:repeat>
                    
                </tbody>
            </table>
        </div>
    </div>
    <script>
    function toggleChildren(element) {
        var parentRow = element.closest('tr');
        var icon = element.textContent;
        var childRows = [];
        var nextRow = parentRow.nextElementSibling;
        
        // Collect all descendant rows
        while (nextRow && parseInt(nextRow.cells[0].style.paddingLeft) > parseInt(parentRow.cells[0].style.paddingLeft)) {
            if (parseInt(nextRow.cells[0].style.paddingLeft) > parseInt(parentRow.cells[0].style.paddingLeft)) {
                childRows.push(nextRow);
            }
            nextRow = nextRow.nextElementSibling;
        }
        // Toggle the display of descendant rows
        for (var i = 0; i < childRows.length; i++) {
            if (childRows[i].style.display === 'none') {
                childRows[i].style.display = '';
            } else {
                childRows[i].style.display = 'none';
            }
        }
        // Toggle the icon
        element.textContent = icon === 'ʌ' ? 'v' : 'ʌ';
    }
    function formatNumberIndian(number) {
        var parts = number.toString().split('.');
        var mainNumber = parts[0].replace(/(\d)(?=(\d\d)+\d$)/g, "$1,");
        if (parts[1]) {
            return mainNumber + '.' + parts[1];
        } else {
            return mainNumber;
        }
    }
    // Apply Indian number formatting to all elements with numerical values
    document.addEventListener('DOMContentLoaded', function () {
        var numberElements = document.querySelectorAll('.table th:not(:first-child), .table td:not(:first-child)');
        
        numberElements.forEach(function (element) {
            var text = element.textContent.trim();
            if (!isNaN(parseFloat(text.replace(/,/g, '')))) {
                element.textContent = formatNumberIndian(parseFloat(text));
            }
        });
    });
    </script>
 </apex:page>