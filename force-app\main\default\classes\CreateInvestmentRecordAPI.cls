/****************************************************
 CreateInvestmentRecordAPI was created for commitment line items 10/05/23
****************************************************/
@RestResource(urlMapping='/CreateInvestmentRecordAPI/*')
global with sharing class CreateInvestmentRecordAPI {

    @HttpPost
    global static responseWrapper CreateInvestment()
    { 
        responseWrapper WResponse = new responseWrapper();
        
        try
        {
            restRequest req = RestContext.request;
            restResponse res = RestContext.response;
            String JsonReqString = req.requestBody.toString();
            
            requestWrapper wResp =(requestWrapper) JSON.deserialize(JsonReqString,requestWrapper.class);
            System.debug('wResp>>>>>'+wResp);
            
            if(WResp != null && wResp.InvList !=null){
                
                List<Investment__c> InsertInvList = new List<investment__c> ();
                Set<id> parentAccid = New Set<id>();             
                Map<id,Contact> InvestorMap = new Map<id,Contact>();
                
                for(investmentRequestWrapper Crw :wResp.InvList )
                {
                    if(Crw.memberSalesforceId != null)
                    {
                        parentAccid.add(Crw.memberSalesforceId);     
                    }
                    
                    else
                        WResponse.status = false;
                		WResponse.message ='Memberid is required to create Investment record.';
                }
                  List<Contact> InvestorList = [SELECT id,AIF_Contributor__c,Investor_Full_Name__c,AccountId,Investor_s_PAN__c,Residential_Status__c,Investment_in_Own_Name_Family_Member__c,Investor_Father_Husband_s_name__c,Contributor_Address__c,Investor_Premier_Status__c FROM Contact WHERE (Investor_Premier_Status__c ='Onboarded on Premier +' OR Investor_Premier_Status__c='Onboarded on Premier Syndicate' OR Investor_Premier_Status__c= 'Onboarded on Premier Select') AND AIF_Contributor__c = True AND AccountId In:parentAccid];  
                    for(Contact Con: InvestorList)
                    {
                        //ContIdMap.put(Con.id ,Con.Investor_s_PAN__c);
                        InvestorMap.put(Con.AccountId,Con);
                    }
                system.debug('InvestorPANMap>>>>>'+InvestorMap);
                
                for(investmentRequestWrapper Crw :wResp.InvList ){
                    
                    Investment__c Inv = new Investment__c ();
                    
                    Inv.Type__c ='Pending confirmation';
                    Inv.Issue_Type__c = 'Primary - AIF';
                    Inv.Investor_Type__c ='Via AIF';
                    //Inv.Investor_Type__c = Crw.InvType;
                    Inv.Startup_Round__c = Crw.roundId;
                    Inv.Account__c = Crw.memberSalesforceId;
                    Inv.Investor__c = InvestorMap.get(Crw.memberSalesforceId).id;
                    //System.debug('ParentAccountname>>'+Inv.Accounts.Name);
                    System.debug('PAN>>'+Inv.Investor_s_PAN__c);
                    
                    Inv.Investor_Name__c =InvestorMap.get(Crw.memberSalesforceId).Investor_Full_Name__c;
                    Inv.Committed_Amount__c = Crw.commitedAmount;
                    Inv.Final_Commitment_Amount__c = Crw.commitedAmount;
                    Inv.Residential_Status__c = InvestorMap.get(Crw.memberSalesforceId).Residential_Status__c;
                    Inv.Investment_in_Own_Name_Family_Member__c = InvestorMap.get(Crw.memberSalesforceId).Investment_in_Own_Name_Family_Member__c;
                    Inv.Postal_Address__c = InvestorMap.get(Crw.memberSalesforceId).Contributor_Address__c;
                    Inv.Investor_Father_Husband_s_name__c = InvestorMap.get(Crw.memberSalesforceId).Investor_Father_Husband_s_name__c;
                    Inv.Investor_s_PAN__c = InvestorMap.get(Crw.memberSalesforceId).Investor_s_PAN__c;
                    InsertInvList.add(Inv);
                }
               
                Insert InsertInvList;
                WResponse.status = true;
                WResponse.message ='Investment records are created Successfully';
                
            }
             else
            {
                WResponse.status = false;
                WResponse.message= 'InvList is null';
            }
        }
        
        catch(exception e)
        {
            WResponse.status = false;
            WResponse.message= 'Exception:'+e.getMessage();
        }
        system.debug('WResponse.message>>>>'+WResponse.message);
        return WResponse;
    }
    global class investmentRequestWrapper 
    {
        global String roundId;
       //Global String Investor;
        global string memberSalesforceId;
       // global string InvName;
        global Decimal commitedAmount;
    }
    
    global Class requestWrapper
    {
       global List<investmentRequestWrapper> InvList;
    }
    
    global class responseWrapper
    {
        global boolean Status;
        global String Message;
        
    }
}