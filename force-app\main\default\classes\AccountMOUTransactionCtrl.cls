Public class AccountMOUTransactionCtrl {
    Public List<Account> childAccountList;
    Public Set<String> recIds;
    Public Task taskObj{get;set;}
    Public List<MOU_Details__c> MOUObjList; 
    Public List<MOU_Rule_Entery__c> MOURuleEnteryList; 
    
    Public AccountMOUTransactionCtrl(ApexPages.StandardController cntlr){
        system.debug('cntlr>>>>'+cntlr.getId());        
        childAccountList = new List<Account>();
        MOUObjList = new List<MOU_Details__c>();
        Date todayD = Date.today();
        Map<String,MOU_Rule_Entery__c> feesEnteryMap = new Map<String,MOU_Rule_Entery__c>();
        
        childAccountList = [Select id from account where Partner_parent_account__c=:cntlr.getId()];    
        AggregateResult invAggObj = [select sum(Investment_Amount_Balance__c)totalInvestment,Sum(Exit_amount_to_be_transferred__c)ExitAmount from Investment__c where  Investor__r.Account.Partner_parent_account__c =:cntlr.getId()];   
        MOUObjList = [Select id,MOU_Start_Date__c,MOU_End_Date__c,(select id,name,Fees_Percentage__c,Fees_Type__c,Lower_Threshold__c,Membership_Level__c,MOU_Rule__c,Upper_Threshold__c from MOU_Rule_Enteries__r) from MOU_Details__c where Applied_To_Account__c=:cntlr.getId() AND Is_Active__c=true AND MOU_Start_Date__c <= :todayD And  MOU_End_Date__c >= :todayD order by createddate desc limit 1];
        System.debug('MOUObjList >>>>>>>>>>>'+MOUObjList );
        
        if(MOUObjList.size()>0)
        {
            MOURuleEnteryList = new List<MOU_Rule_Entery__c>();
            Decimal exitAmount = (Decimal) invAggObj.get('ExitAmount');
            Decimal investAmount= (Decimal) invAggObj.get('totalInvestment');
            
            System.debug('investAmout>>>>>>>>>>>'+investAmount);
            System.debug('exitAmount>>>>>>>>>>>'+exitAmount);
            
            for(MOU_Rule_Entery__c rEntery : MOUObjList[0].MOU_Rule_Enteries__r)
            {
            System.debug('rEntery.Lower_Threshold__c>>>>>>>>>>>'+rEntery.Lower_Threshold__c);
            System.debug('rEntery.Upper_Threshold__c>>>>>>>>>>>'+rEntery.Upper_Threshold__c);
                if(((rEntery.Lower_Threshold__c==null || rEntery.Lower_Threshold__c==0) && investAmount<=rEntery.Upper_Threshold__c) 
                || ((rEntery.Upper_Threshold__c==null || rEntery.Upper_Threshold__c==0) && investAmount>=rEntery.Lower_Threshold__c) 
                || (rEntery.Lower_Threshold__c!=null && rEntery.Upper_Threshold__c!=null && investAmount>= rEntery.Lower_Threshold__c && investAmount<=rEntery.Upper_Threshold__c)
                )
                {
                    feesEnteryMap.put(rEntery.Fees_Type__c,rEntery);
                }
            }
            System.debug('feesEnteryMap>>>>>>>>>>>'+feesEnteryMap);
        }
    }
    
    Public Pagereference saveTask()
    {
        system.debug('Save Task recIds>>>>'+recIds );
        system.debug('Save Task taskObj>>>>'+taskObj);
        if(recIds !=null && recIds.size()>0)
        {
            try
            {
                List<Task> taskInsertList = new List<Task>();
                for(String Ids : recIds)
                {
                    Task ts = new Task();
                    ts.WhatId = Ids;
                    ts.Subject = taskObj.Subject__c;
                    ts.Subject__c = taskObj.Subject__c;
                    ts.Description = taskObj.Description;
                    ts.ActivityDate = Date.today();
                    ts.Status = 'Completed';
                    
                    if(taskObj.ActivityDate!=null)
                    {
                        ts.Status = 'Open';
                        ts.ActivityDate = taskObj.ActivityDate;
                    }
                    taskInsertList.add(ts);
                }
                if(taskInsertList!=null && taskInsertList.size()>0)
                    insert taskInsertList;
                
                taskObj = new Task();
                system.debug('taskInsertList>>>>'+taskInsertList);
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.CONFIRM,'Activity has been created successfully.'));
            }
            catch(Exception e)
            {
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,''+e.getMessage())); 
            }
        }
        else
        {
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'Please go back and select at least one record to create the activity.')); 
        }
        
        return null;
    }
    Public Pagereference cancelBtn()
    {
        return new ApexPages.Action('{!List}').invoke();
        //return new PageReference('/001');
    }
}