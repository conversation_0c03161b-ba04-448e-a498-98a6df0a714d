@RestResource(urlMapping='/CreateCaseRecordAPI/*')
global with sharing class CreateCaseRecordAPI{

    @HttpPost
    global Static ResponseWrapper createCase()
    {
        responseWrapper wResponse = new responseWrapper();
 
        try
        {
            RestRequest req = RestContext.request;
            RestResponse res = Restcontext.response;
            string jsonReqString=req.requestBody.tostring();
                        
            requestWrapper wResp=(requestWrapper) JSON.deserialize(jsonReqString,requestWrapper.class);
            
            If(wResp!=null && wResp.caseList!=null)
            {
                wResponse.feedbackIdMap = new Map<String,String>();
                List<Case> caseInsertList = new List<case>();
                Map<String,Id> primaryNumberMap = new Map<String,Id>();
                Set<Integer> countryCodeSet = new Set<Integer>();
                Set<String> primaryNumberSet = new Set<String>();
                id recordTypeId = Schema.SObjectType.case.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
                
                List<Account> accList = new List<Account>();
                
                for(caseRequestWrapper crw : wResp.caseList)
                {
                    if(crw.issueRaisedByContact!=null)    
                        primaryNumberSet.add(crw.issueRaisedByContact);
                    
                    if(crw.issueRaisedByCountryCode!=null && crw.issueRaisedByCountryCode.isNumeric())
                        countryCodeSet.add(Integer.valueof(crw.issueRaisedByCountryCode.Trim()));
     
                }
                
                accList = [select id,Primary_Contact__c,Primary_Country_Code__c from account where Primary_Contact__c in :primaryNumberSet AND Primary_Country_Code__c in :countryCodeSet];
                for(Account acc : accList)
                {
                    primaryNumberMap.put(acc.Primary_Country_Code__c +'-'+acc.Primary_Contact__c,acc.Id);
                }
                
                for(caseRequestWrapper crw : wResp.caseList)
                {
                    Case c = new Case();
                    c.Description = crw.description;
                    c.Status = crw.status;
                    c.Origin = crw.origin;
                    c.Lifecycle_Phase__c = crw.lifecyclePhase;
                    c.Subject = crw.title;
                    c.Mobile_App_Feedback_Id__c = crw.feedbackId;
                    c.Date_Issue_Raised__c = crw.issueRaisedDate;
                    c.RecordTypeId = recordTypeId;
                    
                    if(primaryNumberMap!=null && primaryNumberMap.size()>0 && primaryNumberMap.containsKey(crw.issueRaisedByCountryCode+'-'+crw.issueRaisedByContact) && primaryNumberMap.get(crw.issueRaisedByCountryCode+'-'+crw.issueRaisedByContact)!=null)
                    {
                        c.Issue_raised_By__c = primaryNumberMap.get(crw.issueRaisedByCountryCode+'-'+crw.issueRaisedByContact);
                    }
                    else
                    {
                        wResponse.status = false;
                        wResponse.message= 'No Account found for Issue Raised By with given primary contact.';
                        return wResponse;
                        
                    }   
                    //c.Responsibility_to_Solve__c = '**********';
                    
                    caseInsertList.add(c);
                    
                }
                
                if(caseInsertList!=null && caseInsertList.size()>0)
                {
                    Insert caseInsertList;
                    //return 'Case record created successfully: '+c.Id;
                    
                    for(Case c : caseInsertList)
                    {
                        wResponse.feedbackIdMap.put(c.Mobile_App_Feedback_Id__c,c.Id);
                    }
                } 
                //insert attachment
                wResponse.status = true;
                wResponse.message= 'Case record created successfully';
            }
            else
            {
                wResponse.status = false;
                wResponse.message= 'Case list is null';
            }
            
        }
        catch(exception e)
        {
            wResponse.status = false;
            wResponse.message= 'Exception:'+e.getMessage();
        }
        
        return wResponse;
        //return JSON.Serialize(wResponse);
       
    }

    global class caseRequestWrapper{

       global string feedbackId;
       global string status;
       global string description;
       global string origin;
       global string lifecyclePhase;
       global string title;
       global string issueRaisedByContact;
       global string issueRaisedByCountryCode;
       global Blob attachment;
       global date issueRaisedDate;
    }
    global class requestWrapper{
        global List<caseRequestWrapper> caseList;
    
    }
    global class responseWrapper{
       global boolean status;
       global string message;
       global Map<String,String> feedbackIdMap;
    }    
}