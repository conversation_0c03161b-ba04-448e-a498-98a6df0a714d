@isTest
public class MatchingCapitalDevelopmentControllerTest {

    @isTest
    public static void testGetMatchingCapitalDevelopments() {
        
        List<Startup__c> startupList = new List<Startup__c>();
        
        Startup__c testStartup1 = new Startup__c(Public_Name__c = 'Test Startup1', Legal_Name__c = 'Test Startup1', Industry_Sector__c = 'Gaming', Fundraise_Amount__c = 0.5);
        Startup__c testStartup2 = new Startup__c(Public_Name__c = 'Test Startup2', Legal_Name__c = 'Test Startup2', Industry_Sector__c = 'Gaming', Fundraise_Amount__c = null);
        Startup__c testStartup3 = new Startup__c(Public_Name__c = 'Test Startup3', Legal_Name__c = 'Test Startup3', Industry_Sector__c = null, Fundraise_Amount__c = 0.5);
       
        startupList.add(testStartup1);
        startupList.add(testStartup2);
        startupList.add(testStartup3);    
        
        insert startupList;
        
        MatchingCapitalDevelopmentController.getMatchingCapitalDevelopments(testStartup1.Id);
        MatchingCapitalDevelopmentController.getMatchingCapitalDevelopments(testStartup2.Id);
        MatchingCapitalDevelopmentController.getMatchingCapitalDevelopments(testStartup3.Id);
        
    }
}