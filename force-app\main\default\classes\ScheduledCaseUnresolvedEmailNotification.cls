global class ScheduledCaseUnresolvedEmailNotification implements Schedulable {
    
    global void execute(SchedulableContext sc) {
        sendEmailNotifications();
    }
    
    public void sendEmailNotifications() {
        List<Case> casesToSendEmailList = [SELECT Id,Relevant_Team__c,Date_of_issue_assigned_Internal__c,Due_date_for_closure__c,OwnerId, Subject, Description, Owner.Email, Responsibility_to_Solve__r.email,Responsibility_to_solve_Internal__r.email FROM Case WHERE CreatedDate = THIS_YEAR AND Status ='WIP' AND Relevant_Team__c !=null];
        List<Messaging.SingleEmailMessage> emailsList = new List<Messaging.SingleEmailMessage>();
        Map<String, Case_escalation_setting__mdt> metadataMap = new Map<String, Case_escalation_setting__mdt>();
        Set<Integer> twoDayDiffSet = new Set<Integer>{1,2};
        Set<Integer> afterTwoDaysDiffSet = new Set<Integer>{4,7,15,20,30,50,70,100,130,150,170,200};
        EmailUtility eCls = new EmailUtility();
        Id conId = ecls.getDummyContactId();
        metadataMap = eCls.getCaseEscalationSetting();

        for (Case c : casesToSendEmailList) {
            Messaging.SingleEmailMessage email;
            //System.debug('c>>>>>>'+c.Id);
            //System.debug('Due_date_for_closure__c>>>>>>'+c.Due_date_for_closure__c);                
            //System.debug('Date.today()>>>'+Date.today());                
            
            Integer dayDiff = getDateDifference(Date.today(),c.Due_date_for_closure__c);   
            System.debug('dayDiff >>>>>>>>>>>>'+dayDiff);             
            
            //4b. For the cases where the Due Date for closure is already filled, remiders should only go twice
            if(c.Due_date_for_closure__c!=null)
            {
                if(twoDayDiffSet.contains(dayDiff))
                {
                    email = new Messaging.SingleEmailMessage();
                    List<String> toAddrList = new List<String>();
                    List<String> ccAddrList = new List<String>();
                    
                    toAddrList.addAll(eCls.getCustomerSuccessSPOCEmail(c,metadataMap));
                    //Commented by Bharat as per discussion with mauli on 11-03-2025
                    //ccAddrList.addAll(eCls.getFunHead(c,metadataMap));
                    
                    if(c.Responsibility_to_solve_Internal__r.email!=null)
                    {
                        ccAddrList.add(c.Responsibility_to_solve_Internal__r.email);
                    }
                    
                    System.debug('Case>>>>>>'+c);
                    System.debug('toAddrList>>>>>>'+toAddrList+'<<ccAddrList>>'+ccAddrList);
                    
                    if(toAddrList.size()>0 || ccAddrList.size()>0)
                    {
                        ID templateId = EmailUtility.getTemplateId('DUEDATEMEET48HOURS');
                        
                        if(dayDiff==1){
                            templateId = EmailUtility.getTemplateId('DUEDATEMEET24HOURS');
                        }
                        
                        email = eCls.createMailTemplateObj(c.Id,templateId,toAddrList,ccAddrList,null,null,conId);
                        emailsList.add(email);
                    }
                }
            }else
            {
                //4a. If the case is till unresolved on the second day.
                if(getDateDifference(c.Date_of_issue_assigned_Internal__c,Date.today())==2)
                {
                    email = new Messaging.SingleEmailMessage();
                    List<String> toAddrList = new List<String>();
                    List<String> ccAddrList = new List<String>();
                    
                    toAddrList.addAll(eCls.getCustomerSuccessSPOCEmail(c,metadataMap));
                    //Commented by Bharat as per discussion with mauli on 11-03-2025
                    //ccAddrList.addAll(eCls.getFunHead(c,metadataMap));

                    if(c.Responsibility_to_solve_Internal__r.email!=null)
                    {
                        ccAddrList.add(c.Responsibility_to_solve_Internal__r.email);
                    }
                    System.debug('Case>>>>>>'+c);
                    System.debug('toAddrList>>>>>>'+toAddrList+'<<ccAddrList>>'+ccAddrList);
                    if(toAddrList.size()>0 || ccAddrList.size()>0)
                    {
                        ID templateId = EmailUtility.getTemplateId('CASEUNRESOLVEDSECONDDAY');
                        email = eCls.createMailTemplateObj(c.Id,templateId,toAddrList,ccAddrList,null,null,conId);
                        emailsList.add(email);
                    }
                }//5a. If the case is till unresolved after second day then send the reminder on days 4,7,15,20,30,50,70,100,130,150,170,200
                else if(afterTwoDaysDiffSet.contains(getDateDifference(c.Date_of_issue_assigned_Internal__c,Date.today())))
                {
                    email = new Messaging.SingleEmailMessage();
                    List<String> toAddrList = new List<String>();
                    List<String> ccAddrList = new List<String>();
                    
                    toAddrList.addAll(eCls.getCustomerSuccessSPOCEmail(c,metadataMap));
                    ccAddrList.addAll(eCls.getFunHead(c,metadataMap));
                    if(c.Responsibility_to_solve_Internal__r.email!=null)
                    {
                        ccAddrList.add(c.Responsibility_to_solve_Internal__r.email);
                    }
                    
                    System.debug('Case>>>>>>'+c);
                    System.debug('toAddrList>>>>>>'+toAddrList+'<<ccAddrList>>'+ccAddrList);
                    if(toAddrList.size()>0 || ccAddrList.size()>0)
                    {
                        ID templateId = EmailUtility.getTemplateId('CASEUNRESOLVEDAFTER2DAYS');
                        email = eCls.createMailTemplateObj(c.Id,templateId,toAddrList,ccAddrList,null,null,conId);
                        emailsList.add(email);
                    }
                }
            }
        }
        
        system.debug('emailsList>>>>>>>>>>'+emailsList);
        
        if (!emailsList.isEmpty()) {
            eCls.sendEmailBulk(emailsList);
        }
    }
    
    Public Static Integer getDateDifference(Date startDate,Date endDate){
        Integer daysDifference = 0;
        if(startDate!=null && endDate!=null)
            daysDifference = startDate.daysBetween(endDate);
        
        //system.debug('hjhjh>>>>>'+endDate.daysBetween(startDate));
        System.debug('The difference in days is: ' + daysDifference);
        return daysDifference;
    }
    /*
    String jobName = 'Scheduled Unresolved Case Email Notification Daily Job';
String cronExpression = '0 0 0 * * ?';
System.schedule(jobName, cronExpression, new ScheduledCaseUnresolvedEmailNotification());

*/
}