public class UpdateAccountBatch implements Database.Batchable<SObject> {
      
    public Database.QueryLocator start(Database.BatchableContext BC) {
        id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
        List<Account> Acclist =[SELECT Id,name,Primary_Contact__c,Unique_referral_code__c FROM Account WHERE recordtype.name ='IPV' AND Unique_referral_code__c = null LIMIT 100];
        return Database.getQueryLocator('SELECT Id,name,Primary_Contact__c,Unique_referral_code__c FROM  Account WHERE Unique_referral_code__c = null AND date_of_addition__c != null  LIMIT 100');
    }
   
    public void execute(Database.BatchableContext BC, List<Account> scope) {
		
		integer count = 1;
        string Unique_Referral_Code = '';
        string firstpart = '' ;
        string lastPart = '';
        
		//TriggerControl.bypassTrigger = true;
             
        for (Account acc : scope) {
            
			if(String.isNotBlank(acc.Name) && acc.Name.length()>=3 ){
                    String memberName = acc.Name;
                    Integer lastSpaceIndex = memberName.lastIndexOf(' ');
                    System.debug('memberName.IndexOf>>>'+ memberName.IndexOf(' '));
                    
                    if(memberName.length() >= lastSpaceIndex + 4){
                        firstPart = memberName.subString(lastSpaceIndex+1 , lastSpaceIndex+4).toUpperCase() + '-';
                        System.debug('First>>>'+ firstPart);
                    }
                    else if(memberName.IndexOf(' ') + 3 <= lastSpaceIndex ){
                        firstPart = memberName.subString(memberName.IndexOf(' ')+1 , memberName.IndexOf(' ')+4).toUpperCase() + '-';
                        System.debug('NEW>>>'+ firstPart);
                    }
                    else if(memberName.IndexOf(' ') == 2 && memberName.length()==5){
                        firstPart = 'IPV-';
                        System.debug('Second>>>'+ firstPart);
                    }
                    else if(memberName.IndexOf('.') == 2 && memberName.length()<= 6){
                        firstPart = 'IPV-';
                        System.debug('Third>>>'+ firstPart);
                    }
                    else{
                        firstPart = memberName.subString(0 , 3).toUpperCase() + '-';
                        System.debug('Final>>>'+ firstPart);
                    }
                }
                else if(String.isNotBlank(acc.Name) && acc.Name.length()<3){
                    String memberName = acc.Name;
                    firstPart = 'IPV-';
                }
                
                if(acc.Primary_Contact__c != null && acc.Primary_Contact__c.length()>0) {
                    String primaryContact = String.valueOf(acc.Primary_Contact__c);
                    if(primaryContact.length() >= 5) {
                        lastPart = '-' +  primaryContact.substring(primaryContact.length()-5 , primaryContact.length()) ;
                        System.debug('primaryContact>>>'+lastPart);
                    }
                }
                
                Unique_Referral_Code = firstPart + '0' + String.valueOf(count) + lastPart ;
                String query = 'SELECT Id, Name, Unique_Referral_Code__c FROM Account WHERE Unique_Referral_Code__c LIKE \'' + firstPart + '%\' AND Unique_Referral_Code__c LIKE \'%' + lastPart + '\' ORDER BY Unique_Referral_Code__c DESC';
                List<Account> existAcc = Database.query(query);
                System.debug('asdf>>>>' + existAcc);
                
                if(existAcc != null && existAcc.size() > 0){
                    Integer index = 4; // Index of the character we want to extract, counting from 0
                    string latestReferCode = existAcc[0].Unique_Referral_Code__c ;
                    Integer oldCount = Integer.Valueof(latestReferCode.substring(index, index + 2));
                    Integer newCount = oldCount + 1 ;
                    
                    if(newCount <10 ){
                        Unique_Referral_Code = firstPart + '0' + String.valueOf(newCount) + lastPart ;
                    }else{
                        Unique_Referral_Code = firstPart + String.valueOf(newCount) + lastPart ;
                    }
                    System.debug('oldCount>>>>>>>>>>>>>>>.' + oldCount);
                    System.debug('newCount>>>>>>>>>>>>>.>' + newCount);
                }
                if(Unique_Referral_Code != null){
                    acc.Unique_Referral_Code__c = Unique_Referral_Code ;
                }
                system.debug('Unique_Referral_Code__c>>>>>>>>>>>>>>>>>>>>..' + Unique_Referral_Code);
        }
            
        update scope;
        //System.debug('All Account records have been updated with LastModifiedDate.' +Scope.LastModifiedDate);   
        //TriggerControl.bypassTrigger = false;
    }
      
    public void finish(Database.BatchableContext BC) {
        
        System.debug('All Account records have been updated.');
    }
}