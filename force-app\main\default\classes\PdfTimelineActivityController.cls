public class PdfTimelineActivityController {
    public String accountName { get; set; }
    public List<DateGroupWrapper> activitiesByDate { get; set; }

    public PdfTimelineActivityController() {
        String accountId = ApexPages.currentPage().getParameters().get('accountId');
        String startDateStr = ApexPages.currentPage().getParameters().get('startDate');
        String endDateStr = ApexPages.currentPage().getParameters().get('endDate');
        String filtersStr = ApexPages.currentPage().getParameters().get('filters');

      
        Date startDate = (startDateStr != null && startDateStr != '') ? Date.valueOf(startDateStr) : null;
        Date endDate = (endDateStr != null && endDateStr != '') ? Date.valueOf(endDateStr) : null;
        List<String> filters = (filtersStr != null && filtersStr != '') ? filtersStr.split(',') : new List<String>();

      
        if (filters.isEmpty()) {
            filters = null;  
        }

        
        List<TimelineActivityLogController.ActivityWrapper> logActivities = TimelineActivityLogController.getActivities(accountId, filters, startDate, endDate);


        List<ActivityWrapper> activityWrappers = new List<ActivityWrapper>();
        for (TimelineActivityLogController.ActivityWrapper logActivity : logActivities) {
            activityWrappers.add(new ActivityWrapper(logActivity.activityType, logActivity.activityDetail, logActivity.activityTimestamp));
        }

        
        this.activitiesByDate = groupActivitiesByDate(activityWrappers);

        this.accountName = getAccountName(accountId);
        System.debug('Name To Be Sent >>> ' + this.accountName);
    }

    private String getAccountName(String accountId) {
        String name = '';
        Id recordId = accountId;
        if(String.valueOf(recordId.getSobjectType()) == 'Account')
        {
            System.debug('Accounttttt : ' + String.valueOf(recordId.getSobjectType()));
            Account acc = [SELECT Name FROM Account WHERE Id = :accountId LIMIT 1];
            name = acc.Name;
        }
        else if(String.valueOf(recordId.getSobjectType()) == 'Lead__c')
        {
            System.debug('Leaddddd : ' + String.valueOf(recordId.getSobjectType()));
            Lead__c lead = [SELECT Name FROM Lead__c WHERE Id = :accountId LIMIT 1];
            name = lead.Name;
            System.debug('Lead Name : ' + name);
        }
        return name;
    }

    // Method to group activities by date
    private List<DateGroupWrapper> groupActivitiesByDate(List<ActivityWrapper> activities) {
        Map<Date, List<ActivityWrapper>> groupedActivities = new Map<Date, List<ActivityWrapper>>();

        for (ActivityWrapper activity : activities) {
            Date activityDate = activity.activityTimestamp.date();
            if (!groupedActivities.containsKey(activityDate)) {
                groupedActivities.put(activityDate, new List<ActivityWrapper>());
            }
            groupedActivities.get(activityDate).add(activity);
        }

        List<DateGroupWrapper> dateGroups = new List<DateGroupWrapper>();
        for (Date groupDate : groupedActivities.keySet()) {
            dateGroups.add(new DateGroupWrapper(groupDate, groupedActivities.get(groupDate)));
        }

        return dateGroups;
    }


    public class DateGroupWrapper {
        public Date groupDate { get; set; }
        public List<ActivityWrapper> activities { get; set; }

        public DateGroupWrapper(Date dates, List<ActivityWrapper> activities) {
            this.groupDate = dates;
            this.activities = activities;
        }
    }

    public class ActivityWrapper {
        public String activityType { get; set; }
        public String activityDetail { get; set; }
        public DateTime activityTimestamp { get; set; }
         public String formattedDate { get; set; }
    public String formattedTime { get; set; }


        public ActivityWrapper(String type, String detail, DateTime timestamp) {
            this.activityType = type;
            this.activityDetail = detail;
            this.activityTimestamp = timestamp;
             this.formattedDate = timestamp.format('dd-MM-yyyy'); 
        this.formattedTime = timestamp.format('HH:mm a');
        }

       public String getFormattedTime() {
            return activityTimestamp.format('HH:mm a');
        }

        
        public String getFormattedDate() {
            return activityTimestamp.format('dd-MM-yyyy') ;
        } 
    }
}