/**************************************************************************
 This test class is for NewUpdateCallStatusBatch created on 8th Nov 2023. 
 *************************************************************************/

@isTest
public class NewUpdateCallStatusBatchTest {
    @isTest
    static void testBatchL1Overdue() {
        Account testAccount = TestFactory.createAccount();
        
        testAccount.Date_of_Payment__c = Date.newInstance(2020, 1, 1);
        testAccount.L1_Call_status__c = 'Yet To Happen';
        testAccount.L2_Call_status__c = 'Yet To Happen';
        testAccount.L3_Call_status__c = 'Yet To Happen';
        testAccount.L4_Call_status__c = 'Yet To Happen';
        testAccount.L5_Call_status__c = 'Yet To Happen';
        testAccount.L6_Call_status__c = 'Yet To Happen';
        insert testAccount;

        Test.startTest();
        NewUpdateCallStatusBatch batchJob = new NewUpdateCallStatusBatch(200);
        Database.executeBatch(batchJob);
        Test.stopTest();

        Account updatedAccount = [SELECT L1_Call_status__c, L1_Call_delayed_days__c, 
                                  L2_Call_status__c, L2_Call_delayed_days__c, 
                                  L3_Call_status__c, L3_Call_delayed_days__c, 
                                  L4_Call_status__c, L4_Call_delayed_days__c, 
                                  L5_Call_status__c, L5_Call_delayed_days__c, 
                                  L6_Call_status__c, L6_Call_delayed_days__c
                                  FROM Account WHERE Id = :testAccount.Id];

		System.assertEquals('Overdue', updatedAccount.L1_Call_status__c);
        System.assertEquals('Overdue', updatedAccount.L2_Call_status__c);
        System.assertEquals('Overdue', updatedAccount.L3_Call_status__c);
        System.assertEquals('Overdue', updatedAccount.L4_Call_status__c);
        System.assertEquals('Overdue', updatedAccount.L5_Call_status__c);
        System.assertEquals('Overdue', updatedAccount.L6_Call_status__c);
        //System.assertEquals(15, updatedAccount.L1_Call_delayed_days__c, 'L1 Call Delayed Days should be 15');
    }

    @isTest
    static void testBatchL2Overdue() {
        Account testAccount = TestFactory.createAccount();
        testAccount.Date_of_Payment__c = Date.newInstance(2020, 1, 1);
        testAccount.L1_Call_status__c = 'Overdue'; 
        testAccount.L2_Call_status__c = 'Overdue';
        testAccount.L3_Call_status__c = 'Overdue';
        testAccount.L4_Call_status__c = 'Overdue';
        testAccount.L5_Call_status__c = 'Overdue';
        testAccount.L6_Call_status__c = 'Overdue';
        insert testAccount;

        Test.startTest();
        NewUpdateCallStatusBatch batchJob = new NewUpdateCallStatusBatch(200);
        Database.executeBatch(batchJob);
        Test.stopTest();

        Account updatedAccount = [SELECT L1_Call_status__c, 
                                  L2_Call_status__c, L2_Call_delayed_days__c, 
                                  L3_Call_status__c, L3_Call_delayed_days__c, 
                                  L4_Call_status__c, L4_Call_delayed_days__c, 
                                  L5_Call_status__c, L5_Call_delayed_days__c, 
                                  L6_Call_status__c, L6_Call_delayed_days__c FROM Account WHERE Id = :testAccount.Id];
        System.assertEquals('Overdue', updatedAccount.L1_Call_status__c);
        System.assertEquals('Overdue', updatedAccount.L2_Call_status__c);
        System.assertEquals('Overdue', updatedAccount.L3_Call_status__c);
        System.assertEquals('Overdue', updatedAccount.L4_Call_status__c);
        System.assertEquals('Overdue', updatedAccount.L5_Call_status__c);
        System.assertEquals('Overdue', updatedAccount.L6_Call_status__c);
        //System.assertEquals(60, updatedAccount.L2_Call_delayed_days__c, 'L2 Call Delayed Days should be 60');
    }
}