<!-- inline VF page for VC to add contact details -->
<apex:page standardController="Venture_Connect__c" extensions="VCAddContactPersonController" lightningStylesheets="true" id="vfpage" applyBodyTag="false">
    <apex:slds />
    <script src="//code.jquery.com/jquery-1.11.0.min.js"></script>
    
    <style type="text/css">
        @media (min-width: 30em)
    .detailList .dataCol input[type="text"]{
        min-width: 15rem !important;
    }
    body .detailList .dataCol input[type="text"], body .detailList .dataCol input[type="date"], body .detailList .dataCol input[type="password"], body .detailList .dataCol textarea, body .detailList .dataCol select, .slds-vf-scope .detailList .dataCol input[type="text"], .slds-vf-scope .detailList .dataCol input[type="date"], .slds-vf-scope .detailList .dataCol input[type="password"], .slds-vf-scope .detailList .dataCol textarea, .slds-vf-scope .detailList .dataCol select {
        min-width: 15rem !important;
    }
    
    .border {
        border-color: red !important; 
        border-width: 2px !important; 
        border-style: solid !important;
    }
    .errorMessage {
        font-family:"Arial"; 
        font-size:40px;
    }
    
    td {
        height : 0px;
    }
    .dropdown-container {
        position: relative;
        width: 250px;
        font-family: Arial, sans-serif;
    }
    
    .dropdown-button {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #ccc;
        border-radius: 6px;
        background-color: #fdfdfd;
        cursor: pointer;
        text-align: left;
    }
    
    .dropdown-button:after {
        content: '▼';
        float: right;
        margin-top: 2px;
    }
    
    .checkbox-dropdown {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        max-height: 180px;
        overflow-y: auto;
        border: 1px solid #ccc;
        border-radius: 6px;
        background-color: #fff;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        z-index: 1000;
    }
    
    .checkbox-dropdown label {
        display: block;
        padding: 8px 12px;
        cursor: pointer;
    }
    
    .checkbox-dropdown label:hover {
        background-color: #f0f0f0;
    }
    
    .show {
        display: block;
    }    
    </style>
    
    <script>
        var j$ = jQuery.noConflict();
     function toggleDropdown(btn) {
            const row = btn.closest('.dropdown-container');
            const menu = row.querySelector('.checkbox-dropdown');
            menu.classList.toggle('show');
        }

        function toggleValue(checkbox) {
            const container = checkbox.closest('.dropdown-container');
            const checkboxes = container.querySelectorAll('input[type="checkbox"]');
            const selectedValues = Array.from(checkboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value);

            const label = container.querySelector('.dropdown-button');
            label.innerText = selectedValues.length > 0
                ? selectedValues.length + " option(s) selected"
                : "Select Options";

            const hiddenInput = container.querySelector("input[type='hidden']");
            hiddenInput.value = selectedValues.join(',');
        }

        function updateAllHiddenFields() {
            document.querySelectorAll('.dropdown-container').forEach(container => {
                const checkboxes = container.querySelectorAll('input[type="checkbox"]');
                const selectedValues = Array.from(checkboxes)
                    .filter(cb => cb.checked)
                    .map(cb => cb.value);

                const hiddenInput = container.querySelector("input[type='hidden']");
                if (hiddenInput) {
                    hiddenInput.value = selectedValues.join(',');
                }
            });
        }

        document.addEventListener('click', function (e) {
            document.querySelectorAll(".checkbox-dropdown").forEach(menu => {
                if (!menu.contains(e.target) && !menu.previousElementSibling.contains(e.target)) {
                    menu.classList.remove('show');
                }
            });
        });

        j$(document).ready(function () {
            j$('input[type=hidden]').each(function () {
                const sectors = j$(this).val().split(',');
                const container = j$(this).closest('.dropdown-container');
                const checkboxes = container.find('input[type=checkbox]');
                checkboxes.each(function () {
                    j$(this).prop('checked', sectors.includes(j$(this).val()));
                });

                const label = container.find('.dropdown-button');
                if (sectors.length === 1 && sectors[0]) {
                    label.text(sectors.length + ' option selected');
                } else if (sectors.length > 1) {
                    label.text(sectors.length + ' options selected');
                } else {
                    label.text('Select Options');
                }
            });
        });
   
    function inputLimiter(e,allow) {
        var AllowableCharacters = '';
        
        if (allow == 'Numbers'){AllowableCharacters='1234567890';}
        
        var k = document.all?parseInt(e.keyCode): parseInt(e.which);
        if (k!=13 && k!=8 && k!=0){
            if ((e.ctrlKey==false) && (e.altKey==false)) {
                return (AllowableCharacters.indexOf(String.fromCharCode(k))!=-1);
            } else {
                return true;
            }
        } else {
            return true;
        }
    } 
    
    function validate() {
        //console.log('valid11>>>>'); 
        var valid = true;
        var validEmail = true;
        var validURL = true;
        
        j$("[id$='emailinput']").each(function() {
            
            var pattern = /^([a-z\d!#$%&'*+\-\/=?^_`{|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+(\.[a-z\d!#$%&'*+\-\/=?^_`{|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+)*|"((([ \t]*\r\n)?[ \t]+)?([\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*(([ \t]*\r\n)?[ \t]+)?")@(([a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|[a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF][a-z\d\-._~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]*[a-z\d\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])\.)+([a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|[a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF][a-z\d\-._~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]*[a-z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])\.?$/i;
            var email = j$(this).val();
            validEmail = pattern.test(email);
            if (!validEmail) {
                j$(this).addClass('border');                   
            }
            else
            {
                j$(this).removeClass('border');
            }
            
        });
        if(validEmail){
            document.getElementById("myOutput1").value="";
            
        }
        else{
            document.getElementById("myOutput1").value="Please add valid email address";
            return false;
        }
        
        j$("[id$='inputURL']").each(function() {
            validURL = true;
            var URLpattern = /^(http|https)?:\/\/[a-zA-Z0-9-\.]+\.[a-z]{2,4}/;
            var urlinput = j$(this).val();
            if(urlinput != ''){
                validURL = URLpattern.test(urlinput);
                if (!validURL) {
                    j$(this).addClass('border');                   
                }
                else
                {
                    j$(this).removeClass('border');
                }
            }
            
            
        });
        if(validURL){
            document.getElementById("myOutput1").value="";                    
        }
        else{
            document.getElementById("myOutput1").value="Please enter valid URL";
            return false;
        }
        
        j$('input.notBlank').each(function() {
            
            // console.log('valid2>>>>',j$(this).label);    
            
            if (j$(this).val() == '') {
                j$(this).addClass('border');
                valid = false;
            }
            else
            {
                j$(this).removeClass('border');
            }
            
        });
        //console.log('valid3>>>>'+valid);   
        
        if(valid && validEmail && validURL)
        {
            document.getElementById("myOutput1").value="";
            callSave();            
            
        }
        else
            document.getElementById("myOutput1").value="Please add required(*) values.";
        
        //console.log('valid>>>>'+valid); 
        
        return valid;
    }
    
   
    </script>
    
    <apex:form id="frmID">
        <apex:pagemessages />
        <c:LoadingIcon />
         <apex:actionFunction name="syncHiddenSectors" action="{!noOp}" rerender="pgb" />
        <apex:pageBlock id="pbl">
            <output id="myOutput1" name="x" for="a b" style="color:red;font-size:15px;"></output>
            <apex:pageBlockSection >
                <apex:pageblockTable value="{!cPersonWrapList}" var="item" id="pgb" >
                    <apex:column headerValue="* Spoc Name" >
                        <apex:inputText value="{!item.contactPerson}" style="width:130px" styleClass="notBlank" >
                        </apex:inputText>
                    </apex:column>
                    <apex:column headerValue="* Spoc Email">
                        <apex:inputText id="emailinput" value="{!item.emailID}" style="width:130px" styleClass="notBlank"/>
                    </apex:column>
                    <apex:column headerValue="Phone No">
                        <apex:inputText id="phone" value="{!item.phoneNo}" style="width:130px" onkeypress="return inputLimiter(event,'Numbers');"/>
                    </apex:column>
                    <apex:column headerValue="LinkedIN URL">
                        <apex:inputText id="inputURL" value="{!item.linkedINURL}" style="width:130px" />
                    </apex:column>
                    <apex:column headerValue="* Spoc Designation">
                        <apex:inputText value="{!item.spocDesignation}" style="width:130px" id="designation" styleClass="notBlank" />
                    </apex:column>
                    <apex:column headerValue="Spoc Location">
                        <apex:selectList value="{!item.spocLocation}" size="1" styleClass="slds-select spoc-dropdown" id="spocloc">
                            <apex:selectOptions value="{!locationOptions}" />
                        </apex:selectList>
                    </apex:column>
                    <apex:column headerValue="Spoc Tagging Basis">
                        <apex:selectList id="spoctagging" value="{!item.spocTaggingBasis}" size="1" style="width:130px">
                            <apex:selectOption itemValue="" itemLabel="--Select--" />
                            <apex:selectOption itemValue="Sector" itemLabel="Sector" />
                            <apex:selectOption itemValue="Geography" itemLabel="Geography" />
                            <apex:selectOption itemValue="Existing Relationship" itemLabel="Existing Relationship" />
                        </apex:selectList>
                    </apex:column>
                    <apex:column headerValue="Sector Focus">
                        <div class="dropdown-container sector-dropdown">
                            <div class="dropdown-button" onclick="toggleDropdown(this)">Select Options</div>
                            <div class="checkbox-dropdown">
                                <apex:repeat value="{!sectorFocus}" var="opt">
                                    <label>
                                        <input type="checkbox" value="{!opt.value}" onclick="toggleValue(this)" />
                                        {!opt.label}
                                    </label>
                                </apex:repeat>
                            </div>
                            <span class="hiddenFieldWrapper">
                                <apex:inputHidden id="selectedSectors" value="{!item.selectedSectors}" />
                            </span>
                        </div>
                    </apex:column>
                    
                    <apex:column headerValue="IPV Spoc Mentor">
                        <apex:selectList value="{!item.ipvSpocMentor}" size="1" id="spocmentor">
                            <apex:selectOptions value="{!mentorOptions}" />
                        </apex:selectList>
                    </apex:column>
                    <apex:column headerValue="IPV Spoc Lead">
                        <apex:selectList value="{!item.ipvSpocLead}" size="1" style="width:130px" id="spoclead">
                            <apex:selectOptions value="{!leadOptions}" />
                        </apex:selectList>
                    </apex:column>
                     <apex:column headerValue="IPV Spoc Co- Lead">
                        <apex:inputText value="{!item.ipvSpocCoLead}" style="width:130px" id="ipvspoccolead"/>
                    </apex:column>
                     <apex:column headerValue="IPV Spoc Support">
                        <apex:inputText value="{!item.ipvSpocSupport}" style="width:130px" id="ipvspocsupport"/>
                    </apex:column>
                    <apex:column headerValue="Connected">
                        <apex:selectList value="{!item.connected}" style="width:130px" id="connected" size="1">
                            <apex:selectOption itemValue="" itemLabel="--Select--" />
                            <apex:selectOption itemValue="yes" itemLabel="Yes" />
                            <apex:selectOption itemValue="no" itemLabel="No" />
                        </apex:selectList>
                    </apex:column>
                    <apex:column headerValue="Market Segment">
                        <div class="dropdown-container market-dropdown">
                            <div class="dropdown-button" onclick="toggleDropdown(this)">Select Options</div>
                            <div class="checkbox-dropdown">
                                <apex:repeat value="{!marketSegment}" var="opt">
                                    <label>
                                        <input type="checkbox" value="{!opt.value}" onclick="toggleValue(this)" />
                                        {!opt.label}
                                    </label>
                                </apex:repeat>
                            </div>
                            <span class="hiddenFieldWrapper">
                                <apex:inputHidden id="marketSegment" value="{!item.marketSegment}" />
                            </span>
                        </div>
                    </apex:column>
                    <apex:column headerValue="Remarks">
                        <apex:inputText value="{!item.remarks}" style="width:130px" id="remarks"/>
                    </apex:column>

                    <!--
<apex:column headerValue="iIndex">
<apex:inputText value="{!item.iIndex}"/>
</apex:column>
-->
                    <apex:column headerValue="Action"> 
                        <apex:commandLink value="Add" action="{!addRow}" style="color:#61afe8;" onclick="updateAllHiddenFields(); syncHiddenSectors();" />&nbsp;&nbsp;||&nbsp;&nbsp;
                        <apex:commandLink value="Remove" action="{!removeRow}" style="color:red;" >
                            <apex:param value="{!item.iIndex}" name="index" />
                        </apex:commandLink>
                    </apex:column>
                </apex:pageblockTable>
            </apex:pageBlockSection>
            <div align="center" draggable="false" >
                <apex:commandButton value="Save Contacts" onclick="return validate()" reRender="vfpage" status="loadStatus" />
            </div>    
            <apex:actionFunction name="callSave" Action="{!saveAction}" reRender="errmsg,vfpage" status="loadStatus" />
        </apex:pageBlock>  
    </apex:form>   
</apex:page>