@isTest 
public class SecondaryModuleTriggerTest{

    @TestSetup
    public static void setup() {

        Account acc= TestFactory.createAccount();
        acc.Name ='Test Acc';
        insert acc;

        Startup__c str = TestFactory.createStartUp();
        insert str;
        
        Startup_Round__c sr = TestFactory.createStartUpRound( acc.Id , acc.Id ,str.Id);
        sr.Round_Type__c = 'Raise';
        sr.Issue_Price__c = 100;
        insert sr;

        Contact con = new Contact();
        con.FirstName = 'Test';
        con.LastName = 'Test';
        con.Phone = '**********';
        con.Email = '<EMAIL>';
        con.AccountId = acc.id;
        con.Investor_s_PAN__c = '**********';
        con.AIF_Contributor__c = true;
        con.KYC_Status__c = 'KYC - WIP';
        insert con;
    }

    @isTest
    Public static void testSecondaryModuleTriggerHandler() {

        Account acc= [SELECT Id , Primary_Contact__c from account limit 1];
        Contact con = [ SELECT Id FROM Contact LIMIT 1 ];
        Startup__c str = [ SELECT Id FROM Startup__c LIMIT 1 ];
        Startup_Round__c sr = [ SELECT Id FROM Startup_Round__c LIMIT 1 ];

        Secondary_Module__c sm = new Secondary_Module__c();
        sm.Bid_Price__c = 123;
        sm.Commitment_Request_Amount__c = 123;
        sm.Compliance_Consent_T_C__c = TRUE;
        sm.Date_Time_of_Filling_the_Form__c = Datetime.now();
        sm.Investor_Name__c = con.ID;
        sm.Member_Name__c = acc.ID;
        sm.Startup__c = str.ID;
        sm.Startup_Round__c = sr.ID;
        sm.Status__c = 'Request Submitted';
        insert sm;
    }

    @isTest
    public static void testCreateSecondaryRequestRecordAPI(){
        
        Account acc= [SELECT Id , Primary_Contact__c from account limit 1];
        Contact con = [ SELECT Id FROM Contact LIMIT 1 ];
        Startup__c str = [ SELECT Id FROM Startup__c LIMIT 1 ];
        Startup_Round__c sr = [ SELECT Id FROM Startup_Round__c LIMIT 1 ];

        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        
        CreateSecondaryRequestRecordAPI.requestWrapper requestWrapper = new CreateSecondaryRequestRecordAPI.requestWrapper();
        CreateSecondaryRequestRecordAPI.SecondaryModuleRequestWrapper secReqWrapper = new CreateSecondaryRequestRecordAPI.SecondaryModuleRequestWrapper();
        
        secReqWrapper.request_at = Datetime.now();
        secReqWrapper.salesforce_user_account_id = acc.ID;
        secReqWrapper.salesforce_investor_id = con.ID;
        secReqWrapper.contact = acc.Primary_Contact__c;
        secReqWrapper.startup_id = str.ID;
        secReqWrapper.startup_round_id = sr.ID;
        secReqWrapper.bid_price = 123;
        secReqWrapper.commitment_request_amount = 123;
        secReqWrapper.compliance_consent_t_c = TRUE;

        System.debug('secReqWrapper' + secReqWrapper);

        requestWrapper.dataList = new List<CreateSecondaryRequestRecordAPI.SecondaryModuleRequestWrapper>{secReqWrapper};
        String jsonReq = JSON.serialize(requestWrapper);
        req.requestURI = '/services/apexrest/CreateSecondaryRequestRecordAPI/';
        req.httpMethod = 'POST';
        req.requestBody = Blob.valueOf(jsonReq);
        RestContext.request = req;
        RestContext.response = res;

        CreateSecondaryRequestRecordAPI.ResponseWrapper response = CreateSecondaryRequestRecordAPI.createSecondaryRequestRecord();
    }
    
    @isTest
    static void testSendRequestDetails_Success() {
        
        Account acc= [SELECT Id , Primary_Contact__c from account limit 1];
        Contact con = [ SELECT Id FROM Contact LIMIT 1 ];
        Startup__c str = [ SELECT Id FROM Startup__c LIMIT 1 ];
        Startup_Round__c sr = [ SELECT Id FROM Startup_Round__c LIMIT 1 ];
        List<Secondary_Module__c> requestList = new List<Secondary_Module__c>();
        
        Secondary_Module__c sec = new Secondary_Module__c(
            Member_Name__c = acc.Id,
            Commitment_Request_Amount__c = 10000,
            Bid_Price__c = 9500,
            Date_Time_of_Filling_the_Form__c = System.today(),
            Status__c = 'Request Submitted',
            Startup__c = str.Id,
            Startup_Round__c = sr.Id
        );
        requestList.add(sec);
        
         Secondary_Module__c sec1 = new Secondary_Module__c(
            Member_Name__c = acc.Id,
            Commitment_Request_Amount__c = 10000,
            Bid_Price__c = 9500,
            Date_Time_of_Filling_the_Form__c = System.today(),
            Status__c = 'Rejected',
            Reason_for_Rejection__c = 'testing',
            Startup__c = str.Id,
            Startup_Round__c = sr.Id
        );
        requestList.add(sec1);
        
         Secondary_Module__c sec2 = new Secondary_Module__c(
            Member_Name__c = acc.Id,
            Commitment_Request_Amount__c = 10000,
            Bid_Price__c = 9500,
            Date_Time_of_Filling_the_Form__c = System.today(),
            Status__c = 'Under Review',
            Startup__c = str.Id,
            Startup_Round__c = sr.Id
        );
        requestList.add(sec2);
        
         Secondary_Module__c sec3 = new Secondary_Module__c(
            Member_Name__c = acc.Id,
            Commitment_Request_Amount__c = 10000,
            Bid_Price__c = 9500,
            Date_Time_of_Filling_the_Form__c = System.today(),
            Status__c = 'Transaction Done',
            Startup__c = str.Id,
            Startup_Round__c = sr.Id
        );
        requestList.add(sec3);
        
        insert requestList;
        
        // Prepare mock request body
        Map<String, Object> requestBody = new Map<String, Object>{
            'pageNumber' => 1,
            'perPage' => 10,
            'status_type' => new List<Integer>{0,1,2,3},
            'salesforce_account_id' => acc.Id
        };

        String jsonInput = JSON.serialize(requestBody);
        RestContext.request = new RestRequest();
        RestContext.request.requestBody = Blob.valueOf(jsonInput);
        RestContext.request.httpMethod = 'POST';
        RestContext.request.addHeader('Content-Type', 'application/json');

        // Call the method
        SecondaryRequestRestAPIController.ResponseWrapper response = SecondaryRequestRestAPIController.sendRequestDetails();

    }
}