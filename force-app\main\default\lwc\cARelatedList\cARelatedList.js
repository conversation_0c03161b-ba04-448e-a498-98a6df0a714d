import { LightningElement, wire, api } from 'lwc';
import getDocuments from '@salesforce/apex/CARelatedListController.getDocuments';

const columns = [
    { label: 'AIF Document Name', fieldName: 'Name' },
    { label: 'AIF Document Initiation Date', fieldName: 'AIF_Document_Initiation_Date__c' },
    { label: 'AIF Document Acknowledged/Signed Date', fieldName: 'AIF_Document_Acknowledged_Signed_Date__c' },
    { label: 'AIF Document Number', fieldName: 'AIF_Document_Number__c' },
    { label: 'AIF Document Signed By', fieldName: 'AIF_Document_Signed_By__c' },
    { label: 'AIF Document Amount', fieldName: 'AIF_Document_Amount__c' },
];

export default class CARelatedListController extends LightningElement {
    @api recordId;
    columns = columns;

    @wire(getDocuments, { recordId: '$recordId'})
    documents;
   
}