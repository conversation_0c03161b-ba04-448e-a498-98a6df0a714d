@isTest
public class LeadWhatsappSchedulerTest {
    @isTest
    static void testLeadWhatsappScheduler() {
        // Create test data - Leads
        List<Lead__c> testLeads = new List<Lead__c>();
        for (Integer i = 0; i < 1; i++) {
            testLeads.add(new Lead__c(
               Relationship_Owner__c = USerInfo.getUserId(),
            Primary_Contact__c = '9988998800',
            Lead_Source__c = 'Facebook'
            ));
        }
        insert testLeads;

        // Get the Lead IDs and add them to a set
        Set<Id> leadIds = new Set<Id>();
        for (Lead__c lead : testLeads) {
            leadIds.add(lead.Id);
        }

        // Call the scheduleJob method to schedule the job
        Test.startTest();
        LeadWhatsappScheduler.scheduleJob('Test Lead Whatsapp Job', '0 0 0 15 3 ? 2024', leadIds);
        Test.stopTest();

    }
}