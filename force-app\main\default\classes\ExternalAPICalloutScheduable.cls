global with sharing class ExternalAPICalloutScheduable implements Schedulable {
    global void execute(SchedulableContext sc) {
        List<String> sfids = new List<String>();

        // Fetch records (example)
        List<Account> accounts = [SELECT Id FROM Account WHERE RecordType.Name = 'IPV' /*AND App_Signup_Date__c != null LIMIT 5000*/];
        for (Account acc : accounts) {
            sfids.add(acc.Id);
        }
        
        List<Lead__c> leads = [SELECT Id FROM Lead__c WHERE RecordType.Name = 'IPV' AND App_Signup_Date__c != null];
        for (Lead__c lead : leads) {
            sfids.add(lead.Id);
        }
        
        // Split into chunks
        List<List<String>> sfidChunks = splitList(sfids, 300);

        // Pass the first chunk to the Queueable class
        if (!sfidChunks.isEmpty()) {
            System.enqueueJob(new ExternalAPICalloutQueueable(sfidChunks, 0));
        }
    }

    public List<List<String>> splitList(List<String> originalList, Integer chunkSize) {
        List<List<String>> chunks = new List<List<String>>();
        List<String> currentChunk = new List<String>();
        
        for (Integer i = 0; i < originalList.size(); i++) {
            currentChunk.add(originalList[i]);
            if (currentChunk.size() == chunkSize || i == originalList.size() - 1) {
                chunks.add(currentChunk);
                currentChunk = new List<String>(); // Reset chunk
            }
        }
        return chunks;
    }
}