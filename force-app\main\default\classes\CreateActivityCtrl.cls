Public class CreateActivityCtrl {
    Public List<sObject> selObjLst;
    Public Set<String> recIds;
    Public Task taskObj{get;set;}
    
    Public CreateActivityCtrl(ApexPages.StandardSetController cntlr){
        taskObj = new Task();
        selObjLst = cntlr.getSelected(); //get selected records from account list view
        recIds = new Set<String>();
        
        for(sObject acc : selObjLst){
            recIds.add(acc.Id);//build list of ids string concatenated with comma                         
        }
        //recIds = recIds.removeEnd(','); 
        system.debug('recIds>>>>'+recIds );        
    }
    
    Public Pagereference saveTask()
    {
        system.debug('Save Task recIds>>>>'+recIds );
        system.debug('Save Task taskObj>>>>'+taskObj);
        if(recIds !=null && recIds.size()>0)
        {
            try
            {
                List<Task> taskInsertList = new List<Task>();
                for(String Ids : recIds)
                {
                    Task ts = new Task();
                    ts.WhatId = Ids;
                    ts.Subject = taskObj.Subject__c;
                    ts.Subject__c = taskObj.Subject__c;
                    ts.Description = taskObj.Description;
                    ts.ActivityDate = Date.today();
                    ts.Status = 'Completed';
                    
                    if(taskObj.ActivityDate!=null)
                    {
                        ts.Status = 'Open';
                        ts.ActivityDate = taskObj.ActivityDate;
                    }
                    taskInsertList.add(ts);
                }
                if(taskInsertList!=null && taskInsertList.size()>0)
                    insert taskInsertList;
                
                taskObj = new Task();
                system.debug('taskInsertList>>>>'+taskInsertList);
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.CONFIRM,'Activity has been created successfully.'));
            }
            catch(Exception e)
            {
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,''+e.getMessage())); 
            }
        }
        else
        {
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'Please go back and select at least one record to create the activity.')); 
        }
        
        return null;
    }
    Public Pagereference cancelBtn()
    {
        return new ApexPages.Action('{!List}').invoke();
        //return new PageReference('/001');
    }
}