/*******************************************************************************************************************

		Test Class For VCNetworkRouteToMarketTriggerHandler AND VCNetworkRouteToMarketTrigger

********************************************************************************************************************/

@isTest
public class VCNetworkRouteToMarketTriggerHandlerTest {
	
    @isTest
    public static void testVCNetwork()
    {
		//VCNetworkRouteToMarketTriggerHandler.afterInsertDelete(listOfVCNetworks());
    
        List<VC_Network_Route_to_Market__c> vcNetworkList = listOfVCNetworks();
        Test.startTest();
        VCNetworkRouteToMarketTriggerHandler.afterInsertDelete(vcNetworkList);
        
        delete vcNetworkList;
        VCNetworkRouteToMarketTriggerHandler.afterInsertDelete(vcNetworkList);
        Test.stopTest();
    }

	public static List<VC_Network_Route_to_Market__c> listOfVCNetworks()
    {
		List<VC_Network_Route_to_Market__c> vcNetworkList = new List<VC_Network_Route_to_Market__c>();
        List<Venture_Connect__c> vcListToInsert = new List<Venture_Connect__c>();
        
        Capital_Development__c capitalDev = new Capital_Development__c();
        capitalDev.Name = 'Test Capital Development';
        insert capitalDev;
        
        Transaction_Advisory_History__c transactionAdvisory = new Transaction_Advisory_History__c();
        transactionAdvisory.Capital_Development__c = capitalDev.Id;
        transactionAdvisory.Name = 'Test Transaction Advisory History';
        insert transactionAdvisory;
        
        Venture_Connect__c vc1 = new Venture_Connect__c();
        vc1.Name = 'Venture One';
        vcListToInsert.add(vc1);
        
        Venture_Connect__c vc2 = new Venture_Connect__c();
        vc2.Name = 'Venture Two';
        vcListToInsert.add(vc2);
        
        Venture_Connect__c vc3 = new Venture_Connect__c();
        vc3.Name = 'Venture Three';
        vcListToInsert.add(vc3);
        
        Venture_Connect__c vc4 = new Venture_Connect__c();
        vc4.Name = 'Venture Four';
        vcListToInsert.add(vc4);
        
        Venture_Connect__c vc5 = new Venture_Connect__c();
        vc5.Name = 'Venture Five';
        vcListToInsert.add(vc5);
        
        if(!vcListToInsert.isEmpty())
        {
            insert vcListToInsert;
        }
        
        VC_Network_Route_to_Market__c vcNetwork1 = new VC_Network_Route_to_Market__c();
        vcNetwork1.Transaction_Advisory_History__c = transactionAdvisory.Id;
		vcNetwork1.Venture_Connect__c = vc1.Id;
        vcNetworkList.add(vcNetwork1);

        VC_Network_Route_to_Market__c vcNetwork2 = new VC_Network_Route_to_Market__c();
        vcNetwork2.Transaction_Advisory_History__c = transactionAdvisory.Id;
		vcNetwork2.Venture_Connect__c = vc2.Id;
        vcNetworkList.add(vcNetwork2);
        
        VC_Network_Route_to_Market__c vcNetwork3 = new VC_Network_Route_to_Market__c();
        vcNetwork3.Transaction_Advisory_History__c = transactionAdvisory.Id;
		vcNetwork3.Venture_Connect__c = vc3.Id;
        vcNetworkList.add(vcNetwork3);
        
        VC_Network_Route_to_Market__c vcNetwork4 = new VC_Network_Route_to_Market__c();
        vcNetwork4.Transaction_Advisory_History__c = transactionAdvisory.Id;
		vcNetwork4.Venture_Connect__c = vc4.Id;
        vcNetworkList.add(vcNetwork4);
        
        VC_Network_Route_to_Market__c vcNetwork5 = new VC_Network_Route_to_Market__c();
        vcNetwork5.Transaction_Advisory_History__c = transactionAdvisory.Id;
		vcNetwork5.Venture_Connect__c = vc5.Id;
        vcNetworkList.add(vcNetwork5);
        
        if(!vcNetworkList.isEmpty())
        {
            insert vcNetworkList;
        }
        return vcNetworkList;
    }
}