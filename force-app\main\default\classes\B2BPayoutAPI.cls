@RestResource(urlMapping='/B2BPayoutAPI/*')
global with sharing class B2BPayoutAPI {
    private static String CLASS_NAME = 'B2BPayoutAPI';
    
    @HttpPost
    global static ReqWrapper sendPayoutTransactions() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        Map<String, Object> requestBody = (Map<String, Object>)JSON.deserializeUntyped(req.requestBody.toString());
        System.debug('requestBody: ' + requestBody);
        Integer pageSize = (Integer)requestBody.get('pageSize');
        Integer pageNo = (Integer)requestBody.get('pageNo');
        List<Object> orglistObj = (List<Object>)requestBody.get('orglist');// A,B,C
        String startDateString = (String)requestBody.get('startDate');
        String endDateString = (String)requestBody.get('endDate'); 
        
        Set<Id> AccIdSet = new Set<Id>();
        ReqWrapper errorResponse = new ReqWrapper();
        
        for(Object obj : orglistObj) {
            String orgIdString = String.valueOf(obj);
            if (orgIdString.length() == 18 || orgIdString.length() == 15) {
                AccIdSet.add((Id)orgIdString);
            } else {
                errorResponse.error = 'Invalid Salesforce Id';
                System.debug('Invalid Salesforce Id: ' + orgIdString);
                return errorResponse; 
            }
        }
        System.debug('AccIdSet: ' + AccIdSet);
        
        if (pageSize == null || pageNo == null || AccIdSet.isEmpty()) {
            res.statusCode = 400; // Bad Request
            errorResponse.error = 'pageSize, pageNo, and orglist are required parameters';
            return errorResponse;        
        }
        
        if (startDateString == null || endDateString == null) {
            res.statusCode = 400; // Bad Request
            errorResponse.error = 'startDate and endDate are required parameters';
            return errorResponse;
        }
        
        Date startDate;
        Date endDate;
        
        try {
            startDate = Date.valueOf(startDateString);
            endDate = Date.valueOf(endDateString);
        } catch (Exception e) {
            res.statusCode = 400; // Bad Request
            errorResponse.error = 'Invalid date format. Please use YYYY-MM-DD.';
            return errorResponse;
        }

        List<PayoutTransactionWrapper> transactionList;
        try {
            transactionList = queryTransactions(AccIdSet, pageSize, pageNo, startDate, endDate);
            System.debug('transactionList: ' + transactionList);
            ReqWrapper jsonResponse = new ReqWrapper();
            jsonResponse.payoutTransactions = transactionList;
            jsonResponse.pageSize = pageSize;
            jsonResponse.pageNo = pageNo;
            jsonResponse.totalCount = transactionList.size();
            jsonResponse.error = '';

            res.statusCode = 200; // OK
            res.addHeader('Content-Type', 'application/json');
            res.responseBody = Blob.valueOf(JSON.serialize(jsonResponse));
            System.debug('jsonResponse===>' + JSON.serialize(jsonResponse));
            return jsonResponse;
            
        } catch(Exception e) {
            res.statusCode = 500; // Internal Server Error
            Error_Log__c log =  new Error_Log__c();
            log.Class_Name__c = CLASS_NAME;
            log.method__c = 'B2BPayoutAPI' ;
            log.Error_Message__c = e.getMessage();
            System.debug(e.getMessage());
            insert log;
            System.debug('Error sending request to B2B web App : ' + log.Error_Message__c);  
            
             //send error email
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setToAddresses(new String[] { '<EMAIL>' });
            email.setSubject('Error while sending payout data');
            email.setPlainTextBody(
                'An error occurred in the class B2BPayoutAPI. Below are the details:\n\n' +
                'Timestamp: ' + DateTime.now().format('yyyy-MM-dd HH:mm:ss') + '\n' +
                'Request Parameters:\n' +
                '  pageSize: ' + pageSize + '\n' +
                '  pageNo: ' + pageNo + '\n' +
                '  orglist: ' + JSON.serialize(orglistObj) + '\n' +
                'AccIdSet: ' + JSON.serialize(AccIdSet) + '\n\n' +
                'Class Name: ' + CLASS_NAME + '\n' +
                'Error Message: ' + e.getMessage() + '\n' +
                'Error Stack Trace: ' + e.getStackTraceString() + '\n\n' +
                'Error Log ID: ' + log.Id + '\n' 
            );

   		    Messaging.sendEmail(new Messaging.SingleEmailMessage[] { email });

            errorResponse.error = 'An error occurred while querying payout transactions data';
            return errorResponse;
        }    
    }
    
    public static List<PayoutTransactionWrapper> queryTransactions(Set<Id> AccIdSet, Integer pageSize, Integer pageNo , Date startDate, Date endDate) {
        System.debug('AccIdSet:'+AccIdSet);
        System.debug('startDate:'+startDate + ' '+'endDate'+ endDate);
         Integer startingRow = (pageNo - 1) * pageSize;
        List<PayoutTransactionWrapper> transactionList = new List<PayoutTransactionWrapper>();
        if (AccIdSet == null) {    
            return transactionList; 
        }

        Id specificId = System.Label.b2b_tenenat_user;
       
        List<Account> account = [SELECT Id, Name, Primary_Country_Code__c, Primary_Contact__c, Actual_Email__c
                   FROM Account
                   WHERE Id = :specificId
                   LIMIT 1];
        
        Account specificAccount;
        if (!account.isEmpty()) {
           specificAccount = account[0];
        } else {
            System.debug('No Account found with Id: ' + specificId);
           
        }
        
        List<Payout_Transaction__c > transactions= [SELECT Id, Name, Transaction_Date__c, Amount_for_payout_calculation__c,Syndicate_Payout_Share__c, Payout_Status__c , Payout_Type__c, B2B_Member_Name__c, Payout_Date__c,IPVs_Share__c, Startup_Round_Name__r.Round_Closed__c,
                                                    Investment__c,B2B_Tenant__c, B2B_Tenant__r.Name, B2B_Tenant__r.Actual_Email__c, B2B_Tenant__r.Primary_Contact__c, B2B_Tenant__r.Primary_Country_Code__c, Startup_Round_Name__r.Startup__r.Portfolio_map_Sector__c,
                                                    Startup_Name__c, Startup_Round_Name__c, Startup_Round_Name__r.Name, Startup_Round_Name__r.External_Deal__c, Startup_Round_Name__r.Syndicate_Owner__c, Startup_Round_Name__r.Startup__r.Legal_Name__c, Startup_Round_Name__r.Startup__c
                                                    from Payout_Transaction__c where B2B_Tenant__c IN: AccIdSet AND Transaction_Date__c >= :startDate AND Transaction_Date__c <= :endDate ORDER BY Id LIMIT :pageSize OFFSET :startingRow];
       
        System.debug('transactions=>'+transactions);
        
        Set<Id> memberIds = new Set<Id>();
        for(Payout_Transaction__c pt : transactions){
            if(pt.B2B_Member_Name__c != null){
                memberIds.add(pt.B2B_Member_Name__c);
            }
        }
        System.debug('memberIds=>'+memberIds);
        
        
        List<Account> memberDetails = [SELECT Id, Name,Partner_Type__c,Tenant_SPOC_Account__c,Tenant_SPOC_Account__r.Name,Special_Relationship_Type__c,Investor_Source__c,
                                       Partner_parent_account__c,Partner_parent_account__r.Name,Partner_parent_account__r.Primary_Country_Code__c,Partner_parent_account__r.Primary_Contact__c,
                                       Partner_parent_account__r.Actual_Email__c from Account where Id IN :memberIds];
        
        System.debug('memberDetails=>'+memberDetails);
        
        Map<Id, Account> memberDetailsMap = new Map<Id, Account>(memberDetails);
        
        Set<Id> investmentIds = new Set<Id>();
        for(Payout_Transaction__c  pt : transactions){
            if(pt.Investment__c != null){
                investmentIds.add(pt.Investment__c);
            }
        }
        
        Map<Id, Investment__c> investorsMap = new Map<Id, Investment__c>([SELECT Id, Name, Investor__r.Name, Date_of_transaction__c, Investor__c FROM Investment__c WHERE Id IN :investmentIds]);
        
        for(Payout_Transaction__c  pt : transactions){
            
            if (pt.B2B_Member_Name__c != null && !memberDetailsMap.containsKey(pt.B2B_Member_Name__c)) {
                continue;
            }
            
            PayoutTransactionWrapper payoutObj = new PayoutTransactionWrapper();
            payoutObj.transactionId = pt.Id;
            payoutObj.name = pt.Name;
            payoutObj.transactionDate = pt.Transaction_Date__c;
            payoutObj.amount = pt.Amount_for_payout_calculation__c;
            payoutObj.payoutFees = pt.Syndicate_Payout_Share__c;
            payoutObj.payoutStatus = pt.Payout_Status__c;
            payoutObj.ipvsShare = pt.IPVs_Share__c;
            payoutObj.payoutType = pt.Payout_Type__c;
            payoutObj.payoutDate = pt.Payout_Date__c;
            
            Account memberAccount = memberDetailsMap.get(pt.B2B_Member_Name__c);
            if (memberAccount != null) {
                B2bMemberWrapper b2bMember = new B2bMemberWrapper();
                b2bMember.name = memberAccount.Name;
                b2bMember.salesforceId =memberAccount.Id;
                b2bMember.partnerType = memberAccount.Partner_Type__c;
                b2bMember.spocId = memberAccount.Tenant_SPOC_Account__c;
                b2bMember.spocName = memberAccount.Name;
                b2bMember.specialRelationType = memberAccount.Special_Relationship_Type__c;
                b2bMember.investorSource = memberAccount.Investor_Source__c;
                payoutObj.b2bMember = b2bMember; 
                
                ParentAccWrapper parentAc = new ParentAccWrapper();
                if (memberAccount.Partner_parent_account__c != null) { 
                    parentAc.salesforceId = memberAccount.Partner_parent_account__c;
                    parentAc.memberName = memberAccount.Partner_parent_account__r != null ? memberAccount.Partner_parent_account__r.Name : null;
                    parentAc.pcountryCode = memberAccount.Partner_parent_account__r != null ? String.valueOf(memberAccount.Partner_parent_account__r.Primary_Country_Code__c) : null;
                    parentAc.pcontact = memberAccount.Partner_parent_account__r != null ? memberAccount.Partner_parent_account__r.Primary_Contact__c : null;
                    parentAc.pemail = memberAccount.Partner_parent_account__r != null ? memberAccount.Partner_parent_account__r.Actual_Email__c : null;
                } else 
                {
                    parentAc.salesforceId = specificAccount.Id; 
                    parentAc.memberName = specificAccount.Name;
                    parentAc.pcountryCode = String.valueOf(specificAccount.Primary_Country_Code__c);
                    parentAc.pcontact = specificAccount.Primary_Contact__c;
                    parentAc.pemail = specificAccount.Actual_Email__c;
                }
                payoutObj.parentAc = parentAc;
            } 
            else {
                payoutObj.b2bMember = null;
            }
            
            TransactionParentAccWrapper transactionParentAc = new TransactionParentAccWrapper();
            if(pt.B2B_Tenant__c != null){
                
                transactionParentAc.salesforceId = pt.B2B_Tenant__c;
                transactionParentAc.memberName = pt.B2B_Tenant__r.Name;
                transactionParentAc.pemail = pt.B2B_Tenant__r.Actual_Email__c;
                transactionParentAc.pcontact = pt.B2B_Tenant__r.Primary_Contact__c;
                transactionParentAc.pcountryCode = String.valueOf(pt.B2B_Tenant__r.Primary_Country_Code__c);
                payoutObj.transactionParentAc = transactionParentAc;
            } else 
            {
                payoutObj.transactionParentAc = null;
            }
            
            String dealType = (pt.Startup_Round_Name__r.External_Deal__c == 'Yes') ? 'Syndicate' : 'IPV';
            String dealStatus = (pt.Startup_Round_Name__r.Round_Closed__c == True) ? 'Closed' : 'WIP';
            StartupWrapper startup = new StartupWrapper();
            if (null != pt.Startup_Round_Name__r){
                startup.startupId = pt.Startup_Round_Name__r.Startup__c;
                startup.publicName = pt.Startup_Name__c;
                startup.legalName = pt.Startup_Round_Name__r.Startup__r.Legal_Name__c;
                startup.sector = pt.Startup_Round_Name__r.Startup__r.Portfolio_map_Sector__c;
                
                StartupRoundWrapper roundObj = new StartupRoundWrapper();
                roundObj.id = pt.Startup_Round_Name__c;
                roundObj.name = pt.Startup_Round_Name__r.Name;
                roundObj.dealType = dealType;
                roundObj.dealStatus = dealStatus;
                roundObj.syndicateOwner = pt.Startup_Round_Name__r.Syndicate_Owner__c;
                if (pt.Startup_Round_Name__r.Syndicate_Owner__c != null) {
                    roundObj.source = 'Syndicate';
                } else {
                    roundObj.source = 'IPV';
                }
                
                startup.startupRoundObj = roundObj;
                payoutObj.startup = startup;
            } else {
                payoutObj.startup = null;
            }
            
            if(pt.Investment__c != null && investorsMap.containsKey(pt.Investment__c)){
                InvestorWrapper investor = new InvestorWrapper();
                investor.investorId = investorsMap.get(pt.Investment__c).Investor__c;
                investor.name = investorsMap.get(pt.Investment__c).Investor__r.Name;
                payoutObj.investor = investor;
            }  
            transactionList.add(payoutObj);
        }   
        System.debug(transactionList);
        return transactionList;   
    } 
    
    global class PayoutTransactionWrapper {
        public String transactionId { get; set; }
        public Date transactionDate { get; set; }
        public String name { get; set; }
        public String payoutType { get; set; }
        public Decimal amount { get; set; }
        public Decimal payoutFees { get; set; }
        public String payoutStatus { get; set; }
        public Date payoutDate { get; set; }
        public Decimal ipvsShare { get; set; }
        public B2bMemberWrapper b2bMember { get; set; }
        public InvestorWrapper investor { get; set; }
        public StartupWrapper startup { get; set; }
        public ParentAccWrapper parentAc { get; set; }
        public TransactionParentAccWrapper transactionParentAc { get; set; }
    }
    
    global class B2bMemberWrapper {
        public String name { get; set; }
        public String salesforceId { get; set; }
        public String specialRelationType { get; set; }
        public String partnerType { get; set; }
        public String spocId { get; set; }
        public String spocName { get; set; }
        public String investorSource { get; set; }
    }
    
    global class ParentAccWrapper {
        public String salesforceId { get; set; }
        public String memberName { get; set; }
        public String pcountryCode { get; set; }
        public String pcontact { get; set; }
        public String pemail { get; set; }
    }
    
    global class TransactionParentAccWrapper {
        public String salesforceId { get; set; }
        public String memberName { get; set; }
        public String pcountryCode { get; set; }
        public String pcontact { get; set; }
        public String pemail { get; set; }
    }
    
    global class InvestorWrapper {
        public String investorId { get; set; }
        public String name { get; set; }
    }

    public class StartupWrapper{
        public String startupId;
        public String publicName;
        public String legalName;
        public String sector;
        public StartupRoundWrapper startupRoundObj;
    } 
    
    public class StartupRoundWrapper{
        public String id;
        public String name;
        public String dealType;
        public String dealStatus;
        public String syndicateOwner;
        public String source;
    }
    global class ReqWrapper{
        public Integer totalCount { get; set; }
        public Integer pageSize { get; set; }
        public Integer pageNo { get; set; }
        public String error { get; set; }
        public List<PayoutTransactionWrapper> payoutTransactions { get; set; }
    }
}