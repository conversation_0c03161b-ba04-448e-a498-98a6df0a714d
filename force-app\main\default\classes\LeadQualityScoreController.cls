public class LeadQualityScoreController {
	
    @AuraEnabled(cacheable=true)
    public static Map<String , Integer> getLeadQualityScore(Id leadId)
    {
        Lead__c lead = [SELECT Id , Name , Lead_Quality_Score__c , Lead_Quality_Score_Percentage__c ,Lead_Source__c , College_Tier__c , 
                        Designation_Band__c , Referred_By__c ,  Residential_Status__c , Total_Number_of_Investor_Calls__c , 
                        Total_Number_of_Founder_Calls__c , Total_Number_of_Offline_Online_event__c , Total_Referred_Account_Leads__c ,
                        Bot_Input__c , App_Signup_Date__c , Are_They_Part_of_Other_Platform__c , Bot_Journey_Stage__c
                        FROM Lead__c WHERE RecordType.Name ='IPV' AND Id =: leadId];
        
        System.debug('Lead >>>>> ' + lead);
        Map<String , Integer> fieldsWithScores = new Map<String , Integer>();
        
        Integer totalScore = 0;
        Integer eachScore;
        
        
        //   1. Score of "Are They Part of Other Platform"
        if(lead.Are_They_Part_of_Other_Platform__c != null)
        {
        	if(lead.Are_They_Part_of_Other_Platform__c == 'Yes')
            {
                eachScore = 2;
                fieldsWithScores.put('Are They Part of Other Platform' , eachScore);
            }
            else if(lead.Are_They_Part_of_Other_Platform__c == 'No')
            {
                eachScore = 0;
                fieldsWithScores.put('Are They Part of Other Platform' , eachScore);
            }    
        }
        else
        {
            eachScore = 0;
                fieldsWithScores.put('Are They Part of Other Platform' , eachScore);
        }
        
        
        
		//   2. Score of "App Signup Date"       
        if(lead.App_Signup_Date__c != null)
        {
            eachScore = 2;
            fieldsWithScores.put('App Signup Date' , eachScore);
        }
        else
        {
            eachScore = 1;
            fieldsWithScores.put('App Signup Date' , eachScore);
        }
        
        
        
        //   3. Score of "Total Referred (Account/Leads)"
        if(lead.Total_Referred_Account_Leads__c != null)
        {
        	if(lead.Total_Referred_Account_Leads__c == 1)
            {
                eachScore = 1;
                fieldsWithScores.put('Total Referred (Account/Leads)' , eachScore);
            }
            else if(lead.Total_Referred_Account_Leads__c >= 2 && lead.Total_Referred_Account_Leads__c <= 5)
            {
                eachScore = 3;
                fieldsWithScores.put('Total Referred (Account/Leads)' , eachScore);
            }
            else if(lead.Total_Referred_Account_Leads__c >= 6 && lead.Total_Referred_Account_Leads__c <= 10)
            {
                eachScore = 4;
                fieldsWithScores.put('Total Referred (Account/Leads)' , eachScore);
            }
            else if(lead.Total_Referred_Account_Leads__c > 10)
            {
                eachScore = 5;
                fieldsWithScores.put('Total Referred (Account/Leads)' , eachScore);
            }    
        }
        else
        {
             eachScore = 0;
			 fieldsWithScores.put('Total Referred (Account/Leads)' , eachScore);
        }
               
        
        //   4. Score of "Bot Input"
        if(lead.Bot_Input__c != null)
        {
            eachScore = 2;
            fieldsWithScores.put('Bot Input' , eachScore);
        }
        else if(lead.Bot_Input__c == null && lead.Bot_Journey_Stage__c == null)
        {
            eachScore = 1;
            fieldsWithScores.put('Bot Input' , eachScore);
        }
        else if(lead.Bot_Input__c == null && lead.Bot_Journey_Stage__c != null)
        {
            eachScore = 0;
            fieldsWithScores.put('Bot Input' , eachScore);
        }
        
		
        //   5. Score of "Total Number of offline/ online events"
        if(lead.Total_Number_of_Offline_Online_event__c != null)
        {            
            if(lead.Total_Number_of_Offline_Online_event__c == 0)
            {
                eachScore = 0;
                fieldsWithScores.put('Total Number of offline/ online events' , eachScore);
            }
            else if(lead.Total_Number_of_Offline_Online_event__c >= 1 && lead.Total_Number_of_Offline_Online_event__c <= 4)
            {
                eachScore = 3;
                fieldsWithScores.put('Total Number of offline/ online events' , eachScore);
            }
            else if(lead.Total_Number_of_Offline_Online_event__c >= 5)
            {
                eachScore = 5;
                fieldsWithScores.put('Total Number of offline/ online events' , eachScore);
            }
        }
        else
        {
			eachScore = 0;
            fieldsWithScores.put('Total Number of offline/ online events' , eachScore);
        }
        
        
        //   6. Score of "Total Number of Investor Calls"
        if(lead.Total_Number_of_Investor_Calls__c != null)
        {
            if(lead.Total_Number_of_Investor_Calls__c == 0)
            {
                eachScore = 0;
                fieldsWithScores.put('Total Number of Investor Calls' , eachScore);
            }
            else if(lead.Total_Number_of_Investor_Calls__c >= 1 && lead.Total_Number_of_Investor_Calls__c <= 4)
            {
                eachScore = 2;
                fieldsWithScores.put('Total Number of Investor Calls' , eachScore);
            }
            else if(lead.Total_Number_of_Investor_Calls__c >= 5 && lead.Total_Number_of_Investor_Calls__c <= 9)
            {
                eachScore = 4;
                fieldsWithScores.put('Total Number of Investor Calls' , eachScore);
            }
            else if(lead.Total_Number_of_Investor_Calls__c >= 10)
            {
                eachScore = 5;
                fieldsWithScores.put('Total Number of Investor Calls' , eachScore);
            }
        }
        else
        {
             eachScore = 0;
             fieldsWithScores.put('Total Number of Investor Calls' , eachScore);
        }
        
        
        //   7. Score of "Total Number of Founder Calls"
        if(lead.Total_Number_of_Founder_Calls__c != null)
        {
            if(lead.Total_Number_of_Founder_Calls__c == 0)
            {
                eachScore = 0;
                fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
            }
            else if(lead.Total_Number_of_Founder_Calls__c >= 1 && lead.Total_Number_of_Founder_Calls__c <= 4)
            {
                eachScore = 1;
                fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
            }    
            else if(lead.Total_Number_of_Founder_Calls__c >= 5 && lead.Total_Number_of_Founder_Calls__c <= 9)
            {
                eachScore = 2;
                fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
            }    
            else if(lead.Total_Number_of_Founder_Calls__c >= 10 && lead.Total_Number_of_Founder_Calls__c <= 19)
            {
                eachScore = 3;
                fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
            }
            else if(lead.Total_Number_of_Founder_Calls__c >= 20)
            {
                eachScore = 4;
                fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
            }
        }
        else
        {
             eachScore = 0;
             fieldsWithScores.put('Total Number of Founder Calls' , eachScore);
        }

        
        //   8. Score of "Residential Status"
        if(lead.Residential_Status__c != null)
        {
            if(lead.Residential_Status__c == 'NRI')
            {
                eachScore = 2;
                fieldsWithScores.put('Residential Status' , eachScore);
            }
            else if(lead.Residential_Status__c == 'INDIAN')
            {
                eachScore = 1;
                fieldsWithScores.put('Residential Status' , eachScore);
            }
        }
        else
        {
             eachScore = 0;
             fieldsWithScores.put('Residential Status' , eachScore);
        }
        
        
        //   9. Score of "Designation Band"
        if(lead.Designation_Band__c == null)
        {
			eachScore = 3;
            fieldsWithScores.put('Designation Band' , eachScore);
        }
        else if(lead.Designation_Band__c == 'Band 1')
        {
            eachScore = 5;
            fieldsWithScores.put('Designation Band' , eachScore);            
        }
        else if(lead.Designation_Band__c == 'Band 2')
        {
            eachScore = 4;
            fieldsWithScores.put('Designation Band' , eachScore);
        }
        else if(lead.Designation_Band__c == 'Band 3')
        {
            eachScore = 2;
            fieldsWithScores.put('Designation Band' , eachScore);
        }
        else if(lead.Designation_Band__c == 'Band 4')
        {
            eachScore = 1;
            fieldsWithScores.put('Designation Band' , eachScore);
        }
        
        
        
        //   10. Score of "College Tier"
        if(lead.College_Tier__c == null)
        {
         	eachScore = 1;
            fieldsWithScores.put('College Tier' , eachScore);
        }
        else if(lead.College_Tier__c == 'Tier 1')
        {
            eachScore = 5;
            fieldsWithScores.put('College Tier' , eachScore);
        }
        else if(lead.College_Tier__c == 'Tier 2')
        {
            eachScore = 3;
            fieldsWithScores.put('College Tier' , eachScore);
        }
        else if(lead.College_Tier__c == 'Tier 3')
        {
            eachScore = 1;
            fieldsWithScores.put('College Tier' , eachScore);
        }
        
        if(lead.Referred_By__c != null)
        {
            Account account = [SELECT Total_Amount_Invested__c FROM Account WHERE Id = :lead.Referred_By__c LIMIT 1];
            System.debug('Account Total Investment >>>>> ' + account.Total_Amount_Invested__c);
            
            //   11. Score of "Total Investment by Referrer"
            if(account.Total_Amount_Invested__c == 0 || account.Total_Amount_Invested__c == null)
            {
                eachScore = 1;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);
            }        
            else if(account.Total_Amount_Invested__c >= 1 && account.Total_Amount_Invested__c <= 500000)
            {
                eachScore = 2;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);
            }
            else if(account.Total_Amount_Invested__c > 500000 && account.Total_Amount_Invested__c <= 1000000)
            {
                eachScore = 3;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);
            }
            else if(account.Total_Amount_Invested__c > 1000000 && account.Total_Amount_Invested__c <= 2000000)
            {
                eachScore = 4;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);
            }
            else if(account.Total_Amount_Invested__c > 2000000)
            {
                eachScore = 5;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);
            }
        }
        else
        {
                eachScore = 0;
                fieldsWithScores.put('Total Investment by Referrer' , eachScore);		
        }
        
        
        System.debug('Lead Source >>> ' + lead.Lead_Source__c);
        
        
        Set<String> leadSources = new Set<String>{
            'Referral','Others','Organic-Website','Linkedin','Facebook','Online Events','Organic-App Sign Up','Startup Calls','Sales Navigator','Offline Events',
            'Google Ads','B2B-Wealth Manager','App Referral','External database','B2B-Partnerships','B2B Real Estate','App','Offline Events-Gated Community',
            'B2B-Credit Cards','B2B-Luxury Brands','B2B-Corporates','Organic-social media','Whatsapp','Member referral','Email','Google Form','Ozonetel','Website',
            'Linkedin Ads','Linkedin - ORAI','Facebook Ads','Facebook - ORAI','Masterclass','External Masterclass Sessions','Webinar leads','App Sign Up','Feedback Form',
            'Saturday Calls','Wednesday Calls','Linkedin - Sales Navigator','Sales QL','Google SEM','WealthTrust','Investor Harvesting','Status Match','Sweetbox DB',
            'MF Database','Partnerships','Partnerships - Vinners','ORAI','Real Estate Project','Instagram','FB Comment','FB DM','Social Media Organic','Instagram DM',
            'Twitter', 'Events' , 'Webinar' , 'VC Connect Person' , 'Member Referral'
        };

        //   12. Score of "Lead Source"
        if(lead.Lead_Source__c != null)
        {
            if(lead.Lead_Source__c == 'Referral' || lead.Lead_Source__c == 'Whatsapp' || lead.Lead_Source__c == 'Member referral')
            {
                eachScore = 5;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'Others' || lead.Lead_Source__c == 'Google Form' || lead.Lead_Source__c == 'Email' || lead.Lead_Source__c == 'VC Connect Person' || lead.Lead_Source__c == 'Ozonetel')
            {
                eachScore = 2;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'Organic-Website' || lead.Lead_Source__c == 'Website')
            {
                eachScore = 4;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'Linkedin' || lead.Lead_Source__c == 'Linkedin Ads' || lead.Lead_Source__c == 'Linkedin - ORAI')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'Facebook' || lead.Lead_Source__c == 'Facebook Ads' || lead.Lead_Source__c == 'Facebook - ORAI')
            {
                eachScore = 2;
                fieldsWithScores.put('Lead Source' , eachScore);                
            }
            else if(lead.Lead_Source__c == 'Online Events' || lead.Lead_Source__c == 'Events' || lead.Lead_Source__c == 'Masterclass' || lead.Lead_Source__c == 'External Masterclass Sessions' || lead.Lead_Source__c == 'Webinar leads' || lead.Lead_Source__c == 'Webinar')
            {
                eachScore = 2;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'Organic-App Sign Up' || lead.Lead_Source__c == 'App Sign Up')
            {
                eachScore = 4;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'Startup Calls' || lead.Lead_Source__c == 'Feedback Form' || lead.Lead_Source__c == 'Saturday Calls' || lead.Lead_Source__c == 'Wednesday Calls')
            {
                eachScore = 2;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'Sales Navigator' || lead.Lead_Source__c == 'Linkedin - Sales Navigator' || lead.Lead_Source__c == 'Sales QL')
            {
                eachScore = 2;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'Offline Events')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'Google Ads' || lead.Lead_Source__c == 'GoogleSEM')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'B2B-Wealth Manager' || lead.Lead_Source__c == 'WealthTrust')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            if(lead.Lead_Source__c == 'App Referral')
            {
                eachScore = 5;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'External database' || lead.Lead_Source__c == 'Investor Harvesting' || lead.Lead_Source__c == 'Status Match' || lead.Lead_Source__c == 'Sweetbox DB' || lead.Lead_Source__c == 'MF Database')
            {
                eachScore = 2;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'B2B-Partnerships' || lead.Lead_Source__c == 'Partnerships' || lead.Lead_Source__c == 'Partnerships - Vinners' || lead.Lead_Source__c == 'ORAI')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'B2B Real Estate')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'App')
            {
                eachScore = 4;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'Offline Events-Gated Community')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'B2B-Credit Cards' || lead.Lead_Source__c == 'Real Estate Project')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'B2B-Luxury Brands')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'B2B-Corporates')
            {
                eachScore = 3;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(lead.Lead_Source__c == 'Organic-social media' || lead.Lead_Source__c == 'Instagram' || lead.Lead_Source__c == 'FB Comment' || lead.Lead_Source__c == 'FB DM' || lead.Lead_Source__c == 'Social Media Organic' || lead.Lead_Source__c == 'Instagram DM' || lead.Lead_Source__c == 'Twitter')
            {
                eachScore = 4;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
            else if(!leadSources.contains(lead.Lead_Source__c))
            {
                eachScore = 1;
                fieldsWithScores.put('Lead Source' , eachScore);
            }
        }
        else
        {
            eachScore = 0;
            fieldsWithScores.put('Lead Source' , eachScore);
        }
              
        
        // Calculate total score
        for (Integer score : fieldsWithScores.values()) {
            totalScore += score;
        }
                
		
        // Calculate percentage
        Integer maximumPossibleScore = maxScore();
        Decimal percentage = (Decimal.valueOf(totalScore) / Decimal.valueOf(maximumPossibleScore)) * 100;
        
		// Update the Lead Quality Score % & Lead Quality Score field
		lead.Lead_Quality_Score__c = totalScore;
        lead.Lead_Quality_Score_Percentage__c = percentage.setScale(2, System.RoundingMode.HALF_UP);        
        fieldsWithScores.put('Lead Quality Score', totalScore);
        
        System.enqueueJob(new UpdateQualityScoreJob(lead));
       
        System.debug('The Map of Fields With Its Score >>>>>> ' + fieldsWithScores);
        
        return fieldsWithScores;
    }

	public static Integer maxScore()
    {
        Integer totalPossibleScore = 0;
        Integer leadSourceMAXScore = 5;
        Integer totalInvestmentMAXScore = 5;
        Integer collegerTierMAXScore = 5;
        Integer designationMAXScore = 5;
        Integer residentMAXScore = 2;
        Integer totalNumberOfInvestorMAXScore = 4;
        Integer totalNumberOfFounderMAXScore = 5;
        Integer totalNumberOfEventsMAXScore = 5;
        Integer botInputMAXScore = 2;
        Integer appSignUpMAXScore = 2;
        Integer totalRefferedMAXScore = 5;
        Integer areTheyPartOfAngelInvestmentMAXScore = 2;
        
        
        //  Counting The Total Possible/Maximum Score Of Quality Lead
        totalPossibleScore = leadSourceMAXScore + totalInvestmentMAXScore + collegerTierMAXScore + designationMAXScore + residentMAXScore + totalNumberOfInvestorMAXScore + totalNumberOfFounderMAXScore + totalNumberOfEventsMAXScore +  botInputMAXScore + appSignUpMAXScore + totalRefferedMAXScore +  areTheyPartOfAngelInvestmentMAXScore;
        
        return totalPossibleScore;
    }
}