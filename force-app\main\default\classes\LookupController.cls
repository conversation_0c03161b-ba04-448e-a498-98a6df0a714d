public class LookupController {
    /**
* Returns JSON of list of ResultWrapper to Lex Components
* @objectName - Name of SObject
* @fld_API_Text - API name of field to display to user while searching
* @fld_API_Val - API name of field to be returned by Lookup COmponent
* @lim   - Total number of record to be returned
* @fld_API_Search - API name of field to be searched
* @searchText - text to be searched
* */
    @AuraEnabled(cacheable=true)
    public static String searchDB(String objectName, String fld_API_Text, String fld_API_Val, 
                                  Integer lim,String fld_API_Search,String searchText ){
                                      
                                      searchText='\'' + String.escapeSingleQuotes(searchText.trim()) + '%\'';
                                      
                                      
                                      String query = 'SELECT Id,'+fld_API_Text+
                                          ' FROM '+objectName+
                                          ' WHERE '+fld_API_Text+' LIKE '+searchText+ 
                                          ' LIMIT 10';
                                      system.debug('query ::'+query);
                                      List<sObject> sobjList = Database.query(query);
                                      List<ResultWrapper> lstRet = new List<ResultWrapper>();
                                      
                                      for(SObject s : sobjList){
                                          ResultWrapper obj = new ResultWrapper();
                                          obj.accId = s.Id;
                                          obj.objName = objectName;
                                          obj.text = String.valueOf(s.get(fld_API_Text)) ;
                                          obj.val = String.valueOf(s.get(fld_API_Val))  ;
                                          lstRet.add(obj);
                                      } 
                                      return JSON.serialize(lstRet) ;
                                  }
    
    @AuraEnabled
    public static List<Contact> getContacts(String accountId){
        system.debug('Id --'+accountId);
        List<Contact> cons = [SELECT Id,Name from Contact WHERE accountID = :accountId];
        if(!cons.isEmpty()) return cons;
        return null;
    }
    
    @AuraEnabled(cacheable=true)
	public static void verifyCKYC(String panNo,Date DOB,String Id){ 
        KYCController.verifyCKYC(panNo,DOB,Id);
    }
    
    @AuraEnabled
    public static void verifyAadhar(String aadharFile,string panFile,String Id){
        system.debug('aadharFile ::'+aadharFile);
        system.debug('panFile ::'+panFile);
        KYCController.verifyAadhar(aadharFile,panFile,Id);
    }
    
   
    
    public class ResultWrapper{
        public String objName {get;set;}
        public String text{get;set;}
        public String val{get;set;}
        public String accID{get;set;}
    }
}