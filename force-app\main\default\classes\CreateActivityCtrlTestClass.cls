/************************************************************************
    Test Class for CreateActivityCtrl.
************************************************************************/

@isTest(SeeAllData=false)
private class CreateActivityCtrlTestClass{
    
    @testSetup static void setup() {
        account acc = TestFactory.createAccount();
        insert acc;
    }
    
    static testMethod void testAccountToLead()
    {
        test.startTest();
            account parAcc = [select id,Primary_Contact__c from account limit 1];
            PageReference pageRef = Page.CreateActivityPage;
            test.setCurrentPageReference(pageRef);
            pageRef.getParameters().put('id',parAcc.Id);
            
            List <Account> lstAccount = new List<Account>();
             
            lstAccount.add(parAcc);
            
            ApexPages.StandardSetController stdSetController = new ApexPages.StandardSetController(lstAccount);
            stdSetController.setSelected(lstAccount);
            CreateActivityCtrl ext = new CreateActivityCtrl(stdSetController);
            ext.taskObj.Subject__c = 'Call';
            ext.taskObj.Description = 'Call';
            ext.saveTask();
            ext.cancelBtn();
            
            lstAccount = new List<Account>();
            stdSetController = new ApexPages.StandardSetController(lstAccount);
            stdSetController.setSelected(lstAccount);
            ext = new CreateActivityCtrl(stdSetController);
            ext.taskObj.Subject__c = 'Call';
            ext.taskObj.Description = 'Call';
            ext.saveTask();


         test.stopTest();
    }
    
}