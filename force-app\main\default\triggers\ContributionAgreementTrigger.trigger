trigger ContributionAgreementTrigger on Contribution_Agreement__c (before insert,before update,after insert,after update) {
    List<System_Setting__c> settingList = System_Setting__c.getall().values();
    
    if(settingList!=null && settingList.size()>0 && !settingList[0].CATriggerActivated__c)
    {
        system.debug('Returning from CA Trigger because of custom setting>>>'+settingList);
        return;
    }
    if(trigger.isBefore){
        ContributionTriggerHandler.bulkBefore(Trigger.new); 
    }else{
        ContributionTriggerHandler.bulkAfter(Trigger.new,trigger.old);
    }
    

    // Added by Sahilparvat for Gift City BRD.
    if (Trigger.isAfter && (Trigger.isInsert || Trigger.isUpdate)) {
        ContributionTriggerHandler.fundModuleRollUpSummary(Trigger.new, Trigger.oldMap);
    }

    if (Trigger.isBefore) {

        if (Trigger.isInsert) {
            ContributionTriggerHandler.assignFundSpecificNumbers(Trigger.new, null);
        }

        if (Trigger.isUpdate) {
            ContributionTriggerHandler.assignFundSpecificNumbers(Trigger.new, Trigger.oldMap);
        }  
    }
        
}