public class campaignTriggerHandler {
    
    public void afterInsert(List<Campaign> cmpList){
        
        set<Id> cmpIdSet = NEW Set<Id>();
        for (Campaign cmp : cmpList){
            cmpIdSet.add(cmp.Id);
        }
        
        if(cmpIdSet!=null && cmpIdSet.size()>0){
            PromoCodeAPI.sendCampaignDetails(cmpIdSet);
        }
    }
    
    public void afterUpdate(List<Campaign> cmpList){
        
        set<Id> cmpIdSet = NEW Set<Id>();
        for (Campaign cmp : cmpList){
            cmpIdSet.add(cmp.Id);
        }
        
        if(cmpIdSet!=null && cmpIdSet.size()>0){
            PromoCodeAPI.sendCampaignDetails(cmpIdSet);
        }
    }
}