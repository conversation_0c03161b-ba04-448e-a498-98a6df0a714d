@isTest
public class TaskTriggerHandlerTest {
    @isTest
    static void testUpdateCallStatus() {
        // Create test data for tasks
      
        List<account> Acclist = new list <Account>();
        Account testAccount = TestFactory.createAccount();
        testAccount.Name = 'Test Account';
        testAccount.Date_of_Payment__c =Date.newInstance(2023, 12, 21);
        
        Acclist.add(testAccount);
        Account testAccount1 = TestFactory.createAccount();
        testAccount.Name = 'Test Account2';
        testAccount.Date_of_Payment__c = Date.newInstance(2023, 08, 21);
        
        Acclist.add(testAccount1);
        insert Acclist;
         
        list<task> tklist = new list<task>();
        Task testTask = new Task(WhatId = testAccount.Id,Subject ='Call',Type ='L1',Outcome__c ='Call Done',Status ='Completed');
        
        tklist.add(testtask);
        Task testTask1 = new Task(WhatId = testAccount.Id,Subject ='Call',Type ='L2',Outcome__c ='Call Done',Status ='Completed' , Task_Due_Date_and_Time__c = Date.Today());
        
        tklist.add(testtask1);
        
        Task testTask2 = new Task(WhatId = Acclist[1].Id,Subject = 'Call',Type ='L2',Outcome__c ='Call Done',Status ='Open');
        
        tklist.add(testtask2); 
        insert tklist;

        // Schedule the job
        Test.startTest();
        Set<Id> taskIds = new Set<Id>{testTask.Id};
        TaskTriggerHandler.updateCallStatus(taskIds);
        TaskTriggerHandler.beforeInsertUpdate(tklist);
        Test.stopTest();

        // Verify the results
        List<Account> updatedAccounts = [SELECT Id, L1_Call_status__c, L2_Call_status__c FROM Account WHERE Id =:Acclist];
        System.assertEquals('Completed', updatedAccounts[0].L1_Call_status__c);
        //System.assertEquals('Completed', updatedAccounts[0].L2_Call_status__c);
        System.assertEquals('Yet To Happen', updatedAccounts[1].L2_Call_status__c);
       
    }
}