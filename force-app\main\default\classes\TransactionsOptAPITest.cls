@isTest 
public class TransactionsOptAPITest{
    
    
    public static testMethod void testPostCallout(){
         // Create a mock request
        RestRequest request = new RestRequest();
        RestResponse response = new RestResponse();
        request.requestURI = '/services/apexrest/transactionUtility';
        request.httpMethod = 'POST';
        String requestBody = '{"VirtualAccountNumber": "12345","TransactionAmount": 1000.00,"Mode": "Online","SendertoReceiverInformation": "Payment for services","RemitterAccountNumber": "54321","RemitterIFSC_Code": "IFSC123","RemitterName": "Test Name","Date": "2023-08-21 19:26:40","UTRnumber": "**********","clientCode": "cl0101","senderName": "Test"}';
        request.requestBody = Blob.valueOf(requestBody);
        
        // Set the mock request and response in the RestContext
        RestContext.request = request;
        RestContext.response = response;
        TransactionsOptAPI.transaction();       
    }
    
  /*  @isTest
    static void testHttpPost() {
        // prepare test-data
    
        //As Per Best Practice it is important to instantiate the Rest Context
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/transactionUtility'; //Request URL
        req.httpMethod = 'GET';
    
        RestContext.request = req;
        //RestContext.response= res;
    
       
          TransactionsOptAPI.transaction();//HttpClass.updateCustomObject();
        
    
     }*/
}