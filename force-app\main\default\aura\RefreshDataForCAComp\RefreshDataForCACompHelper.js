({
    pollBatchStatus: function(component, jobId) {
        var action = component.get("c.checkBatchStatus");
        action.setParams({ jobId: jobId });
        
        action.setCallback(this, function(response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var status = response.getReturnValue();
                if (status === "Completed") {
                    alert("Batch process completed successfully.");
                    component.set("v.showSpinner", false);
                } else if (status === "Failed") {
                    alert("Batch process failed.");
                    component.set("v.showSpinner", false);
                } else {
                    // If the status is not completed or failed, poll again after a delay
                    window.setTimeout(
                        $A.getCallback(function() {
                            this.pollBatchStatus(component, jobId);
                        }.bind(this)), 2000
                    );
                }
            } else {
                // Handle error
                var errors = response.getError();
                var message = "Unknown error";
                if (errors && errors[0] && errors[0].message) {
                    message = errors[0].message;
                }
                alert(message);
                component.set("v.showSpinner", false);
            }
        });
        
        $A.enqueueAction(action);
    }
})