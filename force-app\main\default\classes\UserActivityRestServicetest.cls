global class UserActivityRestServicetest {

   /*public static void callExternalApiWithMockResponse(Set<Id> recordIds, String startDate, String endDate, String mockResponse) {
        // Map activity types to their corresponding descriptions
        Map<Integer, String> activityDetailsMap = new Map<Integer, String>{
            0 => 'Startup',
            1 => 'Agenda',
            2 => 'Sign IN',
            3 => 'Referral',
            4 => 'Discussion',
            5 => 'Comment',
            6 => 'Commitment Request',
            7 => 'Contact your RM',
            8 => 'FAQs',
            9 => 'Feedback',
            10 => 'Leaderboard',
            11 => 'Marking Startup as Favourite',
            12 => 'Portfolio',
            13 => 'Refer a Startup',
            14 => 'Query Raised',
            15 => 'Startup Call'
        };

        try {
            // Deserialize the response to a List of Objects
            List<Object> apiResponse = (List<Object>) JSON.deserializeUntyped(mockResponse);

            List<User_Activity__c> accountActivities = new List<User_Activity__c>();
            List<User_Activity__c> leadActivities = new List<User_Activity__c>();

            for (Object activityObj : apiResponse) {
                Map<String, Object> activityData = (Map<String, Object>) activityObj;

                String salesforce_user_account_id = (String) activityData.get('salesforce_user_account_id');
                Integer activityType = (Integer) activityData.get('activity_type'); // Correct key
                String activitySummary = (String) activityData.get('activity_summary'); // Correct key

                // Map the activity type to the corresponding string
                String activityDetail = activityDetailsMap.containsKey(activityType) ? activityDetailsMap.get(activityType) : 'Unknown Activity';

                if (salesforce_user_account_id != null) {
                    Id recordId = salesforce_user_account_id;
                    String sourceObjectName = recordId.getSObjectType().getDescribe().getName();

                    if (sourceObjectName == 'Account') {
                        User_Activity__c activity = new User_Activity__c();
                        activity.Related_Account__c = salesforce_user_account_id;
                        activity.Activity_Detail_RICH__c = activityDetail + ' >>> ' + activitySummary;  // Combine the details
                        activity.Activity_Type__c =activityDetail;
                        activity.Time_Stamp__c =date.today();
                        accountActivities.add(activity);
                        System.debug('Creating User_Activity__c record for Account ID: ' + salesforce_user_account_id);
                    } else if (sourceObjectName == 'Lead__c') {
                        User_Activity__c activity = new User_Activity__c();
                        activity.Related_Lead__c = salesforce_user_account_id;
                        activity.Activity_Detail_RICH__c = activityDetail + ' >>> ' + activitySummary;  // Combine the details
                        activity.Activity_Type__c =activityDetail;
                        activity.Time_Stamp__c =date.today();
                        leadActivities.add(activity);
                        System.debug('Creating User_Activity__c record for Lead ID: ' + salesforce_user_account_id);
                    }
                } else {
                    System.debug('ID not found in the original recordIds: ' + salesforce_user_account_id);
                }
            }

            // Insert Account User_Activity__c records
            if (!accountActivities.isEmpty()) {
                insert accountActivities;
                System.debug('Inserted ' + accountActivities.size() + ' User_Activity__c records for Accounts.');
            } else {
                System.debug('No Account activities to insert.');
            }

            // Insert Lead User_Activity__c records
            if (!leadActivities.isEmpty()) {
                insert leadActivities;
                System.debug('Inserted ' + leadActivities.size() + ' User_Activity__c records for Leads.');
            } else {
                System.debug('No Lead activities to insert.');
            }

        } catch (Exception e) {
            System.debug('Exception occurred while processing mock response: ' + e.getMessage());
        }
    } */
}