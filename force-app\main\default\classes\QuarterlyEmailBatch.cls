global class QuarterlyEmailBatch implements Database.Batchable<SObject> {
    
    public Id accountRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
    public Id leadRecordTypeId = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
    
    global Database.QueryLocator start(Database.BatchableContext bc) {
        return Database.getQueryLocator([
            SELECT Id,
                   Name,
                   Actual_Email__c,
                   Relationship_Manager__r.Name,
                   Relationship_Manager__r.Contact_No__c,
                   Preferred_Email__c,
                   Quaterly_Mail_Catagory__c
            FROM Account
            WHERE Has_Been_Emailed__c = FALSE
              AND Actual_Email__c != NULL
            AND  RecordTypeId =: accountRecordTypeId
              AND Preferred_Email__c != NULL
              AND Membership_Status__c IN (
                  'Paid IPV Fee', 'Paid Community', 'Paid by CXO Points', 
                  'Paid by IPV Points', 'AIM+ Sales'
              )
            LIMIT 1000
        ]);
    }

    global void execute(Database.BatchableContext bc, List<Account> scope) {
       
        String quarter = getPreviousQuarter();
        List<String> parts = quarter.split('_');
        Integer qNum = Integer.valueOf(parts[0].replace('Q', ''));
        Integer qYear = Integer.valueOf(parts[1]);

        Date quarterStart = getQuarterStart(qNum, qYear);
        Date quarterEnd = getQuarterEnd(qNum, qYear);

        Set<Id> parentIds = new Set<Id>();
        for (Account a : scope) parentIds.add(a.Id);

     
        Map<Id, List<Account>> referredAccByParent = new Map<Id, List<Account>>();
        for (Account childAcc : [
            SELECT Name, Membership_Status__c, Date_of_receiving_lead__c, ParentId
            FROM Account
            WHERE ParentId IN :parentIds
            AND  RecordTypeId =: accountRecordTypeId
              AND Date_of_receiving_lead__c >= :quarterStart
              AND Date_of_receiving_lead__c <= :quarterEnd
              AND Membership_Status__c != null
        ]) {
            List<Account> listForParent = referredAccByParent.get(childAcc.ParentId);
            if (listForParent == null) listForParent = new List<Account>();
            listForParent.add(childAcc);
            referredAccByParent.put(childAcc.ParentId, listForParent);
        }

        
        Map<Id, List<Lead__c>> referredLeadByParent = new Map<Id, List<Lead__c>>();
        for (Lead__c ld : [
            SELECT Name, Membership_Status__c, Date_of_receiving_lead__c, Referred_By__c
            FROM Lead__c
            WHERE Referred_By__c IN :parentIds
              AND RecordTypeId =: leadRecordTypeId 
              AND Date_of_receiving_lead__c >= :quarterStart
              AND Date_of_receiving_lead__c <= :quarterEnd
              AND Membership_Status__c != 'AIM+ Sales'
              
        ]) {
            List<Lead__c> listForParent = referredLeadByParent.get(ld.Referred_By__c);
            if (listForParent == null) listForParent = new List<Lead__c>();
            listForParent.add(ld);
            referredLeadByParent.put(ld.Referred_By__c, listForParent);
        }

        List<Messaging.SingleEmailMessage> mails = new List<Messaging.SingleEmailMessage>();
        List<Account> updates = new List<Account>();
        Map<Id , String> emailMap = new Map<Id , String>();
        List<String> emailList = new List<String>();

        for (Account acc : scope) {
            
            String selectedEmail = null;

            if (acc.Actual_Email__c != null && acc.Actual_Email__c != '' && acc.Actual_Email__c != ' ' ) {
                List<String> pieces = acc.Actual_Email__c.split(' ');
                if (!pieces.isEmpty()) {
                    selectedEmail = pieces[0].trim();
                    emailMap.put(acc.Id, pieces[0].trim());
                    emailList.add(pieces[0].trim());
                }
            }

            List<Account> qAccs = referredAccByParent.get(acc.Id);
            List<Lead__c> qLeads = referredLeadByParent.get(acc.Id);
            Boolean hasQuarterReferral = 
                (qAccs != null && !qAccs.isEmpty()) ||
                (qLeads != null && !qLeads.isEmpty());

            IPVQuaterlyEmailContent.EmailContent emailContent;

            if (hasQuarterReferral) {
                emailContent = IPVQuaterlyEmailContent.generateReferralEmail(
                    acc.Name,
                    acc.Relationship_Manager__r != null ? acc.Relationship_Manager__r.Name : '',
                    quarter,
                    acc.Id,
                    acc.Relationship_Manager__r != null ? acc.Relationship_Manager__r.Contact_No__c : '',
                    qAccs,
                    qLeads
                );
            } else {
                emailContent = IPVQuaterlyEmailContent.generateMotivationalEmail(
                    acc.Name,
                    acc.Relationship_Manager__r != null ? acc.Relationship_Manager__r.Name : '',
                    acc.Relationship_Manager__r != null ? acc.Relationship_Manager__r.Contact_No__c : '',
                    quarter
                );
            }

            if(selectedEmail != null) {

                Messaging.SingleEmailMessage msg = new Messaging.SingleEmailMessage();
                msg.setToAddresses(new String[] { selectedEmail });
                msg.setSubject(emailContent.subjectLine);
                msg.setHtmlBody(emailContent.body);
                
                Messaging.EmailFileAttachment att = new Messaging.EmailFileAttachment();
                att.setFileName(emailContent.attachmentName);
                att.setBody(emailContent.attachmentBody);
                att.setContentType('application/pdf');
                msg.setFileAttachments(new Messaging.EmailFileAttachment[] { att });
                
                mails.add(msg);
                
                acc.Has_Been_Emailed__c = true;
                acc.Last_Quarter_Email_Sent_Date__c = System.today();
                acc.Email_Quater__c = quarter;
                updates.add(acc);
            }
        }

        try {
            if (!mails.isEmpty()){
                system.debug('Mail sent to email>>>>'+mails);
                System.debug('Before Sending E-Mail List ::: ' + JSON.serialize(emailList));
                System.debug('Before Sending E-Mail Map ::: ' + JSON.serialize(emailMap));
                Messaging.sendEmail(mails);
            }   
        } catch (Exception e) {
          System.debug('No valid emails found — skipping send.'+ mails);      
          System.debug('Error sending email: ' + e.getMessage());
          System.debug('Failed becuase of E-Mail List ::: ' + JSON.serialize(emailList));
          System.debug('Failed becuase of E-Mail Map ::: ' + JSON.serialize(emailMap));
        }
 
        if (!updates.isEmpty()){
            update updates;
        }
    }

    global void finish(Database.BatchableContext bc) {}

   
    private String getPreviousQuarter() {
        Date today = Date.today();
        Integer currentMonth = today.month();
        Integer currentYear = today.year();

        Integer previousQuarter;
        Integer yearOfQuarter = currentYear;

        if (currentMonth >= 1 && currentMonth <= 3) {
            previousQuarter = 4;
            yearOfQuarter = currentYear - 1;
        } else if (currentMonth >= 4 && currentMonth <= 6) {
            previousQuarter = 1;
        } else if (currentMonth >= 7 && currentMonth <= 9) {
            previousQuarter = 2;
        } else {
            previousQuarter = 3;
        }

        return 'Q' + previousQuarter + '_' + yearOfQuarter;
    }

    private Date getQuarterStart(Integer quarter, Integer year) {
        Integer startMonth = (quarter - 1) * 3 + 1;
        return Date.newInstance(year, startMonth, 1);
    }

    private Date getQuarterEnd(Integer quarter, Integer year) {
        Integer endMonth = quarter * 3;
        Integer lastDay = Date.daysInMonth(year, endMonth);
        return Date.newInstance(year, endMonth, lastDay);
    }
}