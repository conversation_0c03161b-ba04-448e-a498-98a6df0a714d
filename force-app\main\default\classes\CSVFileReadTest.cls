@isTest
public class CSVFileReadTest {
    public static testmethod void testCase1(){
        Data_Entry__c entry =  new Data_Entry__c();
        insert entry;
        Account testAccount = TestFactory.createAccount();
        insert testAccount;
        Contact con = new Contact();
        con.FirstName='Test';
        con.LastName='Test';
        con.Accountid= testAccount.id;
        con.Investor_s_PAN__c = '**********';
        con.AIF_Contributor__c = true;
        //contList.add(Cont);
        insert con; 
        Contribution_Agreement__c ca = new Contribution_Agreement__c();
        CA.investor1__c =  con.id;
        ca.Virtual_Account_Number__c = '********';
        insert ca;
        Contribution_Agreement__c cac = [select id,name from Contribution_Agreement__c where id = : ca.id];
        ApexPages.StandardController sc = new ApexPages.StandardController(entry);
        CSVFileRead csvCon = new CSVFileRead(sc);
        csvCon.acNumber = '**********';
        csvCon.IFSCNo = '**********';
        csvCon.contriNo = cac.name;
        csvCon.csvFileBody = Blob.valueOf('test,<EMAIL>,test11,test111,<EMAIL>,Individual\rtest2,<EMAIL>,test3,test3,<EMAIL>,Individual\rtest2,<EMAIL>,test4,test4,<EMAIL>,Individual\r');
        csvCon.importCSVFile();
        csvCon.csvFileBody = Blob.valueOf('test,<EMAIL>,test11,test111,<EMAIL>,Non-Individual\rtest2,<EMAIL>,test3,test3,<EMAIL>,Non-Individual\rtest2,<EMAIL>,test4,test4,<EMAIL>,Non-Individual\r');
        csvCon.importCSVFile();
        Test.setMock(HttpCalloutMock.class, new CSVFileReadMock());
        test.startTest();
        csvCon.importSingleData();
        Test.stopTest();
        
        
    }
    
    public static testmethod void testCase2(){
        Data_Entry__c entry =  new Data_Entry__c();
        insert entry;
        Account testAccount = TestFactory.createAccount();
        insert testAccount;
        Contact con = new Contact();
        con.FirstName='Test';
        con.LastName='Test';
        con.Accountid= testAccount.id;
        con.Investor_s_PAN__c = '**********';
        con.AIF_Contributor__c = true;
        //contList.add(Cont);
        insert con; 
        Contribution_Agreement__c ca = new Contribution_Agreement__c();
        CA.investor1__c =  con.id;
        ca.Virtual_Account_Number__c = '********';
        insert ca;
        Contribution_Agreement__c cac = [select id,name from Contribution_Agreement__c where id = : ca.id];
        ApexPages.StandardController sc = new ApexPages.StandardController(entry);
        CSVFileRead csvCon = new CSVFileRead(sc);
        csvCon.acNumber = '**********';
        csvCon.IFSCNo = '**********';
        csvCon.contriNo = cac.name;        
        csvCon.csvFileBody = Blob.valueOf('CA,0********900000,IFSC000000,Test Bank Name,Name\rCA0011,0********900000,IFSC000000,test3,ABC\rCA0011,0********900000,IFSC000000,Test Bank Name,ASD\r');
        
        Test.setMock(HttpCalloutMock.class, new CSVFileReadMock());
        test.startTest();
        csvCon.importBankData();
        Test.stopTest();       
    }
    
    
}