public class AccountRestAPIController {

    Private static String Class_Name ='AccountRestAPIController';
    
    @future (Callout=True)
    Public static void UpdateAccountDetails (Set<id> AccId, boolean isInsert){
        
        try
        {
            String JsonData;
            Final String endURL ='https://orailap.azurewebsites.net/api/IPV/UpdateLead';
            HttpRequest request = new HttpRequest();
        	request.setEndpoint(endURL);
       	 	request.setHeader('Content-Type','application/json');
        	request.setMethod('PUT');
        	request.setTimeout(120000);
            List<Account> AccountInfo = [SELECT Id, Name,Company__c,Personal_Email__c,Full_Primary_Contact__c,Are_you_a_first_time_investor_c__c,Designation__c,Is_Converted_From_Lead__c,Lead_Source__c, Record_Type_Primary_Contact_Unique_Key__c FROM Account WHERE Id IN:AccId];
            System.debug('AccRecords>>' +AccountInfo);
            Map<String, Object> jsonMap = new Map<String, Object>();
            
            if(AccountInfo.Size() > 0)
            {
                JSONGenerator jsonGen = JSON.createGenerator(true);
                for(Account Acc : AccountInfo)
                {
                    if(Acc.Is_Converted_From_Lead__c == true && Acc.Lead_Source__c =='ORAI')
                    {
                   		if(Acc.Name!=null && Acc.name != '')
                        {
                        	jsonMap.put('MemberName',Acc.Name);
                    	}
                    	If(acc.Designation__c != null)
                      	 	jsonMap.put('Designation', Acc.Designation__c);
                        else
							jsonMap.put('Designation', '');                            
                    		
                        jsonMap.put('PhoneNumber', Acc.Full_Primary_Contact__c);
                      	 	
                        jsonMap.put('PersonalEmail', Acc.Personal_Email__c);
                        
                        if (Acc.Are_you_a_first_time_investor_c__c != null)
                       	 	jsonMap.put('Are_you_a_first_time_investor', Acc.Are_you_a_first_time_investor_c__c);
                        else
                            jsonMap.put('Are_you_a_first_time_investor','');
                        if (Acc.Company__c != null)
                         	jsonMap.put('Company', Acc.Company__c);
                        else
                            jsonMap.put('Company','');
                   }
                	
                    JsonData = JSON.serialize(jsonMap);
    				System.debug('JSON Data: ' + jsonData);
                    request.setBody(jsonData);
                	Http http1 = new Http();
                	HTTPResponse res1 = http1.send(request);
                	system.debug('res1 >>>'+res1 );
                }
              }
        }
        Catch(Exception ex)
        {
            system.debug('Error '+ex);
            String errorMessage = 'Error updating account details: ' + ex.getMessage();
            Error_Log__c log =  new Error_Log__c();
            log.Class_Name__c = Class_Name;
            log.method__c = 'UpdateAccountDetails' ;
            log.Error_Message__c = errorMessage;
            insert log;
        }
    }
}