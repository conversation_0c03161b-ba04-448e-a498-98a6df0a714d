<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Startup_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Startup__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Startup_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Startup__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Lead_Record_Page2</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Lead__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Lead_Record_Page2</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Lead__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Venture_Connect_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Venture_Connect__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Venture_Connect_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Venture_Connect__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Capital_Development_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Capital_Development__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Capital_Development_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Capital_Development__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Contact_Detail_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Contact_Detail__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Contact_Detail_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Contact_Detail__c</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#0070D2</headerColor>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>IPV Application</label>
    <navType>Standard</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Legal_Profile_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.IPV</recordType>
        <type>Flexipage</type>
        <profile>Audit_N</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Legal_Profile_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.IPV</recordType>
        <type>Flexipage</type>
        <profile>Audit_N</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Capital_Development_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Capital_Development__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Capital_Development_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Capital_Development__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Capital_Development_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Capital_Development__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>SystemAdminCustom</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Capital_Development_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Capital_Development__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>SystemAdminCustom</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Contact_Detail_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Contact_Detail__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Contact_Detail_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact_Detail__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Contact_Detail_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Contact_Detail__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>SystemAdminCustom</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Contact_Detail_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact_Detail__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>SystemAdminCustom</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Source_of_truth_layout</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Startup_Round__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>_Exitt</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Source_of_truth_layout</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Startup_Round__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>_Exitt</profile>
    </profileActionOverrides>
    <tabs>standard-Account</tabs>
    <tabs>standard-Contact</tabs>
    <tabs>standard-Case</tabs>
    <tabs>Lead__c</tabs>
    <tabs>standard-Feed</tabs>
    <tabs>Exit_Investment</tabs>
    <tabs>Data_Import_Wizard</tabs>
    <tabs>Data_Import_Mapping__c</tabs>
    <tabs>standard-report</tabs>
    <tabs>standard-Dashboard</tabs>
    <tabs>Startup__c</tabs>
    <tabs>Events__c</tabs>
    <tabs>Import_Wizard_History__c</tabs>
    <tabs>Investment__c</tabs>
    <tabs>Venture_Connect__c</tabs>
    <tabs>Error_Log__c</tabs>
    <tabs>Contribution_Agreement__c</tabs>
    <tabs>Data_Entry__c</tabs>
    <tabs>Account_With_Lead__c</tabs>
    <tabs>LetsGrow__c</tabs>
    <tabs>Capital_Development__c</tabs>
    <tabs>Points_Transaction__c</tabs>
    <tabs>Call_Tracker__c</tabs>
    <tabs>Action_Item_Tracker__c</tabs>
    <tabs>MOU_Details__c</tabs>
    <tabs>Payout_Transaction__c</tabs>
    <tabs>User_Activity__c</tabs>
    <tabs>SQL_Details__c</tabs>
    <tabs>Transaction__c</tabs>
    <tabs>Startup_Pipeline__c</tabs>
    <tabs>Secondary_Module__c</tabs>
    <tabs>Fund_Module__c</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>ISV_Application_UtilityBar</utilityBar>
</CustomApplication>
