@isTest
public class ScheduledCaseWeeklyEmailNotificationTest {
    
    @isTest
    static void verifyWeeklyEmailNotificationJob() {
 Profile userProfile = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        
        User newUser = new User(
            FirstName = 'John',
            LastName = 'Doe',
            Username = 'johndoe' + DateTime.now().getTime() + '@example.com',
            Email = 'mailto:<EMAIL>',
            Alias = 'jdoe',
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            ProfileId = userProfile.Id,
            UserRoleId = null,
            CompanyName = 'Your Company',
            Division = 'Your Division',
            Department = 'Your Department',
            Title = 'Your Title',
            Street = '1234 Main St',
            City = 'San Francisco',
            State = 'CA',
            PostalCode = '94105',
            Country = 'USA'
        );
        Account acc = TestFactory.createAccount();
        insert acc;
         Contact cont = new Contact();
                cont.FirstName='Test';
                cont.LastName='Test';
                cont.Email = '<EMAIL>';
                cont.Accountid= acc.id;
                cont.Investor_s_PAN__c = '**********';
                cont.Investment_in_Own_Name_Family_Member__c = 'Own Name';
                cont.Send_Auto_Com_Email__c = true;
              insert cont;
        
       
        User systemAdminUser = [SELECT Id, ProfileId, Profile.Name , Name FROM User WHERE Profile.Name = 'System Administrator' LIMIT 1];
        
        Case cs1 = new Case();
        cs1.Issue_raised_By__c = acc.Id;
        cs1.Priority = 'High';
        cs1.Status ='WIP';
        cs1.Date_Issue_Raised__c = date.today();
        cs1.Description = 'test Case 1';
        cs1.Relevant_Team__c = 'Tech Related';
        cs1.Date_of_issue_assigned_Internal__c = date.today()-2 ;
        cs1.Responsibility_to_solve_Internal__c = systemAdminUser.Id; 
        insert cs1;
        
        Case cs2 = new Case();
        cs2.Issue_raised_By__c = acc.Id;
        cs2.Priority = 'High';
        cs2.Status ='WIP';
        cs2.Date_Issue_Raised__c = date.today();
        cs2.Description = 'test Case 2';
        cs2.Relevant_Team__c = 'Tech Related';
        cs2.Date_of_issue_assigned_Internal__c = date.today()-4 ;
        cs2.Responsibility_to_solve_Internal__c = systemAdminUser.Id; 
        insert cs2;
        
        Case cs3 = new Case();
        cs3.Issue_raised_By__c = acc.Id;
        cs3.Priority = 'High';
        cs3.Status ='WIP';
        cs3.Date_Issue_Raised__c = date.today();
        cs3.Description = 'test Case 3';
        cs3.Relevant_Team__c = 'Tech hello';
        cs3.Date_of_issue_assigned_Internal__c = date.today()-1 ;
        cs3.Responsibility_to_solve_Internal__c = systemAdminUser.Id; 
        cs3.Due_date_for_closure__c = date.today()+1;
        insert cs3;
        
        Case cs4 = new Case();
        cs4.Issue_raised_By__c = acc.Id;
        cs4.Priority = 'High';
        cs4.Status ='WIP';
        cs4.Date_Issue_Raised__c = date.today();
        cs4.Description = 'test hello';
        cs4.Date_of_issue_assigned_Internal__c = date.today()-2 ;
        cs4.Responsibility_to_solve_Internal__c = systemAdminUser.Id; 
        insert cs4;
        
       
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseController());
        Test.startTest();
        String jobName = 'ScheduledCaseWeeklyEmailNotification Scheduler Test';
        ScheduledCaseWeeklyEmailNotification scheduler = new ScheduledCaseWeeklyEmailNotification();
         scheduler.execute(null);
        String cronExpression = '0 0 * * * ?'; 
        System.schedule(jobName, cronExpression, scheduler);
        
        Test.stopTest();
        

      
        
    }
}