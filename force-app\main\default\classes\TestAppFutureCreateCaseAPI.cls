@IsTest
public class TestAppFutureCreateCaseAPI {
	@TestSetup
    public static void setupData(){
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseController());
        test.startTest();
        Account acc = TestFactory.createAccount();
        insert acc;
        
        Case cs = new Case();
   		cs.Issue_raised_By__c = acc.Id;
        cs.Description = 'This is Test';
        cs.Origin = 'Whatsapp';
    	cs.Priority = 'High';
    	cs.Status = 'WIP';
    	insert cs;
        if(cs.Status == 'WIP'){
            update cs;
        }
        Test.stopTest();
    }
    
    public static testMethod void doFutureTest(){
        Set<Id> caseIdSet = new Set<Id>();
        List<Case> c = [Select Id,CaseNumber, Description, Origin, Issue_raised_By__c , Priority, Status from Case];
        for(Case cs: c) {
            caseIdSet.add(cs.Id);
            System.debug('CaseId Here'+caseIdSet );
        }
        
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new AppFutureCreateCaseAPIMock());
        AppFutureCreateCaseAPI.sendCaseInfo(caseIdSet);
        Test.stopTest();
    }
}