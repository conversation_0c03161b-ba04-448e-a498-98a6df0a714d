<template>
    
    <lightning-card title="Investors">
        <lightning-datatable
            data={contactData}
            columns={contactColumns}
            key-field="Name"
            hide-checkbox-column></lightning-datatable>
    </lightning-card>  
    <br/>
    <lightning-card title="Documents">
        <lightning-datatable
            data={data}
            columns={columns}
            key-field="Name"
            hide-checkbox-column></lightning-datatable>
            <div style="float:right;padding-top: 5px;padding-right: 1%;"><b>Total : </b> <span style="padding-left: 5px;">{docTotal}</span></div>
    </lightning-card>  
    <br/>
    <lightning-card title="All Investments including Off - Market Purchase">
        <lightning-datatable
            data={invData}
            columns={investmentColumns}
            key-field="id"
            hide-checkbox-column></lightning-datatable>
            <div style="float:right;padding-top: 5px;padding-right: 1%;"><b>Total : </b><span style="padding-left: 5px;">{invTotal}</span></div>
    </lightning-card>  
    <br/>
    <lightning-card title="Committed Investments">
        <lightning-datatable
            data={committedData}
            columns={committedColumns}
            key-field="id"
            hide-checkbox-column></lightning-datatable>
            <div style="float:right;padding-top: 5px;padding-right: 1%;"><b>Total : </b> <span style="padding-left: 5px;"> {commitTotal}</span></div>
    </lightning-card>  
    <br/>
    <lightning-card title="Redemptions">
        <lightning-datatable
            data={redData}
            columns={redemptionColumns}
            key-field="id"
            hide-checkbox-column></lightning-datatable>
            <div style="float:right;padding-top: 5px;padding-right: 1%;"><b>Total : </b> <span style="padding-left: 5px;"> {redTotal}</span></div>
    </lightning-card>  
    <br/>
    <lightning-card title="Transactions">
        <lightning-datatable
            data={transactionData}
            columns={transactionColumns}
            key-field="id"
            hide-checkbox-column></lightning-datatable>
            <div style="float:right;padding-top: 5px;padding-right: 1%;"><b>Total : </b> <span style="padding-left: 5px;">{transTotal}</span> </div>
    </lightning-card> 
    <br/>
        <lightning-card title="Fee">
            <lightning-datatable
                data={feeData}
                columns={feeColumns}
                key-field="id"
                hide-checkbox-column>
            </lightning-datatable>        
        <div style="float:right;padding-top: 5px;padding-right: 1%;"><b>Total : </b> <span style="padding-left: 5px;">{feeTotal}</span></div>
    </lightning-card> 
        <br/>
    <lightning-card title="Bank">
        <lightning-datatable
            data={bankData}
            columns={bankColumns}
            key-field="id"
            hide-checkbox-column>
        </lightning-datatable>
    </lightning-card> 
    <br/>
    <lightning-card title="Off Market Purchase">
        <lightning-datatable
            data={offMarketData}
            columns={offMarketColumns}
            key-field="id"
            hide-checkbox-column>
        </lightning-datatable>
        <div style="float:right;padding-top: 5px;padding-right: 1%;"><b>Total : </b><span style="padding-left: 5px;">{offmTotal}</span>  </div>
    </lightning-card>   
    <br/>
    <lightning-card title="Off Market Sales">
        <lightning-datatable
            data={offMarketSales}
            columns={offMarketColumns}
            key-field="id"
            hide-checkbox-column>
        </lightning-datatable>
        <div style="float:right;padding-top: 5px;padding-right: 1%;"><b>Total : </b><span style="padding-left: 5px;">{offmsTotal}</span>  </div>
    </lightning-card> 
    <br/>
    <lightning-card title="RePayment Data">
        <lightning-datatable
            data={rePaymentData}
            columns={rePaymentColumns}
            key-field="id"
            hide-checkbox-column>
        </lightning-datatable>
    </lightning-card>      
</template>