@isTest(SeeAllData=false)
public class B2BcustomerAPITest {
    
    @testSetup
    public static void setup() {
        Id ipvRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
       
        //This section add for skip the Account Trigger 
        System_Setting__c settingRecord = new System_Setting__c();
        settingRecord.Name = 'Test';
        settingRecord.AccountTriggerActivated__c = true;
        insert settingRecord;

        Account spocAcc = new Account(
            RecordTypeId = ipvRecordTypeId,
            Name = 'spoc Account',
            Personal_Email__c = '<EMAIL>',
            Primary_Country_Code__c = 91,
            Primary_Contact__c = '**********',
            Preferred_Email__c = 'Personal',
            Lead_Source__c = 'ORAI',
            Relationship_Manager__c = UserInfo.getUserId(),
            Title__c = 'Mr',
            Date_of_Addition__c = Date.Today(),
            Primary_Group_Name__c = 'IPV 1'
        );
        insert spocAcc;
        
        // Creating a tenant account (parent account)
        Account parentAccount = new Account(
            RecordTypeId = ipvRecordTypeId,
            Name = 'Parent Account',
            Is_Tenant__c = true,
            Partner_Type__c = 'Individual',
            Tenant_SPOC_Account__c = spocAcc.Id,
            Personal_Email__c = '<EMAIL>',
            Primary_Country_Code__c = 91,
            Primary_Contact__c = '**********',
            Preferred_Email__c = 'Personal',
            Lead_Source__c = 'ORAI',
            Relationship_Manager__c = UserInfo.getUserId(),
            Title__c = 'Mr',
            Date_of_Addition__c = Date.Today(),
            Primary_Group_Name__c = 'IPV 1'
        );
        insert parentAccount;
        
         String uniqueRmCode = generateUniqueRmCode();
        
        // Creating an Org RM account
        Account orgRMAccount = new Account(
            RecordTypeId = ipvRecordTypeId,
            Name = 'Org RM Account',
            Personal_Email__c = '<EMAIL>',
            Partner_parent_account__c = parentAccount.Id,
            Primary_Contact__c = '**********',
            Primary_Country_Code__c = 91,
            Preferred_Email__c = 'Personal',
            B2B_User_Type__c = 'L1',
            Lead_Source__c = 'ORAI',
            Relationship_Manager__c = UserInfo.getUserId(),
            Title__c = 'Mr',
            Date_of_Addition__c = Date.Today(),
            Primary_Group_Name__c = 'IPV 1'
        );
        
        
        insert orgRMAccount;
        
        // Creating customer Account
        Account customerAcc = new Account(
            RecordTypeId = ipvRecordTypeId,
            Name = 'B2B Customer',
            Partner_parent_account__c = parentAccount.Id,
            Org_RM__c = orgRMAccount.Id,
            Personal_Email__c = '<EMAIL>',
            Primary_Contact__c = '**********',
            Primary_Country_Code__c = 91,
            B2B_User_Type__c = 'Customer',
            Onboarding_Status__c = 'KYC In Process',
            Is_Tenant__c = false,
            Preferred_Email__c = 'Personal',
            Lead_Source__c = 'ORAI',
            Relationship_Manager__c = UserInfo.getUserId(),
            Title__c = 'Mr',
            Partner_Type__c = 'Individual',
            Membership_Validity__c = Date.Today(),
            Membership_Status__c = 'On Trial',
            PAN__c = '**********',
            Date_of_Addition__c = Date.Today(),
            Primary_Group_Name__c = 'IPV 1'
        );
        insert customerAcc;
        
        Startup__c str = new Startup__c(
            Public_Name__c = 'testStartup',
            Legal_Name__c = 'testStartup'
        );
        insert str;
        
        Startup_Round__c round = new Startup_Round__c(
            Name = 'test startup round',
            Round_Type__c = 'Internal Transfers',
            Doi_Percent_Fee__c = 20,
            Doi_Percent_Equity__c = 30,
            Startup__c = str.Id,
            Co_Lead_Member__c = customerAcc.Id,
            Lead_Member__c = customerAcc.Id,
            Lead_Analyst__c = customerAcc.Id,
            Date_Of_Founders_Call__c = date.newInstance(2024, 9, 15),
            Pre_Money_Valuation__c = 10,
            Date_of_Investor_Call__c = Date.Today(),
            External_Deal__c = 'Yes'
        );
        insert round;
        
        //investor data
        List<Contact> contacts = new List<Contact>();
        contacts.add(new Contact(
            FirstName = 'Test',
            LastName = 'Test',
            Phone = '**********',
            Email = '<EMAIL>',
            AccountId = customerAcc.id,
            Investor_s_PAN__c = '**********',
            AIF_Contributor__c = true,
            KYC_Status__c = 'KYC - WIP'
        ));
        contacts.add(new Contact(
            FirstName = 'Test1',
            LastName = 'Test1',
            Phone = '**********',
            Email = '<EMAIL>',
            AccountId = customerAcc.id,
            Investor_s_PAN__c = '**********',
            AIF_Contributor__c = true,
            KYC_Status__c = 'KYC - WIP'
        ));
        insert contacts;
        
        //investment data
        Investment__c inv = new Investment__c(
          //  RecordTypeId = investentRecordTypeId,
            Account__c = customerAcc.Id,
            Type__c = 'Invested',
            Investor_Name__c = 'test inv name',
            Investor__c = contacts[0].Id,
            Startup_Round__c = round.Id,
            Investor_Type__c = 'Via AIF',
            Startup_Round_Name__c = round.Name,
            Investor_s_PAN__c = '**********',
            Investment_Amount__c = 743943,
            Investment_Fee_Received__c = 88595,
            Exit_Fee_received__c = 8585,
            IRR_Value__c = 8,
            Final_Commitment_Amount__c = 40000,
            Investment_Amount_Due__c = 60000,
            Investment_Fee_Due__c = 3000,
            Date_of_Drawdown_sent__c = Date.today(),
            Date_of_transaction__c = Date.today()
        );
        insert inv;
        
        Investment__c inv2 = new Investment__c(
           // RecordTypeId = invRecordTypeId,
            Account__c = customerAcc.Id,
            Type__c = 'Committed',
            Investor_Name__c = 'test inv name',
            Investor__c = contacts[0].Id,
            Startup_Round__c = round.Id,
            Investor_Type__c = 'Via Platform',
            Startup_Round_Name__c = round.Name,
            Investor_s_PAN__c = '**********',
            Final_Commitment_Amount__c = 743943,
            Investment_Fee_Received__c = 88595,
            Exit_Fee_received__c = 8585,
            IRR_Value__c = 8,
            Investment_Amount_Due__c = 60000,
            Investment_Fee_Due__c = 3000,
            Date_of_Drawdown_sent__c = Date.today(),
            Date_of_transaction__c = Date.today()
        );
        insert inv2;
        
        Investment__c inv3 = new Investment__c(
          //  RecordTypeId = invRecordTypeId,
            Account__c = customerAcc.Id,
            Type__c = 'Exit',
            Investor_Name__c = 'test inv name',
            Investor__c = contacts[0].Id,
            Startup_Round__c = round.Id,
            Investor_Type__c = 'Via Platform',
            Startup_Round_Name__c = round.Name,
            Investor_s_PAN__c = '**********',
            Final_Commitment_Amount__c = 743943,
            Investment_Fee_Received__c = 88595,
            Exit_Fee_received__c = 8585,
            IRR_Value__c = 8,
            Exit_Date__c = Date.today(),
            Exit_amount_to_be_transferred__c = 85950,
            Investment_Amount_Due__c = 60000,
            Investment_Fee_Due__c = 3000,
            Date_of_Drawdown_sent__c = Date.today(),
            Parent_Investment__c = inv2.Id
        );
        insert inv3;
        
        Contribution_Agreement__c ca = new Contribution_Agreement__c(
            Investor1__c = contacts[0].Id,
            Investor2__c = contacts[1].Id,
            Total_Contribution_Amount__c = 8484038
        );
        insert ca;
    }
    
    public static String generateUniqueRmCode() {
        String chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        String rmCode = '';
        Integer charLength = chars.length();
        for (Integer i = 0; i < 8; i++) {
            Integer randomIndex = Math.mod(Math.abs(Crypto.getRandomInteger()), charLength);
            rmCode += chars.substring(randomIndex, randomIndex + 1);
        }
        return rmCode;
    }
    
    // Test method to cover the scenario where pageSize, pageNo, and orglist are null
    @isTest
    static void testSendCustomerDetailsNullParameters() {
        List<Account> customerAccounts = [SELECT Id FROM Account WHERE Name = 'B2B Customer' limit 1];
        
        if (customerAccounts != null && !customerAccounts.isEmpty()) {
            List<String> orgIds = new List<String>();
            for(Account acc : customerAccounts) {
                orgIds.add(acc.Id);
            }
            
            Map<String, Object> requestBody = new Map<String, Object>{
                'pageSize' => null,
                    'pageNo' => null,
                    'orglist' => orgIds
                    };
                        
            RestRequest request = new RestRequest();
            request.requestUri = System.URL.getOrgDomainURL().toExternalForm() + '/services/apexrest/B2BcustomerAPI';
            request.httpMethod = 'POST';
            request.requestBody = Blob.valueOf(JSON.serialize(requestBody));
            
            RestContext.request = request;
            RestResponse res = new RestResponse();
            RestContext.response = res;
            Test.startTest();
            B2BcustomerAPI.sendCustomerDetails();
            Test.stopTest();
        } else {
            System.debug('No B2B Customer accounts found for testing.');
        }
    }
    
    // Test method to cover the scenario where the method executes successfully
    @isTest
    static void testSendCustomerDetailsSuccess() {
        List<Account> customerAccounts = [SELECT Id FROM Account WHERE Name = 'B2B Customer'limit 1];
        
        if (customerAccounts != null && !customerAccounts.isEmpty()) {
            List<String> orgIds = new List<String>();
            for(Account acc : customerAccounts) {
                orgIds.add(acc.Id);
            }
            
            Map<String, Object> requestBody = new Map<String, Object>{
                'pageSize' => 10,
                    'pageNo' => 1,
                    'orglist' => orgIds
                    };
                        
            RestRequest request = new RestRequest();
            request.requestUri = System.URL.getOrgDomainURL().toExternalForm() + '/services/apexrest/B2BcustomerAPI';
            request.httpMethod = 'POST';
            request.requestBody = Blob.valueOf(JSON.serialize(requestBody));
            
            RestContext.request = request;
            RestResponse res = new RestResponse();
            RestContext.response = res;
            
            Test.startTest();
            B2BcustomerAPI.sendCustomerDetails();
            Test.stopTest();
        } else {
            System.debug('No B2B Customer accounts found for testing.');
        }
    }
    
   @isTest 
    static void queryCustomersTest() {
        
        Set<Id> acId = new Set<Id>();
        for(Account ac : [select Id,Name from Account]){
            acId.add(ac.Id);
        }
        
        // Query B2B Customer accounts
        List<Account> customerAccounts = [SELECT Id, Name,Primary_Country_Code__C,Primary_Contact__c, Personal_Email__c,Membership_Validity__c, Membership_Status__c, PAN__c  from Account where Name = 'B2B Customer'];
        Set<Id> accountIds = new Set<Id>();
        for(Account acc : customerAccounts){
            accountIds.add(acc.Id);
        }
        List<Contact> investorList = [SELECT Id, Name, Phone, Email from Contact where AccountId IN :accountIds];
        List<Investment__c> investments = [SELECT Id, Type__c, Investment_Amount__c, Investor_Type__c,Startup_Round__c,
                                       Investment_Fee_Received__c, Date_of_SH_4__c, Exit_Fee_received__c, IRR_Value__c,
                                       IRR_Percentage_Formula__c, Number_of_Units_Held__c, Exit_Date__c, Exit_amount_to_be_transferred__c from Investment__c where Account__c IN :accountIds];
        Set<Id> investorIds = new Set<Id>(); 
        for(Contact con : investorList){
            investorIds.add(con.Id);
        }
        
        for(Account customer : customerAccounts){
            B2BcustomerAPI.CustomerWrapper customerObj = new B2BcustomerAPI.CustomerWrapper(customer.Id, customer.Name, customer.Personal_Email__c, String.valueOf(customer.Primary_Country_Code__C), customer.Primary_Contact__c, customer.Membership_Status__c,'Active', String.valueOf(customer.Membership_Validity__c),500, customer.PAN__c, 'IPV');
        }
        for(Contact con : investorList){
            B2BcustomerAPI.InvestorWrapper investorObj = new B2BcustomerAPI.InvestorWrapper(con.Id, con.Name, con.Email, con.Phone, 'Completed');
        }
        
        for(Investment__c inv : investments){
            B2BcustomerAPI.InvestmentWrapper investmentObj = new B2BcustomerAPI.InvestmentWrapper('', Decimal.valueOf(7798070),'Invested', 'Via AIF',inv.Investment_Fee_Received__c,String.valueOf(Date.today()), inv.Exit_Fee_received__c, inv.IRR_Value__c, inv.IRR_Percentage_Formula__c,inv.Number_of_Units_Held__c, String.valueOf(inv.Exit_Date__c),inv.Exit_amount_to_be_transferred__c, 'IPV', 'WIP', 10000, 5000,String.valueOf(Date.today()),String.valueOf(Date.today()), String.valueOf(Date.today()),false );
        }
        
        Test.startTest();
        B2BcustomerAPI.queryCustomers(accountIds , 100 , 1, false, acId );
        B2BcustomerAPI.queryCustomers(null,0,0, false, null);
        B2BcustomerAPI.queryCustomers(acId,10,1, true ,accountIds);
        Test.stopTest();
    }     
    
    @isTest
    static void testGetNonB2bAccountIds() {
        Set<Id> nonB2bAccountIds = B2BcustomerAPI.getNonB2bAccountIds();
      
    }
    

}