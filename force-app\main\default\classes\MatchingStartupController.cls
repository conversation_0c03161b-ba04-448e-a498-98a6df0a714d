public with sharing class MatchingStartupController {
    @AuraEnabled(cacheable=true)
    public static List<Startup__c> getMatchingInvestmentBanking(Id capitalDevelopmentId) {
        
        // Getting The Details fo CD.
        Capital_Development__c capitalDevelopment = [SELECT Id, Minimum_Transaction_Size__c, Maximum_Transaction_Size__c, Industry_Sector__c FROM Capital_Development__c WHERE Id = :capitalDevelopmentId LIMIT 1];

        Decimal minTransactionSize;
        Decimal maxTransactionSize;
        List<String> capitalIndustries = new List<String>();
        
        if (!String.isBlank(capitalDevelopment.Industry_Sector__c)) {
        	capitalIndustries = capitalDevelopment.Industry_Sector__c.split(';');
          	System.debug('Startup Industries >>>>> '+ capitalIndustries);
        }
        else
        {
        	System.debug('The Size of List >>>> '+ capitalIndustries.size());
        }    
        
        minTransactionSize = capitalDevelopment.Minimum_Transaction_Size__c;
        maxTransactionSize = capitalDevelopment.Maximum_Transaction_Size__c;
            
        String dynamicQuery = null;

        // Minimum , Maximum & Industry Size Exist
        if (minTransactionSize != null && maxTransactionSize != null && capitalIndustries.size() > 0) {
            dynamicQuery = 'SELECT Id, Public_Name__c, Industry_Sector__c, Fundraise_Amount__c, Fundraise_Type__c FROM Startup__c WHERE ';
            dynamicQuery += ' Fundraise_Amount__c >= :minTransactionSize AND Fundraise_Amount__c <= :maxTransactionSize AND Industry_Sector__c INCLUDES (';
            
            for (String industry : capitalIndustries) {
                dynamicQuery += '\'' + String.escapeSingleQuotes(industry) + '\',';
            }
            dynamicQuery = dynamicQuery.removeEnd(',') + ')';
        }
        
        // Maximum & Industry Exist
        else if (minTransactionSize == null && maxTransactionSize != null && capitalIndustries.size() > 0) {
            dynamicQuery = 'SELECT Id, Public_Name__c, Industry_Sector__c, Fundraise_Amount__c, Fundraise_Type__c FROM Startup__c WHERE ';
            dynamicQuery += ' Fundraise_Amount__c <= :maxTransactionSize AND Industry_Sector__c INCLUDES (';
            for (String industry : capitalIndustries) {
                dynamicQuery += '\'' + String.escapeSingleQuotes(industry) + '\',';
            }
            dynamicQuery = dynamicQuery.removeEnd(',') + ')';
        }
        
        // Minimum & Industry Exist
        else if (minTransactionSize != null && maxTransactionSize == null && capitalIndustries.size() > 0) {
            dynamicQuery = 'SELECT Id, Public_Name__c, Industry_Sector__c, Fundraise_Amount__c, Fundraise_Type__c FROM Startup__c WHERE ';
            dynamicQuery += ' Fundraise_Amount__c >= :minTransactionSize AND Industry_Sector__c INCLUDES (';
            for (String industry : capitalIndustries) {
                dynamicQuery += '\'' + String.escapeSingleQuotes(industry) + '\',';
            }
            dynamicQuery = dynamicQuery.removeEnd(',') + ')';
        }
        
        // Minimum & Maximum Exist
        else if(minTransactionSize != null && maxTransactionSize != null && capitalIndustries.isEmpty())
        {
            dynamicQuery = 'SELECT Id, Public_Name__c, Industry_Sector__c, Fundraise_Amount__c, Fundraise_Type__c FROM Startup__c WHERE ';
            dynamicQuery += ' Fundraise_Amount__c >= :minTransactionSize AND Fundraise_Amount__c <= :maxTransactionSize';
		}
        
        // Only Industry Exist
        else if (minTransactionSize == null && maxTransactionSize == null && capitalIndustries.size() > 0) {
            dynamicQuery = 'SELECT Id, Public_Name__c, Industry_Sector__c, Fundraise_Amount__c, Fundraise_Type__c FROM Startup__c WHERE ';
            dynamicQuery += ' Industry_Sector__c INCLUDES (';
            for (String industry : capitalIndustries) {
                dynamicQuery += '\'' + String.escapeSingleQuotes(industry) + '\',';
            }
            dynamicQuery = dynamicQuery.removeEnd(',') + ')';
        }
        
        //  Only Minimum Exist
        else if (minTransactionSize != null && maxTransactionSize == null && capitalIndustries.isEmpty())
        {
            dynamicQuery = 'SELECT Id, Public_Name__c, Industry_Sector__c, Fundraise_Amount__c, Fundraise_Type__c FROM Startup__c WHERE ';
            dynamicQuery += ' Fundraise_Amount__c >= :minTransactionSize';
        }
        
        //  Only Maximum Exist        
        else if(maxTransactionSize != null && minTransactionSize == null && capitalIndustries.isEmpty())
        {
            dynamicQuery = 'SELECT Id, Public_Name__c, Industry_Sector__c, Fundraise_Amount__c, Fundraise_Type__c FROM Startup__c WHERE ';
            dynamicQuery += ' Fundraise_Amount__c <= :maxTransactionSize';
        }
        
        
        // Getting The Matching Startups.
        List<Startup__c> matchingStartups = new List<Startup__c>();
        if(dynamicQuery != null)
        {
		 	matchingStartups = Database.query(dynamicQuery);
        }

        return matchingStartups;
    }
}