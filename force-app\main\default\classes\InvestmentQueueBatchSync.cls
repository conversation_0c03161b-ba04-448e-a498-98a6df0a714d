public class InvestmentQueueBatchSync implements Queueable, Database.AllowsCallouts {
    
    private Set<Id> investmentIds = new Set<Id>();

    public InvestmentQueueBatchSync(Set<Id> investmentIds) {
        this.investmentIds = investmentIds;
    }

    public void execute(QueueableContext context) {
        
        if (investmentIds.size() > 0) {
            InvestmentRestAPIController.sendInvestmentDetails(investmentIds , false);
        }

        // Adding Delay of 10 Seconds
        delayExecution(10);
    }

    private void delayExecution(Integer delayInSeconds) {
        Long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() < startTime + (delayInSeconds * 1000)) {
            // Loop until the delay time is reached (10 Seconds)
        }
    }
}