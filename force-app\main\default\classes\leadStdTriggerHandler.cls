public class leadStdTriggerHandler {
    
    Public void beforeInsert(list<lead> ldlist)
    {
       for(Lead Lds : ldlist)
        {
            
            if(Lds.LastName == null || Lds.LastName == '' || Lds.LastName == '[not provided]'){
                Lds.LastName = '.';
            }
            
            if(lds.Status == null || lds.Status == '')
            {
                lds.Status ='New';	
            } 
         }
    }
	
    Static Set<String> expectedLeadSourceSet = new Set<String>{'GoogleSEM','ORAI','SonyLIVweb','MBALandingPage','Linkedin','Facebook','Youtube','Twitter','Ozonetel'};

   	public void afterInsert(List<Lead> ldList) {
            
        Set<String> primaryContacts = new Set<String>();
        Map<String, Lead__c> existingLeads = new Map<String, Lead__c>();
        Map<String, Account> existingAccounts = new Map<String, Account>();
        List<Lead__c> customLead = new List<Lead__c>();
        List<Account> accountToUpdate = new List<Account>();
        List<Lead__c> leadListToInsert = new List<Lead__c>();
        Set<Id> setToDeleteLeads = new Set<Id>();
        Id recordTypeId = Schema.SObjectType.Lead__c.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
		String linkedInCampaign , facebookCampaign;
        String couponCode;
        Map<String , String> couponCodeMap = new Map<String , String>();
        Map<String , String> linkedInMap = new Map<String , String>();
        Map<String , String> facebookMap = new Map<String , String>();

        for (Lead ld : ldList) {
            String phoneNumber;
            
            if (ld.Phone != null) {
                
                phoneNumber = ld.Phone.replaceAll('\\+91|\\s', '');
                 
                if(phoneNumber.startsWith('0'))
                {
                    phoneNumber = phoneNumber.substring(1);
                    primaryContacts.add(phoneNumber);
                }
                else if(phoneNumber.startsWith('91') && phoneNumber.length() >= 12)
                {
					phoneNumber = phoneNumber.substring(2);
                    primaryContacts.add(phoneNumber);                    
                }
                else
                {
                    phoneNumber = ld.Phone.replaceAll('\\+91|\\s', '');
                    phoneNumber = ld.Phone.replaceAll('[^0-9]', '');
                    
                    if(phoneNumber.startsWith('91') && phoneNumber.length() >= 12)
                    {
                        phoneNumber = phoneNumber.subString(2);
                    }
                    
                    primaryContacts.add(phoneNumber);
                }
                
                System.debug('Primary Contacts >>>>> ' + primaryContacts);
                
            }
			
            if(ld.Linkedin_campaign__c != null)
            {
                linkedInCampaign = ld.Linkedin_campaign__c;
            }
            
            if(ld.Facebook_Campaign_Name__c != null)
            {
                facebookCampaign = ld.Facebook_Campaign_Name__c;
            }
            
			if(ld.LeadSource == 'Website-ipvangelinvesting' && phoneNumber != null && ld.couponCode__c != null)
            {
                couponCodeMap.put(phoneNumber ,ld.couponCode__c);
            }            
            if(phoneNumber != null && linkedInCampaign != null)
            {
                linkedInMap.put(phoneNumber ,linkedInCampaign);
            }
            if(phoneNumber != null && facebookCampaign != null)
            {
                facebookMap.put(phoneNumber ,facebookCampaign);
            }
        }

        System.debug('Primary Contacts >>>> ' + primaryContacts);
        System.debug('Map of CODE >>>> ' + couponCodeMap);
        // Existing leads with the same Primary Contact
        for (Lead__c existingLead : [SELECT Id, Primary_Contact__c, Retargeting_Status__c , Membership_Status__c , Campaign__c ,  couponCode__c , Facebook_Campaign_Name__c , Lead_Source__c
                                     FROM Lead__c 
                                     WHERE Recordtype.name ='IPV' AND Primary_Contact__c IN :primaryContacts]) {
            
                existingLead.Membership_Status__c = 'Form Refilled';
                if(linkedInMap.containsKey(existingLead.Primary_Contact__c))
                {
					existingLead.Campaign__c = linkedInMap.get(existingLead.Primary_Contact__c);
                    existingLead.Retargeting_status__c = TRUE;    
                }
                if(facebookMap.containsKey(existingLead.Primary_Contact__c))
                {
	                existingLead.Facebook_Campaign_Name__c = facebookMap.get(existingLead.Primary_Contact__c);
                    existingLead.Retargeting_status__c = TRUE;    
                }
                
				if(couponCodeMap.containsKey(existingLead.Primary_Contact__c))
                {
					existingLead.couponCode__c = couponCodeMap.get(existingLead.Primary_Contact__c);
                }
                
                existingLeads.put(existingLead.Primary_Contact__c, existingLead);

       }

        for (Account existingAccount : [SELECT Id, Name, Primary_Contact__c, Retargeting_Status__c , Membership_Status__c , couponCode__c
                                        FROM Account 
                                        WHERE Recordtype.name ='IPV' AND Primary_Contact__c IN :primaryContacts
                                        /*AND (Membership_Status__c = 'Exited by own' OR Membership_Status__c = 'Rotated by IPV')*/]) {
            if((existingAccount.Membership_Status__c == 'Exited By Own' || existingAccount.Membership_Status__c == 'Rotated by IPV' 
                || existingAccount.Membership_Status__c == 'On Trial' || existingAccount.Membership_Status__c == 'On Trial Community' 
                || existingAccount.Membership_Status__c == 'Call Scheduled' || existingAccount.Membership_Status__c == 'No Response 1' 
                || existingAccount.Membership_Status__c == 'No Response 2' || existingAccount.Membership_Status__c == 'No Response 3') 
               	&& (linkedInCampaign != null || facebookCampaign != null))
            {
                if(linkedInMap.containsKey(existingAccount.Primary_Contact__c))
                {
					existingAccount.Campaign__c = linkedInMap.get(existingAccount.Primary_Contact__c);
                    existingAccount.Retargeting_status__c = TRUE;    
                }
                if(facebookMap.containsKey(existingAccount.Primary_Contact__c))
                {
	                existingAccount.Facebook_Campaign_Name__c = facebookMap.get(existingAccount.Primary_Contact__c);
                    existingAccount.Retargeting_status__c = TRUE;    
                }
                existingAccount.Membership_Status__c = 'Exited Member Reactivated';          
                existingAccounts.put(existingAccount.Primary_Contact__c, existingAccount);
            }
            else if(couponCodeMap.containsKey(existingAccount.Primary_Contact__c))
            {
              	existingAccount.Membership_Status__c = 'Form Refilled';
                existingAccount.couponCode__c = couponCodeMap.get(existingAccount.Primary_Contact__c);
                existingAccounts.put(existingAccount.Primary_Contact__c, existingAccount);
            }
       }
        

      
        for (Lead ld : ldList) {
            
            String phone;
            if (ld.Phone != null) {
                
                String phoneNumber = ld.Phone.replaceAll('\\+91|\\s', '');
                                
                if(phoneNumber.startsWith('91') && phoneNumber.length() >= 12)
                {
                    phone = phoneNumber.subString(2);
                }else if(phoneNumber.startsWith('0'))
                {
                    phone = phoneNumber.subString(1);                    
                }
                else
                {
                    phone = ld.Phone.replaceAll('\\+91|\\s', '');
                    phone = ld.Phone.replaceAll('[^0-9]', '');
                    
                    if(phone.startsWith('91') && phone.length() >= 12)
                    {
                        phone = phone.subString(2);
                    }
                 }
            }
            
            System.debug('Existing Accounts >>> ' + existingAccounts);
            System.debug('Existing Lead >>> '+ existingLeads);
            
            if (existingAccounts.containsKey(phone)) {
                accountToUpdate.add(existingAccounts.get(phone));
                System.debug('Existing Accounts >>>>> ' + accountToUpdate); 
                update accountToUpdate;
            }
            else if (existingLeads.containsKey(phone)) {
                customLead.add(existingLeads.get(phone));
                System.debug('Existing Leads >>>>> ' + customLead);
                update customLead;
            }
            else 
            {
                 Lead__c ldc = new Lead__c();
                 String fullName = (ld.salutation != NULL ? ld.salutation + ' ' : '') +
                 (ld.FirstName != NULL ? ld.FirstName + ' ': '') + (ld.lastName != NULL ? ld.lastName : '');
                 ldc.RecordTypeId = recordTypeId;
                 system.debug('fullName::' +fullName);
                 ldc.Name = fullName.trim(); // ld.FirstName != NULL ? ld.FirstName : '';
                 if (expectedLeadSourceSet.contains(ld.utm_source__c)){
                     ldc.Lead_Source__c = ld.utm_source__c;
                 }else{
                       ldc.Lead_Source__c = 'MBALandingPage';//Changing to Facebook
                 }
                 system.debug('ASTest: '+ ldc.Lead_Source__c);
                 ldc.Designation__c = ld.Designation__c;
                 ldc.Are_you_a_first_time_investor__c = ld.Are_you_a_first_time_investor__c;
                 ldc.Company__c = ld.Company;
                 //DateTime createdDate = ld.CreatedDate;
                 ldc.Date_of_receiving_lead__c = Date.today();//Date.newInstance(createdDate.year(), createdDate.month(), createdDate.day()); //date.today();
                 ldc.Personal_Email__c = ld.Email;
                 ldc.Preferred_Email__c ='Personal';
                 ldc.Membership_Status__c = 'New Lead';
                 if(ld.Phone != null){
                     string num = ld.Phone.replaceAll('[^0-9]', '');
                     if(num.startsWith('91'))
                     {
                         num = num.substring(2);
                     }
                     ldc.Primary_Contact__c = num;
                 }
                 ldc.Primary_Country_Code__c = 91;
                 ldc.utm_campaign__c = ld.utm_campaign__c;
                 ldc.utm_medium__c = ld.utm_medium__c;
                 ldc.Relationship_Owner__c = Label.Lead_Default_RM_Owner;

                // Commented By Sahilparvat on 11.03.2025 Beacause this field is not gonna use further. As Discussed with Mauli Ma'am.
                //  if(ld.I_would_like_to_join_ipv__c != null && ld.I_would_like_to_join_ipv__c =='a founder looking to raise funds' || ld.I_would_like_to_join_ipv__c =='a startup looking to raise funds'){
                //      ldc.Membership_Status__c ='Startup looking for fund';
                //  }
                 //ldc.I_would_like_to_join_ipv__c = ld.I_would_like_to_join_ipv__c;

                // Added By Sahilparvat On 11.03.2025
                // LinkedIn Question & Answer Mapping To Custom Lead Object.
                if (ld.LinkedIn_Question_1__c != null) {
                    ldc.LinkedIn_Question_1__c = ld.LinkedIn_Question_1__c;
                }
                if (ld.LinkedIn_Question_2__c != null) {
                    ldc.LinkedIn_Question_2__c = ld.LinkedIn_Question_2__c;
                }
                if (ld.LinkedIn_Question_3__c != null) {
                    ldc.LinkedIn_Question_3__c = ld.LinkedIn_Question_3__c;
                }

                if(ld.LinkedIn_Answer_1__c != null){
                    ldc.LinkedIn_Answer_1__c = ld.LinkedIn_Answer_1__c;
                }
                if(ld.LinkedIn_Answer_2__c != null){
                    ldc.LinkedIn_Answer_2__c = ld.LinkedIn_Answer_2__c;
                }
                if(ld.LinkedIn_Answer_3__c != null){
                    ldc.LinkedIn_Answer_3__c = ld.LinkedIn_Answer_3__c;
                }

                if(( ld.LinkedIn_Answer_1__c != null || ld.LinkedIn_Answer_2__c != null || ld.LinkedIn_Answer_3__c != null ) && ( ld.LinkedIn_Answer_1__c == 'a founder looking to raise funds' || ld.LinkedIn_Answer_2__c == 'a founder looking to raise funds' || ld.LinkedIn_Answer_3__c == 'a founder looking to raise funds' || ld.LinkedIn_Answer_1__c == 'a startup looking to raise funds' || ld.LinkedIn_Answer_2__c == 'a startup looking to raise funds' || ld.LinkedIn_Answer_3__c == 'a startup looking to raise funds' ))
                {
                    ldc.Membership_Status__c ='Startup looking for fund';
                }

                // Added by Sahilparvat on 12.03.2025 As Discussed with Mauli Ma'am. To Indentify that The LEAD is from external source.
                ldc.Lead_From_External_Platform__c = true;
                
                ldc.Campaign__c = ld.Linkedin_campaign__c;
                 
                /* if (ld.utm_medium__c == null)
                    {
                        ldc.utm_campaign__c = 'SharkTank';
                        ldc.Lead_Source__c = 'MBALandingPage';
                        ldc.Campaign__c = 'SharkTank';
                    }
					else
					{
                       ldc.utm_source__c = ld.utm_source__c;
                    }*/
                 
                if(ld.Facebook_Campaign_Name__c != null || ld.Linkedin_campaign_Id__c !=null)
                {        
                     if(ld.I_would_like_IPV_to_block_my_calendar_on__c == 'Yes')
                     {
                        ldc.I_would_like_IPV_to_block_my_calendar_on__c = true;
                     }
                     else
                     {
                        ldc.I_would_like_IPV_to_block_my_calendar_on__c = false;
                     }
                                
                     ldc.Facebook_Campaign_Name__c = ld.Facebook_Campaign_Name__c;
                     ldc.Preferred_Email__c = 'Personal';
                     ldc.Personal_Email__c = ld.Email;
                     ldc.Linkedin__c = ld.Linkedin__c;
                     ldc.Relationship_Owner__c =Label.Lead_Rm_for_FB_And_LKDN;
                     ldc.Sales_Manager__c = Label.Lead_Rm_for_FB_And_LKDN;
                                
                     if(ld.LeadSource == 'LinkedIn'){
                         ldc.Lead_Source__c = ld.LeadSource;
                     }
                     else
                     {
					     ldc.Lead_Source__c = 'Facebook';
                     }
                                                
                     if(ld.Company == null || ld.Company =='[not provided]' || ld.Company ==''){
                         ldc.Company__c = 'Dummy Company';
                     }
                     else
                     {
						ldc.Company__c = ld.Company;
                     }
                     
                     if(ld.City ==null || ld.City =='')
                     {
                        ldc.City__c = 'Dummy City';
                     }
                     else
                     {
                         ldc.City__c = ld.City;
                     }
                                                   
                     if(ld.Designation__c == null)
                     {
                         ldc.Designation__c = 'Dummy Designation';
                     }
                     else
                     {
                          ldc.Designation__c = ld.Designation__c;
                     }
                 }
                
                 if(ld.LeadSource == 'Website-ipvangelinvesting' && ld.couponCode__c != null)
                 {
                     ldc.Lead_Source__c = 'SBI Landing Page';
                     ldc.couponCode__c = ld.couponCode__c;
                     ldc.Campaign__c = 'SBI Partnership';
                     ldc.Relationship_Manager__c = Label.Lead_Rm_for_FB_And_LKDN;
                 }

                // Added By Sahilparvat on 14.04.2025
                // Country Code Bifurcation Based on The Country Name. 
                if(ld.LeadSource == 'LinkedIn' && ld.Country_Name__c != null && ld.Phone != null)
                {
                    if(countryCodeMap.containsKey(ld.Country_Name__c))
                    {
                        ldc.Primary_Country_Code__c = countryCodeMap.get(ld.Country_Name__c);
                        ldc.Primary_Contact__c = ld.Phone.replaceAll('[^0-9]', ''); 
                    }
                    else
                    {
                        ldc.Primary_Country_Code__c = 91;
                    }
                }

                if(ld.Country_Name__c != null)
                {
                    ldc.Country_Name__c = ld.Country_Name__c;
                }
                
                leadListToInsert.add(ldc);
                setToDeleteLeads.add(ld.Id);
            }
        }

        // Perform DML operations
        if (!leadListToInsert.isEmpty()) {
            insert leadListToInsert;
        }
    
        if (!customLead.isEmpty()) {
            update customLead;
        } else if (!accountToUpdate.isEmpty()) {
            update accountToUpdate;
        }

        //commenting delete record as of now for better debugging of UTM parameter issue.
        //deleteLeadRecords(setToDeleteLeads);
}
    
    @future
    Public static void deleteLeadRecords(Set<id> SetTodeleteLeads)
    {
        //Set<id> Ids = SetTodeleteLeads;
        System.debug('IdsToDeleterecords>>>'+SetTodeleteLeads);
        //delete [Select id from Lead where id In:SetTodeleteLeads];
    }

    public static Map<String, Integer> countryCodeMap = new Map<String, Integer>{
        'Andorra' => 376,
        'United Arab Emirates' => 971,
        'Afghanistan' => 93,
        'Antigua and Barbuda' => 1268,
        'Anguilla' => 1264,
        'Armenia' => 374,
        'Bonaire, Curaçao, Saba, Sint Eustatius, and Sint Maarten' => 599,
        'Angola' => 244,
        'Antarctica' => 672,
        'Argentina' => 54,
        'American Samoa' => 1684,
        'Austria' => 43,
        'Australia' => 61,
        'Aruba' => 297,
        'Aland Islands' => 358,
        'Azerbaijan' => 994,
        'Bosnia and Herzegovina' => 387,
        'Barbados' => 1246,
        'Bangladesh' => 880,
        'Belgium' => 32,
        'Burkina Faso' => 226,
        'Bulgaria' => 359,
        'Bahrain' => 973,
        'Burundi' => 257,
        'Benin' => 229,
        'Bermuda' => 1441,
        'Brunei Darussalam' => 673,
        'Bolivia' => 591,
        'Brazil' => 55,
        'Bahamas' => 1242,
        'Bhutan' => 975,
        'Bouvet Island' => 47,
        'Botswana' => 267,
        'Belarus' => 375,
        'Belize' => 501,
        'Canada' => 1,
        'Caribbean Nations' => 1,  // (shared dialing code, like Jamaica, Barbados etc.)
        'Cocos (Keeling) Islands' => 61,
        'Democratic Republic of the Congo' => 243,
        'Central African Republic' => 236,
        'Congo' => 242,
        'Switzerland' => 41,
        'Cote D\'Ivoire (Ivory Coast)' => 225,
        'Cook Islands' => 682,
        'Chile' => 56,
        'Cameroon' => 237,
        'China' => 86,
        'Colombia' => 57,
        'Costa Rica' => 506,
        'Serbia' => 381,
        'Cuba' => 53,
        'Cabo Verde' => 238,
        'Christmas Island' => 61,
        'Cyprus' => 357,
        'Czech Republic' => 420,
        'Germany' => 49,
        'Djibouti' => 253,
        'Denmark' => 45,
        'Dominica' => 1767,
        'Dominican Republic' => 1809,
        'Algeria' => 213,
        'Ecuador' => 593,
        'Estonia' => 372,
        'Egypt' => 20,
        'Western Sahara' => 212,
        'Eritrea' => 291,
        'Spain' => 34,
        'Ethiopia' => 251,
        'Finland' => 358,
        'Fiji' => 679,
        'Falkland Islands' => 500,
        'Micronesia' => 691,
        'Faroe Islands' => 298,
        'France' => 33,
        'Gabon' => 241,
        'United Kingdom' => 44,
        'Grenada' => 1473,
        'Georgia' => 995,
        'French Guiana' => 594,
        'Guernsey' => 44,
        'Ghana' => 233,
        'Gibraltar' => 350,
        'Greenland' => 299,
        'Gambia' => 220,
        'Guinea' => 224,
        'Guadeloupe' => 590,
        'Equatorial Guinea' => 240,
        'Greece' => 30,
        'South Georgia and the South Sandwich Islands' => 500,
        'Guatemala' => 502,
        'Guam' => 1671,
        'Guinea-Bissau' => 245,
        'Guyana' => 592,
        'Hong Kong' => 852,
        'Honduras' => 504,
        'Croatia' => 385,
        'Haiti' => 509,
        'Hungary' => 36,
        'Indonesia' => 62,
        'Ireland' => 353,
        'Israel' => 972,
        'Isle of Man' => 44,
        'India' => 91,
        'British Indian Ocean Territory' => 246,
        'Iraq' => 964,
        'Iran' => 98,
        'Iceland' => 354,
        'Italy' => 39,
        'Jersey' => 44,
        'Jamaica' => 1876,
        'Jordan' => 962,
        'Japan' => 81,
        'Kenya' => 254,
        'Kyrgyzstan' => 996,
        'Cambodia' => 855,
        'Kiribati' => 686,
        'Comoros' => 269,
        'Saint Kitts and Nevis' => 1869,
        'North Korea' => 850,
        'South Korea' => 82,
        'Kuwait' => 965,
        'Cayman Islands' => 1345,
        'Kazakhstan' => 7,
        'Laos' => 856,
        'Lebanon' => 961,
        'Saint Lucia' => 1758,
        'Liechtenstein' => 423,
        'Sri Lanka' => 94,
        'Liberia' => 231,
        'Lesotho' => 266,
        'Lithuania' => 370,
        'Luxembourg' => 352,
        'Latvia' => 371,
        'Libya' => 218,
        'Morocco' => 212,
        'Monaco' => 377,
        'Moldova' => 373,
        'Montenegro' => 382,
        'Saint Martin' => 590,
        'Madagascar' => 261,
        'Marshall Islands' => 692,
        'North Macedonia' => 389,
        'Mali' => 223,
        'Myanmar' => 95,
        'Mongolia' => 976,
        'Macau' => 853,
        'Northern Mariana Islands' => 1670,
        'Martinique' => 596,
        'Mauritania' => 222,
        'Montserrat' => 1664,
        'Malta' => 356,
        'Mauritius' => 230,
        'Maldives' => 960,
        'Malawi' => 265,
        'Mexico' => 52,
        'Malaysia' => 60,
        'Mozambique' => 258,
        'Namibia' => 264,
        'New Caledonia' => 687,
        'Niger' => 227,
        'Norfolk Island' => 672,
        'Nigeria' => 234,
        'Nicaragua' => 505,
        'Netherlands' => 31,
        'Norway' => 47,
        'Nepal' => 977,
        'Nauru' => 674,
        'Niue' => 683,
        'New Zealand' => 64,
        'Oman' => 968,
        'Panama' => 507,
        'Peru' => 51,
        'French Polynesia' => 689,
        'Papua New Guinea' => 675,
        'Philippines' => 63,
        'Pakistan' => 92,
        'Poland' => 48,
        'Saint Pierre and Miquelon' => 508,
        'Pitcairn' => 64,
        'Puerto Rico' => 1,
        'Palestine' => 970,
        'Portugal' => 351,
        'Palau' => 680,
        'Paraguay' => 595,
        'Qatar' => 974,
        'Reunion' => 262,
        'Romania' => 40,
        'Serbia and Montenegro' => 381,
        'Russia' => 7,
        'Rwanda' => 250,
        'Saudi Arabia' => 966,
        'Solomon Islands' => 677,
        'Seychelles' => 248,
        'Sudan' => 249,
        'Sweden' => 46,
        'Singapore' => 65,
        'Saint Helena' => 290,
        'Slovenia' => 386,
        'Svalbard and Jan Mayen' => 47,
        'Slovakia' => 421,
        'Sierra Leone' => 232,
        'San Marino' => 378,
        'Senegal' => 221,
        'Somalia' => 252,
        'Suriname' => 597,
        'South Sudan' => 211,
        'Sao Tome and Principe' => 239,
        'El Salvador' => 503,
        'Sint Maarten' => 1721,
        'Syria' => 963,
        'Eswatini' => 268,
        'Turks and Caicos Islands' => 1649,
        'Chad' => 235,
        'French Southern Territories' => 262,
        'Togo' => 228,
        'Thailand' => 66,
        'Tajikistan' => 992,
        'Tokelau' => 690,
        'Timor-Leste' => 670,
        'Turkmenistan' => 993,
        'Tunisia' => 216,
        'Tonga' => 676,
        'Turkey' => 90,
        'Trinidad and Tobago' => 1868,
        'Tuvalu' => 688,
        'Taiwan' => 886,
        'Tanzania' => 255,
        'Ukraine' => 380,
        'Uganda' => 256,
        'United States Minor Outlying Islands' => 1,
        'United States' => 1,
        'Uruguay' => 598,
        'Uzbekistan' => 998,
        'Vatican City' => 379,
        'Saint Vincent and the Grenadines' => 1784,
        'Venezuela' => 58,
        'British Virgin Islands' => 1284,
        'U.S. Virgin Islands' => 1340,
        'Vietnam' => 84,
        'Vanuatu' => 678,
        'Wallis and Futuna' => 681,
        'Samoa' => 685,
        'Yemen' => 967,
        'Mayotte' => 262,
        'South Africa' => 27,
        'Zambia' => 260,
        'Zimbabwe' => 263
    };
}