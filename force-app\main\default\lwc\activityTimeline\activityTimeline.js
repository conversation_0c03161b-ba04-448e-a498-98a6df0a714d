import { LightningElement, api, track, wire } from 'lwc';
import getActivities from '@salesforce/apex/TimelineActivityLogController.getActivities';
import { NavigationMixin } from 'lightning/navigation';

const PAGE_SIZE = 5; // Number of activities per page

export default class ActivityTimeline extends NavigationMixin(LightningElement) {
    @api recordId;
    @track activities = [];
    @track groupedActivities = [];
    @track isModalOpen = false;
    @track selectedFilters = [];
    @track startDate = null;
    @track endDate = null;
    @track activeTab = 'tab1'; // Default tab
    @track currentPage = 1;
    @track totalPages = 1;

    activityIcons = {
        'Industry change': 'utility:company',
        'Ownership change': 'utility:change_owner',
        'Membership Status Change': 'utility:meet_focus_presenter',
        'Referred By change': 'utility:internal_share',
        'RM change': 'utility:replace',
    };

    @wire(getActivities, {
        accountId: '$recordId',
        activityTypes: '$selectedFilters',
        startDate: '$startDate',
        endDate: '$endDate'
    })
    wiredActivities({ error, data }) {
        if (data) {
            this.activities = data.map(activity => ({
                ...activity,
                activityTimestamp: this.formatTimestamp(activity.activityTimestamp),
                iconName: this.activityIcons[activity.activityType] // Map to appropriate icon
            }));
            this.totalPages = Math.ceil(this.activities.length / PAGE_SIZE);
            this.groupActivitiesByDate();
        } else if (error) {
            console.error('Error fetching activities:', error);
        }
    }

    groupActivitiesByDate() {
        const grouped = {};
        const start = (this.currentPage - 1) * PAGE_SIZE;
        const end = this.currentPage * PAGE_SIZE;
        const paginatedActivities = this.activities.slice(start, end);

        paginatedActivities.forEach(activity => {
            if (
                (this.selectedFilters.length === 0 || this.selectedFilters.includes(activity.activityType)) &&
                (!this.startDate || new Date(activity.activityTimestamp) >= new Date(this.startDate)) &&
                (!this.endDate || new Date(activity.activityTimestamp) <= new Date(this.endDate))
            ) {
                const date = new Date(activity.activityTimestamp).toLocaleDateString();
                if (!grouped[date]) {
                    grouped[date] = [];
                }
                grouped[date].push(activity);
            }
        });

        this.groupedActivities = Object.keys(grouped).map(date => ({
            date,
            activities: grouped[date]
        }));
    }

    handlePageChange(event) {
        const direction = event.target.dataset.type;
        if (direction === 'next' && this.currentPage < this.totalPages) {
            this.currentPage += 1;
        } else if (direction === 'previous' && this.currentPage > 1) {
            this.currentPage -= 1;
        }
        this.groupActivitiesByDate();
    }

    get disablePrevious() {
        return this.currentPage === 1;
    }

    get disableNext() {
        return this.currentPage === this.totalPages;
    }

    openModal() {
        this.clearModalState();
        this.isModalOpen = true;
    }

    closeModal() {
        this.isModalOpen = false;
    }

    handleFilterChange(event) {
        const { value, checked } = event.target;
        if (checked) {
            this.selectedFilters = [...this.selectedFilters, value];
        } else {
            this.selectedFilters = this.selectedFilters.filter(filter => filter !== value);
        }
        this.currentPage = 1; // Reset to first page when filters change
        this.groupActivitiesByDate();
    }

    handleDateChange(event) {
        const { name, value } = event.target;
        if (name === 'startDate') {
            this.startDate = value ? new Date(value).toISOString() : null;
        } else if (name === 'endDate') {
            this.endDate = value ? new Date(value).toISOString() : null;
        }
        this.currentPage = 1; // Reset to first page when date range changes
        this.groupActivitiesByDate();
    }

    applyFilters() {
        this.currentPage = 1; // Reset to first page after applying filters
        this.groupActivitiesByDate();
        this.closeModal();
    }

    clearAllFilters() {
        this.selectedFilters = [];
        this.startDate = null;
        this.endDate = null;
        this.currentPage = 1; // Reset to first page after clearing filters
        this.clearCheckboxes();
        this.groupActivitiesByDate();
    }

    handleTabChange(event) {
        this.activeTab = event.target.dataset.tab;
    }

    handleDownload() {
        const filters = this.selectedFilters.join(',');
        const startDate = this.startDate ? this.startDate.split('T')[0] : '';
        const endDate = this.endDate ? this.endDate.split('T')[0] : '';

        const url = `/apex/TimelinePdf?accountId=${this.recordId}&startDate=${startDate}&endDate=${endDate}&filters=${filters}`;
        window.open(url, '_blank');
    }

    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
        };
        return new Intl.DateTimeFormat('en-US', options).format(date);
    }

    clearCheckboxes() {
        const checkboxes = this.template.querySelectorAll('lightning-input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    clearModalState() {
        this.clearCheckboxes();
    }

    get isTab1() {
        return this.activeTab === 'tab1';
    }

    get isTab2() {
        return this.activeTab === 'tab2';
    }

    get isTab3() {
        return this.activeTab === 'tab3';
    }
}