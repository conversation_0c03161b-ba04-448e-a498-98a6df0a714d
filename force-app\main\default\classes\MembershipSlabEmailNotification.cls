global class MembershipSlabEmailNotification implements Schedulable{

     global void execute(SchedulableContext ctx) {
     goldMembershipEmail();
         membershipSlabUpdationEmail();
     }
  
    public static void goldMembershipEmail()
    {
        List<Messaging.SingleEmailMessage> emailList = new List<Messaging.SingleEmailMessage>();
        
        String htmlContent = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html><head><title>Email template</title><link href=\'https://fonts.googleapis.com/css?family=Roboto:400,700\' rel=\'stylesheet\' type=\'text/css\'><style type="text/css">@media screen and (max-width: 600px){.mTableBox{min-height: 80px;}.mobileFlex{display: flex;flex-direction: column;padding-left: 40px;}}@font-face{font-family:\'CAVIARDREAMS\';font-style:normal;font-weight:400;src:url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/CAVIARDREAMS.TTF);}@font-face{font-family:\'CELIAS\';font-style:normal;font-weight:400;src:url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/CELIAS.TTF);}@font-face{font-family:\'CELIAS\';font-style:normal;font-weight:600;src:url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/CELIAS-BOLD.TTF);}body{font-family:\'CELIAS\';}</style></head><body style="text-align: center; margin: 0; padding-top: 10px; padding-bottom: 10px; padding-left: 0; padding-right: 0; -webkit-text-size-adjust: 100%;background-color: #f2f4f6; color: #000000" align="center"><div style="display:none; white-space:nowrap; font:15px courier; color:#ffffff; line-height:0; width:600px !important; min-width:600px !important; max-width:600px !important;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</div><table border="0" cellpadding="0" cellspacing="0" align="center" style="border:1px solid #dedede;text-align: center; vertical-align: middle; width: 600px; max-width: 600px;background-color: #ffffff;border-collapse: collapse;" width="600"><tbody><tr><td colspan="2" style="vertical-align: middle;"><img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/IPV+Logo.png" alt="Logo" style="max-width: 150px;position: relative;bottom: -25px;"></td></tr><tr style="background: #D3AA50;"><td colspan="2"><table border="0" cellpadding="0" cellspacing="0" style="height: 25px;border-collapse: collapse;"><tr><td width="200px"></td><td style="border-radius: 0 0 13px 13px;background: #fff;width: 200px;"></td><td width="200px"></td></tr></table></td></tr><tr><td colspan="2" style="background: #D3AA50;height: 15px;"></td></tr><tr><td colspan="2" style="background: #D3AA50;vertical-align: middle;"><p style="color: #fff;font-size: 30px;text-align: center;margin: 0;letter-spacing: 1px;">Welcome to IPV\'s <b>"Gold Club"</b></p></td></tr><tr><td colspan="2" style="background: #D3AA50;height: 20px;"></td></tr><tr align="center" style="background: #D3AA50;"><td colspan="2"><table border="0" cellpadding="0" cellspacing="0" width="80%" style="border-collapse:collapse;background-image: url(https://stage-ipv.s3.ap-south-1.amazonaws.com/static/bg.png);"><tr><td style="height: 20px;"></td></tr><tr><td align="center"><img style="display: block;" src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Icon.png" alt="icon" width="60px"><img style="display: block;" src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Congratulations.png" alt="congratulations" width="300px"><p style="margin: 0;height: 20px;"></p><p style="margin: 0;color:#D3AA50;line-height: 22px;letter-spacing: 1px;">on becoming a <b >Gold Member</b> with</p><p style="margin: 0;color: #D3AA50;line-height: 22px;letter-spacing: 1px;">Inflection Point Ventures</p></td></tr><tr><td style="height: 20px;"></td></tr></table></td></tr><tr><td colspan="2" style="height: 20px;background: #D3AA50;"></td></tr><tr align="center"><td  colspan="2"><table border="0" cellpadding="0" cellspacing="0" width="80%" style="border-collapse:collapse;"><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;"><b>Dear Member,</b></p></td></tr><tr><td colspan="3" style="height: 10px;"></td></tr><tr><td colspan="3"><p style="margin: 0;padding: 10px;background: #D3AA50;color: #fff;border-radius: 15px;font-weight: 500;letter-spacing: 1px">Heartiest congratulations on becoming esteemed "Gold member" with IPV! Your unwavering support and active engagement have woven an irreplaceable thread in our vibrant community\'s tapestry.</p></td></tr><tr><td colspan="3" style="height: 15px;"></td></tr><tr><td colspan="3"><p style="margin: 0;letter-spacing: 1px">As a Gold member, you are now part of an exclusive circle, and we are thrilled to offer you exceptional privileges as a token of our appreciation.</p></td></tr><tr><td colspan="3" style="height: 15px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 600;font-size: 17px;">IPV Privileges</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 600;font-size: 17px;">Gold Member</p></td></tr><tr><td colspan="3" style="height: 5px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Discount on membership fee on renewal</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">50% Discount</p></td></tr><tr><td colspan="3" style="height: 5px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Renewal Bonus (Points)</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">100</p></td></tr><tr><td colspan="3" style="height: 5px;"></td></tr><tr><td width="345px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Eligible to become a Selection Panel Member*</p></td><td width="5px"></td><td width="150px"><p class="mTableBox" style="margin: 0;padding: 10px 15px;background: #BC964A;color: #fff;border-radius: 4px;font-weight: 500;font-size: 14px;">Yes</p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;">We are thrilled to showcase your achievements and dedication to <b>growing India\'s startup ecosystem on our various platforms.</b></p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;">However, if you prefer not to have your name announced publicly, please let us know and we will respect your privacy.</p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr><tr><td colspan="3"><p style="margin: 0;">Thank you for being an invaluable part of our journey, and here\'s to many more milestones ahead!</p></td></tr><tr><td colspan="3" style="height: 20px;"></td></tr></table></td></tr><tr><td width="70%" style="background: #BC964A;color: #fff;"><table border="0" cellpadding="0" cellspacing="0" style="border-collapse:collapse;"><tr style="height: 20px"><td></td></tr><tr class="mobileFlex"><td width="50px"></td><td width="150px"><img src="https://stage-ipv.s3.ap-south-1.amazonaws.com/static/Ankur+Mittal.png" height="150px" width="150px" alt="Ankur Mittal" /></td><td><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-size: 14px;padding-bottom: 6px">Warm regards,</p><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-weight: 600;font-size: 26px;padding-bottom: 3px">Ankur Mittal</p><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-size: 14px;padding-bottom: 3px">(Co-Founder)</p><p style="margin: 0;text-align: left;padding-left: 10px;letter-spacing: 1px;font-size: 14px;">Inflection Point Ventures</p><p style="margin: 0;height: 10px;"></p></td><td width="30px;"></td></tr></table></td><td width="150px;"></td></tr><tr><td colspan="2" style="height: 30px;"></td></tr></tbody></table></body></html>';
    //Id templateId = [SELECT Id , Name FROM EmailTemplate WHERE Name = 'Gold Membership Email Template' LIMIT 1].Id;
        Date currentDay = Date.Today();
        Date nextYear = Date.Today().addDays(365);
        Map<Id, Id> relationshipManagerMap = new Map<Id , Id>();
        
        List<Account> goldMembershipAccounts = [SELECT Id ,Name , Actual_Email__c, Personal_Email__c ,Official_Email__c , Relationship_Manager__c FROM Account WHERE Membership_Slab__c = 'Gold' AND Date_of_Slab_Updation__c =: currentDay AND Membership_Slab_Validity_Upto__c =: nextYear];
        
        for(Account account : goldMembershipAccounts)
        {
            if(account.Relationship_Manager__c != null)
            {
              relationshipManagerMap.put(account.Id , account.Relationship_Manager__c);            
            }
        }
        
        Map<Id, User> userMap = new Map<Id, User>([SELECT Id, Name, Email FROM User WHERE Id IN :relationshipManagerMap.values()]);
                 
        for(Account account : goldMembershipAccounts)
        {
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setToAddresses(new String[]{account.Actual_Email__c});
            email.setSubject('Welcome to IPV\'s "Gold Club"');
            email.setHtmlBody(htmlContent);
            //email.setTemplateId(templateId);
            if(account.Relationship_Manager__c != null)
            {
                User usr = userMap.get(relationshipManagerMap.get(account.Id));
               
                if (usr != null && String.isNotBlank(usr.Email)) {
                    email.setCCAddresses(new String[]{usr.Email});                    
              }
            }
            emailList.add(email);
            System.debug('Account Name And Email >>>> ' + account.Name );
        }
        
        if (!emailList.isEmpty()) 
        {
            Messaging.sendEmail(emailList);
          System.debug('Mail Sent >>> ');
        }
    }
    
    public static void membershipSlabUpdationEmail()
    {
        List<Messaging.SingleEmailMessage> emailList = new List<Messaging.SingleEmailMessage>();
        Date currentDay = Date.today();
        Date nextYear = Date.today().addDays(365);
        String oldSlab = null; 
        String newSlab = null;
        String newValidity = null;
        Map<Id, Id> relationshipManagerMap = new Map<Id, Id>();
        
        List<Account> membershipUpdation = [SELECT Id, Name, Membership_Slab__c, Date_of_Slab_Updation__c, Membership_Slab_Validity_Upto__c , Membership_Slab_History__c , Relationship_Manager__c
                                            FROM Account
                                            WHERE (
                                            (Membership_Slab__c = 'Bronze' AND Date_of_Slab_Updation__c = :currentDay AND Membership_Slab_Validity_Upto__c = NULL)
                                            OR
                                            (Membership_Slab__c = 'Silver' AND Date_of_Slab_Updation__c = :currentDay AND Membership_Slab_Validity_Upto__c = :nextYear)
                                            OR
                                            (Membership_Slab__c = 'Gold' AND Date_of_Slab_Updation__c = :currentDay AND Membership_Slab_Validity_Upto__c = :nextYear))
                                            AND Relationship_Manager__c != null ];
        
            for (Account account : membershipUpdation) {
                if (account.Relationship_Manager__c != null) {
                    relationshipManagerMap.put(account.Id, account.Relationship_Manager__c);                
                }
            }
    
          Map<Id, User> userMap = new Map<Id, User>([SELECT Id, Name, Email FROM User WHERE Id IN :relationshipManagerMap.values()]);
        
        for (Account account : membershipUpdation) {
            String membershipSlabHistory = account.Membership_Slab_History__c;
            if (String.isNotBlank(membershipSlabHistory)) {
                // Identify the JSON objects within the long text area
                List<String> jsonEntries = new List<String>();
                Integer startIdx = 0;
                while (startIdx < membershipSlabHistory.length()) {
                    Integer endIdx = membershipSlabHistory.indexOf('}', startIdx) + 1;
                    if (endIdx > 0) {
                        String jsonEntry = membershipSlabHistory.substring(startIdx, endIdx);
                        jsonEntries.add(jsonEntry);
                        startIdx = endIdx;
                    } else {
                        break;
                    }
                }
    
                // Extract the last JSON entry
                String lastEntry = jsonEntries[jsonEntries.size() - 1];
    
                // Deserialize the last entry
                Map<String, Object> lastEntryMap = (Map<String, Object>) JSON.deserializeUntyped(lastEntry);
    
                oldSlab = (String) lastEntryMap.get('oldSlab');
                newSlab = (String) lastEntryMap.get('newSlab');
                newValidity = (String) lastEntryMap.get('newValidity');
                System.debug('Old Slab >>>>> ' + oldSlab);
                System.debug('New Slab >>>>> ' + newSlab);
            }
            if (oldSlab == '') {
                System.debug('Old Slab Is NULL For ' + account.Name + ' >>>> ' + oldSlab);
            } else if (oldSlab != null) {
                System.debug('Old Slab Is Not NULL For ' + account.Name + ' >>>>> ' + oldSlab);
            }
    
            if (account.Relationship_Manager__c != null && oldSlab != '') {
                String fullURL = URL.getOrgDomainURL().toExternalForm() + '/' + account.Id;
                User usr = userMap.get(relationshipManagerMap.get(account.Id));
    
                if (usr != null && String.isNotBlank(usr.Email)) {
                    String htmlContent = '<!DOCTYPE html>' +
                                         '<html lang="en">' +
                                         '<head></head>' +
                                         '<body>' +
                                         '<p>Hi <b>' + usr.Name + ',</b></p>' +
                                         '<p>Hope this E-Mail finds you well.</p>' +
                                         '<p>' +
                                         'This is to inform you that membership slab of <b>' + account.Name + '</b> has ' +
                                         'changed from ' + oldSlab + ' to ' + newSlab + ' on ' + account.Date_of_Slab_Updation__c.format() + '. ' +
                                         'The slab will be valid till ' + newValidity + '.' +
                                         '</p>' +
                                         '<p>Link of Member Account: <a href=" ' + fullURL + '"> ' + fullURL + ' </a></p>' +
                                         '</body>' +
                                         '</html>';
                    Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                    email.setToAddresses(new String[]{usr.Email});
                    email.setSubject('Change of Membership Slab From ' + oldSlab + ' To ' + account.Membership_Slab__c + ' - ' + account.Name );
                    email.setHtmlBody(htmlContent);
                    emailList.add(email);
    
                    System.debug('Account Name And Email >>>> ' + account.Name);
                } else {
                    System.debug('Email address is invalid for user associated with account: ' + account.Name);
                }
            }
        }
        
        if (!emailList.isEmpty()) {
            Messaging.sendEmail(emailList);
            System.debug('Mail Sent >>> ');
        }
    }
}