<apex:page Controller="GeneratePDFController" standardStylesheets="false" title="CA" applyBodyTag="false" contentType="application/pdf#CA.pdf" applyHtmlTag="false" sidebar="false" showHeader="false" renderAs="pdf">
    <html>
        
        <head>
            <style type="text/css">
                @page {
                size: A4;margin-top:10%;margin-bottom:10%;               
                    @top-right {
                        float:right;
                        content: element(header); 
                    }
                    @bottom-center {
                        content: element(footer);
                    }
                	@bottom-right {
                        content: "Page " counter(page) " of " counter(pages);
                		font-size:10px;
                    }
                }
                div.header {                    
                    width:100%;
                    position: running(header);
                }
                div.footer {
                    display: block;
                    //page-break-inside:always;
                    position: running(footer);
                }
                .page-break {
                    display:block;
                    page-break-after:always;
                }
                table{
                    table-layout: fixed;
                    width:100%;vertical-align: top;
                    //border-collapse: collapse;
                    page-break-inside: avoid !important;
                }
                div{page-break-inside: avoid !important;}
                td{vertical-align: top;
                	word-break: break-all !important;}
                table td.wrappable {
    				white-space: normal;
                	font-size:10px;
					word-wrap: break-word;
				} 
            </style>
            
        </head>
        <div class="header" >
                <img src="{!URLFOR($Resource.CompanyLogo)}" width="100%" />
            </div>
        <body>
            
            
            <apex:variable value="{!0}" var="TotalInv"/> 
            <apex:variable value="{!0}" var="TotalRed"/> 
            <apex:variable value="{!0}" var="TotalOMS"/>
            <apex:variable value="{!0}" var="TotalOMP"/>
			<apex:variable value="{!0}" var="Totalfee"/>
            <apex:variable value="{!0}" var="Total"/>
            <apex:variable value="{!0}" var="TotalGrossAmt"/> 
            <apex:variable value="{!0}" var="TotalExitFee"/> 
            <apex:variable value="{!0}" var="TotalCarryFee"/>
            <apex:variable value="{!0}" var="TotalAmtPaid"/>
			<apex:variable value="{!0}" var="TotalTDS"/>
            <apex:variable value="{!0}" var="TotalNetAmt"/>

            <center>            
                <span style="font-size:24px;"><b>FirstPort Capital</b></span><br/>     
                <span style="font-size:14px;"><b>ACCOUNT STATEMENT</b></span><br/>
                <b>{!CADate}</b>
            </center>
            <br/><br/>
            <table border="1" cellspacing="0" cellpadding="5" style="font-size:12px;">
                
              <!--  <col style="width: 35%;" />               
                <col style="width: 15%;" />
                <col style="width: 25%;" />
                <col style="width: 25%;" />-->
                <tr>
                    <td><b>{!con.Investor1__r.Name}<br/>{!con.Investor2__r.Name}</b></td>
                   <!-- <td width="35%"><b><apex:repeat value="{!cont}" var="c" >
                    		{!c.Name}<br/> 
                    	</apex:repeat></b></td>-->
                    <td width="7%">
                        
                    </td>
                    <td width="9%">
                    </td>
                    <td><b>CA Number :</b></td><td>{!con.Name}</td>
                </tr>
                <tr>
                    <td >{!con.Address__c}</td><td></td><td width="20%">
                    </td>
                    <td ><b>Resident Status :</b></td><td>{!resStatus}</td>
                   
                </tr>
                <tr>
                    <td></td><td></td> <td width="20%">
                    </td>                   
                    <td><b>Nominee :</b></td><td>{!con.Nominee_1__c}<br/>
                    {!con.Nominee_2__c}<br/>
                    {!con.Nominee_3__c}</td>
                </tr>
                <tr>
                    <td><b>Mobile : {!phone}</b></td><td></td><td width="20%">
                    </td>
                    <td>
                        <b>PAN :</b>
                    </td>
                    <td>
                        {!pan}
                    </td>
                </tr>
                <tr >
                    <td ><b>Email :</b> {!con.Email_ID__c}</td>
                    <td ></td><td width="20%">
                    </td>
                    <td >
                        <b>Bank Name :</b>
                    </td>
                    <td>
                        {!con.Bank_Name__c}
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        
                    </td>
                    <td>
                        <b>Account Number :</b>
                    </td>
                    <td>
                        {!con.Account_Number__c}
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <b>Contribution Summary : As on {!toDateWallet} (INR)</b> 
                    </td>
                    <td>
                        <b>Account IFSC :</b>
                    </td>
                    <td>
                        {!con.Account_IFSC__c}
                    </td>
                </tr>
                <tr>
                    <td width="35%">
                        <b>Capital Commitment :</b>
                    </td>
                    <td colspan="2" width="15%" align="right">
                        <apex:outputText value="{0, Number}">
                                        <apex:param value="{!totalCAAmt}"/>
                                    </apex:outputText> 
                    </td>
                    <td>
                        <b>Virtual Account :</b>
                    </td>
                    <td>
                      <!--  {!con.Virtual_Accounts__c}-->
                    </td>
                </tr>
                <tr>
                    <td  width="35%">
                        <b>Total Drawdown :</b>
                    </td>
                    <td colspan="2"  align="right">
                        <apex:outputText value="{0, Number}">
                                        <apex:param value="{!totalDrawdown}"/>
                                    </apex:outputText> 
                    </td>
                    <td>
                        <b>Virtual IFSC :</b>
                    </td>
                    <td>
                       <!-- {!con.Virtual_IFSC__c}-->
                    </td>
                </tr>
                <tr>
                    <td >
                        <b>Balance Capital Commitment :</b>
                    </td>
                    <td colspan="2" align="right">
                        <apex:outputText value="{0, Number}">
                                        <apex:param value="{!BalanceDrawdown}"/>
                                    </apex:outputText> 
                    </td>
                    <td>
                        <b>Investment Manager :</b>
                    </td>
                    <td>
                        FirstPort Ventures LLP
                    </td>
                </tr>
                <tr>
                    <td >
                        <b>Balance Available :</b>
                    </td>
                    <td colspan="2" align="right">
                        <apex:outputText value="{0, Number}">
                                        <apex:param value="{!walletBalance}"/>
                                    </apex:outputText> 
                    </td>
                    <td>
                        <b>Relationship Manager :</b>
                    </td>
                    <td>
                        {!con.Investor1__r.Account.Relationship_Manager__r.Name}
                    </td>
                </tr>
            </table>
            <br/><br/>
            <apex:pageBlock rendered="{!invList.size != 0}"> 
                <center>
                    <div class="noBreak" align="center" style="padding:2px;border:1px solid">
                        <b>Account Summary: {!CADate}</b> 
                    </div>
                </center>
                <table border="1" cellspacing="0" cellpadding="5" width="100%" style="font-size:12px;">
                    <tr>
                        <th align="center">Scheme Name</th>
                       <!-- <th align="center">Date of Allotment</th>-->
                        <th align="center">Class</th>
                        <th align="center">Units</th>
                        <th align="center">Face Value</th>
                        <th align="center">INR</th>
                    </tr>  
                    <apex:repeat value="{!invList}" var="inv" >
                        <tr>                
                            <td align="center" class="wrappable">{!inv.Startup_Round__r.Startup_Name__c}</td>
                           <!-- <td align="center"><apex:outputText value="{0,date,dd'/'MM'/'yyyy}">
                                <apex:param value="{!inv.Date_of_Allotment__c}" /> 
                                </apex:outputText></td>-->
                            <td align="center">{!inv.Investment_AIF_Class__c}</td>
                            <td align="center">
                                <apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Number_of_Units_Held__c}"/>
                                    </apex:outputText> 
                            </td>
                            <td align="center">100</td>
                             <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Investment_Amount__c}"/>
                                    </apex:outputText>    
                                </td> 
                        </tr>
                        <apex:variable var="TotalInv" value="{!TotalInv + IF(inv.Investment_Amount__c = null, 0, inv.Investment_Amount__c)}"/>
                    </apex:repeat>
                    <tr>
                    	<td colspan="4" style="text-align: right">Total</td>
                        <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!TotalInv}"/>
                                    </apex:outputText>    
                                </td> 
                    </tr>
                </table>
            </apex:pageBlock>
            <br/><br/>
            
            <apex:pageBlock rendered="{!If(AND(redList.size != 0,redList != null),true,false)}">   
                <center>
                    <div class="noBreak" align="center" style="padding:2px;border:1px solid">
                        <b>Redemption Summary: {!CADate}</b> 
                    </div>
                </center>
                <table border="1" cellspacing="0" cellpadding="5" width="100%" style="font-size:12px;">
                    
                    <tr>
                        <th align="center">Scheme Name</th>
                        <th align="center">Date of Redemption</th>
                        <th align="center">Class</th>
                        <th align="center">Units</th>
                        <th align="center">Face Value</th>
                        <th align="center">INR</th>
                    </tr>  
                    <apex:repeat value="{!redList}" var="inv" >
                        <tr>                
                            <td align="center" class="wrappable">{!inv.Startup_Round__r.Startup_Name__c}</td>
                            <td align="center"><apex:outputText value="{0,date,dd'/'MM'/'yyyy}">
                                <apex:param value="{!inv.Date_of_SH_4__c}" /> 
                                </apex:outputText></td>
                            <td align="center">{!inv.Investment_AIF_Class__c}</td>
                            <td align="center">
                                <apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Number_of_Units_Held__c}"/>
                                    </apex:outputText>  
                            </td>
                            <td align="center">100</td>
                             <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Investment_Amount__c}"/>
                                    </apex:outputText>    
                                </td>   
                        </tr>
                        <apex:variable var="TotalRed" value="{!TotalRed + IF(inv.Investment_Amount__c = null, 0, inv.Investment_Amount__c)}"/>
                    </apex:repeat>
                    <tr>
                    	<td colspan="5" style="text-align: right">Total</td>
                        <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!TotalRed}"/>
                                    </apex:outputText>    
                                </td> 
                    </tr>
                </table>
                
            </apex:pageBlock>
            <br/><br/>
            
            <apex:pageBlock rendered="{!If(AND(offMarketPurchase.size != 0,offMarketPurchase != null),true,false)}">   
                <center>
                    <div class="noBreak" align="center" style="padding:2px;border:1px solid">
                        <b>Off Market Purchase Summary: {!CADate}</b> 
                    </div>
                </center>
                <table border="1" cellspacing="0" cellpadding="5" width="100%" style="font-size:12px;">
                    
                    <tr>
                        <th align="center">Scheme Name</th>
                        <th align="center">Date of Transfer Deed</th>
                        <th align="center">Class</th>
                        <th align="center">Units</th>
                        <th align="center">Face Value</th>
                        <th align="center">INR</th>
                    </tr>  
                    <apex:repeat value="{!offMarketPurchase}" var="inv" >
                        <tbody style="page-break-inside: avoid;">
                            <tr>                
                                <td align="center" class="wrappable">{!inv.Startup_Round__r.Startup_Name__c}</td>
                                <td align="center"><apex:outputText value="{0,date,dd'/'MM'/'yyyy}">
                                <apex:param value="{!inv.Date_on_Transfer_Deed__c}" /> 
                                </apex:outputText></td>
                                <td align="center">{!inv.Investment_AIF_Class__c}</td>
                            	<td align="center">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Number_of_Units_Held__c}"/>
                                    </apex:outputText>  
                                </td>
                            	<td align="center">100</td>   
                                <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Considered_Investment_Amount__c}"/>
                                    </apex:outputText>    
                                </td>                            	
                            </tr>
                        </tbody>  
                        <apex:variable var="TotalOMP" value="{!TotalOMP + IF(inv.Considered_Investment_Amount__c = null, 0, inv.Considered_Investment_Amount__c)}"/>

                    </apex:repeat>
                    <tr>
                    	<td colspan="5" style="text-align: right">Total</td>
                        <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!TotalOMP}"/>
                                    </apex:outputText>    
                                </td> 
                    </tr>
                </table>
                
            </apex:pageBlock>
            <br/><br/>
            
            <apex:pageBlock rendered="{!If(AND(offMarketSales.size != 0,offMarketSales != null),true,false)}">   
                <center>
                    <div class="noBreak" align="center" style="padding:2px;border:1px solid">
                        <b>Off Market Sales Summary: {!CADate}</b> 
                    </div>
                </center>
                <table border="1" cellspacing="0" cellpadding="5" width="100%" style="font-size:12px;">
                    
                    <tr>
                        <th align="center">Scheme Name</th>
                        <th align="center">Date of Transfer Deed</th>
                        <th align="center">Class</th>
                        <th align="center">Units</th>
                        <th align="center">Face Value</th>
                        <th align="center">INR</th>
                    </tr>  
                    <apex:repeat value="{!offMarketSales}" var="inv" >
                        <tbody style="page-break-inside: avoid;">
                            <tr>                
                                <td align="center" class="wrappable">{!inv.Startup_Round__r.Startup_Name__c}</td>
                                <td align="center"><apex:outputText value="{0,date,dd'/'MM'/'yyyy}">
                                <apex:param value="{!inv.Date_on_Transfer_Deed__c}" /> 
                                </apex:outputText></td>
                                <td align="center">{!inv.Investment_AIF_Class__c}</td>
                            	<td align="center">
                                    <apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Number_of_Units_Held__c}"/>
                                    </apex:outputText>  
                                </td>
                            	<td align="center">100</td>                                
                            	<td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Investment_Amount__c}"/>
                                    </apex:outputText>    
                                </td>
                            </tr>
                        </tbody>  
                        <apex:variable var="TotalOMS" value="{!TotalOMS + IF(inv.Investment_Amount__c = null, 0, inv.Investment_Amount__c)}"/>

                    </apex:repeat>
                    <tr>
                    	<td colspan="5" style="text-align: right">Total</td>
                       <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!TotalOMS}"/>
                                    </apex:outputText>    
                                </td> 
                    </tr>
                </table>
                
            </apex:pageBlock>
            <br/><br/>
            
            <apex:pageBlock rendered="{!transList.size != 0}" >   
                <center>
                    <div class="noBreak" align="center" style="padding:2px;border:1px solid">
                        <b>Transaction Summary: {!CADate}</b> 
                    </div>
                </center>
                <table border="1" cellspacing="0" cellpadding="3" width="100% !important;" style="font-size:12px;">
                    
                    <tr>
                        <th align="center" width="50%">Transaction Type</th>
                        <th align="center" width="20%">Date of Transfer</th>                   
                        <th align="center" width="30%">INR</th>
                    </tr>  
                    
                    <apex:repeat value="{!transList}" var="tran" >
                        <tbody>
                            <tr>                
                                <td align="center" width="50%">{!tran.Transaction_Type__c}</td>
                                <td align="center" width="20%"><apex:outputText value="{0,date,dd'/'MM'/'yyyy}">
                                <apex:param value="{!tran.Transaction_Date__c}" /> 
                                </apex:outputText></td>                        
                                <td align="right" width="30%">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!tran.Amount__c}"/>
                                    </apex:outputText>    
                                </td>
                                <apex:variable var="Total" value="{!Total + IF(tran.Amount__c = null, 0, tran.Amount__c)}"/>
                                
                            </tr>
                        </tbody>
                    </apex:repeat>
                    <tr>
                    	<td colspan="2" style="text-align: right">Total</td>
                        <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!Total}"/>
                                    </apex:outputText>    
                                </td> 
                    </tr>
                </table>
                
            </apex:pageBlock>
            <br/><br/>
            
            <apex:pageBlock rendered="{!If(AND(feeList.size != 0,feeList != null),true,false)}">   
                <center>
                    <div class="noBreak" align="center" style="padding:2px;border:1px solid">
                        <b>Fee Deduction: {!CADate}</b> 
                    </div>
                </center>
                <table border="1" cellspacing="0" cellpadding="5" width="100%" style="font-size:12px;">
                    
                    <tr>
                        <th align="center" width="25%">Scheme Name</th>
                        <th align="center" width="20%">Date</th>
                        <th align="center" width="35%">Management Fee</th>
                        <th align="center" width="20%">INR</th>
                    </tr>  
                    <apex:repeat value="{!feeList}" var="inv" >
                        <tbody style="page-break-inside: avoid;">
                            <tr>                
                                <td align="center" class="wrappable" width="25%">{!inv.Startup_Round__r.Startup_Name__c}</td>
                                <td align="center" width="20%"><apex:outputText value="{0,date,dd'/'MM'/'yyyy}">
                                <apex:param value="{!inv.Date_of_transaction__c}" /> 
                                </apex:outputText></td>
                                <td align="center" width="35%">Management Fee Contribution</td>
                                <td align="right" width="20%">
                                    <apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Investment_Fee_Received__c}"/>
                                    </apex:outputText>
                                </td><!--Exit_Fee_received__c-->
                            </tr>
                        </tbody>
                        <apex:variable var="Totalfee" value="{!Totalfee + IF(inv.Investment_Fee_Received__c = null, 0, inv.Investment_Fee_Received__c)}"/>
                        
                    </apex:repeat>
                    <tr>
                    	<td colspan="3" style="text-align: right">Total</td>
                        <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!Totalfee}"/>
                                    </apex:outputText>    
                                </td> 
                    </tr>
                </table>
                
            </apex:pageBlock>    
            
             <apex:pageBlock rendered="{!If(AND(rePaymentList.size != 0,rePaymentList != null),true,false)}">   
                <center>
                    <div class="noBreak" align="center" style="padding:2px;border:1px solid">
                        <b>RePayment Data: {!CADate}</b> 
                    </div>
                </center>
                <table border="1" cellspacing="0" cellpadding="5" width="100%" style="font-size:12px;">
                    
                    <tr>
                        <th align="center" width="25%">Scheme Name</th>
                        <th align="center" width="20%">Gross Amount Payable</th>
                        <th align="center" width="20%">Exit Fee</th>
                        <th align="center" width="20%">Carry Fee</th>                        
						<th align="center" width="20%">Total Amount Paid including TDS</th>
                        <th align="center" width="20%">TDS Deducted</th>
                        <th align="center" width="20%">Net Amount Paid</th>
                    </tr>  
                    <apex:repeat value="{!rePaymentList}" var="inv" >
                        <tbody style="page-break-inside: avoid;">
                            <tr>                
                                <td align="center" class="wrappable" width="25%">{!inv.Startup_Round__r.Startup_Name__c}</td>
                                <td align="right" width="20%">
                                    <apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Exit_amount_to_be_transferred__c}"/>
                                    </apex:outputText>
                                </td>
                                <td align="right" width="20%">
                                    <apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Exit_Fee_received__c}"/>
                                    </apex:outputText>
                                </td>
                                <td align="right" width="20%">
                                    <apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Carry_fee_received__c}"/>
                                    </apex:outputText>
                                </td>
                                <td align="right" width="20%">
                                    <apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Total_Amount_Paid_Including_TDS__c}"/>
                                    </apex:outputText>
                                </td>
                                
                                <td align="right" width="20%">
                                    <apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.TDS_Deducted__c}"/>
                                    </apex:outputText>
                                </td>                                
                                <td align="right" width="20%">
                                    <apex:outputText value="{0, Number}">
                                        <apex:param value="{!inv.Net_Amount_Paid__c}"/>
                                    </apex:outputText>
                                </td><!--Exit_Fee_received__c-->
                            </tr>
                        </tbody>
                   		 <apex:variable var="TotalGrossAmt" value="{!TotalGrossAmt + IF(inv.Exit_amount_to_be_transferred__c = null, 0, inv.Exit_amount_to_be_transferred__c)}"/>
						 <apex:variable var="TotalExitFee" value="{!TotalExitFee + IF(inv.Exit_Fee_received__c = null, 0, inv.Exit_Fee_received__c)}"/>
                        <apex:variable var="TotalCarryFee" value="{!TotalCarryFee + IF(inv.Carry_fee_received__c = null, 0, inv.Carry_fee_received__c)}"/>
                        <apex:variable var="TotalAmtPaid" value="{!TotalAmtPaid + IF(inv.Total_Amount_Paid_Including_TDS__c = null, 0, inv.Total_Amount_Paid_Including_TDS__c)}"/>
                        <apex:variable var="TotalTDS" value="{!TotalTDS + IF(inv.TDS_Deducted__c = null, 0, inv.TDS_Deducted__c)}"/>
                        <apex:variable var="TotalNetAmt" value="{!TotalNetAmt + IF(inv.Net_Amount_Paid__c = null, 0, inv.Net_Amount_Paid__c)}"/>
                    </apex:repeat>
                    <tr>
                    	<td style="text-align: right">Total</td>
                        <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!TotalGrossAmt}"/>
                                    </apex:outputText>    
                                </td> 
                        <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!TotalExitFee}"/>
                                    </apex:outputText>    
                                </td> 
                        <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!TotalCarryFee}"/>
                                    </apex:outputText>    
                                </td> 
                        <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!TotalAmtPaid}"/>
                                    </apex:outputText>    
                                </td> 
                        <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!TotalTDS}"/>
                                    </apex:outputText>    
                                </td> 
                        <td align="right">
                                	<apex:outputText value="{0, Number}">
                                        <apex:param value="{!TotalNetAmt}"/>
                                    </apex:outputText>    
                                </td> 
                    </tr>
                                   </table>
                
            </apex:pageBlock>  
            
            <div style="font-size:13px;">
                <br/><br/>
                <b>                       
                    This is a computer-generated report hence no signature required.<br/>
                    You are requested to review this carefully <NAME_EMAIL> in case of any discrepancy. The contents of this
                    statement will be considered to be correct if no error is reported within 30 days from the date of this statement.</b>
            </div>
            
            <div class="footer" style="text-align:center;font-size:10px;">
                <b>Investment Manager:</b> FirstPort Ventures LLP<br/>
                <b>Address:</b>239, 2nd Floor Tower A, JMD Megapolis, Sector - 48, Sohna Road, Gurugram-122018, Haryana, India<br/>
                <b>Contact:</b> +91-9319393591<b>Email:</b> <EMAIL><br/>
                <b>Fund's Registration No.:</b> IN/AIF1/21-22/0874                   
            </div>
            
        </body>
        
        
    </html>
</apex:page>