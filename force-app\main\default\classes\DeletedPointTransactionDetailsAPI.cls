global class DeletedPointTransactionDetailsAPI {
    
    private static String CLASS_NAME = 'DeletedPointTransactionDetailsAPI';
    global static string accessToken;
    
    @future(callout=true)
    public static void sendDeletedPointTransactionDetails(Set<Id> transactionIds){
       /* 
        try{

            Map<String,String> credMAp = new Map<String,String>();
            String jsonData;
            String endURLSetting;
            credMAp.put('password','hello');	
            credMAp.put('mobile','8866104284');
            credMAp.put('country_code','+91');
            
            List<API_Setting__c> settingList = API_Setting__c.getall().values();
            system.debug('StartupRestAPIController API_Setting__c>>>'+settingList);
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].End_URL__c))
            {
                endURLSetting = ''+settingList[0].End_URL__c;
            }
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Username__c))
            {
                credMAp.put('mobile',''+settingList[0].Mobile_Username__c);
            }
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Password__c))
            {
                credMAp.put('password',''+settingList[0].Mobile_Password__c);
            }
            
            system.debug('--credMAp--'+credMAp);
            system.debug('--endURLSetting--'+endURLSetting);

            Map<String , Set<Id>> transactionMap = new Map<String, Set<Id>>();
            transactionMap.put('transactionIds', transactionIds);
            system.debug('transactionMap--->'+transactionMap);

            if(transactionIds != null && transactionIds.size() > 0){
             
                JSONGenerator jsonGen = JSON.createGenerator(true);
                jsonGen.writeStartObject(); 
                jsonGen.writeFieldName('transactionIds');
                jsonGen.writeObject(transactionIds);
                jsonGen.writeEndObject();
                jsonData = jsonGen.getAsString();
                system.debug('jsonData---> ' + jsonData);

                HttpRequest req = new HttpRequest();
                String enddURL = endURLSetting+'/userAuthentication/login-admin';
                
                String bodyy =  JSON.serialize(credMAp);
                system.debug('---'+bodyy);
                req.setEndpoint(enddURL);
                req.setHeader('Content-Type','application/json');
                req.setMethod('POST');
                req.setTimeout(120000);
                req.setBody(bodyy);
                Http http = new Http();
                HTTPResponse res = http.send(req);
                system.debug('****');
                System.debug(res.getStatusCode());
                System.debug('res---'+res.toString());
                System.debug('STATUS:'+res.getStatus());
                
                
                if(res.getStatusCode() == 200){
                    system.debug('Data sent'+res.getBody());
                    String jsonstr = JSON.serialize(res.getBody());
                    JSONParser parser = JSON.createParser(jsonStr);
                    Map<String, Object> results = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                    String tkn = JSON.serialize(results.get('data'));
                    Map<String, Object> token = (Map<String, Object>) JSON.deserializeUntyped(tkn);
                    accessToken = String.valueOF(token.get('token'));
                    system.debug('accessToken  '+token.get('token'));
                    system.debug('accessToken in add  '+accessToken);
                    
                    enddURL = null;
                    enddURL = endURLSetting + '/userPoints/delete-transaction';

                    system.debug('enddURL--->'+enddURL);

                    HttpRequest request = new HttpRequest();
                    request.setEndpoint(enddURL);
                    request.setHeader('Authorization','Bearer ' + accessToken);
                    request.setHeader('Content-Type','application/json');
                    request.setMethod('POST');

                    request.setTimeout(120000);
                    request.setBody(jsonData);
                    
                    Http http1 = new Http();
                    HTTPResponse res1 = http1.send(request);
                    system.debug('****');
                    System.debug(res1.getStatusCode());
                    System.debug('res--add-'+res1.getBody());
                    System.debug('STATUS:'+res1.getStatus());
                    
                }
            }

        }
        catch(Exception e){
            system.debug('Exception in Delete Points API : ' + e.getMessage());
        }
            */
    }
}